{"analysis_duration_seconds": 0.06769108772277832, "cache_configuration": {"max_size": 1000, "ttl_seconds": 3600, "current_size": 86}, "hit_miss_analysis": {"method_stats": {"get_volume": {"total_gets": 127, "hits": 107, "misses": 20, "hit_rate_percent": 84.25196850393701, "avg_get_time_ms": 0.011585897804553233, "total_sets": 0, "set_success_rate": 0}, "get_cascade_prediction": {"total_gets": 56, "hits": 41, "misses": 15, "hit_rate_percent": 73.21428571428571, "avg_get_time_ms": 0.016010410815527263, "total_sets": 0, "set_success_rate": 0}}, "overall": {"total_gets": 183, "total_hits": 148, "overall_hit_rate": 80.87431693989072}}, "component_sharing": {"sharing_effective": true, "update_consistent": true, "stats_before": {"hits": 147, "misses": 35, "volume_calculations": 96, "cascade_calculations": 91, "evictions": 0, "hit_rate": 0.8076923076923077, "cache_size": 86, "max_size": 1000, "uptime_seconds": 0.18065500259399414, "requests_per_second": 1007.4451157548547}, "stats_after": {"hits": 148, "misses": 35, "volume_calculations": 97, "cascade_calculations": 91, "evictions": 0, "hit_rate": 0.8087431693989071, "cache_size": 86, "max_size": 1000, "uptime_seconds": 0.18081402778625488, "requests_per_second": 1012.0896162787171}}, "thrashing_analysis": {"get_volume": {"recent_hit_rate": 100.0, "max_miss_streak": 0, "avg_miss_streak": 0, "redundant_calculations": 19, "total_recent_accesses": 20, "thrashing_risk": "LOW"}, "get_cascade_prediction": {"recent_hit_rate": 100.0, "max_miss_streak": 0, "avg_miss_streak": 0, "redundant_calculations": 14, "total_recent_accesses": 20, "thrashing_risk": "LOW"}}, "memory_analysis": {"growth_analysis": {"cache_size_growth": 86, "gc_objects_growth": 4}, "efficiency": {"total_operations": 371, "objects_per_operation": 0.01078167115902965}, "memory_spikes": [], "snapshots": [{"timestamp": 1753980950.479656, "label": "initial", "cache_size": 0, "gc_objects": 21965, "gc_stats": [{"collections": 44, "collected": 175, "uncollectable": 0}, {"collections": 3, "collected": 0, "uncollectable": 0}, {"collections": 1, "collected": 0, "uncollectable": 0}]}, {"timestamp": 1753980950.4913018, "label": "after_volume_test", "cache_size": 20, "gc_objects": 21960, "gc_stats": [{"collections": 44, "collected": 175, "uncollectable": 0}, {"collections": 3, "collected": 0, "uncollectable": 0}, {"collections": 2, "collected": 0, "uncollectable": 0}]}, {"timestamp": 1753980950.5018911, "label": "after_cascade_test", "cache_size": 35, "gc_objects": 21965, "gc_stats": [{"collections": 44, "collected": 175, "uncollectable": 0}, {"collections": 3, "collected": 0, "uncollectable": 0}, {"collections": 3, "collected": 0, "uncollectable": 0}]}, {"timestamp": 1753980950.518404, "label": "after_extended_test", "cache_size": 85, "gc_objects": 21967, "gc_stats": [{"collections": 44, "collected": 175, "uncollectable": 0}, {"collections": 3, "collected": 0, "uncollectable": 0}, {"collections": 4, "collected": 0, "uncollectable": 0}]}, {"timestamp": 1753980950.5383139, "label": "after_thrashing_test", "cache_size": 86, "gc_objects": 21969, "gc_stats": [{"collections": 44, "collected": 175, "uncollectable": 0}, {"collections": 3, "collected": 0, "uncollectable": 0}, {"collections": 5, "collected": 0, "uncollectable": 0}]}]}, "cache_stats": {"hits": 148, "misses": 35, "volume_calculations": 97, "cascade_calculations": 91, "evictions": 0, "hit_rate": 0.8087431693989071, "cache_size": 86, "max_size": 1000, "uptime_seconds": 0.18117785453796387, "requests_per_second": 1010.0572195574505}, "access_patterns_summary": {"set_volume": {"total_accesses": 97, "get_operations": 0, "set_operations": 97}, "get_volume": {"total_accesses": 127, "get_operations": 127, "set_operations": 0}, "set_cascade_prediction": {"total_accesses": 91, "get_operations": 0, "set_operations": 91}, "get_cascade_prediction": {"total_accesses": 56, "get_operations": 56, "set_operations": 0}}}