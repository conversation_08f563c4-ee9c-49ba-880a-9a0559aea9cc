#!/usr/bin/env python3
"""
System Status Check: Grok API, Calculations, and JSON Issues
"""

import os
import inspect

def check_grok_api():
    print('🔍 CHECKING GROK API OPERATIONAL STATUS...')
    
    # Check API key
    api_key = os.getenv('GROK_API_KEY')
    if api_key:
        if api_key.startswith('xai-') and len(api_key) >= 20:
            print('✅ GROK_API_KEY is properly formatted and available')
            print(f'   Key format: {api_key[:8]}...{api_key[-4:]} ({len(api_key)} chars)')
            return True
        else:
            print(f'❌ GROK_API_KEY has invalid format')
            return False
    else:
        print('❌ GROK_API_KEY not found in environment')
        return False

def check_api_client():
    try:
        from src.grok_api_client import GrokAPIClient
        from src.config import AppConfig
        
        config = AppConfig()
        client = GrokAPIClient(config)
        print('✅ Grok API client created successfully')
        return True
    except Exception as e:
        print(f'❌ Grok API client creation failed: {e}')
        return False

def check_calculations():
    print('\n🔍 CHECKING NEW CALCULATIONS vs FALLBACKS...')
    
    try:
        from src.utils import load_json_data
        
        # Check lunch session
        lunch_data = load_json_data('Lunch_session_enhanced_2025_07_25.json')
        print('✅ Loaded Lunch enhanced session')
        
        unit_b = lunch_data.get('grok_enhanced_calculations', {}).get('unit_b_energy_structure', {})
        energy_rate = unit_b.get('energy_accumulation', {}).get('energy_rate', 1.0)
        
        unit_a = lunch_data.get('grok_enhanced_calculations', {}).get('unit_a_foundation', {})
        v_synthetic = unit_a.get('hybrid_volume', {}).get('v_synthetic', 128.66)
        foundation_calcs = unit_a.get('foundation_calculations', {})
        
        unit_d = lunch_data.get('grok_enhanced_calculations', {}).get('unit_d_integration_validation', {})
        momentum_strength = unit_d.get('integration_validation', {}).get('key_extracted_values', {}).get('momentum_strength', 0)
        
        print(f'🔬 CALCULATION QUALITY CHECK:')
        
        # Energy rate check
        if energy_rate != 1.0:
            print(f'   Energy rate: {energy_rate} ✅ REAL CALCULATION')
            energy_good = True
        else:
            print(f'   Energy rate: {energy_rate} ❌ FALLBACK VALUE')
            energy_good = False
            
        # V_synthetic check  
        if abs(v_synthetic - 128.66) > 1.0:
            print(f'   V_synthetic: {v_synthetic} ✅ DYNAMIC CALCULATION')
            vsynthetic_good = True
        else:
            print(f'   V_synthetic: {v_synthetic} ❌ STATIC FALLBACK')
            vsynthetic_good = False
            
        # Foundation calculations check
        if foundation_calcs:
            print(f'   Foundation calcs: {len(foundation_calcs)} fields ✅ PRESENT')
            foundation_good = True
        else:
            print(f'   Foundation calcs: EMPTY ❌ API FAILURE')
            foundation_good = False
            
        # Momentum strength check
        if momentum_strength > 0:
            print(f'   Momentum strength: {momentum_strength} ✅ REAL CALCULATION')
            momentum_good = True
        else:
            print(f'   Momentum strength: {momentum_strength} ❌ FALLBACK/MISSING')
            momentum_good = False
            
        return energy_good, vsynthetic_good, foundation_good, momentum_good

    except Exception as e:
        print(f'❌ Enhanced file check failed: {e}')
        return False, False, False, False

def check_json_standardization():
    print('\n🔍 CHECKING JSON STANDARDIZATION SITUATION...')
    
    try:
        from src.utils import load_json_data, save_json_data
        print('✅ Standardized JSON functions available')
        
        issues = []
        
        # Pipeline check
        try:
            from src.pipeline import GrokPipeline
            pipeline_source = inspect.getsource(GrokPipeline)
            if 'load_json_data' in pipeline_source:
                print('✅ Pipeline uses standardized load_json_data')
            else:
                print('❌ Pipeline missing standardized load_json_data')
                issues.append('Pipeline missing load_json_data')
                
            if 'save_json_data' in pipeline_source:
                print('✅ Pipeline uses standardized save_json_data')
            else:
                print('❌ Pipeline missing standardized save_json_data')
                issues.append('Pipeline missing save_json_data')
        except Exception as e:
            print(f'⚠️ Pipeline check failed: {e}')
            issues.append(f'Pipeline check error: {e}')
        
        # Preprocessing agent check
        try:
            from src.preprocessing_agent import PreprocessingAgent
            agent_source = inspect.getsource(PreprocessingAgent)
            if 'load_json_data' in agent_source:
                print('✅ Preprocessing agent uses standardized load_json_data')
            else:
                print('❌ Preprocessing agent missing standardized load_json_data')
                issues.append('Preprocessing agent missing load_json_data')
        except Exception as e:
            print(f'⚠️ Preprocessing agent check failed: {e}')
            issues.append(f'Preprocessing agent check error: {e}')
            
        return issues

    except Exception as e:
        print(f'❌ JSON standardization check failed: {e}')
        return [f'JSON standardization check failed: {e}']

def main():
    print('=' * 60)
    print('🔍 COMPREHENSIVE SYSTEM STATUS CHECK')
    print('=' * 60)
    
    # Check Grok API
    api_key_ok = check_grok_api()
    api_client_ok = check_api_client()
    
    # Check calculations
    energy_ok, vsynthetic_ok, foundation_ok, momentum_ok = check_calculations()
    
    # Check JSON
    json_issues = check_json_standardization()
    
    print('\n📊 FINAL ASSESSMENT:')
    print('=' * 40)
    
    print(f'🔑 GROK API STATUS:')
    if api_key_ok:
        print(f'   ✅ API Key: Available and properly formatted')
    else:
        print(f'   ❌ API Key: Missing or invalid format')
        
    if api_client_ok:
        print(f'   ✅ API Client: Can be created successfully')
    else:
        print(f'   ❌ API Client: Creation failed')
    
    print(f'\n🧮 CALCULATION STATUS:')
    total_calcs = sum([energy_ok, vsynthetic_ok, foundation_ok, momentum_ok])
    print(f'   Real calculations: {total_calcs}/4 ({100*total_calcs/4:.0f}%)')
    if energy_ok:
        print(f'   ✅ Energy rate: Using real API calculations')
    else:
        print(f'   ❌ Energy rate: Using fallback values')
        
    if vsynthetic_ok:
        print(f'   ✅ V_synthetic: Using dynamic Hawkes calculations')
    else:
        print(f'   ❌ V_synthetic: Using static fallback (128.66)')
        
    if foundation_ok:
        print(f'   ✅ Foundation: API calculations present')
    else:
        print(f'   ❌ Foundation: Empty (API failure)')
        
    if momentum_ok:
        print(f'   ✅ Momentum: Real strength calculations')
    else:
        print(f'   ❌ Momentum: Missing/fallback values')
    
    print(f'\n📄 JSON STANDARDIZATION STATUS:')
    if not json_issues:
        print(f'   ✅ All systems use standardized JSON functions')
    else:
        print(f'   ⚠️ Issues found: {len(json_issues)}')
        for issue in json_issues:
            print(f'      - {issue}')
    
    print('\n🎯 CRITICAL ISSUES TO ADDRESS:')
    critical_issues = []
    
    if not api_key_ok:
        critical_issues.append('Set GROK_API_KEY environment variable')
    if not foundation_ok:
        critical_issues.append('Fix Unit A foundation_calculations API failure')
    if not vsynthetic_ok:
        critical_issues.append('Enable dynamic Hawkes v_synthetic calculation')
    if json_issues:
        critical_issues.append('Complete JSON standardization')
    
    if critical_issues:
        for i, issue in enumerate(critical_issues, 1):
            print(f'   {i}. {issue}')
    else:
        print('   🎉 No critical issues found - system operational!')

if __name__ == "__main__":
    main()