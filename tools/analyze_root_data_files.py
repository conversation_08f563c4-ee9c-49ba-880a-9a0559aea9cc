#!/usr/bin/env python3
"""
Root Directory Data Files Analysis - Categorize and organize JSON, MD, PDF files
Identifies duplications, low-quality test results, and organizational issues
"""

import os
import json
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set, Tuple
from collections import defaultdict

def analyze_root_data_files():
    """Analyze and categorize all non-Python files in root directory"""
    
    root_dir = Path("/Users/<USER>/grok-claude-automation")
    
    print(f"🔍 ROOT DIRECTORY DATA FILES ANALYSIS")
    print(f"=" * 50)
    
    # Get all data files
    json_files = list(root_dir.glob("*.json"))
    md_files = list(root_dir.glob("*.md"))
    pdf_files = list(root_dir.glob("*.pdf"))
    
    print(f"JSON files: {len(json_files)}")
    print(f"MD files: {len(md_files)}")
    print(f"PDF files: {len(pdf_files)}")
    
    return analyze_json_files(json_files), analyze_md_files(md_files), analyze_pdf_files(pdf_files)

def analyze_json_files(json_files: List[Path]) -> Dict:
    """Detailed JSON file analysis with categorization and quality assessment"""
    
    print(f"\n📊 JSON FILES DETAILED ANALYSIS")
    print(f"=" * 40)
    
    categories = {
        "📈 Session Data (Level 1)": {
            "patterns": [r".*Lvl-1.*\.json$", r".*_session_.*\.json$"],
            "files": [],
            "description": "Core market session data files"
        },
        "🔄 Tracker State Files": {
            "patterns": [r".*grokEnhanced.*\.json$"],
            "files": [],
            "description": "HTF/FVG/Liquidity tracker files"
        },
        "🧪 Validation & Test Results": {
            "patterns": [r".*validation.*\.json$", r".*test.*\.json$", r".*results.*\.json$"],
            "files": [],
            "description": "Test results and validation outputs"  
        },
        "🔮 Prediction Results": {
            "patterns": [r".*prediction.*\.json$", r".*cascade.*\.json$"],
            "files": [],
            "description": "Prediction outputs and cascade results"
        },
        "🏗️ HTF System Files": {
            "patterns": [r".*htf.*\.json$", r".*HTF.*\.json$"],
            "files": [],
            "description": "Higher Timeframe system data"
        },
        "📋 Templates & Config": {
            "patterns": [r".*template.*\.json$", r".*config.*\.json$", r".*TEMPLATE.*\.json$"],
            "files": [],
            "description": "Configuration and template files"
        },
        "📊 Analysis & Reports": {
            "patterns": [r".*analysis.*\.json$", r".*report.*\.json$", r".*comparison.*\.json$"],
            "files": [],
            "description": "Analysis outputs and reports"
        },
        "🗃️ Database Files": {
            "patterns": [r".*database.*\.json$", r".*db.*\.json$"],
            "files": [],
            "description": "Database and historical data"
        }
    }
    
    # Categorize files
    categorized_files = set()
    
    for file_path in json_files:
        filename = file_path.name
        file_categorized = False
        
        for category_name, category_info in categories.items():
            for pattern in category_info["patterns"]:
                if re.search(pattern, filename, re.IGNORECASE):
                    categories[category_name]["files"].append(file_path)
                    categorized_files.add(filename)
                    file_categorized = True
                    break
            if file_categorized:
                break
    
    # Find uncategorized files
    all_json_filenames = {f.name for f in json_files}
    uncategorized = all_json_filenames - categorized_files
    
    # Print categorization results
    total_categorized = 0
    for category_name, category_info in categories.items():
        files = category_info["files"]
        if files:
            print(f"\n{category_name} ({len(files)} files):")
            print(f"  📝 {category_info['description']}")
            total_categorized += len(files)
            
            # Show first few files as examples
            for file_path in sorted(files)[:5]:
                size_kb = file_path.stat().st_size / 1024
                print(f"    • {file_path.name} ({size_kb:.1f}KB)")
            if len(files) > 5:
                print(f"    • ... and {len(files) - 5} more")
    
    if uncategorized:
        print(f"\n❓ UNCATEGORIZED ({len(uncategorized)} files):")
        for filename in sorted(list(uncategorized)[:10]):
            print(f"  • {filename}")
        if len(uncategorized) > 10:
            print(f"  • ... and {len(uncategorized) - 10} more")
    
    print(f"\n📈 JSON SUMMARY:")
    print(f"Total files: {len(json_files)}")
    print(f"Categorized: {total_categorized}")
    print(f"Uncategorized: {len(uncategorized)}")
    print(f"Coverage: {total_categorized/len(json_files)*100:.1f}%")
    
    return analyze_json_quality(categories, json_files)

def analyze_json_quality(categories: Dict, json_files: List[Path]) -> Dict:
    """Analyze JSON file quality and identify issues"""
    
    print(f"\n🔍 JSON QUALITY ANALYSIS")
    print(f"=" * 30)
    
    issues = {
        "🔄 Potential Duplicates": [],
        "📏 Suspiciously Small Files": [],
        "📈 Suspiciously Large Files": [],
        "🔢 Multiple Versions": [],
        "⚠️ Parse Errors": []
    }
    
    # Group files by base name to find duplicates/versions
    base_groups = defaultdict(list)
    
    for file_path in json_files:
        # Extract base name (remove dates, versions, extensions)
        base_name = re.sub(r'_\d{4}[-_]\d{2}[-_]\d{2}', '', file_path.stem)
        base_name = re.sub(r'_\d{8}', '', base_name)
        base_name = re.sub(r'_20\d{6}_\d{6}', '', base_name)
        base_groups[base_name].append(file_path)
    
    # Analyze each file
    for file_path in json_files:
        size_bytes = file_path.stat().st_size
        
        # Check file size issues
        if size_bytes < 300:  # Less than 300 bytes is suspiciously small
            issues["📏 Suspiciously Small Files"].append((file_path, size_bytes))
        elif size_bytes > 50000:  # More than 50KB might be too large
            issues["📈 Suspiciously Large Files"].append((file_path, size_bytes))
        
        # Check if file can be parsed
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                
                # Check for minimal/test content
                if isinstance(data, dict) and len(str(data)) < 500:
                    issues["📏 Suspiciously Small Files"].append((file_path, len(str(data))))
                    
        except json.JSONDecodeError as e:
            issues["⚠️ Parse Errors"].append((file_path, str(e)))
        except Exception as e:
            issues["⚠️ Parse Errors"].append((file_path, f"Read error: {e}"))
    
    # Find potential duplicates and versions
    for base_name, files in base_groups.items():
        if len(files) > 1:
            issues["🔢 Multiple Versions"].append((base_name, files))
    
    # Print quality issues
    for issue_type, issue_list in issues.items():
        if issue_list:
            print(f"\n{issue_type} ({len(issue_list)} items):")
            
            if issue_type == "🔢 Multiple Versions":
                for base_name, files in issue_list[:5]:
                    print(f"  📁 {base_name}:")
                    for file_path in files:
                        print(f"    • {file_path.name}")
            else:
                for item in issue_list[:8]:
                    if isinstance(item, tuple):
                        file_path, detail = item
                        if issue_type in ["📏 Suspiciously Small Files", "📈 Suspiciously Large Files"]:
                            print(f"    • {file_path.name} ({detail} bytes)")
                        else:
                            print(f"    • {file_path.name}: {detail}")
                    else:
                        print(f"    • {item}")
                        
            if len(issue_list) > 8:
                print(f"    • ... and {len(issue_list) - 8} more")
    
    return {"categories": categories, "issues": issues}

def analyze_md_files(md_files: List[Path]) -> Dict:
    """Analyze markdown files"""
    
    print(f"\n📄 MARKDOWN FILES ANALYSIS")
    print(f"=" * 30)
    
    md_categories = {
        "📋 Documentation": [],
        "🔧 Configuration": [],
        "📊 Analysis Reports": [],
        "📝 Templates": []
    }
    
    for file_path in md_files:
        filename = file_path.name.lower()
        size_kb = file_path.stat().st_size / 1024
        
        if 'readme' in filename or 'claude' in filename:
            md_categories["📋 Documentation"].append((file_path, size_kb))
        elif 'template' in filename or 'prompt' in filename:
            md_categories["📝 Templates"].append((file_path, size_kb))
        elif 'analysis' in filename or 'report' in filename:
            md_categories["📊 Analysis Reports"].append((file_path, size_kb))
        else:
            md_categories["🔧 Configuration"].append((file_path, size_kb))
    
    for category, files in md_categories.items():
        if files:
            print(f"\n{category} ({len(files)} files):")
            for file_path, size_kb in files:
                print(f"  • {file_path.name} ({size_kb:.1f}KB)")
    
    return md_categories

def analyze_pdf_files(pdf_files: List[Path]) -> Dict:
    """Analyze PDF files"""
    
    print(f"\n📕 PDF FILES ANALYSIS")
    print(f"=" * 30)
    
    pdf_info = []
    for file_path in pdf_files:
        size_kb = file_path.stat().st_size / 1024
        pdf_info.append((file_path, size_kb))
        print(f"  • {file_path.name} ({size_kb:.1f}KB)")
    
    return pdf_info

def generate_cleanup_strategy(json_analysis: Dict, md_categories: Dict, pdf_info: List):
    """Generate comprehensive cleanup strategy"""
    
    print(f"\n🏗️ COMPREHENSIVE DATA CLEANUP STRATEGY")
    print(f"=" * 50)
    
    print(f"\n🎯 PRIORITY 1: IMMEDIATE ACTIONS")
    print(f"-" * 30)
    
    # JSON file organization
    json_moves = {
        "data/sessions/level_1/": ["📈 Session Data (Level 1)"],
        "data/trackers/": ["🔄 Tracker State Files"],
        "data/validation/": ["🧪 Validation & Test Results"],
        "data/predictions/": ["🔮 Prediction Results"],  
        "data/htf/": ["🏗️ HTF System Files"],
        "data/config/": ["📋 Templates & Config"],
        "data/analysis/": ["📊 Analysis & Reports"],
        "data/database/": ["🗃️ Database Files"]
    }
    
    print(f"📁 JSON FILE ORGANIZATION:")
    for target_dir, categories in json_moves.items():
        total_files = 0
        for category in categories:
            if category in json_analysis["categories"]:
                total_files += len(json_analysis["categories"][category]["files"])
        if total_files > 0:
            print(f"  {target_dir} ← {total_files} files")
    
    # MD file organization  
    md_moves = {
        "docs/": ["📋 Documentation", "📊 Analysis Reports"],
        "templates/": ["📝 Templates"],
        "config/": ["🔧 Configuration"]
    }
    
    print(f"\n📄 MARKDOWN FILE ORGANIZATION:")
    for target_dir, categories in md_moves.items():
        total_files = sum(len(md_categories[cat]) for cat in categories if cat in md_categories)
        if total_files > 0:
            print(f"  {target_dir} ← {total_files} files")
    
    print(f"\n📕 PDF FILES: Move to docs/references/")
    
    print(f"\n⚠️ PRIORITY 2: QUALITY ISSUES TO ADDRESS")
    print(f"-" * 30)
    
    issues = json_analysis["issues"]
    
    if issues["📏 Suspiciously Small Files"]:
        print(f"🗑️ REVIEW FOR DELETION ({len(issues['📏 Suspiciously Small Files'])} files):")
        print(f"   Small files likely to be test artifacts or empty results")
        
    if issues["🔢 Multiple Versions"]:
        print(f"🔄 CONSOLIDATE VERSIONS ({len(issues['🔢 Multiple Versions'])} groups):")
        print(f"   Keep latest/best version, archive or delete duplicates")
        
    if issues["⚠️ Parse Errors"]:
        print(f"🔧 FIX OR DELETE ({len(issues['⚠️ Parse Errors'])} files):")
        print(f"   Corrupted JSON files that cannot be parsed")
    
    print(f"\n💡 IMPLEMENTATION APPROACH")
    print(f"-" * 30)
    print(f"1. 🗂️ Create organized directory structure")
    print(f"2. 📋 Move files by category (safest first)")
    print(f"3. 🔍 Review and consolidate multiple versions")
    print(f"4. 🗑️ Delete confirmed low-quality/test files") 
    print(f"5. 🧪 Test system functionality after each step")
    print(f"6. 📝 Update any hardcoded file paths in code")

if __name__ == "__main__":
    json_analysis, md_categories, pdf_info = analyze_root_data_files()
    generate_cleanup_strategy(json_analysis, md_categories, pdf_info)