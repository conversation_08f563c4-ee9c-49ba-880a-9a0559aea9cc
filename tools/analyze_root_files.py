#!/usr/bin/env python3
"""
Root Directory Analysis - Categorize and organize 100+ root Python files
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Set
from collections import defaultdict

def analyze_root_files():
    """Analyze and categorize all Python files in root directory"""
    
    root_dir = Path("/Users/<USER>/grok-claude-automation")
    python_files = list(root_dir.glob("*.py"))
    
    print(f"🔍 ROOT DIRECTORY ANALYSIS")
    print(f"=" * 50)
    print(f"Total Python files in root: {len(python_files)}")
    
    # Categorization patterns
    categories = {
        "🎯 Core System": {
            "patterns": [r"^cli\.py$", r"^main\.py$", r"^app\.py$"],
            "files": []
        },
        "🧪 Tests & Validation": {
            "patterns": [r".*test.*", r".*validation.*", r".*validate.*", r"ground_truth.*"],
            "files": []
        },
        "🎬 Demos & Examples": {
            "patterns": [r".*demo.*", r".*example.*", r".*sample.*", r"test_.*", r".*_test$"],
            "files": []
        },
        "🤖 Predictors & Models": {
            "patterns": [r".*predictor.*", r".*prediction.*", r".*model.*", r".*classifier.*"],
            "files": []
        },
        "⚙️ Processing Scripts": {
            "patterns": [r"process_.*", r"run_.*", r".*_processor.*", r"extract.*", r"batch.*"],
            "files": []
        },
        "📊 Analysis & Reports": {
            "patterns": [r".*analysis.*", r".*analyzer.*", r".*report.*", r".*comparison.*"],
            "files": []
        },
        "🔧 Utilities & Tools": {
            "patterns": [r".*util.*", r".*tool.*", r".*helper.*", r"check_.*", r".*manager.*"],
            "files": []
        },
        "🏭 Production Systems": {
            "patterns": [r".*production.*", r"definitive.*", r".*system.*", r".*package.*"],
            "files": []
        },
        "🧬 Experimental": {
            "patterns": [r"experimental.*", r".*experiment.*", r"enhanced.*", r"corrected.*"],
            "files": []
        },
        "📈 Monte Carlo & Simulation": {
            "patterns": [r".*monte_carlo.*", r".*simulation.*", r".*ensemble.*"],
            "files": []
        },
        "🕰️ Timing & Events": {
            "patterns": [r".*timing.*", r".*event.*", r".*cascade.*", r".*hawkes.*"],
            "files": []
        },
        "🗃️ Data & Integration": {
            "patterns": [r".*integration.*", r".*data.*", r".*json.*", r".*htf.*"],
            "files": []
        }
    }
    
    # Track uncategorized files
    categorized_files = set()
    
    # Categorize files
    for file_path in python_files:
        filename = file_path.name
        file_categorized = False
        
        for category_name, category_info in categories.items():
            for pattern in category_info["patterns"]:
                if re.search(pattern, filename, re.IGNORECASE):
                    categories[category_name]["files"].append(filename)
                    categorized_files.add(filename)
                    file_categorized = True
                    break
            if file_categorized:
                break
    
    # Find uncategorized files
    all_filenames = {f.name for f in python_files}
    uncategorized = all_filenames - categorized_files
    
    # Print analysis
    print(f"\n📊 CATEGORIZATION RESULTS")
    print(f"=" * 50)
    
    total_categorized = 0
    for category_name, category_info in categories.items():
        files = category_info["files"]
        if files:
            print(f"\n{category_name} ({len(files)} files):")
            total_categorized += len(files)
            for filename in sorted(files)[:8]:  # Show first 8
                print(f"  • {filename}")
            if len(files) > 8:
                print(f"  • ... and {len(files) - 8} more")
    
    if uncategorized:
        print(f"\n❓ UNCATEGORIZED ({len(uncategorized)} files):")
        for filename in sorted(list(uncategorized)[:10]):
            print(f"  • {filename}")
        if len(uncategorized) > 10:
            print(f"  • ... and {len(uncategorized) - 10} more")
    
    print(f"\n📈 SUMMARY STATISTICS")
    print(f"=" * 30)
    print(f"Total files: {len(python_files)}")
    print(f"Categorized: {total_categorized}")
    print(f"Uncategorized: {len(uncategorized)}")
    print(f"Coverage: {total_categorized/len(python_files)*100:.1f}%")
    
    return categories, uncategorized

def generate_cleanup_strategy(categories: Dict, uncategorized: Set):
    """Generate safe cleanup and organization strategy"""
    
    print(f"\n🏗️ CLEANUP STRATEGY RECOMMENDATIONS")
    print(f"=" * 50)
    
    # Priority 1: Safe to move (no dependencies)
    safe_moves = {
        "scripts/demos/": ["🎬 Demos & Examples"],
        "scripts/analysis/": ["📊 Analysis & Reports"],  
        "scripts/processing/": ["⚙️ Processing Scripts"],
        "scripts/experimental/": ["🧬 Experimental"],
        "validation/": ["🧪 Tests & Validation"],
        "tools/": ["🔧 Utilities & Tools"]
    }
    
    print("🟢 PRIORITY 1: SAFE TO MOVE (No Core Dependencies)")
    for target_dir, category_names in safe_moves.items():
        total_files = sum(len(categories[cat]["files"]) for cat in category_names if cat in categories)
        print(f"  📁 {target_dir} ← {total_files} files")
        for cat_name in category_names:
            if cat_name in categories and categories[cat_name]["files"]:
                example_files = categories[cat_name]["files"][:3]
                print(f"     {cat_name}: {', '.join(example_files)}...")
    
    # Priority 2: Careful review needed
    careful_moves = {
        "src/prediction/models/": ["🤖 Predictors & Models"],
        "src/prediction/monte_carlo/": ["📈 Monte Carlo & Simulation"],
        "src/prediction/timing/": ["🕰️ Timing & Events"],
        "src/integration/": ["🗃️ Data & Integration"],
        "production/": ["🏭 Production Systems"]
    }
    
    print(f"\n🟡 PRIORITY 2: CAREFUL REVIEW NEEDED (May Have Dependencies)")
    for target_dir, category_names in careful_moves.items():
        total_files = sum(len(categories[cat]["files"]) for cat in category_names if cat in categories)
        print(f"  📁 {target_dir} ← {total_files} files")
    
    # Priority 3: Keep in root (core system)
    keep_in_root = ["🎯 Core System"]
    
    print(f"\n🔴 PRIORITY 3: KEEP IN ROOT")
    for cat_name in keep_in_root:
        if cat_name in categories and categories[cat_name]["files"]:
            files = categories[cat_name]["files"]
            print(f"  {cat_name}: {', '.join(files)}")
    
    print(f"\n💡 IMPLEMENTATION APPROACH")
    print(f"-" * 30)
    print(f"1. 🟢 Start with Priority 1 (safest moves)")
    print(f"2. 🧪 Test system after each category move")
    print(f"3. 🔍 Review Priority 2 files for dependencies")
    print(f"4. 📋 Create import update checklist")
    print(f"5. 🔒 Keep backups until testing complete")
    
    return safe_moves, careful_moves

def check_import_dependencies(categories: Dict):
    """Check which files might have import dependencies"""
    
    print(f"\n🔗 DEPENDENCY ANALYSIS")
    print(f"=" * 30)
    
    root_dir = Path("/Users/<USER>/grok-claude-automation")
    high_dependency_files = []
    
    # Check for files that are likely imported by others
    common_import_patterns = [
        r".*util.*",
        r".*config.*", 
        r".*client.*",
        r".*manager.*",
        r".*base.*",
        r".*common.*"
    ]
    
    all_files = []
    for category_info in categories.values():
        all_files.extend(category_info["files"])
    
    for filename in all_files:
        for pattern in common_import_patterns:
            if re.search(pattern, filename, re.IGNORECASE):
                high_dependency_files.append(filename)
                break
    
    if high_dependency_files:
        print(f"⚠️ HIGH DEPENDENCY RISK ({len(high_dependency_files)} files):")
        for filename in sorted(high_dependency_files)[:10]:
            print(f"  • {filename}")
    
    return high_dependency_files

if __name__ == "__main__":
    categories, uncategorized = analyze_root_files()
    safe_moves, careful_moves = generate_cleanup_strategy(categories, uncategorized)
    high_dep_files = check_import_dependencies(categories)
    
    print(f"\n🎯 NEXT STEPS RECOMMENDATION")
    print(f"=" * 40)
    print(f"1. Create target directories")
    print(f"2. Move Priority 1 files (lowest risk)")
    print(f"3. Update any broken imports")
    print(f"4. Test core functionality")
    print(f"5. Proceed to Priority 2 if all tests pass")