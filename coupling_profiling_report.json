{"total_duration_ms": 8.69830600277055, "timing_breakdown": {"HTF Operations": 5.2156790043227375, "Session Operations": 0.015368990716524422, "Coupling Operations": 0.03502899198792875}, "operation_timing": {"htf_load_intelligence_events": {"duration_ms": 5.168501011212356, "success": true, "result_size": 5144}, "htf_parameter_retrieval": {"duration_ms": 0.003430002834647894, "success": true, "result_size": 51}, "htf_hawkes_calculation": {"duration_ms": 0.04374799027573317, "success": true, "result_size": 24}, "session_parameter_loading": {"duration_ms": 0.0070389942266047, "success": true, "result_size": 96}, "session_hawkes_calculation": {"duration_ms": 0.008329996489919722, "success": true, "result_size": 6}, "gamma_base_calculation": {"duration_ms": 0.0062920007621869445, "success": true, "result_size": 6}, "gamma_adaptive_factors": {"duration_ms": 0.013625991414301097, "success": true, "result_size": 39}, "gamma_final_calculation": {"duration_ms": 0.005363006494008005, "success": true, "result_size": 44}, "coupling_component_calculation": {"duration_ms": 0.003051987732760608, "success": true, "result_size": 17}, "total_intensity_calculation": {"duration_ms": 0.0028850045055150986, "success": true, "result_size": 17}, "cascade_threshold_evaluation": {"duration_ms": 0.003811001079156995, "success": true, "result_size": 26}}, "calculation_results": {"htf_intensity": 461.69446801842673, "session_intensity": 0.5302, "gamma_coupling": 1.5, "lambda_total": 693.0719020276401, "cascade_triggered": true}, "coupling_reached": true, "performance_insights": {"slowest_operation": "htf_load_intelligence_events", "fastest_operation": "total_intensity_calculation"}, "profile_statistics": {"total_function_calls": 4316, "primitive_calls": 0, "profile_output": "         4316 function calls (4306 primitive calls) in 0.009 seconds\n\n   Ordered by: cumulative time\n   List reduced from 184 to 20 due to restriction <20>\n\n   ncalls  tottime  percall  cumtime  percall filename:lineno(function)\n       11    0.000    0.000    0.008    0.001 /Users/<USER>/grok-claude-automation/coupling_calculation_profiler.py:49(time_operation)\n        1    0.000    0.000    0.007    0.007 /Users/<USER>/grok-claude-automation/coupling_calculation_profiler.py:96(profile_htf_intensity_calculation)\n        1    0.000    0.000    0.005    0.005 /Users/<USER>/grok-claude-automation/src/htf_master_controller_enhanced.py:210(load_intelligence_htf_events)\n       32    0.000    0.000    0.003    0.000 /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py:1660(_log)\n       30    0.000    0.000    0.003    0.000 /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py:1517(debug)\n       31    0.000    0.000    0.002    0.000 "}}