#!/usr/bin/env python3
"""
Backward Compatibility Wrapper for utils.py
Maintains 100% compatibility while allowing gradual migration to modular structure
"""

# Import from both original and new modular locations
try:
    # Try new modular structure first
    from .infrastructure.json_utils import (
        setup_logger,
        get_logger,
        log_prediction_start,
        log_prediction_result, 
        log_error,
        log_warning,
        log_import_warning,
        log_file_operation,
        ensure_directory,
        safe_json_load,
        safe_json_save,
        load_json_data,
        save_json_data,
        logger
    )
    print("🔄 Using modular JSON utilities")
except ImportError:
    # Fallback to original implementation
    from .utils_original_backup import (
        setup_logger,
        get_logger,
        log_prediction_start,
        log_prediction_result,
        log_error, 
        log_warning,
        log_import_warning,
        log_file_operation,
        ensure_directory,
        safe_json_load,
        safe_json_save,
        load_json_data,
        save_json_data,
        logger
    )
    print("🔄 Using original JSON utilities")

# Re-export everything for backward compatibility
__all__ = [
    'setup_logger',
    'get_logger',
    'log_prediction_start',
    'log_prediction_result',
    'log_error',
    'log_warning', 
    'log_import_warning',
    'log_file_operation',
    'ensure_directory',
    'safe_json_load',
    'safe_json_save',
    'load_json_data',
    'save_json_data',
    'logger'
]