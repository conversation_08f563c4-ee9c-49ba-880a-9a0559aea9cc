#!/usr/bin/env python3
"""
Grok Unit E: Template Population
Converts calculated results from Units A-D into the final grokEnhanced template format.
"""

from typing import Dict, Any
import json
from datetime import datetime
try:
    from .tracker_state import TrackerStateManager
except ImportError:
    from tracker_state import TrackerStateManager


class TemplatePopulator:
    """Converts pipeline calculations into grokEnhanced template format."""
    
    def __init__(self):
        """Initialize the template populator."""
        self.tracker_manager = TrackerStateManager()
    
    def populate_grok_enhanced_template(self, 
                                      unit_a_results: Dict[Any, Any],
                                      unit_b_results: Dict[Any, Any], 
                                      unit_c_results: Dict[Any, Any],
                                      unit_d_results: Dict[Any, Any],
                                      session_data: Dict[Any, Any],
                                      original_tracker_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Convert pipeline results into the grokEnhanced template format with tracker updates.
        
        Args:
            unit_a_results: Foundation calculations from Unit A
            unit_b_results: Energy & structure calculations from Unit B
            unit_c_results: Advanced dynamics from Unit C
            unit_d_results: Integration & validation from Unit D
            session_data: Original session data
            original_tracker_context: Original tracker context for state updates
            
        Returns:
            Dictionary containing grokEnhanced template and updated tracker states
        """
        
        # Extract key values from each unit
        alpha_t = unit_a_results.get('hybrid_volume', {}).get('alpha_t', 0)
        gamma_base = unit_a_results.get('time_dilation_base', {}).get('gamma_base', 0)
        v_hybrid = unit_a_results.get('hybrid_volume', {}).get('v_hybrid', 0)
        
        energy_rate = unit_b_results.get('energy_accumulation', {}).get('energy_rate', 0)
        structural_integrity = unit_b_results.get('structural_integrity', 0)
        intensity_coefficient = unit_b_results.get('gradient_dynamics', {}).get('intensity_coefficient', 0)
        
        momentum_strength = unit_c_results.get('temporal_momentum', {}).get('momentum_strength', 0)
        consolidation_strength = unit_c_results.get('consolidation_analysis', {}).get('consolidation_strength', 0)
        dominant_freq = unit_c_results.get('frequency_analysis', {}).get('dominant_freq', 0)
        
        integration_score = unit_d_results.get('validation_results', {}).get('integration_score', 0)
        final_confidence = unit_d_results.get('system_integration', {}).get('final_confidence', 0)
        reliability_score = unit_d_results.get('quality_metrics', {}).get('reliability_score', 0)
        
        # Create the exact grokEnhanced template structure matching your format
        grok_enhanced = {
            "session_metadata": session_data.get("session_metadata", {}),
            
            "price_data": session_data.get("price_data", {}),
            
            "session_phases": session_data.get("phase_transitions", []),
            
            "structures_identified": session_data.get("structures_identified", {}),
            
            "micro_timing_analysis": {
                "energy_accumulation": {
                    "rate_per_minute": energy_rate,
                    "time_since_last_impulse": unit_c_results.get('consolidation_analysis', {}).get('consolidation_duration', 60),
                    "buildup_speed": "moderate" if energy_rate < 2.0 else "fast",
                    "energy_state": unit_b_results.get('energy_accumulation', {}).get('accumulation_phase', 'building')
                },
                "gradient_dynamics": {
                    "intensity_trend": "strengthening" if intensity_coefficient > 1.0 else "stable",
                    "approach_rate": intensity_coefficient,
                    "magnetic_pull_state": "strong" if intensity_coefficient > 1.2 else "moderate",
                    "target_proximity": "close" if consolidation_strength > 0.8 else "distant"
                },
                "temporal_momentum": {
                    "movement_frequency_60min": int(dominant_freq * 60),
                    "time_compression_signal": momentum_strength,
                    "session_exhaustion": "low" if momentum_strength > 1.0 else "moderate",
                    "momentum_state": unit_c_results.get('temporal_momentum', {}).get('momentum_direction', 'neutral')
                },
                "consolidation_analysis": {
                    "maturity_phase": "mature" if consolidation_strength > 0.8 else "developing",
                    "time_in_phase": unit_c_results.get('consolidation_analysis', {}).get('consolidation_duration', 60),
                    "energy_coiling_rate": consolidation_strength,
                    "breakout_probability": unit_c_results.get('consolidation_analysis', {}).get('breakout_probability', 0.0)
                }
            },
            
            "behavioral_building_blocks": session_data.get("behavioral_building_blocks", {}),
            
            "fpfvg_status_tracking": session_data.get("fpfvg_status_tracking", {}),
            
            "key_events": session_data.get("key_events", []),
            
            "institutional_signatures": {
                "accumulation_phase": "active" if energy_rate > 1.5 else "inactive",
                "manipulation_phase": "detected" if abs(unit_b_results.get('gradient_dynamics', {}).get('direction_factor', 0)) > 1.0 else "none",
                "distribution_phase": "pending" if unit_c_results.get('consolidation_analysis', {}).get('breakout_probability', 0) > 0.7 else "inactive",
                "equilibrium_phase": "stable" if structural_integrity >= 0.9 else "transitioning"
            },
            
            "opus4_enhancements": self._calculate_corrected_opus4_enhancements(
                unit_a_results, unit_b_results, unit_c_results, unit_d_results, session_data, original_tracker_context
            ),
            
            "timing_enhancements": {
                "predicted_breach_time": f"{int(15.0 * gamma_base)}min_based_on_gamma_enhanced",
                "interval": f"{int(consolidation_strength * 60)}min_consolidation_window",
                "confidence_score": final_confidence,
                "notes": f"momentum_{unit_c_results.get('temporal_momentum', {}).get('momentum_direction', 'neutral')}_energy_rate_{energy_rate:.2f}"
            },
            
            "enhanced_structures": {
                "htf_precedence_adjusted": unit_d_results.get('system_integration', {}).get('units_synchronized', True),
                "fpfvg_memory_impact": f"structural_integrity_{structural_integrity:.2f}_with_momentum_factor_{momentum_strength:.2f}"
            }
        }
        
        # Calculate updated tracker states after processing
        updated_tracker_states = None
        if original_tracker_context:
            # Compile unit results for tracker update
            unit_results = {
                'unit_a_foundation': unit_a_results,
                'unit_b_energy_structure': unit_b_results, 
                'unit_c_advanced_dynamics': unit_c_results,
                'unit_d_integration_validation': unit_d_results
            }
            
            updated_tracker_states = self.tracker_manager.update_tracker_states_post_processing(
                unit_results, session_data, original_tracker_context
            )
        
        # Return both template and updated tracker states
        return {
            "grok_enhanced_template": grok_enhanced,
            "updated_tracker_states": updated_tracker_states,
            "template_metadata": {
                "generation_method": "tracker_enhanced_template_population",
                "units_processed": ["A", "B", "C", "D"],
                "tracker_integration": original_tracker_context is not None,
                "mathematical_corrections_applied": True
            },
            "generation_timestamp": datetime.now().isoformat()
        }
    
    def _calculate_corrected_opus4_enhancements(self, unit_a_results: Dict[Any, Any],
                                              unit_b_results: Dict[Any, Any], 
                                              unit_c_results: Dict[Any, Any],
                                              unit_d_results: Dict[Any, Any],
                                              session_data: Dict[Any, Any],
                                              tracker_context: Dict[str, Any] = None) -> Dict[str, float]:
        """Calculate corrected opus4_enhancements using proper formulas."""
        # Import here to avoid circular imports
        try:
            from .opus4_corrected_formulas import create_corrected_opus4_template
        except ImportError:
            from opus4_corrected_formulas import create_corrected_opus4_template
        
        corrected = create_corrected_opus4_template(
            unit_a_results, unit_b_results, unit_c_results, unit_d_results, session_data, tracker_context
        )
        
        # Remove validation metadata for final output
        if '_validation' in corrected:
            del corrected['_validation']
            
        return corrected
    
    def _classify_liquidity_flow(self, unit_b_results: Dict[Any, Any], unit_c_results: Dict[Any, Any]) -> str:
        """Classify liquidity flow based on energy and momentum analysis."""
        energy_rate = unit_b_results.get('energy_accumulation', {}).get('energy_rate', 0)
        momentum_strength = unit_c_results.get('temporal_momentum', {}).get('momentum_strength', 0)
        
        if energy_rate > 1.5 and momentum_strength > 1.0:
            return "high_energy_directional_flow"
        elif energy_rate > 1.0 and momentum_strength < 0.5:
            return "accumulation_phase_consolidation"
        elif energy_rate < 1.0 and momentum_strength > 1.0:
            return "momentum_driven_expansion"
        else:
            return "balanced_ranging_liquidity"


def populate_template_from_pipeline_results(pipeline_results_file: str, output_file: str = None, 
                                          tracker_context_file: str = None) -> Dict[Any, Any]:
    """
    Load pipeline results and convert to grokEnhanced template format with tracker updates.
    
    Args:
        pipeline_results_file: Path to the complete pipeline results JSON file
        output_file: Optional path to save the grokEnhanced template
        tracker_context_file: Optional path to original tracker context JSON file
        
    Returns:
        Dictionary containing grokEnhanced template and updated tracker states
    """
    # Load pipeline results
    with open(pipeline_results_file, 'r') as f:
        pipeline_data = json.load(f)
    
    # Load original tracker context if provided
    original_tracker_context = None
    if tracker_context_file:
        try:
            with open(tracker_context_file, 'r') as f:
                original_tracker_context = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            print(f"Warning: Could not load tracker context from {tracker_context_file}")
    
    # Try to extract tracker context from pipeline data if not provided separately
    if not original_tracker_context:
        original_tracker_context = pipeline_data.get('tracker_context')
    
    # Extract unit results
    calculations = pipeline_data['grok_enhanced_calculations']
    unit_a = calculations['unit_a_foundation']
    unit_b = calculations['unit_b_energy_structure'] 
    unit_c = calculations['unit_c_advanced_dynamics']
    unit_d = calculations['unit_d_integration_validation']
    session_data = pipeline_data['original_session_data']
    
    # Create template populator and generate enhanced structure with tracker updates
    populator = TemplatePopulator()
    result = populator.populate_grok_enhanced_template(
        unit_a, unit_b, unit_c, unit_d, session_data, original_tracker_context
    )
    
    # Save if output file specified
    if output_file:
        with open(output_file, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"✅ Enhanced template with tracker updates saved to: {output_file}")
        
        # Also save just the template separately for compatibility
        template_only_file = output_file.replace('.json', '_template_only.json')
        with open(template_only_file, 'w') as f:
            json.dump(result['grok_enhanced_template'], f, indent=2)
        print(f"✅ Template-only version saved to: {template_only_file}")
    
    return result


if __name__ == "__main__":
    # Example usage
    result = populate_template_from_pipeline_results(
        'asia_complete_pipeline.json',
        'asia_grok_enhanced_final.json'
    )
    print("🎯 Template population complete!")