#!/usr/bin/env python3
"""
Cascade Forensics Analyzer for HTF Event Reconstruction
Implements Opus 4's forensic approach to reverse-engineer HTF events from cascade timing patterns.

Mathematical Foundation:
τ_cascade = f(λ_session, λ_HTF, λ_noise)
Inverted to: λ_HTF(t) = (1/γ) · [(1/τ_cascade - μ_s) - Σα_s·exp(-β_s·t)]

This enables HTF calibration without waiting for 20+ days of persistence data.
"""

import json
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
from datetime import datetime, timedelta
from scipy.signal import find_peaks
from scipy.optimize import curve_fit
import warnings
warnings.filterwarnings('ignore')

# Import existing HTF components
try:
    from .htf_event_detector import HTFEvent, create_htf_event_detector
    from .hawkes_cascade_predictor import HawkesCascadePredictor
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    print("⚠️ Running in standalone mode - HTF integration limited")

@dataclass
class HTFContribution:
    """HTF contribution extracted from cascade forensics."""
    cascade_session_id: str
    cascade_timestamp: str
    implied_htf_intensity: float
    htf_acceleration_minutes: float
    confidence_score: float
    baseline_deviation: float
    session_type: str

@dataclass
class ReconstructedHTFEvent:
    """HTF event reconstructed from cascade clustering."""
    event_id: str
    reconstructed_time: datetime
    event_type: str
    implied_magnitude: float
    cascades_triggered: List[Dict[str, Any]]
    confidence_score: float
    validation_method: str
    decay_parameters: Dict[str, float]

class CascadeForensicsAnalyzer:
    """
    Forensic analyzer for HTF event reconstruction from cascade timing patterns.
    
    Implements reverse engineering approach: instead of waiting for HTF persistence,
    extract HTF signatures from cascade deviations and reconstruct causal events.
    """
    
    def __init__(self, cascade_database_path: str = "/Users/<USER>/grok-claude-automation/cascade_database.json"):
        """Initialize forensics analyzer with cascade database."""
        
        # Load cascade database
        self.cascade_database = self._load_cascade_database(cascade_database_path)
        self.session_baselines = self.cascade_database['session_baselines']
        self.cascade_events = self.cascade_database['cascade_events']
        
        # HTF coupling parameters (from existing research)
        self.coupling_parameters = {
            'gamma_base': 0.3,      # Base coupling strength
            'decay_beta_h': 0.001,  # HTF decay rate (16.7 hour influence)
            'min_htf_intensity': 0.02,  # Minimum detectable HTF influence
            'cascade_delay_window': 480  # Maximum cascade delay (8 hours)
        }
        
        # Pattern recognition parameters
        self.pattern_thresholds = {
            'peak_height': 0.02,        # Minimum HTF intensity for peak detection
            'peak_distance': 240,       # Minimum 4 hours between HTF events
            'cluster_epsilon': 60,      # Time clustering tolerance (1 hour)
            'min_cluster_size': 2       # Minimum cascades per HTF event
        }
        
        # Initialize components
        self.htf_contributions = []
        self.reconstructed_events = []
        
        print("🔬 CASCADE FORENSICS ANALYZER: HTF reverse engineering initialized")
        print(f"   Cascade database: {len(self.cascade_events)} events")
        print(f"   Session baselines: {len(self.session_baselines)} session types")
        print(f"   HTF coupling γ: {self.coupling_parameters['gamma_base']}")
    
    def _load_cascade_database(self, database_path: str) -> Dict[str, Any]:
        """Load cascade database from extraction."""
        try:
            with open(database_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            raise Exception(f"Could not load cascade database: {e}")
    
    def extract_htf_contributions(self) -> List[HTFContribution]:
        """
        Extract HTF contributions from cascade timing deviations.
        
        Core forensic principle: Cascades occurring significantly earlier than
        baseline indicate HTF acceleration through coupling mechanism.
        """
        print("🔍 HTF EXTRACTION: Analyzing cascade deviations for HTF signatures")
        
        htf_contributions = []
        
        for cascade in self.cascade_events:
            session_type = cascade['session_type']
            baseline = self.session_baselines.get(session_type)
            
            if not baseline:
                continue
                
            # Calculate deviation from baseline
            baseline_time = baseline['mean_cascade_time'] 
            actual_time = cascade['cascade_time_minutes']
            deviation = baseline_time - actual_time  # Positive = early (HTF acceleration)
            
            # HTF influence criteria
            if self._is_htf_influenced(cascade, baseline, deviation):
                # Calculate implied HTF intensity
                # Formula: λ_HTF = htf_acceleration / (γ · τ_cascade)
                gamma = self.coupling_parameters['gamma_base']
                if actual_time > 0:
                    implied_λ_htf = deviation / (gamma * actual_time)
                else:
                    implied_λ_htf = 0
                
                # Calculate confidence
                confidence = self._calculate_htf_confidence(cascade, baseline, deviation)
                
                htf_contribution = HTFContribution(
                    cascade_session_id=cascade['session_id'],
                    cascade_timestamp=cascade['timestamp'],
                    implied_htf_intensity=max(0, implied_λ_htf),
                    htf_acceleration_minutes=deviation,
                    confidence_score=confidence,
                    baseline_deviation=deviation,
                    session_type=session_type
                )
                
                htf_contributions.append(htf_contribution)
        
        self.htf_contributions = htf_contributions
        
        print(f"   Extracted {len(htf_contributions)} HTF contributions")
        print(f"   Average HTF intensity: {np.mean([h.implied_htf_intensity for h in htf_contributions]):.4f}")
        
        return htf_contributions
    
    def _is_htf_influenced(self, cascade: Dict[str, Any], baseline: Dict[str, Any], deviation: float) -> bool:
        """Determine if cascade shows HTF influence."""
        
        # Criteria for HTF influence:
        # 1. Significant early occurrence (>15 minutes)
        # 2. Above baseline intensity  
        # 3. Outside statistical noise (>2 sigma)
        
        return (deviation > 15 and  # 15+ minutes early
                cascade['intensity_estimate'] > baseline['typical_intensity'] * 1.1 and  # 10% higher intensity
                abs(deviation) > baseline['std_cascade_time'] * 1.5)  # Outside 1.5-sigma
    
    def _calculate_htf_confidence(self, cascade: Dict[str, Any], baseline: Dict[str, Any], deviation: float) -> float:
        """Calculate confidence in HTF influence assessment."""
        
        # Deviation magnitude score
        deviation_score = min(abs(deviation) / 45.0, 1.0)  # Max confidence at 45min deviation
        
        # Intensity enhancement score
        intensity_ratio = cascade['intensity_estimate'] / max(baseline['typical_intensity'], 0.1)
        intensity_score = min((intensity_ratio - 1.0) / 1.0, 1.0)  # Max at 2x baseline
        
        # Statistical significance score
        z_score = abs(deviation) / max(baseline['std_cascade_time'], 5.0)
        stat_score = min(z_score / 3.0, 1.0)  # Max at 3-sigma
        
        # Context score from cascade characteristics
        context_score = 0.8 if any(term in cascade.get('context', '').lower() 
                                 for term in ['major', 'delivery', 'sweep', 'break']) else 0.6
        
        # Weighted combination
        confidence = (
            0.4 * deviation_score +
            0.25 * intensity_score +
            0.25 * stat_score +
            0.1 * context_score
        )
        
        return round(min(confidence, 1.0), 3)
    
    def reconstruct_htf_events(self) -> List[ReconstructedHTFEvent]:
        """
        Reconstruct HTF events from HTF contributions using clustering and peak detection.
        
        Approach: HTF contributions cluster around actual HTF event times.
        Use temporal clustering to identify discrete HTF events.
        """
        print("🧩 HTF RECONSTRUCTION: Clustering contributions to identify HTF events")
        
        if not self.htf_contributions:
            self.extract_htf_contributions()
        
        # Build HTF intensity timeline
        htf_timeline = self._build_htf_intensity_timeline()
        
        # Detect peaks in HTF intensity (HTF events)
        htf_events = self._detect_htf_event_peaks(htf_timeline)
        
        # Cluster cascades by temporal proximity to events
        clustered_events = self._cluster_cascades_to_events(htf_events)
        
        # Validate and refine reconstructed events
        validated_events = self._validate_reconstructed_events(clustered_events)
        
        self.reconstructed_events = validated_events
        
        print(f"   Reconstructed {len(validated_events)} HTF events")
        
        return validated_events
    
    def _build_htf_intensity_timeline(self) -> Dict[str, Any]:
        """Build timeline of implied HTF intensity from contributions."""
        
        # Convert timestamps to datetime objects and sort
        timeline_points = []
        
        for contrib in self.htf_contributions:
            # Parse timestamp (simplified - assumes same day)
            try:
                time_parts = contrib.cascade_timestamp.split(':')
                hour = int(time_parts[0])
                minute = int(time_parts[1]) if len(time_parts) > 1 else 0
                
                # Create datetime (using a reference date)
                ref_date = datetime(2025, 7, 28)  # Reference date
                timestamp = ref_date.replace(hour=hour, minute=minute)
                
                timeline_points.append({
                    'timestamp': timestamp,
                    'htf_intensity': contrib.implied_htf_intensity,
                    'confidence': contrib.confidence_score,
                    'contribution': contrib
                })
            except:
                continue
        
        # Sort by timestamp
        timeline_points.sort(key=lambda x: x['timestamp'])
        
        return {
            'timeline_points': timeline_points,
            'intensity_values': [p['htf_intensity'] for p in timeline_points],
            'timestamps': [p['timestamp'] for p in timeline_points],
            'confidence_values': [p['confidence'] for p in timeline_points]
        }
    
    def _detect_htf_event_peaks(self, htf_timeline: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect HTF events as peaks in intensity timeline."""
        
        if len(htf_timeline['intensity_values']) < 3:
            return []
        
        # Find peaks in HTF intensity
        intensity_array = np.array(htf_timeline['intensity_values'])
        
        peaks, properties = find_peaks(
            intensity_array,
            height=self.pattern_thresholds['peak_height'],
            distance=5  # Minimum 5 timeline points between peaks
        )
        
        htf_events = []
        for i, peak_idx in enumerate(peaks):
            peak_time = htf_timeline['timestamps'][peak_idx]
            peak_intensity = intensity_array[peak_idx]
            peak_confidence = htf_timeline['confidence_values'][peak_idx]
            
            # Estimate HTF event timing (cascade occurred after HTF event)
            # Backtrack based on expected delay
            estimated_delay = min(60, peak_intensity * 180)  # 1-3 hours delay estimate
            htf_event_time = peak_time - timedelta(minutes=estimated_delay)
            
            htf_event = {
                'event_id': f'htf_event_{i+1}_{htf_event_time.strftime("%H%M")}',
                'peak_timestamp': peak_time,
                'reconstructed_htf_time': htf_event_time,
                'implied_magnitude': peak_intensity,
                'confidence': peak_confidence,
                'peak_index': peak_idx
            }
            
            htf_events.append(htf_event)
        
        return htf_events
    
    def _cluster_cascades_to_events(self, htf_events: List[Dict[str, Any]]) -> List[ReconstructedHTFEvent]:
        """Cluster cascade contributions to reconstructed HTF events."""
        
        reconstructed_events = []
        
        for event in htf_events:
            htf_time = event['reconstructed_htf_time']
            
            # Find cascades likely triggered by this HTF event
            triggered_cascades = []
            
            for contrib in self.htf_contributions:
                # Parse cascade timestamp
                try:
                    time_parts = contrib.cascade_timestamp.split(':')
                    hour = int(time_parts[0])
                    minute = int(time_parts[1]) if len(time_parts) > 1 else 0
                    
                    cascade_time = htf_time.replace(hour=hour, minute=minute)
                    
                    # Check if cascade occurred after HTF event within reasonable window
                    delay = (cascade_time - htf_time).total_seconds() / 60  # minutes
                    
                    if 30 <= delay <= self.coupling_parameters['cascade_delay_window']:  # 30min to 8h delay
                        triggered_cascades.append({
                            'session_id': contrib.cascade_session_id,
                            'cascade_time': cascade_time,
                            'delay_minutes': delay,
                            'htf_acceleration': contrib.htf_acceleration_minutes,
                            'confidence': contrib.confidence_score
                        })
                except:
                    continue
            
            # Only keep events that triggered multiple cascades
            if len(triggered_cascades) >= self.pattern_thresholds['min_cluster_size']:
                # Classify event type based on characteristics
                event_type = self._classify_htf_event_type(event, triggered_cascades)
                
                # Calculate overall confidence
                cascade_confidences = [c['confidence'] for c in triggered_cascades]
                overall_confidence = (event['confidence'] + np.mean(cascade_confidences)) / 2
                
                # Estimate decay parameters
                decay_params = self._estimate_decay_parameters(triggered_cascades)
                
                reconstructed_event = ReconstructedHTFEvent(
                    event_id=event['event_id'],
                    reconstructed_time=htf_time,
                    event_type=event_type,
                    implied_magnitude=event['implied_magnitude'],
                    cascades_triggered=triggered_cascades,
                    confidence_score=round(overall_confidence, 3),
                    validation_method='cascade_clustering',
                    decay_parameters=decay_params
                )
                
                reconstructed_events.append(reconstructed_event)
        
        return reconstructed_events
    
    def _classify_htf_event_type(self, event: Dict[str, Any], cascades: List[Dict[str, Any]]) -> str:
        """Classify HTF event type based on characteristics."""
        
        magnitude = event['implied_magnitude']
        cascade_count = len(cascades)
        avg_delay = np.mean([c['delay_minutes'] for c in cascades])
        
        # Classification based on magnitude and cascade pattern
        if magnitude > 0.1 and cascade_count >= 4:
            return 'major_liquidity_event'
        elif magnitude > 0.05 and avg_delay < 120:
            return 'weekly_level_interaction'
        elif cascade_count >= 3:
            return 'session_transition_event'
        else:
            return 'minor_htf_influence'
    
    def _estimate_decay_parameters(self, cascades: List[Dict[str, Any]]) -> Dict[str, float]:
        """Estimate decay parameters from cascade timing pattern."""
        
        if len(cascades) < 3:
            return {'lambda': self.coupling_parameters['decay_beta_h'], 'r_squared': 0.0}
        
        # Extract delay times and accelerations
        delays = np.array([c['delay_minutes'] for c in cascades])
        accelerations = np.array([c['htf_acceleration'] for c in cascades])
        
        # Fit exponential decay: acceleration = A * exp(-λ * delay)
        try:
            def exp_decay(t, A, lam):
                return A * np.exp(-lam * t)
            
            popt, _ = curve_fit(exp_decay, delays, accelerations, 
                              bounds=([0, 0], [np.inf, 0.01]), maxfev=1000)
            
            A_fit, lambda_fit = popt
            
            # Calculate R-squared
            predicted = exp_decay(delays, A_fit, lambda_fit)
            ss_res = np.sum((accelerations - predicted) ** 2)
            ss_tot = np.sum((accelerations - np.mean(accelerations)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            return {
                'lambda': float(lambda_fit),
                'amplitude': float(A_fit),
                'r_squared': float(max(0, min(1, r_squared))),
                'fit_quality': 'good' if r_squared > 0.7 else 'moderate'
            }
        except:
            return {
                'lambda': self.coupling_parameters['decay_beta_h'],
                'amplitude': np.mean(accelerations) if len(accelerations) > 0 else 0,
                'r_squared': 0.0,
                'fit_quality': 'failed'
            }
    
    def _validate_reconstructed_events(self, events: List[ReconstructedHTFEvent]) -> List[ReconstructedHTFEvent]:
        """Validate reconstructed events using cross-session consistency."""
        
        validated_events = []
        
        for event in events:
            # Validation criteria
            validation_score = 0
            
            # 1. Multiple session types affected (cross-session consistency)
            session_types = set()
            for cascade in event.cascades_triggered:
                # Extract session type from session_id
                session_id = cascade['session_id'].lower()
                if 'asia' in session_id:
                    session_types.add('ASIA')
                elif 'london' in session_id:
                    session_types.add('LONDON')
                elif 'nyam' in session_id:
                    session_types.add('NY_AM')
                elif 'nypm' in session_id:
                    session_types.add('NY_PM')
            
            if len(session_types) > 1:
                validation_score += 0.3
            
            # 2. Reasonable cascade timing pattern
            delays = [c['delay_minutes'] for c in event.cascades_triggered]
            if 60 <= np.mean(delays) <= 360:  # 1-6 hour average delay
                validation_score += 0.3
            
            # 3. Consistent magnitude across cascades
            accelerations = [c['htf_acceleration'] for c in event.cascades_triggered]
            if len(accelerations) > 1 and np.std(accelerations) / np.mean(accelerations) < 0.5:
                validation_score += 0.2
            
            # 4. Strong confidence scores
            if event.confidence_score > 0.6:
                validation_score += 0.2
            
            # Accept events with validation score > 0.5
            if validation_score > 0.5:
                validated_events.append(event)
        
        return validated_events
    
    def validate_cross_session_consistency(self) -> Dict[str, float]:
        """Validate HTF events using cross-session consistency analysis."""
        
        if not self.reconstructed_events:
            self.reconstruct_htf_events()
        
        print("🔄 VALIDATION: Cross-session consistency analysis")
        
        consistency_metrics = {}
        
        # 1. Session type diversity
        all_session_types = set()
        cross_session_events = 0
        
        for event in self.reconstructed_events:
            event_session_types = set()
            for cascade in event.cascades_triggered:
                session_id = cascade['session_id'].lower()
                session_type = 'UNKNOWN'
                for st in ['asia', 'london', 'nyam', 'nypm', 'lunch', 'premarket']:
                    if st in session_id:
                        session_type = st.upper()
                        break
                
                event_session_types.add(session_type)
                all_session_types.add(session_type)
            
            if len(event_session_types) > 1:
                cross_session_events += 1
        
        consistency_metrics['session_diversity'] = len(all_session_types) / 7.0  # 7 session types
        consistency_metrics['cross_session_rate'] = cross_session_events / max(len(self.reconstructed_events), 1)
        
        # 2. Temporal decay consistency
        decay_consistencies = []
        for event in self.reconstructed_events:
            if event.decay_parameters.get('r_squared', 0) > 0.5:
                decay_consistencies.append(event.decay_parameters['r_squared'])
        
        consistency_metrics['decay_consistency'] = np.mean(decay_consistencies) if decay_consistencies else 0
        
        # 3. Overall consistency score
        consistency_metrics['overall_consistency'] = np.mean([
            consistency_metrics['session_diversity'],
            consistency_metrics['cross_session_rate'], 
            consistency_metrics['decay_consistency']
        ])
        
        print(f"   Session diversity: {consistency_metrics['session_diversity']:.3f}")
        print(f"   Cross-session rate: {consistency_metrics['cross_session_rate']:.3f}")
        print(f"   Decay consistency: {consistency_metrics['decay_consistency']:.3f}")
        print(f"   Overall consistency: {consistency_metrics['overall_consistency']:.3f}")
        
        return consistency_metrics
    
    def generate_htf_event_catalog(self) -> Dict[str, Any]:
        """Generate comprehensive HTF event catalog from forensic analysis."""
        
        if not self.reconstructed_events:
            self.reconstruct_htf_events()
        
        # Validation metrics
        consistency_metrics = self.validate_cross_session_consistency()
        
        catalog = {
            "forensic_analysis_metadata": {
                "analysis_timestamp": datetime.now().isoformat(),
                "methodology": "cascade_forensic_htf_reconstruction",
                "source_cascades": len(self.cascade_events),
                "htf_contributions_found": len(self.htf_contributions),
                "htf_events_reconstructed": len(self.reconstructed_events),
                "validation_consistency": consistency_metrics['overall_consistency']
            },
            "reconstruction_parameters": {
                "coupling_gamma": self.coupling_parameters['gamma_base'],
                "htf_decay_beta": self.coupling_parameters['decay_beta_h'],
                "min_htf_intensity": self.coupling_parameters['min_htf_intensity'],
                "cascade_delay_window_minutes": self.coupling_parameters['cascade_delay_window']
            },
            "validation_metrics": consistency_metrics,
            "reconstructed_htf_events": []
        }
        
        # Add reconstructed events
        for event in self.reconstructed_events:
            catalog["reconstructed_htf_events"].append({
                "event_id": event.event_id,
                "reconstructed_time": event.reconstructed_time.isoformat(),
                "event_type": event.event_type,
                "implied_magnitude": event.implied_magnitude,
                "confidence_score": event.confidence_score,
                "cascades_triggered": [
                    {
                        "session_id": cascade['session_id'],
                        "delay_minutes": cascade['delay_minutes'],
                        "htf_acceleration": cascade['htf_acceleration'],
                        "confidence": cascade['confidence']
                    }
                    for cascade in event.cascades_triggered
                ],
                "decay_parameters": event.decay_parameters,
                "validation_method": event.validation_method
            })
        
        return catalog
    
    def save_htf_event_catalog(self, output_path: str = "/Users/<USER>/grok-claude-automation/htf_reconstructed_events.json"):
        """Save HTF event catalog."""
        
        catalog = self.generate_htf_event_catalog()
        
        try:
            with open(output_path, 'w') as f:
                json.dump(catalog, f, indent=2)
            print(f"💾 HTF event catalog saved to: {output_path}")
            return True
        except Exception as e:
            print(f"❌ Failed to save catalog: {e}")
            return False


def main():
    """Main execution for cascade forensics analysis."""
    print("🔬 CASCADE FORENSICS ANALYZER - HTF REVERSE ENGINEERING")
    print("=" * 70)
    
    # Initialize forensics analyzer
    analyzer = CascadeForensicsAnalyzer()
    
    # Extract HTF contributions from cascade patterns
    htf_contributions = analyzer.extract_htf_contributions()
    
    # Reconstruct HTF events from contributions
    reconstructed_events = analyzer.reconstruct_htf_events()
    
    # Validate with cross-session consistency
    consistency_metrics = analyzer.validate_cross_session_consistency()
    
    # Generate and save HTF event catalog
    analyzer.save_htf_event_catalog()
    
    # Display results
    print(f"\n🎯 FORENSIC RECONSTRUCTION RESULTS:")
    print(f"   HTF contributions extracted: {len(htf_contributions)}")
    print(f"   HTF events reconstructed: {len(reconstructed_events)}")
    print(f"   Cross-session consistency: {consistency_metrics['overall_consistency']:.3f}")
    
    if reconstructed_events:
        print(f"\n🏆 TOP RECONSTRUCTED HTF EVENTS:")
        for i, event in enumerate(sorted(reconstructed_events, key=lambda x: x.confidence_score, reverse=True)[:3], 1):
            print(f"   {i}. {event.event_id}")
            print(f"      Time: {event.reconstructed_time.strftime('%H:%M')}")
            print(f"      Type: {event.event_type}")
            print(f"      Magnitude: {event.implied_magnitude:.4f}")
            print(f"      Cascades triggered: {len(event.cascades_triggered)}")
            print(f"      Confidence: {event.confidence_score:.3f}")
    
    validation_quality = "EXCELLENT" if consistency_metrics['overall_consistency'] > 0.7 else \
                        "GOOD" if consistency_metrics['overall_consistency'] > 0.5 else "MODERATE"
    
    print(f"\n✅ HTF FORENSIC RECONSTRUCTION COMPLETED")
    print(f"   Validation quality: {validation_quality}")
    print(f"   Ready for HTF system integration: {len(reconstructed_events) > 0}")
    
    return analyzer


if __name__ == "__main__":
    main()