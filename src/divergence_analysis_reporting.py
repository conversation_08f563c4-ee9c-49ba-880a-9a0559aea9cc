#!/usr/bin/env python3
"""
Divergence Analysis Reporting System
Identifies and analyzes cases where Hawkes process predictions diverge from
actual market behavior, providing insights for model improvement and
pattern recognition for edge cases.
"""

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
from src.hawkes_cascade_predictor import HawkesCascadePredictor
from src.shadow_validation_system import ShadowValidator
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import json
import numpy as np

@dataclass
class DivergenceCase:
    """Individual divergence case analysis"""
    session_file: str
    predicted_cascade_time: float
    actual_cascade_time: float
    prediction_error: float
    error_magnitude: str  # minor, moderate, major, extreme
    divergence_type: str  # early_prediction, late_prediction, missed_cascade, false_positive
    session_characteristics: Dict[str, Any]
    hawkes_parameters: Dict[str, float]
    potential_causes: List[str]
    market_context: Dict[str, Any]
    analysis_timestamp: str

@dataclass
class DivergencePattern:
    """Pattern of divergences across multiple sessions"""
    pattern_name: str
    occurrence_count: int
    average_error: float
    common_characteristics: Dict[str, Any]
    affected_sessions: List[str]
    pattern_confidence: float
    improvement_recommendations: List[str]

@dataclass
class DivergenceReport:
    """Comprehensive divergence analysis report"""
    report_timestamp: str
    total_sessions_analyzed: int
    total_divergence_cases: int
    divergence_rate: float
    divergence_cases: List[DivergenceCase]
    identified_patterns: List[DivergencePattern]
    model_performance_insights: Dict[str, Any]
    improvement_recommendations: List[str]

class DivergenceAnalyzer:
    """
    Analyzes divergences between Hawkes process predictions and actual cascades
    to identify improvement opportunities and edge case patterns.
    """
    
    def __init__(self):
        self.hawkes_predictor = HawkesCascadePredictor()
        
        # Divergence thresholds
        self.error_thresholds = {
            "minor": 2.0,      # 0-2 minutes error
            "moderate": 5.0,   # 2-5 minutes error
            "major": 10.0,     # 5-10 minutes error
            "extreme": float('inf')  # >10 minutes error
        }
        
        # Divergence type detection
        self.divergence_patterns = {
            "early_prediction": "predicted_time < actual_time - 3",
            "late_prediction": "predicted_time > actual_time + 3", 
            "missed_cascade": "prediction_error > 15",
            "false_positive": "no_actual_cascade_detected"
        }
        
        self.analyzed_cases = []
        self.identified_patterns = []
    
    def analyze_session_divergence(self, session_file: str, 
                                 actual_cascade_time: float) -> DivergenceCase:
        """
        Analyze divergence for a single session.
        
        Args:
            session_file: Path to session data file
            actual_cascade_time: Ground truth cascade timing
            
        Returns:
            Detailed divergence case analysis
        """
        
        print(f"🔍 DIVERGENCE ANALYSIS: {session_file}")
        print("=" * 50)
        
        # Load session data
        session_data = load_json_data(session_file)
        
        # Get Hawkes prediction
        prediction_result = self.hawkes_predictor.predict_cascade_timing(session_data)
        predicted_time = prediction_result.predicted_cascade_time
        prediction_error = abs(predicted_time - actual_cascade_time)
        
        # Classify error magnitude
        error_magnitude = self._classify_error_magnitude(prediction_error)
        
        # Determine divergence type
        divergence_type = self._determine_divergence_type(predicted_time, actual_cascade_time, prediction_error)
        
        # Extract session characteristics
        session_characteristics = self._extract_session_characteristics(session_data)
        
        # Get Hawkes parameters
        hawkes_parameters = {
            "mu": prediction_result.parameters_used.mu,
            "alpha": prediction_result.parameters_used.alpha,
            "beta": prediction_result.parameters_used.beta,
            "threshold": prediction_result.parameters_used.threshold
        }
        
        # Analyze potential causes
        potential_causes = self._analyze_potential_causes(
            session_data, prediction_result, actual_cascade_time
        )
        
        # Extract market context
        market_context = self._extract_market_context(session_data, prediction_result)
        
        print(f"📊 DIVERGENCE RESULTS:")
        print(f"   Predicted: {predicted_time:.1f} min")
        print(f"   Actual: {actual_cascade_time:.1f} min") 
        print(f"   Error: {prediction_error:.2f} min ({error_magnitude})")
        print(f"   Type: {divergence_type}")
        print(f"   Potential causes: {len(potential_causes)}")
        
        # Create divergence case
        case = DivergenceCase(
            session_file=session_file,
            predicted_cascade_time=predicted_time,
            actual_cascade_time=actual_cascade_time,
            prediction_error=prediction_error,
            error_magnitude=error_magnitude,
            divergence_type=divergence_type,
            session_characteristics=session_characteristics,
            hawkes_parameters=hawkes_parameters,
            potential_causes=potential_causes,
            market_context=market_context,
            analysis_timestamp=datetime.now().isoformat()
        )
        
        # Add to analyzed cases
        self.analyzed_cases.append(case)
        
        return case
    
    def _classify_error_magnitude(self, error: float) -> str:
        """Classify prediction error magnitude."""
        
        for magnitude, threshold in self.error_thresholds.items():
            if error <= threshold:
                return magnitude
        return "extreme"
    
    def _determine_divergence_type(self, predicted: float, actual: float, error: float) -> str:
        """Determine type of divergence."""
        
        if error <= 2.0:
            return "acceptable_accuracy"
        elif predicted < actual - 3.0:
            return "early_prediction"
        elif predicted > actual + 3.0:
            return "late_prediction"
        elif error > 15.0:
            return "missed_cascade"
        else:
            return "timing_inaccuracy"
    
    def _extract_session_characteristics(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key session characteristics for analysis."""
        
        price_data = session_data.get("price_data", {})
        session_metadata = session_data.get("session_metadata", {})
        
        return {
            "session_range": price_data.get("range", 0),
            "session_character": price_data.get("session_character", "unknown"),
            "price_open": price_data.get("open", 0),
            "price_close": price_data.get("close", 0),
            "price_high": price_data.get("high", 0),
            "price_low": price_data.get("low", 0),
            "session_duration": session_metadata.get("duration_minutes", 0),
            "total_price_movements": len(session_data.get("price_movements", [])),
            "total_phase_transitions": len(session_data.get("phase_transitions", []))
        }
    
    def _analyze_potential_causes(self, session_data: Dict[str, Any], 
                                prediction_result, actual_cascade_time: float) -> List[str]:
        """Analyze potential causes of divergence."""
        
        causes = []
        
        # Parameter sensitivity analysis
        params = prediction_result.parameters_used
        
        if params.mu < 0.1:
            causes.append("low_baseline_intensity")
        if params.alpha < 0.3:
            causes.append("insufficient_excitation_coefficient")
        if params.beta > 0.05:
            causes.append("fast_decay_rate")
        
        # Session characteristic analysis
        session_chars = self._extract_session_characteristics(session_data)
        
        if session_chars["session_range"] < 20:
            causes.append("low_volatility_session")
        if session_chars["session_range"] > 100:
            causes.append("extreme_volatility_session")
        
        # Event sequence analysis
        events = prediction_result.triggering_events
        if len(events) < 2:
            causes.append("insufficient_triggering_events")
        if len(events) > 10:
            causes.append("excessive_market_noise")
        
        # Timing analysis
        prediction_error = abs(prediction_result.predicted_cascade_time - actual_cascade_time)
        if prediction_error > 10:
            if "consolidation" in session_chars.get("session_character", "").lower():
                causes.append("consolidation_parameter_mismatch")
            if "expansion" in session_chars.get("session_character", "").lower():
                causes.append("expansion_parameter_mismatch")
        
        # Intensity analysis
        if prediction_result.threshold_crossed_at < prediction_result.parameters_used.threshold * 0.5:
            causes.append("insufficient_intensity_buildup")
        
        return causes
    
    def _extract_market_context(self, session_data: Dict[str, Any], 
                              prediction_result) -> Dict[str, Any]:
        """Extract relevant market context for divergence analysis."""
        
        return {
            "prediction_confidence": prediction_result.prediction_confidence,
            "methodology": prediction_result.methodology,
            "intensity_points": len(prediction_result.intensity_buildup),
            "threshold_crossed": prediction_result.threshold_crossed_at >= prediction_result.parameters_used.threshold,
            "triggering_events_count": len(prediction_result.triggering_events),
            "session_complexity": self._calculate_session_complexity(session_data)
        }
    
    def _calculate_session_complexity(self, session_data: Dict[str, Any]) -> float:
        """Calculate session complexity score."""
        
        complexity_factors = [
            len(session_data.get("price_movements", [])),
            len(session_data.get("phase_transitions", [])),
            len(session_data.keys()),  # Number of data sections
        ]
        
        return sum(complexity_factors) / 10.0  # Normalized complexity score
    
    def identify_divergence_patterns(self) -> List[DivergencePattern]:
        """Identify patterns across multiple divergence cases."""
        
        if len(self.analyzed_cases) < 3:
            print("⚠️ Insufficient cases for pattern analysis (minimum 3 required)")
            return []
        
        print(f"🔍 IDENTIFYING PATTERNS FROM {len(self.analyzed_cases)} CASES")
        print("=" * 50)
        
        patterns = []
        
        # Pattern 1: Early prediction pattern
        early_cases = [case for case in self.analyzed_cases if case.divergence_type == "early_prediction"]
        if len(early_cases) >= 2:
            pattern = self._create_pattern("early_prediction_pattern", early_cases)
            patterns.append(pattern)
        
        # Pattern 2: Late prediction pattern  
        late_cases = [case for case in self.analyzed_cases if case.divergence_type == "late_prediction"]
        if len(late_cases) >= 2:
            pattern = self._create_pattern("late_prediction_pattern", late_cases)
            patterns.append(pattern)
        
        # Pattern 3: Low volatility issues
        low_vol_cases = [
            case for case in self.analyzed_cases 
            if case.session_characteristics.get("session_range", 0) < 30
            and case.error_magnitude in ["moderate", "major", "extreme"]
        ]
        if len(low_vol_cases) >= 2:
            pattern = self._create_pattern("low_volatility_pattern", low_vol_cases)
            patterns.append(pattern)
        
        # Pattern 4: Consolidation session issues
        consolidation_cases = [
            case for case in self.analyzed_cases
            if "consolidation" in case.session_characteristics.get("session_character", "").lower()
            and case.prediction_error > 5.0
        ]
        if len(consolidation_cases) >= 2:
            pattern = self._create_pattern("consolidation_timing_pattern", consolidation_cases)
            patterns.append(pattern)
        
        # Pattern 5: Parameter sensitivity pattern
        param_sensitive_cases = [
            case for case in self.analyzed_cases
            if "parameter_mismatch" in " ".join(case.potential_causes)
        ]
        if len(param_sensitive_cases) >= 2:
            pattern = self._create_pattern("parameter_sensitivity_pattern", param_sensitive_cases)
            patterns.append(pattern)
        
        self.identified_patterns = patterns
        
        print(f"✅ IDENTIFIED {len(patterns)} PATTERNS:")
        for pattern in patterns:
            print(f"   {pattern.pattern_name}: {pattern.occurrence_count} cases, avg error {pattern.average_error:.2f}min")
        
        return patterns
    
    def _create_pattern(self, pattern_name: str, cases: List[DivergenceCase]) -> DivergencePattern:
        """Create divergence pattern from similar cases."""
        
        # Calculate statistics
        occurrence_count = len(cases)
        average_error = sum(case.prediction_error for case in cases) / occurrence_count
        
        # Find common characteristics
        common_characteristics = self._find_common_characteristics(cases)
        
        # Get affected sessions
        affected_sessions = [case.session_file for case in cases]
        
        # Calculate pattern confidence
        confidence = min(1.0, occurrence_count / len(self.analyzed_cases))
        
        # Generate improvement recommendations
        recommendations = self._generate_pattern_recommendations(pattern_name, cases, common_characteristics)
        
        return DivergencePattern(
            pattern_name=pattern_name,
            occurrence_count=occurrence_count,
            average_error=average_error,
            common_characteristics=common_characteristics,
            affected_sessions=affected_sessions,
            pattern_confidence=confidence,
            improvement_recommendations=recommendations
        )
    
    def _find_common_characteristics(self, cases: List[DivergenceCase]) -> Dict[str, Any]:
        """Find common characteristics across divergence cases."""
        
        # Analyze session characteristics
        session_ranges = [case.session_characteristics.get("session_range", 0) for case in cases]
        session_characters = [case.session_characteristics.get("session_character", "") for case in cases]
        
        # Analyze Hawkes parameters
        param_ranges = {
            "mu_range": [case.hawkes_parameters["mu"] for case in cases],
            "alpha_range": [case.hawkes_parameters["alpha"] for case in cases],
            "beta_range": [case.hawkes_parameters["beta"] for case in cases]
        }
        
        # Find most common causes
        all_causes = []
        for case in cases:
            all_causes.extend(case.potential_causes)
        
        cause_counts = {}
        for cause in all_causes:
            cause_counts[cause] = cause_counts.get(cause, 0) + 1
        
        common_causes = [cause for cause, count in cause_counts.items() if count >= len(cases) * 0.5]
        
        return {
            "avg_session_range": sum(session_ranges) / len(session_ranges),
            "common_session_character": max(set(session_characters), key=session_characters.count),
            "parameter_ranges": {
                param: {"min": min(values), "max": max(values), "avg": sum(values)/len(values)}
                for param, values in param_ranges.items()
            },
            "common_causes": common_causes,
            "avg_prediction_error": sum(case.prediction_error for case in cases) / len(cases)
        }
    
    def _generate_pattern_recommendations(self, pattern_name: str, cases: List[DivergenceCase], 
                                        common_chars: Dict[str, Any]) -> List[str]:
        """Generate improvement recommendations for identified pattern."""
        
        recommendations = []
        
        if "early_prediction" in pattern_name:
            recommendations.extend([
                "Increase beta decay rate for faster intensity reduction",
                "Lower alpha excitation coefficient to reduce cascade urgency",
                "Add session-specific timing delays for early prediction cases"
            ])
        
        elif "late_prediction" in pattern_name:
            recommendations.extend([
                "Decrease beta decay rate for longer intensity persistence", 
                "Increase alpha excitation coefficient for faster cascade buildup",
                "Lower threshold for earlier cascade detection"
            ])
        
        elif "low_volatility" in pattern_name:
            recommendations.extend([
                "Implement volatility-specific parameter scaling",
                "Add minimum intensity floor for low volatility sessions",
                "Adjust threshold dynamically based on session range"
            ])
        
        elif "consolidation" in pattern_name:
            recommendations.extend([
                "Implement consolidation-specific parameter sets",
                "Add consolidation breakout detection triggers",
                "Extend time horizons for consolidation session analysis"
            ])
        
        elif "parameter_sensitivity" in pattern_name:
            recommendations.extend([
                "Implement adaptive parameter optimization",
                "Add real-time parameter adjustment based on intensity feedback",
                "Create parameter confidence intervals for uncertainty quantification"
            ])
        
        # Add common recommendations based on error characteristics
        avg_error = common_chars.get("avg_prediction_error", 0)
        if avg_error > 10:
            recommendations.append("Consider fundamental model architecture review")
        elif avg_error > 5:
            recommendations.append("Implement fine-tuning for parameter optimization")
        
        return recommendations
    
    def generate_divergence_report(self, output_file: str = None) -> DivergenceReport:
        """Generate comprehensive divergence analysis report."""
        
        print(f"📊 GENERATING DIVERGENCE REPORT")
        print("=" * 50)
        
        # Identify patterns if not already done
        if not self.identified_patterns:
            self.identify_divergence_patterns()
        
        # Calculate overall statistics
        total_sessions = len(self.analyzed_cases)
        divergence_cases = [case for case in self.analyzed_cases if case.error_magnitude != "minor"]
        divergence_rate = len(divergence_cases) / total_sessions if total_sessions > 0 else 0
        
        # Generate model performance insights
        performance_insights = self._generate_performance_insights()
        
        # Generate overall improvement recommendations
        improvement_recommendations = self._generate_overall_recommendations()
        
        # Create report
        report = DivergenceReport(
            report_timestamp=datetime.now().isoformat(),
            total_sessions_analyzed=total_sessions,
            total_divergence_cases=len(divergence_cases),
            divergence_rate=divergence_rate,
            divergence_cases=divergence_cases,
            identified_patterns=self.identified_patterns,
            model_performance_insights=performance_insights,
            improvement_recommendations=improvement_recommendations
        )
        
        # Save report if output file specified
        if output_file:
            report_dict = {
                "divergence_analysis_report": {
                    "report_timestamp": report.report_timestamp,
                    "summary_statistics": {
                        "total_sessions_analyzed": report.total_sessions_analyzed,
                        "total_divergence_cases": report.total_divergence_cases,
                        "divergence_rate": report.divergence_rate
                    },
                    "divergence_cases": [
                        {
                            "session_file": case.session_file,
                            "prediction_error": case.prediction_error,
                            "error_magnitude": case.error_magnitude,
                            "divergence_type": case.divergence_type,
                            "potential_causes": case.potential_causes
                        }
                        for case in report.divergence_cases
                    ],
                    "identified_patterns": [
                        {
                            "pattern_name": pattern.pattern_name,
                            "occurrence_count": pattern.occurrence_count,
                            "average_error": pattern.average_error,
                            "pattern_confidence": pattern.pattern_confidence,
                            "improvement_recommendations": pattern.improvement_recommendations
                        }
                        for pattern in report.identified_patterns
                    ],
                    "model_performance_insights": report.model_performance_insights,
                    "improvement_recommendations": report.improvement_recommendations
                }
            }
            
            save_json_data(report_dict, output_file)
            print(f"📁 Divergence report saved: {output_file}")
        
        print(f"✅ DIVERGENCE REPORT GENERATED:")
        print(f"   Total sessions: {total_sessions}")
        print(f"   Divergence cases: {len(divergence_cases)}")
        print(f"   Divergence rate: {divergence_rate:.2%}")
        print(f"   Patterns identified: {len(self.identified_patterns)}")
        
        return report
    
    def _generate_performance_insights(self) -> Dict[str, Any]:
        """Generate model performance insights from divergence analysis."""
        
        insights = {}
        
        if self.analyzed_cases:
            # Error distribution
            errors = [case.prediction_error for case in self.analyzed_cases]
            insights["error_statistics"] = {
                "mean_error": sum(errors) / len(errors),
                "median_error": sorted(errors)[len(errors)//2],
                "max_error": max(errors),
                "min_error": min(errors)
            }
            
            # Error magnitude distribution
            magnitude_counts = {}
            for case in self.analyzed_cases:
                magnitude_counts[case.error_magnitude] = magnitude_counts.get(case.error_magnitude, 0) + 1
            insights["error_magnitude_distribution"] = magnitude_counts
            
            # Most common causes
            all_causes = []
            for case in self.analyzed_cases:
                all_causes.extend(case.potential_causes)
            
            cause_counts = {}
            for cause in all_causes:
                cause_counts[cause] = cause_counts.get(cause, 0) + 1
            
            insights["most_common_causes"] = dict(sorted(cause_counts.items(), key=lambda x: x[1], reverse=True)[:5])
        
        return insights
    
    def _generate_overall_recommendations(self) -> List[str]:
        """Generate overall improvement recommendations."""
        
        recommendations = []
        
        # Collect pattern-specific recommendations
        pattern_recommendations = []
        for pattern in self.identified_patterns:
            pattern_recommendations.extend(pattern.improvement_recommendations)
        
        # Prioritize most common recommendations
        rec_counts = {}
        for rec in pattern_recommendations:
            rec_counts[rec] = rec_counts.get(rec, 0) + 1
        
        # Add top recommendations
        top_recommendations = sorted(rec_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        recommendations.extend([rec[0] for rec in top_recommendations])
        
        # Add general recommendations
        if len(self.analyzed_cases) > 5:
            recommendations.extend([
                "Implement continuous parameter optimization based on recent performance",
                "Add ensemble prediction methods for improved accuracy",
                "Develop session-specific model variants for different market conditions"
            ])
        
        return recommendations

def test_divergence_analysis():
    """Test divergence analysis system."""
    
    print("🧪 TESTING DIVERGENCE ANALYSIS SYSTEM")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = DivergenceAnalyzer()
    
    # Test multiple sessions with known results
    test_sessions = [
        ('NYAM_Lvl-1_2025_07_25.json', 8.0),  # Known perfect case
        # Add more test cases with intentional errors for pattern testing
    ]
    
    # Analyze divergences
    for session_file, actual_time in test_sessions:
        try:
            case = analyzer.analyze_session_divergence(session_file, actual_time)
            print(f"   Analyzed: {session_file} (error: {case.prediction_error:.2f}min)")
        except Exception as e:
            print(f"   ⚠️ Failed to analyze {session_file}: {e}")
    
    # Generate report if we have cases
    if analyzer.analyzed_cases:
        report = analyzer.generate_divergence_report('divergence_analysis_test_report.json')
        
        print(f"✅ DIVERGENCE ANALYSIS TEST COMPLETE:")
        print(f"   Cases analyzed: {len(analyzer.analyzed_cases)}")
        print(f"   Patterns identified: {len(analyzer.identified_patterns)}")
        print(f"   Divergence rate: {report.divergence_rate:.2%}")
    
    return analyzer

if __name__ == "__main__":
    test_divergence_analysis()