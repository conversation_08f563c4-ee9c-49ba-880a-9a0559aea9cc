#!/usr/bin/env python3
"""
Corrected Opus4 Mathematical Formulas
Implements proper equations with bounds validation
"""

import math
from typing import Dict, Any, Tuple


class Opus4Calculator:
    """Implements correct Opus4 mathematical formulas with proper bounds."""
    
    def __init__(self):
        """Initialize with Opus4 constants."""
        # Fixed constants (exact values)
        self.PHI = 0.11  # Exactly 0.11, not calculated
        self.KAPPA = 0.6  # Exactly 0.6, not calculated
        self.LAMBDA_MEM = 0.06  # Memory lambda constant
        self.DELTA_ACCEL = 0.20  # Delta acceleration constant
        self.BETA_HTF = 0.35  # HTF beta constant
        
        # Parameter bounds
        self.BOUNDS = {
            'lambda_theta_dynamic': (0.02, 0.04),
            'e_threshold_adj': (400, 1000),
            'gamma_enhanced': (0.5, 1.5),
            'confidence': (0.0, 0.95),  # Max 0.95, never 1.0
            'h_score': (0.3, 0.9),
            'd_htf': (5.0, 15.0)
        }
    
    def calculate_lambda_theta_dynamic(self, session_data: Dict[Any, Any], 
                                     micro_timing: Dict[Any, Any]) -> float:
        """
        Calculate λ_theta_dynamic using proper Eq. 13 formula.
        
        Eq. 13: λ_theta = 0.8 * (1 + 0.1 * f_dom) where f_dom is normalized
        """
        # Extract consolidation durations for frequency calculation
        consolidations = micro_timing.get('consolidation_durations', [])
        expansions = micro_timing.get('expansion_velocities', [])
        
        if not consolidations or not expansions:
            return 0.025  # Default within bounds
        
        # Calculate actual dominant frequency from session rhythm
        total_cycles = len(consolidations) + len(expansions)
        session_duration = session_data.get('session_metadata', {}).get('duration_minutes', 300)
        
        # Normalize frequency (cycles per hour)
        f_dom_normalized = (total_cycles / session_duration) * 60 / 10  # Scale to 0-1 range
        f_dom_normalized = min(1.0, max(0.0, f_dom_normalized))
        
        # Apply Eq. 13
        lambda_theta = 0.8 * (1 + 0.1 * f_dom_normalized)
        
        # Convert to proper λ_theta_dynamic range (0.02-0.04)
        lambda_theta_dynamic = 0.02 + (lambda_theta - 0.8) * 0.2  # Scale to bounds
        
        return self._apply_bounds(lambda_theta_dynamic, 'lambda_theta_dynamic')
    
    def calculate_t_memory(self, micro_timing: Dict[Any, Any]) -> float:
        """
        Calculate T_memory from FPFVG interaction timing.
        Required for proper E_threshold calculation.
        """
        # Look for FPFVG interaction sequence
        fpfvg_interactions = micro_timing.get('fpfvg_interaction_sequence', [])
        
        if fpfvg_interactions:
            # Find time since last interaction
            last_interaction = fpfvg_interactions[-1]
            time_from_formation = last_interaction.get('time_from_formation', '15_minutes')
            # Extract minutes from string like "15_minutes"
            try:
                t_memory = float(time_from_formation.split('_')[0])
            except:
                t_memory = 15.0
        else:
            # Default based on session consolidation patterns
            consolidations = micro_timing.get('consolidation_durations', [])
            if consolidations:
                t_memory = sum(c.get('duration_minutes', 15) for c in consolidations) / len(consolidations)
            else:
                t_memory = 15.0
        
        return max(5.0, min(60.0, t_memory))  # Reasonable bounds
    
    def calculate_e_threshold_adj(self, energy_rate: float, t_memory: float) -> float:
        """
        Calculate E_threshold_adj using proper formula with T_memory.
        
        Formula: E_threshold_adj = 85.0 * (1 + 0.2 * exp(-λ_mem * t_memory)) * energy_scaling
        """
        # Apply memory decay
        memory_factor = 1 + 0.2 * math.exp(-self.LAMBDA_MEM * t_memory)
        energy_scaling = energy_rate * 50.0  # Scale energy rate appropriately
        
        e_threshold = 85.0 * memory_factor * energy_scaling
        
        return self._apply_bounds(e_threshold, 'e_threshold_adj')
    
    def calculate_gamma_enhanced(self, gamma_base: float, momentum_strength: float, 
                               consolidation_strength: float = 0.0) -> float:
        """
        Calculate γ_enhanced with proper consolidation interpretation.
        
        Note: γ > 1.2 indicates consolidation, not movement
        """
        # For consolidation (high consolidation_strength), reduce enhancement
        consolidation_factor = max(0.3, 1.0 - consolidation_strength * 0.5)
        
        # Apply momentum with consolidation damping
        momentum_factor = min(0.3, momentum_strength * 0.1 * consolidation_factor)
        
        gamma_enhanced = gamma_base * (1 + momentum_factor)
        
        return self._apply_bounds(gamma_enhanced, 'gamma_enhanced')
    
    def calculate_confidence_score(self, integration_score: float, 
                                 validation_results: Dict[Any, Any]) -> float:
        """
        Calculate realistic confidence score (never 1.0, max 0.95).
        """
        base_confidence = min(0.9, integration_score * 0.7)
        
        # Apply validation penalties
        consistency_bonus = 0.05 if validation_results.get('consistency_check', False) else 0.0
        convergence_bonus = 0.03 if validation_results.get('convergence_achieved', False) else 0.0
        
        confidence = base_confidence + consistency_bonus + convergence_bonus
        
        return self._apply_bounds(confidence, 'confidence')
    
    def validate_session_energy_consistency(self, energy_rate: float, 
                                          session_data: Dict[Any, Any]) -> Tuple[float, str]:
        """
        Validate energy rate against actual session range and duration.
        """
        price_data = session_data.get('price_data', {})
        session_range = price_data.get('range', 46.0)
        duration_minutes = session_data.get('session_metadata', {}).get('duration_minutes', 300)
        
        # Expected energy rate from actual price movement
        expected_energy_rate = session_range / duration_minutes * 60  # Points per hour
        expected_energy_rate *= 0.1  # Scale factor
        
        # Check consistency
        ratio = abs(energy_rate - expected_energy_rate) / expected_energy_rate
        
        if ratio < 0.2:
            consistency = "excellent"
        elif ratio < 0.5:
            consistency = "good"
        else:
            consistency = "warning_large_deviation"
            
        return expected_energy_rate, consistency
    
    def _apply_bounds(self, value: float, param_name: str) -> float:
        """Apply parameter bounds with capping."""
        if param_name not in self.BOUNDS:
            return value
            
        min_val, max_val = self.BOUNDS[param_name]
        return max(min_val, min(max_val, value))
    
    def _apply_tracker_energy_adjustment(self, energy_rate: float, t_memory: float) -> float:
        """Apply T_memory influence to energy rate calculations."""
        # T_memory decay effect on energy accumulation
        # Higher T_memory (longer carryover) increases energy sensitivity
        decay_factor = math.exp(-self.LAMBDA_MEM * (t_memory / 60.0))  # Convert to hours
        energy_adjustment = 1.0 + (1.0 - decay_factor) * 0.2  # Max 20% adjustment
        
        adjusted_energy = energy_rate * energy_adjustment
        return max(0.1, min(5.0, adjusted_energy))  # Reasonable bounds
    
    def _apply_htf_gamma_adjustment(self, gamma_base: float, htf_influence: float) -> float:
        """Apply HTF structure influence to gamma calculations."""
        # HTF structures provide stability/momentum context
        htf_adjustment = 1.0 + (htf_influence * 0.1)  # Max 10% adjustment per unit influence
        
        adjusted_gamma = gamma_base * htf_adjustment
        return max(0.8, min(2.0, adjusted_gamma))  # Reasonable gamma bounds
    
    def _apply_liquidity_momentum_adjustment(self, momentum_strength: float, 
                                           liquidity_gradient: Dict[str, float]) -> float:
        """Apply liquidity gradient effects to momentum calculations."""
        gradient_strength = liquidity_gradient.get('gradient_strength', 0.0)
        liquidity_bias = liquidity_gradient.get('liquidity_bias', 'balanced')
        
        # Liquidity gradient affects momentum persistence
        if liquidity_bias == 'upward_pull':
            bias_factor = 1.1
        elif liquidity_bias == 'downward_pull':
            bias_factor = 1.1  # Both directions increase momentum
        else:
            bias_factor = 1.0
        
        # Apply gradient strength influence
        gradient_adjustment = 1.0 + (gradient_strength * 0.05)  # Subtle influence
        
        adjusted_momentum = momentum_strength * bias_factor * gradient_adjustment
        return max(0.5, min(2.5, adjusted_momentum))  # Reasonable momentum bounds


def create_corrected_opus4_template(unit_a_results: Dict[Any, Any],
                                  unit_b_results: Dict[Any, Any], 
                                  unit_c_results: Dict[Any, Any],
                                  unit_d_results: Dict[Any, Any],
                                  session_data: Dict[Any, Any],
                                  tracker_context: Dict[str, Any] = None) -> Dict[str, float]:
    """
    Create corrected opus4_enhancements with proper formulas and bounds, enhanced with tracker context.
    """
    calc = Opus4Calculator()
    
    # Extract raw values
    gamma_base = unit_a_results.get('time_dilation_base', {}).get('gamma_base', 1.5)
    energy_rate = unit_b_results.get('energy_accumulation', {}).get('energy_rate', 1.0)
    momentum_strength = unit_c_results.get('temporal_momentum', {}).get('momentum_strength', 1.0)
    consolidation_strength = unit_c_results.get('consolidation_analysis', {}).get('consolidation_strength', 0.5)
    
    micro_timing = session_data.get('micro_timing_analysis', {})
    
    # Apply tracker context enhancements if available
    if tracker_context:
        # Apply T_memory influence to energy calculations
        t_memory = tracker_context.get('t_memory', 15.0)
        energy_rate = calc._apply_tracker_energy_adjustment(energy_rate, t_memory)
        
        # Apply HTF influence to gamma calculations
        htf_influence = tracker_context.get('htf_influence_factor', 0.0)
        gamma_base = calc._apply_htf_gamma_adjustment(gamma_base, htf_influence)
        
        # Apply liquidity gradient to momentum calculations
        liquidity_gradient = tracker_context.get('liquidity_gradient', {})
        momentum_strength = calc._apply_liquidity_momentum_adjustment(momentum_strength, liquidity_gradient)
    
    # Calculate using proper formulas
    lambda_theta_dynamic = calc.calculate_lambda_theta_dynamic(session_data, micro_timing)
    t_memory = calc.calculate_t_memory(micro_timing)
    e_threshold_adj = calc.calculate_e_threshold_adj(energy_rate, t_memory)
    gamma_enhanced = calc.calculate_gamma_enhanced(gamma_base, momentum_strength, consolidation_strength)
    confidence = calc.calculate_confidence_score(
        unit_d_results.get('validation_results', {}).get('integration_score', 1.0),
        unit_d_results.get('validation_results', {})
    )
    
    # Validate energy consistency
    expected_energy, consistency = calc.validate_session_energy_consistency(energy_rate, session_data)
    
    corrected_opus4 = {
        "h_score": calc._apply_bounds(unit_a_results.get('time_dilation_base', {}).get('h_score', 0.75), 'h_score'),
        "d_htf": calc._apply_bounds(unit_a_results.get('parameters_validated', {}).get('d_htf', 10.0), 'd_htf'),
        "v_synthetic": unit_a_results.get('hybrid_volume', {}).get('v_synthetic', 128.66),
        "lambda_theta_dynamic": lambda_theta_dynamic,
        "alpha_grad_scaled": unit_a_results.get('time_dilation_base', {}).get('alpha_grad_scaled', 0.365),
        "e_threshold_adj": e_threshold_adj,
        "phi_energy": energy_rate / 2.0,  # This is correct
        "gamma_enhanced": gamma_enhanced,
        "breach_time_min": 15.0 * gamma_enhanced,  # Use enhanced gamma
        "confidence": confidence,
        "lambda_mem": calc.LAMBDA_MEM,  # Exact constant
        "delta_accel": calc.DELTA_ACCEL,  # Exact constant  
        "phi": calc.PHI,  # Exact constant, not calculated
        "kappa": calc.KAPPA,  # Exact constant, not calculated
        "beta_htf": calc.BETA_HTF  # Exact constant
    }
    
    # Add validation metadata
    validation_metadata = {
        "t_memory_used": t_memory,
        "expected_energy_rate": expected_energy,
        "energy_consistency": consistency,
        "bounds_applied": True,
        "formula_corrections": [
            "lambda_theta_dynamic: Applied Eq. 13",
            "e_threshold_adj: Added T_memory calculation", 
            "gamma_enhanced: Applied consolidation dampening",
            "confidence: Capped at 0.95 maximum",
            "phi_kappa: Set to exact constants"
        ]
    }
    
    # Add tracker-specific validation if tracker context was used
    if tracker_context:
        validation_metadata.update({
            "tracker_enhanced": True,
            "tracker_adjustments": [
                f"energy_rate: T_memory adjustment applied (T_memory={tracker_context.get('t_memory', 0):.2f})",
                f"gamma_base: HTF influence applied (influence={tracker_context.get('htf_influence_factor', 0):.3f})",
                f"momentum_strength: Liquidity gradient applied (gradient={tracker_context.get('liquidity_gradient', {}).get('gradient_strength', 0):.3f})"
            ],
            "tracker_context_used": {
                "t_memory": tracker_context.get('t_memory', 0),
                "htf_structures_count": len(tracker_context.get('active_structures', [])),
                "liquidity_levels_count": len(tracker_context.get('untaken_liquidity', []))
            }
        })
    else:
        validation_metadata["tracker_enhanced"] = False
    
    corrected_opus4["_validation"] = validation_metadata
    
    return corrected_opus4


if __name__ == "__main__":
    print("Opus4 Mathematical Formula Corrections")
    print("Key fixes:")
    print("1. λ_theta_dynamic: Proper Eq. 13 implementation")
    print("2. E_threshold_adj: T_memory calculation added")
    print("3. Parameter bounds validation")
    print("4. Exact constants for φ and κ")