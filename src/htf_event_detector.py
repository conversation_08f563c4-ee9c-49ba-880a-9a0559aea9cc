#!/usr/bin/env python3
"""
Higher Timeframe (HTF) Event Detection Engine
Implements Grok's sophisticated HTF pattern recognition for weekly liquidity cycles.

Key Patterns Detected:
- Friday Trap: Session high/low within 5 points of weekly high/low + rejection
- Monday Sweep: Break of Friday's high/low by 5-15 points with reversal  
- Tuesday Cascade: Directional move exceeding 50 points
- Friday Completion: Sweeping Monday's high with weekly cycle completion

Data Sources:
- price_data: High/low calculations and weekly aggregation
- micro_timing_analysis.cascade_events: Move detection and magnitude analysis
- liquidity_analysis.untaken_liquidity: Imbalance assessment and context
"""

import numpy as np
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
import json


@dataclass
class HTFEvent:
    """Structure for detected HTF events."""
    event_type: str  # "friday_trap", "monday_sweep", "tuesday_cascade", "friday_completion"
    timestamp: str
    price_level: float
    magnitude: float
    confidence: float
    pattern_data: Dict[str, Any]
    weekly_context: Dict[str, Any]


@dataclass
class WeeklyContext:
    """Weekly market context for HTF analysis."""
    week_start: str
    week_end: str
    weekly_high: float
    weekly_low: float
    weekly_range: float
    dominant_sessions: List[str]
    liquidity_imbalance_ratio: float
    cycle_phase: str  # "early", "mid", "late"


class HTFEventDetector:
    """
    Higher Timeframe Event Detection Engine
    
    Detects weekly liquidity cycle patterns using threshold-based markers
    and pattern recognition from session-level cascade data.
    """
    
    def __init__(self):
        """Initialize HTF event detector with pattern thresholds."""
        
        # Pattern detection thresholds
        self.FRIDAY_TRAP_TOLERANCE = 5.0  # Points within weekly high/low
        self.MONDAY_SWEEP_RANGE = (5.0, 15.0)  # Point range for sweep detection
        self.TUESDAY_CASCADE_THRESHOLD = 50.0  # Minimum points for cascade
        self.FRIDAY_COMPLETION_THRESHOLD = 0.8  # Proportion of weekly range
        
        # Confidence scoring parameters
        self.BASE_CONFIDENCE = 0.6
        self.MAGNITUDE_WEIGHT = 0.2
        self.CONTEXT_WEIGHT = 0.2
        
        # Weekly cycle tracking
        self.weekly_data_cache = {}
        self.htf_events_history = []
        
        print("🏗️ HTF EVENT DETECTOR: Initialized with Grok's pattern recognition")
        
    def detect_htf_events(self, session_data: Dict[Any, Any], 
                         historical_sessions: Optional[List[Dict[Any, Any]]] = None) -> List[HTFEvent]:
        """
        Detect HTF events from session data with weekly context.
        
        Args:
            session_data: Current session JSON data
            historical_sessions: Optional list of historical sessions for weekly context
            
        Returns:
            List of detected HTF events with confidence scores
        """
        start_time = time.time()
        
        # Extract session metadata
        session_metadata = session_data.get('session_metadata', {})
        session_date = session_metadata.get('date', '')
        session_type = session_metadata.get('session_type', 'unknown')
        
        print(f"🔍 HTF DETECTION: Analyzing {session_type} session {session_date}")
        
        # Build weekly context
        weekly_context = self._build_weekly_context(session_data, historical_sessions)
        
        # Detect pattern-specific events
        events = []
        
        # Friday Trap Detection
        if self._is_friday_session(session_type, session_date):
            friday_events = self._detect_friday_trap(session_data, weekly_context)
            events.extend(friday_events)
            
        # Monday Sweep Detection  
        elif self._is_monday_session(session_type, session_date):
            monday_events = self._detect_monday_sweep(session_data, weekly_context)
            events.extend(monday_events)
            
        # Tuesday Cascade Detection
        elif self._is_tuesday_session(session_type, session_date):
            tuesday_events = self._detect_tuesday_cascade(session_data, weekly_context)
            events.extend(tuesday_events)
            
        # Friday Completion Detection (separate from Friday Trap)
        if self._is_friday_session(session_type, session_date):
            completion_events = self._detect_friday_completion(session_data, weekly_context)
            events.extend(completion_events)
        
        # Store events in history
        self.htf_events_history.extend(events)
        
        processing_time = (time.time() - start_time) * 1000
        print(f"🎯 HTF DETECTION completed: {len(events)} events in {processing_time:.1f}ms")
        
        return events
    
    def _build_weekly_context(self, session_data: Dict[Any, Any], 
                            historical_sessions: Optional[List[Dict[Any, Any]]]) -> WeeklyContext:
        """Build weekly market context from session and historical data."""
        
        session_metadata = session_data.get('session_metadata', {})
        session_date = session_metadata.get('date', '')
        price_data = session_data.get('price_data', {})
        
        # Calculate week boundaries
        week_start, week_end = self._get_week_boundaries(session_date)
        
        # If no historical data, use current session as baseline
        if not historical_sessions:
            return WeeklyContext(
                week_start=week_start,
                week_end=week_end,
                weekly_high=price_data.get('high', 0),
                weekly_low=price_data.get('low', 0),
                weekly_range=price_data.get('range', 0),
                dominant_sessions=[session_metadata.get('session_type', 'unknown')],
                liquidity_imbalance_ratio=self._calculate_liquidity_imbalance(session_data),
                cycle_phase=self._determine_cycle_phase(session_date)
            )
        
        # Aggregate weekly data from historical sessions
        weekly_highs = []
        weekly_lows = []
        session_types = []
        liquidity_ratios = []
        
        # Include current session
        all_sessions = historical_sessions + [session_data]
        
        for session in all_sessions:
            session_price = session.get('price_data', {})
            session_meta = session.get('session_metadata', {})
            
            # Filter to current week
            if self._is_in_week(session_meta.get('date', ''), week_start, week_end):
                weekly_highs.append(session_price.get('high', 0))
                weekly_lows.append(session_price.get('low', 0))
                session_types.append(session_meta.get('session_type', 'unknown'))
                liquidity_ratios.append(self._calculate_liquidity_imbalance(session))
        
        # Calculate weekly metrics
        weekly_high = max(weekly_highs) if weekly_highs else price_data.get('high', 0)
        weekly_low = min(weekly_lows) if weekly_lows else price_data.get('low', 0)
        weekly_range = weekly_high - weekly_low
        
        # Determine dominant sessions
        session_counts = defaultdict(int)
        for st in session_types:
            session_counts[st] += 1
        dominant_sessions = sorted(session_counts.keys(), key=lambda x: session_counts[x], reverse=True)
        
        # Average liquidity imbalance
        avg_liquidity_ratio = np.mean(liquidity_ratios) if liquidity_ratios else 0.0
        
        return WeeklyContext(
            week_start=week_start,
            week_end=week_end,
            weekly_high=weekly_high,
            weekly_low=weekly_low,
            weekly_range=weekly_range,
            dominant_sessions=dominant_sessions,
            liquidity_imbalance_ratio=avg_liquidity_ratio,
            cycle_phase=self._determine_cycle_phase(session_date)
        )
    
    def _detect_friday_trap(self, session_data: Dict[Any, Any], weekly_context: WeeklyContext) -> List[HTFEvent]:
        """
        Detect Friday Trap pattern: Session high/low within 5 points of weekly high/low + rejection.
        """
        events = []
        price_data = session_data.get('price_data', {})
        micro_timing = session_data.get('micro_timing_analysis', {})
        
        session_high = price_data.get('high', 0)
        session_low = price_data.get('low', 0)
        weekly_high = weekly_context.weekly_high
        weekly_low = weekly_context.weekly_low
        
        # Check for Friday Trap High
        high_distance = abs(session_high - weekly_high)
        if high_distance <= self.FRIDAY_TRAP_TOLERANCE:
            # Look for rejection pattern in cascade events
            cascade_events = micro_timing.get('cascade_events', [])
            rejection_found = self._detect_rejection_pattern(cascade_events, session_high, 'high')
            
            if rejection_found:
                confidence = self._calculate_pattern_confidence(
                    'friday_trap_high', high_distance, weekly_context
                )
                
                events.append(HTFEvent(
                    event_type="friday_trap_high",
                    timestamp=session_data.get('session_metadata', {}).get('start_time', ''),
                    price_level=session_high,
                    magnitude=high_distance,
                    confidence=confidence,
                    pattern_data={
                        'weekly_high': weekly_high,
                        'session_high': session_high,
                        'distance_to_weekly': high_distance,
                        'rejection_pattern': rejection_found
                    },
                    weekly_context=weekly_context.__dict__
                ))
        
        # Check for Friday Trap Low
        low_distance = abs(session_low - weekly_low)
        if low_distance <= self.FRIDAY_TRAP_TOLERANCE:
            cascade_events = micro_timing.get('cascade_events', [])
            rejection_found = self._detect_rejection_pattern(cascade_events, session_low, 'low')
            
            if rejection_found:
                confidence = self._calculate_pattern_confidence(
                    'friday_trap_low', low_distance, weekly_context
                )
                
                events.append(HTFEvent(
                    event_type="friday_trap_low",
                    timestamp=session_data.get('session_metadata', {}).get('start_time', ''),
                    price_level=session_low,
                    magnitude=low_distance,
                    confidence=confidence,
                    pattern_data={
                        'weekly_low': weekly_low,
                        'session_low': session_low,
                        'distance_to_weekly': low_distance,
                        'rejection_pattern': rejection_found
                    },
                    weekly_context=weekly_context.__dict__
                ))
        
        return events
    
    def _detect_monday_sweep(self, session_data: Dict[Any, Any], weekly_context: WeeklyContext) -> List[HTFEvent]:
        """
        Detect Monday Sweep: Break of Friday's high/low by 5-15 points with reversal.
        """
        events = []
        
        # Get Friday's levels from weekly context (would be extracted from historical data)
        # For now, approximate from weekly high/low
        friday_high = weekly_context.weekly_high * 0.98  # Approximate Friday high
        friday_low = weekly_context.weekly_low * 1.02   # Approximate Friday low
        
        price_data = session_data.get('price_data', {})
        micro_timing = session_data.get('micro_timing_analysis', {})
        
        session_high = price_data.get('high', 0)
        session_low = price_data.get('low', 0)
        
        # Check for Monday Sweep High (break above Friday high)
        high_break = session_high - friday_high
        if self.MONDAY_SWEEP_RANGE[0] <= high_break <= self.MONDAY_SWEEP_RANGE[1]:
            # Look for reversal pattern
            cascade_events = micro_timing.get('cascade_events', [])
            reversal_found = self._detect_reversal_pattern(cascade_events, session_high)
            
            if reversal_found:
                confidence = self._calculate_pattern_confidence(
                    'monday_sweep_high', high_break, weekly_context
                )
                
                events.append(HTFEvent(
                    event_type="monday_sweep_high",
                    timestamp=session_data.get('session_metadata', {}).get('start_time', ''),
                    price_level=session_high,
                    magnitude=high_break,
                    confidence=confidence,
                    pattern_data={
                        'friday_high': friday_high,
                        'session_high': session_high,
                        'sweep_magnitude': high_break,
                        'reversal_pattern': reversal_found
                    },
                    weekly_context=weekly_context.__dict__
                ))
        
        # Check for Monday Sweep Low (break below Friday low)
        low_break = friday_low - session_low
        if self.MONDAY_SWEEP_RANGE[0] <= low_break <= self.MONDAY_SWEEP_RANGE[1]:
            cascade_events = micro_timing.get('cascade_events', [])
            reversal_found = self._detect_reversal_pattern(cascade_events, session_low)
            
            if reversal_found:
                confidence = self._calculate_pattern_confidence(
                    'monday_sweep_low', low_break, weekly_context
                )
                
                events.append(HTFEvent(
                    event_type="monday_sweep_low",
                    timestamp=session_data.get('session_metadata', {}).get('start_time', ''),
                    price_level=session_low,
                    magnitude=low_break,
                    confidence=confidence,
                    pattern_data={
                        'friday_low': friday_low,
                        'session_low': session_low,
                        'sweep_magnitude': low_break,
                        'reversal_pattern': reversal_found
                    },
                    weekly_context=weekly_context.__dict__
                ))
        
        return events
    
    def _detect_tuesday_cascade(self, session_data: Dict[Any, Any], weekly_context: WeeklyContext) -> List[HTFEvent]:
        """
        Detect Tuesday Cascade: Directional move exceeding 50 points.
        """
        events = []
        micro_timing = session_data.get('micro_timing_analysis', {})
        cascade_events = micro_timing.get('cascade_events', [])
        
        # Look for major cascade events
        for cascade in cascade_events:
            magnitude = cascade.get('magnitude', 0)
            
            if magnitude >= self.TUESDAY_CASCADE_THRESHOLD:
                # Check liquidity imbalance amplification
                liquidity_ratio = weekly_context.liquidity_imbalance_ratio
                amplified = liquidity_ratio > 1.5
                
                confidence = self._calculate_pattern_confidence(
                    'tuesday_cascade', magnitude, weekly_context
                )
                
                # Boost confidence if liquidity amplifies
                if amplified:
                    confidence = min(1.0, confidence * 1.2)
                
                events.append(HTFEvent(
                    event_type="tuesday_cascade",
                    timestamp=cascade.get('timestamp', ''),
                    price_level=cascade.get('price_level', 0),
                    magnitude=magnitude,
                    confidence=confidence,
                    pattern_data={
                        'cascade_magnitude': magnitude,
                        'event_type': cascade.get('event_type', 'unknown'),
                        'duration_minutes': cascade.get('duration_minutes', 0),
                        'liquidity_amplified': amplified,
                        'liquidity_ratio': liquidity_ratio
                    },
                    weekly_context=weekly_context.__dict__
                ))
        
        return events
    
    def _detect_friday_completion(self, session_data: Dict[Any, Any], weekly_context: WeeklyContext) -> List[HTFEvent]:
        """
        Detect Friday Completion: Sweeping Monday's high with weekly cycle completion.
        """
        events = []
        
        # Approximate Monday's high from weekly context
        monday_high = weekly_context.weekly_low + (weekly_context.weekly_range * 0.6)
        
        price_data = session_data.get('price_data', {})
        session_high = price_data.get('high', 0)
        
        # Check if session sweeps Monday's high
        if session_high > monday_high:
            # Check if this represents significant weekly range completion
            range_completion = (session_high - weekly_context.weekly_low) / weekly_context.weekly_range
            
            if range_completion >= self.FRIDAY_COMPLETION_THRESHOLD:
                confidence = self._calculate_pattern_confidence(
                    'friday_completion', range_completion * 100, weekly_context
                )
                
                events.append(HTFEvent(
                    event_type="friday_completion",
                    timestamp=session_data.get('session_metadata', {}).get('start_time', ''),
                    price_level=session_high,
                    magnitude=session_high - monday_high,
                    confidence=confidence,
                    pattern_data={
                        'monday_high': monday_high,
                        'session_high': session_high,
                        'sweep_magnitude': session_high - monday_high,
                        'range_completion': range_completion,
                        'weekly_range': weekly_context.weekly_range
                    },
                    weekly_context=weekly_context.__dict__
                ))
        
        return events
    
    def _detect_rejection_pattern(self, cascade_events: List[Dict], price_level: float, level_type: str) -> bool:
        """Detect rejection pattern from cascade events."""
        for event in cascade_events:
            event_price = event.get('price_level', 0)
            event_type = event.get('event_type', '')
            
            # Look for reversal indicators near the price level
            if abs(event_price - price_level) <= 2.0:  # Within 2 points
                if level_type == 'high' and 'reversal' in event_type.lower():
                    return True
                elif level_type == 'low' and 'reversal' in event_type.lower():
                    return True
        
        return False
    
    def _detect_reversal_pattern(self, cascade_events: List[Dict], price_level: float) -> bool:
        """Detect reversal pattern from cascade events."""
        for event in cascade_events:
            event_price = event.get('price_level', 0)
            event_type = event.get('event_type', '')
            
            # Look for reversal indicators near the price level
            if abs(event_price - price_level) <= 3.0:  # Within 3 points
                if 'reversal' in event_type.lower() or 'expansion' in event_type.lower():
                    return True
        
        return False
    
    def _calculate_pattern_confidence(self, pattern_type: str, magnitude: float, 
                                    weekly_context: WeeklyContext) -> float:
        """Calculate confidence score for detected pattern."""
        
        base_confidence = self.BASE_CONFIDENCE
        
        # Magnitude-based adjustment
        magnitude_factor = min(1.0, magnitude / 50.0) * self.MAGNITUDE_WEIGHT
        
        # Context-based adjustment
        context_factor = 0.0
        if weekly_context.liquidity_imbalance_ratio > 1.2:
            context_factor += 0.1
        if weekly_context.cycle_phase == 'mid':
            context_factor += 0.1
        
        context_factor *= self.CONTEXT_WEIGHT
        
        confidence = base_confidence + magnitude_factor + context_factor
        return max(0.0, min(1.0, confidence))
    
    def _calculate_liquidity_imbalance(self, session_data: Dict[Any, Any]) -> float:
        """Calculate liquidity imbalance ratio from session data."""
        liquidity_analysis = session_data.get('liquidity_analysis', {})
        untaken_liquidity = liquidity_analysis.get('untaken_liquidity', [])
        
        if not untaken_liquidity:
            return 1.0  # Neutral
        
        # Count liquidity by side
        buy_side = sum(1 for level in untaken_liquidity if level.get('side', '') == 'buy')
        sell_side = sum(1 for level in untaken_liquidity if level.get('side', '') == 'sell')
        
        if sell_side == 0:
            return 2.0  # Maximum imbalance toward buy side
        
        return max(buy_side / sell_side, sell_side / buy_side)
    
    def _get_week_boundaries(self, date_str: str) -> Tuple[str, str]:
        """Get week start and end dates."""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            # Get Monday of the week
            week_start = date_obj - timedelta(days=date_obj.weekday())
            week_end = week_start + timedelta(days=6)
            
            return week_start.strftime('%Y-%m-%d'), week_end.strftime('%Y-%m-%d')
        except:
            return date_str, date_str
    
    def _is_in_week(self, date_str: str, week_start: str, week_end: str) -> bool:
        """Check if date is within the specified week."""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            start_obj = datetime.strptime(week_start, '%Y-%m-%d')
            end_obj = datetime.strptime(week_end, '%Y-%m-%d')
            
            return start_obj <= date_obj <= end_obj
        except:
            return False
    
    def _determine_cycle_phase(self, date_str: str) -> str:
        """Determine weekly cycle phase."""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            weekday = date_obj.weekday()  # Monday = 0, Sunday = 6
            
            if weekday <= 1:  # Monday, Tuesday
                return 'early'
            elif weekday <= 3:  # Wednesday, Thursday
                return 'mid'
            else:  # Friday, Weekend
                return 'late'
        except:
            return 'unknown'
    
    def _is_friday_session(self, session_type: str, date_str: str) -> bool:
        """Check if session is on Friday."""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.weekday() == 4  # Friday
        except:
            return 'friday' in session_type.lower()
    
    def _is_monday_session(self, session_type: str, date_str: str) -> bool:
        """Check if session is on Monday."""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.weekday() == 0  # Monday
        except:
            return 'monday' in session_type.lower()
    
    def _is_tuesday_session(self, session_type: str, date_str: str) -> bool:
        """Check if session is on Tuesday."""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.weekday() == 1  # Tuesday
        except:
            return 'tuesday' in session_type.lower()
    
    def get_htf_events_for_intensity(self, lookback_hours: int = 24) -> List[HTFEvent]:
        """Get HTF events for intensity calculation with specified lookback."""
        if not self.htf_events_history:
            return []
        
        # Filter events within lookback period
        cutoff_time = datetime.now() - timedelta(hours=lookback_hours)
        
        filtered_events = []
        for event in self.htf_events_history:
            try:
                event_time = datetime.fromisoformat(event.timestamp.replace('Z', '+00:00'))
                if event_time >= cutoff_time:
                    filtered_events.append(event)
            except:
                # Include events with parsing issues for safety
                filtered_events.append(event)
        
        return filtered_events
    
    def get_detector_status(self) -> Dict[str, Any]:
        """Get HTF detector status and statistics."""
        return {
            'detector_type': 'htf_event_detection_engine',
            'patterns_supported': ['friday_trap', 'monday_sweep', 'tuesday_cascade', 'friday_completion'],
            'events_detected': len(self.htf_events_history),
            'pattern_thresholds': {
                'friday_trap_tolerance': self.FRIDAY_TRAP_TOLERANCE,
                'monday_sweep_range': self.MONDAY_SWEEP_RANGE,
                'tuesday_cascade_threshold': self.TUESDAY_CASCADE_THRESHOLD,
                'friday_completion_threshold': self.FRIDAY_COMPLETION_THRESHOLD
            },
            'weekly_contexts_cached': len(self.weekly_data_cache),
            'confidence_parameters': {
                'base_confidence': self.BASE_CONFIDENCE,
                'magnitude_weight': self.MAGNITUDE_WEIGHT,
                'context_weight': self.CONTEXT_WEIGHT
            }
        }


def create_htf_event_detector() -> HTFEventDetector:
    """Factory function to create HTF event detector."""
    return HTFEventDetector()


if __name__ == "__main__":
    # Test HTF event detector
    print("🧪 TESTING HTF EVENT DETECTOR")
    print("=" * 50)
    
    detector = create_htf_event_detector()
    
    # Mock session data (Friday session)
    mock_session = {
        'session_metadata': {
            'date': '2025-07-25',  # Friday
            'session_type': 'NY_PM',
            'start_time': '13:30:00'
        },
        'price_data': {
            'high': 23459.75,
            'low': 23407.0,
            'range': 52.75
        },
        'micro_timing_analysis': {
            'cascade_events': [
                {
                    'timestamp': '15:38:00',
                    'event_type': 'major_cascade',
                    'price_level': 23450.75,
                    'magnitude': 43.75
                }
            ]
        },
        'liquidity_analysis': {
            'untaken_liquidity': [
                {'side': 'buy', 'level': 23400},
                {'side': 'sell', 'level': 23460}
            ]
        }
    }
    
    # Test HTF event detection
    events = detector.detect_htf_events(mock_session)
    
    print(f"🎯 HTF Events Detected: {len(events)}")
    for event in events:
        print(f"  📊 {event.event_type}: {event.price_level} (confidence: {event.confidence:.2f})")
        print(f"      Magnitude: {event.magnitude:.2f}, Timestamp: {event.timestamp}")
    
    # Show detector status
    status = detector.get_detector_status()
    print(f"\n🔧 Detector Status:")
    print(f"  Events Detected: {status['events_detected']}")
    print(f"  Patterns Supported: {', '.join(status['patterns_supported'])}")
    print(f"  Base Confidence: {status['confidence_parameters']['base_confidence']}")