#!/usr/bin/env python3
"""
Micro Timing Analysis Module with Hawkes Process Integration
Provides precise cascade timing predictions and event sequence analysis
for Unit A foundation calculations and real-time trading applications.
"""

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
from src.hawkes_cascade_predictor import HawkesCascadePredictor
from src.dynamic_synthetic_volume import DynamicSyntheticVolumeCalculator
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class MicroTimingResult:
    """Comprehensive micro timing analysis result"""
    cascade_timing_minutes: float
    cascade_confidence: float
    dynamic_synthetic_volume: float
    intensity_buildup_status: str
    triggering_events: List[Dict[str, Any]]
    session_adaptive_parameters: Dict[str, float]
    micro_timing_insights: Dict[str, Any]
    hawkes_metadata: Dict[str, Any]

@dataclass
class EventSequenceAnalysis:
    """Event sequence pattern analysis"""
    fvg_formation_timing: Optional[float]
    liquidity_sweep_timing: Optional[float]
    cascade_initiation_timing: Optional[float]
    event_chain_probability: float
    sequence_pattern: str
    temporal_relationships: Dict[str, Any]

class MicroTimingAnalyzer:
    """
    Advanced micro timing analysis with Hawkes process integration
    for precise cascade prediction and event sequence modeling.
    """
    
    def __init__(self):
        self.cascade_predictor = HawkesCascadePredictor()
        self.volume_calculator = DynamicSyntheticVolumeCalculator()
        
        # Micro timing thresholds
        self.cascade_confidence_threshold = 0.75
        self.intensity_warning_threshold = 0.8
        self.event_proximity_minutes = 5.0
    
    def analyze_micro_timing(self, session_data: Dict[str, Any], 
                           current_time_minutes: Optional[float] = None) -> MicroTimingResult:
        """
        Perform comprehensive micro timing analysis with Hawkes process.
        
        Args:
            session_data: Complete session data with price movements and events
            current_time_minutes: Current time in minutes from session start (for live analysis)
            
        Returns:
            Complete micro timing analysis with cascade predictions
        """
        
        print("🎯 MICRO TIMING ANALYSIS - HAWKES PROCESS INTEGRATION")
        print("=" * 60)
        
        # 1. Generate Hawkes cascade prediction
        cascade_prediction = self.cascade_predictor.predict_cascade_timing(session_data)
        
        # 2. Calculate dynamic synthetic volume
        volume_components = self.volume_calculator.calculate_dynamic_synthetic_volume(session_data)
        
        # 3. Analyze event sequence patterns
        event_sequence = self._analyze_event_sequences(session_data, cascade_prediction)
        
        # 4. Determine intensity buildup status
        intensity_status = self._assess_intensity_buildup(cascade_prediction, current_time_minutes)
        
        # 5. Extract micro timing insights
        micro_insights = self._extract_micro_timing_insights(
            session_data, cascade_prediction, event_sequence
        )
        
        # 6. Generate session-adaptive parameters summary
        adaptive_params = {
            "mu_baseline_intensity": cascade_prediction.parameters_used.mu,
            "alpha_excitation": cascade_prediction.parameters_used.alpha,
            "beta_decay_rate": cascade_prediction.parameters_used.beta,
            "threshold_cascade": cascade_prediction.parameters_used.threshold,
            "current_intensity": cascade_prediction.threshold_crossed_at
        }
        
        # 7. Compile Hawkes metadata
        hawkes_metadata = {
            "methodology": cascade_prediction.methodology,
            "intensity_timeline_points": len(cascade_prediction.intensity_buildup),
            "triggering_events_identified": len(cascade_prediction.triggering_events),
            "volume_improvement_factor": volume_components.improvement_factor,
            "prediction_timestamp": datetime.now().isoformat()
        }
        
        print(f"📊 MICRO TIMING RESULTS:")
        print(f"   🎯 Cascade Timing: {cascade_prediction.predicted_cascade_time:.1f} minutes")
        print(f"   📈 Confidence: {cascade_prediction.prediction_confidence:.2f}")
        print(f"   🔬 Dynamic Volume: {volume_components.calculated_volume:.2f}")
        print(f"   ⚡ Intensity Status: {intensity_status}")
        print(f"   🔗 Event Chain: {event_sequence.sequence_pattern}")
        
        return MicroTimingResult(
            cascade_timing_minutes=cascade_prediction.predicted_cascade_time,
            cascade_confidence=cascade_prediction.prediction_confidence,
            dynamic_synthetic_volume=volume_components.calculated_volume,
            intensity_buildup_status=intensity_status,
            triggering_events=[
                {
                    "timestamp": event.timestamp,
                    "event_type": event.event_type,
                    "magnitude": event.magnitude,
                    "minutes_from_start": event.minutes_from_start
                }
                for event in cascade_prediction.triggering_events
            ],
            session_adaptive_parameters=adaptive_params,
            micro_timing_insights=micro_insights,
            hawkes_metadata=hawkes_metadata
        )
    
    def _analyze_event_sequences(self, session_data: Dict[str, Any], 
                               cascade_prediction) -> EventSequenceAnalysis:
        """Analyze temporal event sequences for pattern recognition."""
        
        # Extract market events
        events = self.volume_calculator.extract_market_events(session_data)
        
        # Find specific event types and their timing
        fvg_timing = None
        liquidity_timing = None
        cascade_timing = cascade_prediction.predicted_cascade_time
        
        for event in events:
            if event.event_type == 'fvg_event' and fvg_timing is None:
                fvg_timing = event.minutes_from_start
            elif event.event_type == 'liquidity_sweep' and liquidity_timing is None:
                liquidity_timing = event.minutes_from_start
        
        # Determine sequence pattern
        if fvg_timing and liquidity_timing and cascade_timing:
            if fvg_timing < liquidity_timing < cascade_timing:
                pattern = "fvg_formation → liquidity_sweep → cascade"
                chain_probability = 0.85
            elif fvg_timing < cascade_timing:
                pattern = "fvg_formation → cascade"
                chain_probability = 0.75
            else:
                pattern = "consolidation → cascade"
                chain_probability = 0.65
        else:
            pattern = "standard_cascade"
            chain_probability = 0.50
        
        # Calculate temporal relationships
        temporal_relationships = {}
        if fvg_timing and cascade_timing:
            temporal_relationships["fvg_to_cascade_minutes"] = cascade_timing - fvg_timing
        if liquidity_timing and cascade_timing:
            temporal_relationships["liquidity_to_cascade_minutes"] = cascade_timing - liquidity_timing
        
        return EventSequenceAnalysis(
            fvg_formation_timing=fvg_timing,
            liquidity_sweep_timing=liquidity_timing,
            cascade_initiation_timing=cascade_timing,
            event_chain_probability=chain_probability,
            sequence_pattern=pattern,
            temporal_relationships=temporal_relationships
        )
    
    def _assess_intensity_buildup(self, cascade_prediction, 
                                current_time_minutes: Optional[float]) -> str:
        """Assess current intensity buildup status."""
        
        current_intensity = cascade_prediction.threshold_crossed_at
        threshold = cascade_prediction.parameters_used.threshold
        
        if current_intensity >= threshold:
            return "cascade_triggered"
        elif current_intensity >= threshold * self.intensity_warning_threshold:
            return "approaching_threshold"
        elif current_intensity >= threshold * 0.5:
            return "building_intensity"
        else:
            return "baseline_intensity"
    
    def _extract_micro_timing_insights(self, session_data: Dict[str, Any],
                                     cascade_prediction, 
                                     event_sequence: EventSequenceAnalysis) -> Dict[str, Any]:
        """Extract actionable micro timing insights."""
        
        # Session characteristics
        price_data = session_data.get("price_data", {})
        session_range = price_data.get("range", 0)
        session_character = price_data.get("session_character", "unknown")
        
        # Time to cascade
        time_to_cascade = cascade_prediction.predicted_cascade_time
        
        # Generate insights based on analysis
        insights = {
            "cascade_timing_window": {
                "start_minutes": max(0, time_to_cascade - 2),
                "end_minutes": time_to_cascade + 2,
                "optimal_entry_minutes": time_to_cascade - 1
            },
            "risk_assessment": {
                "cascade_probability": cascade_prediction.prediction_confidence,
                "sequence_reliability": event_sequence.event_chain_probability,
                "session_volatility": "high" if session_range > 50 else "moderate" if session_range > 25 else "low"
            },
            "trading_recommendations": {
                "watch_levels": self._identify_key_levels(session_data),
                "optimal_timeframe": "1-minute" if time_to_cascade < 10 else "5-minute",
                "confluence_factors": self._identify_confluence_factors(session_data, event_sequence)
            },
            "session_context": {
                "character": session_character,
                "range_points": session_range,
                "event_sequence_pattern": event_sequence.sequence_pattern
            }
        }
        
        return insights
    
    def _identify_key_levels(self, session_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify key price levels from session data."""
        
        price_data = session_data.get("price_data", {})
        levels = []
        
        # Add session high/low
        if price_data.get("high"):
            levels.append({"level": price_data["high"], "type": "session_high", "importance": "high"})
        if price_data.get("low"):
            levels.append({"level": price_data["low"], "type": "session_low", "importance": "high"})
        
        # Add current/close level  
        if price_data.get("close"):
            levels.append({"level": price_data["close"], "type": "current_price", "importance": "reference"})
        
        return levels
    
    def _identify_confluence_factors(self, session_data: Dict[str, Any], 
                                   event_sequence: EventSequenceAnalysis) -> List[str]:
        """Identify confluence factors supporting cascade prediction."""
        
        factors = []
        
        # Event sequence confluence
        if event_sequence.sequence_pattern != "standard_cascade":
            factors.append(f"event_sequence_{event_sequence.sequence_pattern}")
        
        # Session timing confluence
        session_metadata = session_data.get("session_metadata", {})
        session_duration = session_metadata.get("duration_minutes", 0)
        
        if session_duration > 120:  # Power hour considerations
            factors.append("power_hour_timing")
        
        # Volume confluence
        factors.append("dynamic_synthetic_volume")
        
        # Hawkes process confluence
        factors.append("hawkes_intensity_buildup")
        
        return factors

def analyze_session_micro_timing(session_file: str, 
                               current_time_minutes: Optional[float] = None,
                               output_file: Optional[str] = None) -> MicroTimingResult:
    """
    Analyze micro timing for a session file with Hawkes process integration.
    
    Args:
        session_file: Path to session JSON file
        current_time_minutes: Current time for live analysis
        output_file: Optional output file for results
        
    Returns:
        Complete micro timing analysis result
    """
    
    print(f"📋 ANALYZING MICRO TIMING: {session_file}")
    print("=" * 60)
    
    # Load session data
    session_data = load_json_data(session_file)
    
    # Initialize analyzer
    analyzer = MicroTimingAnalyzer()
    
    # Perform analysis
    result = analyzer.analyze_micro_timing(session_data, current_time_minutes)
    
    # Save results if output file specified
    if output_file:
        results_data = {
            "micro_timing_analysis_results": {
                "session_file": session_file,
                "analysis_timestamp": datetime.now().isoformat(),
                "cascade_timing_minutes": result.cascade_timing_minutes,
                "cascade_confidence": result.cascade_confidence,
                "dynamic_synthetic_volume": result.dynamic_synthetic_volume,
                "intensity_buildup_status": result.intensity_buildup_status,
                "triggering_events": result.triggering_events,
                "session_adaptive_parameters": result.session_adaptive_parameters,
                "micro_timing_insights": result.micro_timing_insights,
                "hawkes_metadata": result.hawkes_metadata
            }
        }
        
        save_json_data(results_data, output_file)
        print(f"📁 Results saved: {output_file}")
    
    return result

def test_micro_timing_analysis():
    """Test micro timing analysis on NYAM session."""
    
    print("🧪 TESTING MICRO TIMING ANALYSIS MODULE")
    print("=" * 60)
    
    # Test on NYAM session with known cascade at 8 minutes
    result = analyze_session_micro_timing(
        'NYAM_Lvl-1_2025_07_25.json',
        output_file='micro_timing_analysis_test_results.json'
    )
    
    print(f"✅ MICRO TIMING TEST RESULTS:")
    print(f"   Cascade Timing: {result.cascade_timing_minutes:.1f} minutes")
    print(f"   Confidence: {result.cascade_confidence:.2f}")
    print(f"   Dynamic Volume: {result.dynamic_synthetic_volume:.2f}")
    print(f"   Intensity Status: {result.intensity_buildup_status}")
    print(f"   Event Chain: {len(result.triggering_events)} triggering events")
    
    return result

if __name__ == "__main__":
    test_micro_timing_analysis()