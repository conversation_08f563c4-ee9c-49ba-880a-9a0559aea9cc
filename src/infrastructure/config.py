#!/usr/bin/env python3
"""
Centralized Configuration Management
Handles API keys, timeouts, and other configuration parameters consistently across the project.
"""

import os
import logging
from typing import Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class APIKeyError(Exception):
    """Custom exception for API key related errors."""
    pass


class APIKeyManager:
    """Centralized API key management with validation and error handling."""
    
    def __init__(self):
        """Initialize the API key manager."""
        self._api_key = None
        self._validated = False
        
    def get_api_key(self, api_key_override: Optional[str] = None) -> str:
        """
        Get the Grok API key with validation.
        
        Args:
            api_key_override: Optional API key to use instead of environment variable
            
        Returns:
            Valid API key string
            
        Raises:
            APIKeyError: If no valid API key is found
        """
        # Use override if provided, otherwise check environment
        if api_key_override:
            self._api_key = api_key_override
        elif not self._api_key:
            self._api_key = os.getenv('GROK_API_KEY')
        
        if not self._api_key:
            raise APIKeyError(
                "GROK_API_KEY not found. Please set the GROK_API_KEY environment variable "
                "or provide an API key parameter. You can obtain an API key from https://console.x.ai/"
            )
        
        # Validate API key format
        if not self._validate_api_key_format(self._api_key):
            raise APIKeyError(
                "Invalid API key format. Grok API keys should start with 'xai-' and be at least 20 characters long."
            )
        
        if not self._validated:
            logger.info("API key loaded successfully")
            self._validated = True
            
        return self._api_key
    
    def _validate_api_key_format(self, api_key: str) -> bool:
        """
        Validate API key format without making API calls.
        
        Args:
            api_key: The API key to validate
            
        Returns:
            True if format appears valid, False otherwise
        """
        if not isinstance(api_key, str):
            return False
        
        # Basic validation: should start with 'xai-' and be reasonably long
        if not api_key.startswith('xai-'):
            return False
        
        if len(api_key) < 20:
            return False
        
        return True
    
    def get_bearer_token(self, api_key_override: Optional[str] = None) -> str:
        """
        Get properly formatted bearer token for Authorization headers.
        
        Args:
            api_key_override: Optional API key to use instead of environment variable
            
        Returns:
            Bearer token string formatted for Authorization header
        """
        api_key = self.get_api_key(api_key_override)
        return f"Bearer {api_key}"
    
    def create_headers(self, api_key_override: Optional[str] = None) -> dict:
        """
        Create standardized headers for HTTP API requests.
        
        Args:
            api_key_override: Optional API key to use instead of environment variable
            
        Returns:
            Dictionary with Authorization and Content-Type headers
        """
        return {
            "Authorization": self.get_bearer_token(api_key_override),
            "Content-Type": "application/json"
        }
    
    def create_subprocess_env(self, api_key_override: Optional[str] = None) -> dict:
        """
        Create environment dictionary for subprocess calls to Grok CLI.
        
        Args:
            api_key_override: Optional API key to use instead of environment variable
            
        Returns:
            Environment dictionary with GROK_API_KEY set
        """
        api_key = self.get_api_key(api_key_override)
        env = os.environ.copy()
        env['GROK_API_KEY'] = api_key
        return env


class TimeoutConfig:
    """Centralized timeout configuration for different operations."""
    
    # Default timeouts in seconds
    DEFAULT_TIMEOUT = 120  # 2 minutes
    UNIT_A_TIMEOUT = 180   # 3 minutes (foundation calculations)
    UNIT_B_TIMEOUT = 300   # 5 minutes (energy structure - most complex)
    UNIT_C_TIMEOUT = 180   # 3 minutes (advanced dynamics)
    UNIT_D_TIMEOUT = 120   # 2 minutes (integration validation)
    
    # Subprocess timeouts
    SUBPROCESS_TIMEOUT = 300  # 5 minutes for CLI calls
    
    @classmethod
    def get_unit_timeout(cls, unit_name: str) -> int:
        """
        Get timeout for specific computational unit.
        
        Args:
            unit_name: Name of the unit (e.g., 'Unit A', 'unit_b', etc.)
            
        Returns:
            Timeout in seconds for the specified unit
        """
        unit_key = unit_name.upper().replace(' ', '_')
        
        timeout_map = {
            'UNIT_A': cls.UNIT_A_TIMEOUT,
            'UNIT_B': cls.UNIT_B_TIMEOUT,
            'UNIT_C': cls.UNIT_C_TIMEOUT,
            'UNIT_D': cls.UNIT_D_TIMEOUT,
        }
        
        return timeout_map.get(unit_key, cls.DEFAULT_TIMEOUT)


class AppConfig:
    """Main application configuration."""
    
    def __init__(self):
        """Initialize application configuration."""
        self.api_key_manager = APIKeyManager()
        self.timeout_config = TimeoutConfig()
    
    def get_api_key(self, api_key_override: Optional[str] = None) -> str:
        """Get API key through the manager."""
        return self.api_key_manager.get_api_key(api_key_override)
    
    def get_headers(self, api_key_override: Optional[str] = None) -> dict:
        """Get HTTP headers through the manager."""
        return self.api_key_manager.create_headers(api_key_override)
    
    def get_subprocess_env(self, api_key_override: Optional[str] = None) -> dict:
        """Get subprocess environment through the manager."""
        return self.api_key_manager.create_subprocess_env(api_key_override)
    
    def get_unit_timeout(self, unit_name: str) -> int:
        """Get timeout for specific unit."""
        return self.timeout_config.get_unit_timeout(unit_name)


# Global configuration instance
config = AppConfig()


# Convenience functions for backward compatibility
def get_api_key(api_key_override: Optional[str] = None) -> str:
    """Get API key using global config."""
    return config.get_api_key(api_key_override)


def get_headers(api_key_override: Optional[str] = None) -> dict:
    """Get HTTP headers using global config."""
    return config.get_headers(api_key_override)


def get_subprocess_env(api_key_override: Optional[str] = None) -> dict:
    """Get subprocess environment using global config."""
    return config.get_subprocess_env(api_key_override)


def get_unit_timeout(unit_name: str) -> int:
    """Get timeout for specific unit using global config."""
    return config.get_unit_timeout(unit_name)


if __name__ == "__main__":
    # Test the configuration
    try:
        print("Testing API key configuration...")
        api_key = get_api_key()
        print(f"✅ API key loaded successfully (length: {len(api_key)})")
        
        headers = get_headers()
        print(f"✅ Headers created: {list(headers.keys())}")
        
        env = get_subprocess_env()
        print(f"✅ Subprocess environment prepared (GROK_API_KEY set: {'GROK_API_KEY' in env})")
        
        print(f"✅ Unit B timeout: {get_unit_timeout('Unit B')} seconds")
        
    except APIKeyError as e:
        print(f"❌ Configuration error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")