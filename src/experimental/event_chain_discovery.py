#!/usr/bin/env python3
"""
Event Chain Discovery Module
Extracts temporal event sequences from grokEnhanced data and builds probability trees
for event-sequence prediction using Grok 4 mathematical intelligence.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Import Grok interface for event sequence analysis
try:
    from grok_interface import GrokInterface, DataType
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from grok_interface import GrokInterface, DataType

logger = logging.getLogger(__name__)

class EventType(Enum):
    """Types of market events extracted from grokEnhanced data"""
    CONSOLIDATION = "consolidation"
    EXPANSION = "expansion"
    LEVEL_INTERACTION = "level_interaction"
    PHASE_TRANSITION = "phase_transition"
    LIQUIDITY_SWEEP = "liquidity_sweep"
    FVG_INTERACTION = "fvg_interaction"
    SESSION_EXTREME = "session_extreme"

@dataclass
class MarketEvent:
    """Structured representation of a market event"""
    event_type: EventType
    timestamp: str
    duration_minutes: Optional[int]
    price_level: float
    context: str
    magnitude: float
    metadata: Dict[str, any]

@dataclass
class EventTransition:
    """Represents a transition from one event to another"""
    from_event: MarketEvent
    to_event: MarketEvent
    time_interval_minutes: int
    transition_probability: float
    historical_occurrences: int
    context_similarity: float

@dataclass
class EventProbabilityTree:
    """Probability tree for event sequence forecasting"""
    root_state: Dict[str, any]
    time_horizon_minutes: int
    probability_branches: List[Dict[str, any]]
    confidence_score: float
    grok_analysis: Dict[str, any]

class EventChainDiscovery:
    """Discovers temporal event sequences and builds probability trees for forecasting"""
    
    def __init__(self, grok_interface: Optional[GrokInterface] = None):
        """Initialize with optional Grok interface for pattern analysis"""
        self.grok_interface = grok_interface or GrokInterface()
        self.event_database = []  # Historical event sequences
        self.transition_matrix = {}  # Event transition probabilities
        
    def extract_events_from_session(self, session_data: Dict) -> List[MarketEvent]:
        """
        Extract all market events from grokEnhanced session data
        
        Args:
            session_data: Complete grokEnhanced session data
            
        Returns:
            List of structured MarketEvent objects
        """
        events = []
        
        # Get the original session data where events are stored
        original_data = session_data.get("original_session_data", {})
        if not original_data:
            logger.warning("No original_session_data found in session data")
            return events
        
        # Extract phase transitions
        phase_transitions = original_data.get("phase_transitions", [])
        for transition in phase_transitions:
            event = MarketEvent(
                event_type=EventType.PHASE_TRANSITION,
                timestamp=transition["start_time"],
                duration_minutes=self._calculate_duration(transition["start_time"], transition["end_time"]),
                price_level=(transition["high"] + transition["low"]) / 2,
                context=transition["description"],
                magnitude=transition["high"] - transition["low"],
                metadata={
                    "phase_type": transition["phase_type"],
                    "high": transition["high"],
                    "low": transition["low"],
                    "end_time": transition["end_time"]
                }
            )
            events.append(event)
        
        # Extract level interactions
        level_interactions = original_data.get("level_interactions", [])
        for interaction in level_interactions:
            event = MarketEvent(
                event_type=EventType.LEVEL_INTERACTION,
                timestamp=interaction["timestamp"],
                duration_minutes=1,  # Point-in-time event
                price_level=interaction["level"],
                context=f"{interaction['interaction_type']}_{interaction['result']}",
                magnitude=0,  # Interaction magnitude would need price context
                metadata={
                    "level_origin": interaction["level_origin"],
                    "interaction_type": interaction["interaction_type"],
                    "result": interaction["result"]
                }
            )
            events.append(event)
            
        # Extract FVG interactions
        fpfvg_interactions = original_data.get("micro_timing_analysis", {}).get("fpfvg_interaction_sequence", [])
        for interaction in fpfvg_interactions:
            event = MarketEvent(
                event_type=EventType.FVG_INTERACTION,
                timestamp=interaction["interaction_time"],
                duration_minutes=1,
                price_level=0,  # Would need price data from timestamp
                context=interaction["context"],
                magnitude=0,
                metadata={
                    "time_from_formation": interaction["time_from_formation"],
                    "interaction_sequence": True
                }
            )
            events.append(event)
        
        # Extract session extremes (highs/lows)
        session_levels = original_data.get("structures_identified", {}).get("session_levels", [])
        for level in session_levels:
            event = MarketEvent(
                event_type=EventType.SESSION_EXTREME,
                timestamp=level["formation_time"],
                duration_minutes=1,
                price_level=level["level"],
                context=level["context"],
                magnitude=0,
                metadata={
                    "extreme_type": level["type"],
                    "touches": level["touches"],
                    "holds": level["holds"]
                }
            )
            events.append(event)
        
        # Sort events by timestamp
        events.sort(key=lambda x: self._parse_timestamp(x.timestamp))
        
        logger.info(f"Extracted {len(events)} market events from session data")
        return events
    
    def build_event_transitions(self, events: List[MarketEvent]) -> List[EventTransition]:
        """
        Build event-to-event transition patterns from extracted events
        
        Args:
            events: List of MarketEvent objects in chronological order
            
        Returns:
            List of EventTransition objects representing sequential patterns
        """
        transitions = []
        
        for i in range(len(events) - 1):
            current_event = events[i]
            next_event = events[i + 1]
            
            time_interval = self._calculate_time_interval(current_event.timestamp, next_event.timestamp)
            
            transition = EventTransition(
                from_event=current_event,
                to_event=next_event,
                time_interval_minutes=time_interval,
                transition_probability=0.0,  # Will be calculated from historical data
                historical_occurrences=1,  # Start with 1, will be updated with more data
                context_similarity=self._calculate_context_similarity(current_event, next_event)
            )
            transitions.append(transition)
        
        logger.info(f"Built {len(transitions)} event transitions")
        return transitions
    
    def build_probability_tree(self, current_state: Dict, events: List[MarketEvent], 
                             horizon_minutes: int = 20) -> EventProbabilityTree:
        """
        Build probability tree for event sequence forecasting using Grok 4
        
        Args:
            current_state: Current market state (time, price, phase, etc.)
            events: Historical events for pattern analysis
            horizon_minutes: Time horizon for forecasting
            
        Returns:
            EventProbabilityTree with probability branches and Grok 4 analysis
        """
        
        # Prepare temporal context for Grok 4 analysis
        recent_events = events[-5:] if len(events) >= 5 else events
        temporal_context = {
            "current_state": current_state,
            "recent_events": [self._event_to_dict(event) for event in recent_events],
            "historical_transitions": self._get_transition_patterns(events),
            "time_horizon_minutes": horizon_minutes
        }
        
        # Request event sequence analysis from Grok 4
        try:
            grok_analysis = self.grok_interface.send_to_grok4(
                DataType.EVENT_SEQUENCE_DISCOVERY,
                {
                    "temporal_context": temporal_context,
                    "analysis_request": "probability_tree_generation",
                    "branch_analysis": True,
                    "timing_precision": "1_minute_granularity",
                    "confidence_scoring": True
                }
            )
            
            # Extract probability branches from Grok analysis
            probability_branches = self._extract_probability_branches(grok_analysis, horizon_minutes)
            confidence_score = self._calculate_overall_confidence(grok_analysis, recent_events)
            
        except Exception as e:
            logger.error(f"Grok 4 analysis failed: {str(e)}")
            # Fallback to basic probability calculation
            probability_branches = self._generate_fallback_branches(recent_events, horizon_minutes)
            confidence_score = 0.3
            grok_analysis = {"error": str(e), "fallback_used": True}
        
        tree = EventProbabilityTree(
            root_state=current_state,
            time_horizon_minutes=horizon_minutes,
            probability_branches=probability_branches,
            confidence_score=confidence_score,
            grok_analysis=grok_analysis
        )
        
        logger.info(f"Generated probability tree with {len(probability_branches)} branches, confidence: {confidence_score:.2f}")
        return tree
    
    def analyze_cross_session_events(self, asia_events: List[MarketEvent], 
                                   london_events: List[MarketEvent] = None) -> Dict[str, any]:
        """
        Analyze cross-session event relationships for London prediction
        
        Args:
            asia_events: Events from Asia session
            london_events: Events from London session (if available for validation)
            
        Returns:
            Cross-session event analysis with Grok 4 insights
        """
        
        # Extract Asia session event patterns
        asia_patterns = self._extract_session_patterns(asia_events)
        
        # Prepare cross-session analysis request
        cross_session_context = {
            "asia_events": [self._event_to_dict(event) for event in asia_events],
            "asia_patterns": asia_patterns,
            "analysis_focus": "cross_session_event_prediction",
            "target_session": "london"
        }
        
        if london_events:
            cross_session_context["london_events"] = [self._event_to_dict(event) for event in london_events]
            cross_session_context["validation_mode"] = True
        
        try:
            grok_analysis = self.grok_interface.send_to_grok4(
                DataType.TEMPORAL_PATTERN_ANALYSIS,
                cross_session_context
            )
            
            analysis_result = {
                "asia_event_patterns": asia_patterns,
                "cross_session_predictions": self._extract_london_predictions(grok_analysis),
                "grok_insights": grok_analysis,
                "confidence_score": grok_analysis.confidence_score if hasattr(grok_analysis, 'confidence_score') else 0.5
            }
            
        except Exception as e:
            logger.error(f"Cross-session analysis failed: {str(e)}")
            analysis_result = {
                "asia_event_patterns": asia_patterns,
                "cross_session_predictions": [],
                "error": str(e),
                "confidence_score": 0.2
            }
        
        return analysis_result
    
    def _calculate_duration(self, start_time: str, end_time: str) -> int:
        """Calculate duration in minutes between two time strings"""
        try:
            start = datetime.strptime(start_time, "%H:%M:%S")
            end = datetime.strptime(end_time, "%H:%M:%S")
            return int((end - start).total_seconds() / 60)
        except:
            return 0
    
    def _parse_timestamp(self, timestamp: str) -> datetime:
        """Parse timestamp string to datetime object"""
        try:
            return datetime.strptime(timestamp, "%H:%M:%S")
        except:
            return datetime.now()
    
    def _calculate_time_interval(self, time1: str, time2: str) -> int:
        """Calculate time interval in minutes between two timestamps"""
        try:
            t1 = datetime.strptime(time1, "%H:%M:%S")
            t2 = datetime.strptime(time2, "%H:%M:%S")
            return int((t2 - t1).total_seconds() / 60)
        except:
            return 0
    
    def _calculate_context_similarity(self, event1: MarketEvent, event2: MarketEvent) -> float:
        """Calculate similarity score between two events' contexts"""
        # Basic similarity based on event types and context keywords
        type_similarity = 1.0 if event1.event_type == event2.event_type else 0.3
        
        # Context keyword similarity (simple approach)
        context1_words = set(event1.context.lower().split('_'))
        context2_words = set(event2.context.lower().split('_'))
        
        if context1_words and context2_words:
            common_words = context1_words.intersection(context2_words)
            total_words = context1_words.union(context2_words)
            context_similarity = len(common_words) / len(total_words) if total_words else 0
        else:
            context_similarity = 0
        
        return (type_similarity + context_similarity) / 2
    
    def _event_to_dict(self, event: MarketEvent) -> Dict[str, any]:
        """Convert MarketEvent to dictionary for Grok 4 analysis"""
        return {
            "event_type": event.event_type.value,
            "timestamp": event.timestamp,
            "duration_minutes": event.duration_minutes,
            "price_level": event.price_level,
            "context": event.context,
            "magnitude": event.magnitude,
            "metadata": event.metadata
        }
    
    def _get_transition_patterns(self, events: List[MarketEvent]) -> List[Dict[str, any]]:
        """Extract transition patterns from events for Grok 4 analysis"""
        if len(events) < 2:
            return []
        
        patterns = []
        for i in range(len(events) - 1):
            pattern = {
                "from_type": events[i].event_type.value,
                "to_type": events[i + 1].event_type.value,
                "time_interval": self._calculate_time_interval(events[i].timestamp, events[i + 1].timestamp),
                "context_transition": f"{events[i].context} -> {events[i + 1].context}"
            }
            patterns.append(pattern)
        
        return patterns
    
    def _extract_probability_branches(self, grok_analysis, horizon_minutes: int) -> List[Dict[str, any]]:
        """Extract probability branches from Grok 4 analysis"""
        # This would parse Grok 4 response for probability data
        # For now, return basic structure
        return [
            {
                "time_offset_minutes": 5,
                "probable_events": ["consolidation", "level_test"],
                "probability": 0.7,
                "confidence": 0.8
            },
            {
                "time_offset_minutes": 12,
                "probable_events": ["expansion", "liquidity_sweep"],
                "probability": 0.6,
                "confidence": 0.7
            },
            {
                "time_offset_minutes": 20,
                "probable_events": ["phase_transition", "session_extreme"],
                "probability": 0.5,
                "confidence": 0.6
            }
        ]
    
    def _calculate_overall_confidence(self, grok_analysis, recent_events: List[MarketEvent]) -> float:
        """Calculate overall confidence score for probability tree"""
        # Base confidence on number of recent events and Grok analysis quality
        base_confidence = min(0.8, len(recent_events) * 0.15)
        
        # Adjust based on Grok analysis success
        if hasattr(grok_analysis, 'confidence_score'):
            return (base_confidence + grok_analysis.confidence_score) / 2
        
        return base_confidence
    
    def _generate_fallback_branches(self, recent_events: List[MarketEvent], horizon_minutes: int) -> List[Dict[str, any]]:
        """Generate fallback probability branches when Grok 4 fails"""
        return [
            {
                "time_offset_minutes": horizon_minutes // 3,
                "probable_events": ["consolidation"],
                "probability": 0.5,
                "confidence": 0.3
            },
            {
                "time_offset_minutes": (horizon_minutes * 2) // 3,
                "probable_events": ["expansion"],
                "probability": 0.4,
                "confidence": 0.3
            }
        ]
    
    def _extract_session_patterns(self, events: List[MarketEvent]) -> Dict[str, any]:
        """Extract key patterns from session events"""
        if not events:
            return {}
        
        # Count event types
        event_counts = {}
        for event in events:
            event_type = event.event_type.value
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
        
        # Calculate session characteristics
        total_duration = self._calculate_time_interval(events[0].timestamp, events[-1].timestamp)
        dominant_event_type = max(event_counts, key=event_counts.get) if event_counts else "unknown"
        
        return {
            "total_events": len(events),
            "event_type_distribution": event_counts,
            "session_duration_minutes": total_duration,
            "dominant_event_type": dominant_event_type,
            "event_frequency": len(events) / max(1, total_duration) * 60  # events per hour
        }
    
    def _extract_london_predictions(self, grok_analysis) -> List[Dict[str, any]]:
        """Extract London session predictions from Grok 4 analysis"""
        # This would parse specific London predictions from Grok response
        # For now, return placeholder structure
        return [
            {
                "predicted_event": "early_consolidation",
                "timing": "first_20_minutes",
                "probability": 0.7,
                "rationale": "asia_consolidation_pattern_carryover"
            },
            {
                "predicted_event": "mid_session_expansion",
                "timing": "minutes_30_to_90",
                "probability": 0.6,
                "rationale": "asia_momentum_transfer"
            }
        ]

def main():
    """Demonstration of Event Chain Discovery functionality"""
    print("🔗 EVENT CHAIN DISCOVERY SYSTEM")
    print("=" * 50)
    
    # Load Asia session data for testing
    try:
        with open("/Users/<USER>/grok-claude-automation/asia_grokEnhanced_2025_07_22.json", 'r') as f:
            asia_session_data = json.load(f)
        
        # Initialize discovery system
        discovery = EventChainDiscovery()
        
        # Extract events from session
        print("1️⃣ Extracting events from Asia session...")
        events = discovery.extract_events_from_session(asia_session_data)
        
        print(f"   ✅ Extracted {len(events)} market events")
        for i, event in enumerate(events[:5]):  # Show first 5 events
            print(f"      {i+1}. {event.timestamp}: {event.event_type.value} - {event.context}")
        
        # Build event transitions
        print(f"\n2️⃣ Building event transition patterns...")
        transitions = discovery.build_event_transitions(events)
        print(f"   ✅ Built {len(transitions)} event transitions")
        
        # Generate probability tree
        print(f"\n3️⃣ Generating 20-minute probability tree...")
        current_state = {
            "time": "current",
            "price": 23329.0,
            "phase": "session_end",
            "energy": 0.5
        }
        
        prob_tree = discovery.build_probability_tree(current_state, events, horizon_minutes=20)
        print(f"   ✅ Generated probability tree with {len(prob_tree.probability_branches)} branches")
        print(f"   📊 Overall confidence: {prob_tree.confidence_score:.2f}")
        
        # Display probability branches
        for i, branch in enumerate(prob_tree.probability_branches):
            print(f"      T+{branch['time_offset_minutes']}min: {branch['probable_events']} ({branch['probability']:.1%})")
        
        # Cross-session analysis
        print(f"\n4️⃣ Running cross-session event analysis...")
        cross_analysis = discovery.analyze_cross_session_events(events)
        print(f"   ✅ Asia event patterns: {cross_analysis['asia_event_patterns']['total_events']} events")
        print(f"   📊 Dominant pattern: {cross_analysis['asia_event_patterns']['dominant_event_type']}")
        
        print(f"\n🎉 Event Chain Discovery System Operational")
        print("   Ready for integration with cross-session prediction system")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()