"""
Grok 4 Interface for Pattern Discovery
API integration for mathematical relationship discovery and Monte Carlo enhancement
"""

import json
import os
import subprocess
from enum import Enum
from typing import Dict, List, Optional, Union
from dataclasses import dataclass

# Import centralized config
try:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from config import config, APIKeyError
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
    from src.config import config, APIKeyError

class DataType(Enum):
    """Types of data analysis for Grok 4"""
    PATTERN_DISCOVERY = "pattern_discovery"
    MATH_RELATIONSHIPS = "math_relationships"
    ANOMALY_ANALYSIS = "anomaly_analysis"
    PARAMETER_OPTIMIZATION = "parameter_optimization"
    ERROR_CORRECTION = "error_correction"
    # New types for reverse engineering
    TIMING_FAILURE_DISCOVERY = "timing_failure_discovery"
    MAGNITUDE_FAILURE_DISCOVERY = "magnitude_failure_discovery"
    LIQUIDITY_FAILURE_DISCOVERY = "liquidity_failure_discovery"
    STATE_TRANSITION_ANALYSIS = "state_transition_analysis"
    ALTERNATIVE_FORMULA_DISCOVERY = "alternative_formula_discovery"
    # New event-sequence prediction types
    EVENT_SEQUENCE_DISCOVERY = "event_sequence_discovery"
    TEMPORAL_PATTERN_ANALYSIS = "temporal_pattern_analysis"
    PROBABILITY_CASCADE_MODELING = "probability_cascade_modeling"
    # New timing-focused analysis types
    TIMING_DISCOVERY = "timing_discovery"
    TEMPORAL_ACCURACY_ANALYSIS = "temporal_accuracy_analysis"
    CASCADE_TIMING_PATTERNS = "cascade_timing_patterns"
    EVENT_SEQUENCE_TIMING = "event_sequence_timing"

@dataclass
class GrokResponse:
    """Structured response from Grok 4"""
    success: bool
    data_type: DataType
    analysis_result: Dict
    mathematical_formulas: List[str]
    implementation_suggestions: List[str]
    confidence_score: float
    raw_response: str

class GrokInterface:
    """Interface for sending analysis packages to Grok 4 and processing responses"""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Grok interface"""
        try:
            self.api_key = config.get_api_key(api_key)
        except APIKeyError as e:
            raise ValueError(str(e))
    
    def send_to_grok4(self, data_type: DataType, payload: Dict) -> GrokResponse:
        """
        Send analysis data to Grok 4 for pattern discovery
        
        Args:
            data_type: Type of analysis to perform
            payload: Analysis data package
            
        Returns:
            Structured Grok response with discoveries
        """
        # Generate specialized prompt based on data type
        prompt = self._generate_prompt(data_type, payload)
        
        # Send to Grok 4
        try:
            raw_response = self._execute_grok_call(prompt)
            
            # Parse response
            parsed_response = self._parse_grok_response(raw_response, data_type)
            
            return parsed_response
            
        except Exception as e:
            return GrokResponse(
                success=False,
                data_type=data_type,
                analysis_result={"error": str(e)},
                mathematical_formulas=[],
                implementation_suggestions=[],
                confidence_score=0.0,
                raw_response=""
            )
    
    def _generate_prompt(self, data_type: DataType, payload: Dict) -> str:
        """Generate specialized prompts for different analysis types"""
        
        if data_type == DataType.PATTERN_DISCOVERY:
            return self._create_pattern_discovery_prompt(payload)
        elif data_type == DataType.MATH_RELATIONSHIPS:
            return self._create_math_relationships_prompt(payload)
        elif data_type == DataType.ANOMALY_ANALYSIS:
            return self._create_anomaly_analysis_prompt(payload)
        elif data_type == DataType.PARAMETER_OPTIMIZATION:
            return self._create_parameter_optimization_prompt(payload)
        elif data_type == DataType.ERROR_CORRECTION:
            return self._create_error_correction_prompt(payload)
        # New reverse engineering prompts
        elif data_type == DataType.TIMING_FAILURE_DISCOVERY:
            return self._create_timing_failure_prompt(payload)
        elif data_type == DataType.MAGNITUDE_FAILURE_DISCOVERY:
            return self._create_magnitude_failure_prompt(payload)
        elif data_type == DataType.LIQUIDITY_FAILURE_DISCOVERY:
            return self._create_liquidity_failure_prompt(payload)
        elif data_type == DataType.STATE_TRANSITION_ANALYSIS:
            return self._create_state_transition_prompt(payload)
        elif data_type == DataType.ALTERNATIVE_FORMULA_DISCOVERY:
            return self._create_alternative_formula_prompt(payload)
        # New event-sequence analysis prompts
        elif data_type == DataType.EVENT_SEQUENCE_DISCOVERY:
            return self._create_event_sequence_prompt(payload)
        elif data_type == DataType.TEMPORAL_PATTERN_ANALYSIS:
            return self._create_temporal_pattern_prompt(payload)
        elif data_type == DataType.PROBABILITY_CASCADE_MODELING:
            return self._create_probability_cascade_prompt(payload)
        # New timing-focused prompts
        elif data_type == DataType.TIMING_DISCOVERY:
            return self._create_timing_discovery_prompt(payload)
        elif data_type == DataType.TEMPORAL_ACCURACY_ANALYSIS:
            return self._create_temporal_accuracy_prompt(payload)
        elif data_type == DataType.CASCADE_TIMING_PATTERNS:
            return self._create_cascade_timing_prompt(payload)
        elif data_type == DataType.EVENT_SEQUENCE_TIMING:
            return self._create_event_sequence_timing_prompt(payload)
        else:
            raise ValueError(f"Unknown data type: {data_type}")
    
    def _create_pattern_discovery_prompt(self, payload: Dict) -> str:
        """Create prompt for discovering timing patterns in event predictions vs actual timing"""
        return f'''
EVENT TIMING PATTERN DISCOVERY ANALYSIS

You are analyzing event timing predictions vs actual event timing to discover temporal patterns and relationships.

PREDICTION VS ACTUAL TIMING DATA:
{json.dumps(payload.get("timing_comparison", {}), indent=2)}

EVENT TIMING SEQUENCES:
{json.dumps(payload.get("event_timing_sequences", {}), indent=2)}

TASK: Discover patterns in timing prediction errors and suggest temporal corrections.

ANALYSIS FRAMEWORK:
1. Identify systematic timing biases in event predictions
2. Find correlations between input parameters and timing error patterns  
3. Discover temporal relationships that could improve timing accuracy
4. Suggest specific timing formula modifications

KEY QUESTIONS TO ANSWER:
- What predicted this cascade timing successfully?
- When will next expansion occur based on current patterns?
- Which temporal factors correlate with timing accuracy?
- How can timing formulas be improved for better event prediction?

RESPONSE FORMAT:
{{
  "discovered_timing_patterns": [
    "Timing pattern description with temporal mathematical basis"
  ],
  "temporal_relationships": [
    "t_memory^1.5 * fvg_density correlates with cascade timing accuracy",
    "session_character 'expansion_consolidation' requires timing_factor * 0.7"
  ],
  "timing_formula_corrections": [
    "cascade_time = base_time * (t_memory^1.5 * consolidation_factor)",
    "consolidation_factor = 0.7 if session_character.contains('consolidation') else 1.0"
  ],
  "implementation_priority": "high|medium|low",
  "confidence_score": 0.85
}}

Focus on temporal precision and timing accuracy improvements.
'''

    def _create_math_relationships_prompt(self, payload: Dict) -> str:
        """Create prompt for discovering mathematical relationships"""
        return f'''
MATHEMATICAL RELATIONSHIP DISCOVERY

Analyze parameter correlations and mathematical relationships in Monte Carlo trading predictions.

CORRELATION DATA:
{json.dumps(payload.get("parameter_correlations", {}), indent=2)}

MATHEMATICAL RELATIONSHIPS:
{json.dumps(payload.get("mathematical_relationships", {}), indent=2)}

TASK: Find hidden mathematical relationships and propose improved formulas.

FOCUS AREAS:
1. Non-linear relationships between parameters
2. Threshold effects and phase transitions
3. Exponential vs linear scaling corrections
4. Parameter interaction effects

RESPONSE FORMAT:
{{
  "discovered_relationships": [
    {{
      "relationship": "t_memory exhibits threshold effect at 5.0",
      "mathematical_formula": "scaling_factor = 1.0 if t_memory < 5.0 else 0.6 * exp(-0.1 * (t_memory - 5.0))",
      "implementation": "Apply to generate_move_magnitude() based on t_memory",
      "confidence": 0.9
    }}
  ],
  "parameter_interactions": [
    {{
      "parameters": ["t_memory", "gamma_enhanced"],
      "interaction_formula": "combined_effect = t_memory * gamma_enhanced^0.7",
      "application": "Range scaling factor"
    }}
  ],
  "proposed_corrections": [
    "Replace fixed scaling with adaptive scaling based on session_character",
    "Implement t_memory threshold effects in consolidation probability"
  ]
}}

Provide precise mathematical formulas ready for implementation.
'''

    def _create_anomaly_analysis_prompt(self, payload: Dict) -> str:
        """Create prompt for analyzing prediction anomalies"""
        return f'''
ANOMALY ANALYSIS - PREDICTION FAILURES

Analyze cases where Monte Carlo predictions failed significantly.

ANOMALY DATA:
{json.dumps(payload.get("anomaly_detection", {}), indent=2)}

ERROR PATTERNS:
{json.dumps(payload.get("error_patterns", {}), indent=2)}

TASK: Identify root causes of prediction failures and propose specific fixes.

ANALYSIS FOCUS:
1. What parameter combinations cause large errors?
2. Which session characteristics are poorly modeled?
3. What mathematical assumptions are violated?
4. How can the model be corrected?

RESPONSE FORMAT:
{{
  "anomaly_root_causes": [
    {{
      "cause": "Consolidation sessions with t_memory > 5.0",
      "error_pattern": "Range overestimation by 400-500%",
      "parameter_signature": {{"t_memory": ">5.0", "session_character": "consolidation"}},
      "correction": "Apply consolidation_factor = 0.2 when detecting consolidation patterns"
    }}
  ],
  "model_improvements": [
    "Add session character detection to magnitude scaling",
    "Implement adaptive range prediction based on historical session behavior"
  ],
  "critical_fixes": [
    {{
      "priority": "high",
      "fix": "Detect 'expansion_then_consolidation' pattern and reduce range prediction by 80%",
      "implementation": "Add session character analysis to PathGenerator.__init__()"
    }}
  ]
}}

Focus on actionable fixes with clear implementation paths.
'''

    def _create_parameter_optimization_prompt(self, payload: Dict) -> str:
        """Create prompt for parameter optimization"""
        return f'''
PARAMETER OPTIMIZATION ANALYSIS

Optimize Monte Carlo parameters based on actual trading session results.

CURRENT PARAMETERS AND ERRORS:
{json.dumps(payload, indent=2)}

TASK: Suggest optimal parameter values and ranges for better prediction accuracy.

OPTIMIZATION TARGETS:
1. Minimize prediction error for both price and range
2. Improve session character classification accuracy
3. Balance between different session types (consolidation vs expansion)
4. Maintain realistic volatility modeling

RESPONSE FORMAT:
{{
  "optimized_parameters": {{
    "generate_move_magnitude": {{
      "current": "gamma(shape=2.0, scale=8.0), max=50.0",
      "optimized": "gamma(shape=1.5, scale=6.0), max=30.0",
      "reasoning": "Reduces overestimation for consolidation sessions"
    }},
    "consolidation_probability": {{
      "current": "1 - exp(-0.1 * t_memory)",
      "optimized": "0.8 if session_character.contains('consolidation') else 1 - exp(-0.15 * t_memory)",
      "reasoning": "Better detection of consolidation vs expansion phases"
    }}
  }},
  "parameter_ranges": {{
    "t_memory_scaling": [3.0, 8.0],
    "gamma_base_range": [1.2, 1.8],
    "magnitude_cap": [25.0, 40.0]
  }},
  "implementation_priority": [
    "Magnitude scaling adjustment (immediate)",
    "Session character detection (high)",
    "Parameter range validation (medium)"
  ]
}}

Provide specific parameter values with mathematical justification.
'''

    def _create_error_correction_prompt(self, payload: Dict) -> str:
        """Create prompt for error correction strategies"""
        return f'''
ERROR CORRECTION STRATEGY DEVELOPMENT

Develop systematic error correction methods for Monte Carlo predictions.

PREDICTION ERRORS:
{json.dumps(payload.get("error_patterns", {}), indent=2)}

SYSTEMATIC BIASES:
{json.dumps(payload.get("systematic_biases", {}), indent=2)}

TASK: Design error correction algorithms and bias adjustment methods.

CORRECTION STRATEGIES:
1. Post-prediction bias adjustment
2. Dynamic parameter scaling during simulation
3. Session-specific correction factors
4. Ensemble methods for improved accuracy

RESPONSE FORMAT:
{{
  "bias_corrections": [
    {{
      "bias_type": "range_overestimation",
      "correction_formula": "corrected_range = predicted_range * correction_factor",
      "correction_factor": "0.3 if session_character == 'consolidation' else 0.8",
      "application_timing": "post_prediction"
    }}
  ],
  "dynamic_corrections": [
    {{
      "trigger": "t_memory > 5.0 AND session_character contains 'consolidation'",
      "adjustment": "magnitude_scaling *= 0.25",
      "implementation": "Within generate_move_magnitude() method"
    }}
  ],
  "ensemble_methods": [
    "Run multiple simulations with different parameter sets",
    "Weight results based on session character matching",
    "Apply correction factors based on historical accuracy"
  ],
  "validation_metrics": [
    "Mean absolute error for price predictions",
    "Range prediction accuracy by session type",
    "Overall model confidence scores"
  ]
}}

Focus on systematic, implementable correction strategies.
'''

    def _create_timing_failure_prompt(self, payload: Dict) -> str:
        """Create prompt for timing failure reverse engineering"""
        return f'''
TIMING FAILURE REVERSE ENGINEERING

You are reverse engineering a Monte Carlo prediction that failed due to timing issues.

FAILURE ANALYSIS:
{json.dumps(payload.get("failure_analysis", {}), indent=2)}

DIVERGENCE POINTS:
{json.dumps(payload.get("divergence_points", {}), indent=2)}

STATE TRANSITIONS:
{json.dumps(payload.get("state_transitions", {}), indent=2)}

TASK: Discover alternative timing formulas when standard predictions fail.

ANALYSIS FOCUS:
1. Given this state transition failed, what mathematical relationship would predict the actual timing?
2. Find alternative timing formulas when liquidity X was hit at time Y but prediction Z failed
3. Discover session character-specific timing corrections
4. Identify T_memory and gamma timing relationship alternatives

RESPONSE FORMAT:
{{
  "alternative_timing_formulas": [
    {{
      "condition": "when session_character contains 'consolidation' AND t_memory > 5.0",
      "original_timing": "22.5 / gamma_enhanced",
      "alternative_timing": "15.0 / (gamma_enhanced * consolidation_factor)",
      "consolidation_factor": "0.6 if 'expansion_then_consolidation' else 1.0",
      "confidence": 0.85
    }}
  ],
  "state_based_corrections": [
    {{
      "state_trigger": "liquidity_level_approached",
      "timing_adjustment": "multiply by session_volatility_factor",
      "implementation": "Within generate_time_to_move() method"
    }}
  ],
  "mathematical_discoveries": [
    "Alternative timing formula based on actual failure patterns"
  ],
  "confidence_score": 0.8
}}

Focus on implementable timing corrections that address the specific failure pattern.
'''

    def _create_magnitude_failure_prompt(self, payload: Dict) -> str:
        """Create prompt for magnitude failure reverse engineering"""
        return f'''
MAGNITUDE FAILURE REVERSE ENGINEERING

You are reverse engineering a Monte Carlo prediction that failed due to incorrect move magnitudes.

FAILURE ANALYSIS:
{json.dumps(payload.get("failure_analysis", {}), indent=2)}

MARKET CONDITIONS:
{json.dumps(payload.get("market_conditions", {}), indent=2)}

BROKEN CHAINS:
{json.dumps(payload.get("broken_chains", {}), indent=2)}

TASK: Discover alternative magnitude scaling formulas when standard predictions fail.

ANALYSIS FOCUS:
1. What magnitude scaling would have predicted the actual move size?
2. Find session character-specific magnitude multipliers for failed cases
3. Discover volatility-based magnitude adjustments
4. Identify liquidity gradient magnitude corrections

RESPONSE FORMAT:
{{
  "alternative_magnitude_formulas": [
    {{
      "failure_context": "consolidation session with t_memory > 5.0",
      "original_formula": "np.random.gamma(shape=2.0, scale=8.0)",
      "alternative_formula": "np.random.gamma(shape=1.5, scale=4.0 * session_dampening)",
      "session_dampening": "0.15 if 'expansion_then_consolidation' else 0.8",
      "confidence": 0.9
    }}
  ],
  "scaling_corrections": [
    {{
      "trigger": "failure_magnitude > 50 AND session_character == 'consolidation'",
      "correction": "magnitude *= 0.1",
      "rationale": "Extreme dampening needed for tight consolidation patterns"
    }}
  ],
  "dynamic_adjustments": [
    "Adaptive magnitude scaling based on historical failure patterns"
  ],
  "confidence_score": 0.85
}}

Focus on precise magnitude corrections that would have prevented the prediction failure.
'''

    def _create_liquidity_failure_prompt(self, payload: Dict) -> str:
        """Create prompt for liquidity interaction failure reverse engineering"""
        return f'''
LIQUIDITY INTERACTION FAILURE REVERSE ENGINEERING

You are reverse engineering a Monte Carlo prediction that failed due to incorrect liquidity interaction modeling.

FAILURE_ANALYSIS:
{json.dumps(payload.get("failure_analysis", {}), indent=2)}

STATE_TRANSITIONS:
{json.dumps(payload.get("state_transitions", {}), indent=2)}

ORIGINAL_PARAMETERS:
{json.dumps(payload.get("original_parameters", {}), indent=2)}

TASK: Discover alternative liquidity interaction formulas when standard models fail.

ANALYSIS FOCUS:
1. What liquidity magnetic pull formula would predict actual price action?
2. Find state transition mathematical relationships for failed cases
3. Discover time-based liquidity interaction corrections
4. Identify liquidity gradient vs price movement alternatives

RESPONSE FORMAT:
{{
  "alternative_liquidity_formulas": [
    {{
      "interaction_type": "liquidity_rejection",
      "original_formula": "pull_strength = 1/abs(level - current_price)",
      "alternative_formula": "pull_strength = session_multiplier / (abs(level - current_price) ** power_factor)",
      "session_multiplier": "0.3 if 'consolidation' else 1.2",
      "power_factor": "1.5 if volatile_session else 1.0",
      "confidence": 0.8
    }}
  ],
  "state_transition_rules": [
    {{
      "from_state": "approaching_liquidity",
      "to_state": "rejecting_liquidity", 
      "condition": "session_character == 'expansion_then_consolidation'",
      "probability_adjustment": "* 0.2"
    }}
  ],
  "magnetic_pull_corrections": [
    "Alternative liquidity magnetic pull based on session character and failure context"
  ],
  "confidence_score": 0.75
}}

Focus on liquidity interaction corrections that address the specific failure patterns.
'''

    def _create_state_transition_prompt(self, payload: Dict) -> str:
        """Create prompt for state transition analysis"""
        return f'''
STATE TRANSITION MATHEMATICAL ANALYSIS

You are analyzing state transitions in liquidity-time space to discover mathematical relationships.

STATE_TRANSITIONS:
{json.dumps(payload.get("state_transitions", {}), indent=2)}

MARKET_CONDITIONS:
{json.dumps(payload.get("market_conditions", {}), indent=2)}

TASK: Discover mathematical relationships governing state transitions in trading systems.

ANALYSIS FOCUS:
1. What mathematical rules govern transitions between liquidity states?
2. Find time-based state transition probabilities
3. Discover session character influence on state changes
4. Identify parameter thresholds that trigger state transitions

RESPONSE FORMAT:
{{
  "state_transition_mathematics": [
    {{
      "transition": "approaching -> touching -> rejecting",
      "mathematical_rule": "P(rejection) = 1 - exp(-session_volatility * time_factor)",
      "session_volatility": "2.5 if 'volatile' in session_character else 0.5",
      "time_factor": "t_memory / 10.0",
      "confidence": 0.8
    }}
  ],
  "threshold_effects": [
    {{
      "parameter": "t_memory",
      "threshold": 5.0,
      "effect": "state_transition_probability *= 0.3 if t_memory > 5.0"
    }}
  ],
  "predictive_rules": [
    "if state == 'touching' and session_character == 'consolidation' then probability(absorption) = 0.8"
  ],
  "confidence_score": 0.85
}}

Focus on mathematically precise state transition rules.
'''

    def _create_alternative_formula_prompt(self, payload: Dict) -> str:
        """Create prompt for discovering completely alternative mathematical approaches"""
        return f'''
ALTERNATIVE FORMULA DISCOVERY

You are tasked with discovering completely alternative mathematical approaches when standard Monte Carlo formulas fail.

COMPLETE_FAILURE_CONTEXT:
{json.dumps(payload, indent=2)}

TASK: Discover fundamentally different mathematical approaches to prediction.

ANALYSIS FOCUS:
1. What completely different mathematical framework would work better?
2. Find alternative probability distributions (beyond gamma)
3. Discover non-linear relationships between parameters
4. Identify ensemble or hybrid approaches

RESPONSE FORMAT:
{{
  "alternative_mathematical_frameworks": [
    {{
      "approach": "Bayesian inference with session character priors",
      "mathematical_basis": "P(outcome|session_character) * P(session_character|parameters)",
      "implementation": "Replace Monte Carlo with Bayesian updating",
      "confidence": 0.7
    }}
  ],
  "alternative_distributions": [
    {{
      "current": "gamma distribution for magnitude",
      "alternative": "mixture of beta and exponential distributions",
      "rationale": "Better models consolidation vs expansion phases",
      "formula": "0.7 * beta(a=2, b=5) + 0.3 * exponential(lambda=session_rate)"
    }}
  ],
  "hybrid_approaches": [
    "Combine deterministic rules for consolidation with stochastic simulation for expansion"
  ],
  "confidence_score": 0.6
}}

Focus on fundamentally different mathematical approaches that could replace failed standard methods.
'''

    def _execute_grok_call(self, prompt: str) -> str:
        """Execute Grok API call using subprocess"""
        try:
            cmd = [
                "grok", 
                "--model", "grok-4-latest",
                "--prompt", prompt
            ]
            
            # Set environment variable for the subprocess using centralized config
            env = config.get_subprocess_env()
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                env=env,
                timeout=config.timeout_config.SUBPROCESS_TIMEOUT  # Centralized subprocess timeout
            )
            
            if result.returncode != 0:
                raise Exception(f"Grok CLI error: {result.stderr}")
            
            return result.stdout.strip()
            
        except subprocess.TimeoutExpired:
            raise Exception("Grok API call timed out")
        except Exception as e:
            raise Exception(f"Grok API error: {str(e)}")
    
    def _parse_grok_response(self, raw_response: str, data_type: DataType) -> GrokResponse:
        """Parse Grok response and extract structured data"""
        try:
            # Try to extract JSON from response
            json_start = raw_response.find('{')
            json_end = raw_response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_content = raw_response[json_start:json_end]
                parsed_data = json.loads(json_content)
                
                # Extract mathematical formulas
                formulas = []
                if "mathematical_relationships" in parsed_data:
                    formulas.extend(parsed_data["mathematical_relationships"])
                if "formula_corrections" in parsed_data:
                    formulas.extend(parsed_data["formula_corrections"])
                if "discovered_relationships" in parsed_data:
                    for rel in parsed_data["discovered_relationships"]:
                        if isinstance(rel, dict) and "mathematical_formula" in rel:
                            formulas.append(rel["mathematical_formula"])
                
                # Extract implementation suggestions
                suggestions = []
                if "implementation_priority" in parsed_data:
                    suggestions.append(f"Priority: {parsed_data['implementation_priority']}")
                if "proposed_corrections" in parsed_data:
                    suggestions.extend(parsed_data["proposed_corrections"])
                if "critical_fixes" in parsed_data:
                    for fix in parsed_data["critical_fixes"]:
                        if isinstance(fix, dict) and "fix" in fix:
                            suggestions.append(fix["fix"])
                
                # Extract confidence score
                confidence = parsed_data.get("confidence_score", 0.7)
                
                return GrokResponse(
                    success=True,
                    data_type=data_type,
                    analysis_result=parsed_data,
                    mathematical_formulas=formulas,
                    implementation_suggestions=suggestions,
                    confidence_score=confidence,
                    raw_response=raw_response
                )
            else:
                # Fallback: treat as text response
                return GrokResponse(
                    success=True,
                    data_type=data_type,
                    analysis_result={"text_analysis": raw_response},
                    mathematical_formulas=[],
                    implementation_suggestions=[],
                    confidence_score=0.5,
                    raw_response=raw_response
                )
                
        except json.JSONDecodeError:
            # Fallback for non-JSON responses
            return GrokResponse(
                success=True,
                data_type=data_type,
                analysis_result={"text_analysis": raw_response},
                mathematical_formulas=[],
                implementation_suggestions=[],
                confidence_score=0.5,
                raw_response=raw_response
            )
    
    def batch_analysis(self, analysis_package: Dict) -> List[GrokResponse]:
        """
        Run multiple analysis types on the same data package
        
        Args:
            analysis_package: Complete analysis data
            
        Returns:
            List of responses from different analysis types
        """
        results = []
        
        # Run pattern discovery
        pattern_response = self.send_to_grok4(DataType.PATTERN_DISCOVERY, analysis_package)
        results.append(pattern_response)
        
        # Run mathematical relationship analysis
        math_response = self.send_to_grok4(DataType.MATH_RELATIONSHIPS, analysis_package)
        results.append(math_response)
        
        # Run anomaly analysis if anomalies detected
        if analysis_package.get("anomaly_detection", {}).get("anomaly_count", 0) > 0:
            anomaly_response = self.send_to_grok4(DataType.ANOMALY_ANALYSIS, analysis_package)
            results.append(anomaly_response)
        
        return results
    
    def save_analysis_results(self, responses: List[GrokResponse], output_file: str):
        """Save Grok analysis results to file"""
        results_data = {
            "analysis_timestamp": json.dumps({"timestamp": "2025-07-24"}),  # Would use datetime in production
            "total_analyses": len(responses),
            "analyses": []
        }
        
        for i, response in enumerate(responses):
            analysis_data = {
                "analysis_id": i + 1,
                "data_type": response.data_type.value,
                "success": response.success,
                "confidence_score": response.confidence_score,
                "mathematical_formulas": response.mathematical_formulas,
                "implementation_suggestions": response.implementation_suggestions,
                "analysis_result": response.analysis_result
            }
            results_data["analyses"].append(analysis_data)
        
        with open(output_file, 'w') as f:
            json.dump(results_data, f, indent=2)
    
    def _create_timing_discovery_prompt(self, payload: Dict) -> str:
        """Create prompt for discovering timing patterns in market events"""
        return f'''
TIMING DISCOVERY ANALYSIS

You are analyzing temporal patterns in market events to discover when specific events occur.

EVENT TIMING DATA:
{json.dumps(payload.get("event_timing_data", {}), indent=2)}

MARKET STATE CONTEXT:
{json.dumps(payload.get("market_state_context", {}), indent=2)}

TASK: Discover what factors predict the timing of market events (WHEN not WHERE).

ANALYSIS FOCUS:
1. What predicted this cascade timing successfully?
2. Which temporal patterns indicate event initiation?
3. How do T_memory and FVG density affect event timing?
4. What mathematical relationships govern event occurrence timing?

RESPONSE FORMAT:
{{
  "timing_predictors": [
    {{
      "factor": "t_memory_acceleration",
      "timing_influence": "cascade events occur when t_memory^1.5 > threshold",
      "accuracy": 0.85
    }}
  ],
  "temporal_formulas": [
    "cascade_timing = base_time * (t_memory^1.5 * fvg_density)",
    "expansion_timing = session_start + (consolidation_duration * 1.3)"
  ],
  "timing_thresholds": [
    {{
      "event_type": "cascade_initiation",
      "threshold": "t_memory > 5.0 AND fvg_density > 0.7",
      "timing_window": "3-8 minutes after threshold breach"
    }}
  ],
  "confidence_score": 0.8
}}

Focus on discovering temporal relationships for precise event timing prediction.
'''

    def _create_temporal_accuracy_prompt(self, payload: Dict) -> str:
        """Create prompt for analyzing temporal prediction accuracy"""
        return f'''
TEMPORAL ACCURACY ANALYSIS

You are analyzing timing prediction accuracy to improve event timing forecasting.

TIMING ACCURACY DATA:
{json.dumps(payload.get("timing_accuracy_data", {}), indent=2)}

PREDICTION ERRORS:
{json.dumps(payload.get("timing_prediction_errors", {}), indent=2)}

TASK: Analyze timing prediction accuracy and suggest improvements for better event timing.

ANALYSIS FOCUS:
1. Which timing predictions were most accurate and why?
2. What factors cause timing prediction errors?
3. How can timing formulas be improved to reduce errors?
4. When will next expansion occur based on current accuracy patterns?

RESPONSE FORMAT:
{{
  "accuracy_analysis": {{
    "successful_predictions": "Predictions with <5min timing error",
    "error_patterns": "Systematic timing biases identified",
    "improvement_areas": "Factors that need timing adjustments"
  }},
  "timing_improvements": [
    {{
      "current_error": "10min average timing error for cascades",
      "improvement": "Apply session_character timing multiplier",
      "expected_accuracy": "3min average timing error"
    }}
  ],
  "next_event_timing": {{
    "event_type": "expansion_phase",
    "predicted_timing": "13:47 ± 4 minutes",
    "confidence": 0.78
  }},
  "confidence_score": 0.82
}}

Focus on timing accuracy improvements and precise event timing predictions.
'''

    def _create_cascade_timing_prompt(self, payload: Dict) -> str:
        """Create prompt for analyzing cascade timing patterns"""
        return f'''
CASCADE TIMING PATTERN ANALYSIS

You are analyzing cascade timing patterns to predict when cascade events will occur.

CASCADE DATA:
{json.dumps(payload.get("cascade_data", {}), indent=2)}

TIMING PATTERNS:
{json.dumps(payload.get("timing_patterns", {}), indent=2)}

TASK: Discover patterns in cascade timing to predict when cascades will initiate.

ANALYSIS FOCUS:
1. What predicted this cascade timing at 13:45 with 0-minute error?
2. Which factors indicate cascade timing initiation?
3. How do consolidation patterns affect cascade timing?
4. What timing windows predict cascade occurrence?

RESPONSE FORMAT:
{{
  "cascade_timing_patterns": [
    {{
      "pattern": "consolidation_to_cascade",
      "timing_formula": "cascade_time = consolidation_end + (t_memory * 2.3)",
      "accuracy": 0.92
    }}
  ],
  "successful_predictions": [
    {{
      "prediction": "13:45 cascade timing",
      "actual": "13:45:00",
      "error_minutes": 0.0,
      "factors": ["t_memory=15.0", "session_character=expansion_consolidation"]
    }}
  ],
  "timing_windows": [
    {{
      "event": "next_cascade",
      "window_start": "14:15",
      "window_end": "14:22",
      "confidence": 0.76
    }}
  ],
  "confidence_score": 0.87
}}

Focus on precise cascade timing prediction with proven accuracy metrics.
'''

    def _create_event_sequence_timing_prompt(self, payload: Dict) -> str:
        """Create prompt for analyzing event sequence timing patterns"""
        return f'''
EVENT SEQUENCE TIMING ANALYSIS

You are analyzing timing patterns in event sequences to predict when sequential events will occur.

EVENT SEQUENCE DATA:
{json.dumps(payload.get("event_sequence_data", {}), indent=2)}

TEMPORAL RELATIONSHIPS:
{json.dumps(payload.get("temporal_relationships", {}), indent=2)}

TASK: Discover timing relationships between sequential market events.

ANALYSIS FOCUS:
1. When will next expansion occur after current consolidation?
2. How do event sequences affect subsequent event timing?
3. What temporal patterns link consecutive events?
4. Which timing formulas predict event sequence progression?

RESPONSE FORMAT:
{{
  "sequence_timing_patterns": [
    {{
      "sequence": "consolidation → pre_cascade → expansion",
      "timing_relationships": [
        "pre_cascade = consolidation_end + 2min",
        "expansion = pre_cascade + 5min"
      ],
      "total_sequence_time": "12-18 minutes"
    }}
  ],
  "next_event_predictions": [
    {{
      "current_event": "consolidation_phase",
      "next_event": "expansion_initiation", 
      "predicted_timing": "14:23 ± 3 minutes",
      "sequence_position": 2
    }}
  ],
  "temporal_dependencies": [
    "Event N+1 timing depends on Event N duration and intensity",
    "T_memory acceleration affects all subsequent event timing"
  ],
  "confidence_score": 0.79
}}

Focus on sequential event timing relationships and temporal dependencies.
'''

# Example usage
if __name__ == "__main__":
    # This would be run after pattern_extractor.py creates the analysis package
    interface = GrokInterface()
    
    # Load analysis package
    try:
        with open("grok4_analysis_package_2025_07_22.json", 'r') as f:
            analysis_package = json.load(f)
        
        print("🚀 Sending analysis package to Grok 4...")
        
        # Run batch analysis
        responses = interface.batch_analysis(analysis_package)
        
        # Save results
        interface.save_analysis_results(responses, "grok4_discoveries_2025_07_22.json")
        
        print(f"✅ Completed {len(responses)} analyses")
        print("📊 Results saved to: grok4_discoveries_2025_07_22.json")
        
        # Print summary
        for i, response in enumerate(responses):
            print(f"\n{i+1}. {response.data_type.value}:")
            print(f"   Success: {response.success}")
            print(f"   Confidence: {response.confidence_score}")
            print(f"   Formulas found: {len(response.mathematical_formulas)}")
            print(f"   Suggestions: {len(response.implementation_suggestions)}")
            
    except FileNotFoundError:
        print("❌ Analysis package not found. Run pattern_extractor.py first.")

    def _create_event_sequence_prompt(self, payload: Dict) -> str:
        """Create prompt for event sequence discovery and forecasting"""
        return f'''
EVENT SEQUENCE DISCOVERY ANALYSIS

You are analyzing temporal market event sequences to build probability trees for event forecasting.

TEMPORAL CONTEXT:
{json.dumps(payload.get("temporal_context", {}), indent=2)}

TASK: Build 20-minute event probability cascade with precise timing predictions.

ANALYSIS FOCUS:
1. What event sequences are most likely given current market state?
2. Calculate conditional probabilities for event chains
3. Identify timing patterns for event occurrence
4. Build branching probability tree for forecast horizon

RESPONSE FORMAT:
{{
  "probability_branches": [
    {{
      "time_offset_minutes": 5,
      "probable_events": ["consolidation", "level_test"],
      "conditional_probability": 0.7,
      "confidence": 0.8,
      "trigger_conditions": "if current phase == consolidation"
    }},
    {{
      "time_offset_minutes": 12,
      "probable_events": ["expansion", "liquidity_sweep"],
      "conditional_probability": 0.6,
      "confidence": 0.7,
      "trigger_conditions": "if liquidity_test fails"
    }}
  ],
  "mathematical_relationships": [
    "P(expansion|consolidation>15min) = 0.7",
    "P(liquidity_sweep|session_extreme_approached) = 0.8"
  ],
  "timing_patterns": [
    "Consolidation typically lasts 15-25 minutes before expansion",
    "Level interactions occur every 8-12 minutes on average"
  ],
  "confidence_score": 0.75
}}

Focus on actionable event predictions with precise timing and probability scoring.
'''

    def _create_temporal_pattern_prompt(self, payload: Dict) -> str:
        """Create prompt for temporal pattern analysis across sessions"""
        return f'''
TEMPORAL PATTERN ANALYSIS

You are analyzing cross-session temporal patterns to predict event sequences.

CROSS-SESSION CONTEXT:
{json.dumps(payload, indent=2)}

TASK: Discover mathematical patterns in event timing and transitions across sessions.

ANALYSIS FOCUS:
1. How do Asia session events influence London event probabilities?
2. What temporal patterns persist across session boundaries?
3. Which event sequences have predictive power for next session?
4. Calculate cross-session event correlation coefficients

RESPONSE FORMAT:
{{
  "cross_session_patterns": [
    {{
      "asia_event": "consolidation_ending",
      "london_probability": "expansion_opening",
      "correlation_strength": 0.75,
      "timing_delay": "15_minutes_into_london"
    }}
  ],
  "temporal_relationships": [
    "Asia liquidity_sweep → London consolidation (0.8 probability)",
    "Asia expansion_phase → London range_bound (0.6 probability)"
  ],
  "predictive_formulas": [
    "P(London_event|Asia_pattern) = base_probability * correlation_factor"
  ],
  "confidence_score": 0.7
}}

Focus on discoverable mathematical relationships between session event patterns.
'''

    def _create_probability_cascade_prompt(self, payload: Dict) -> str:
        """Create prompt for probability cascade modeling"""
        return f'''
PROBABILITY CASCADE MODELING

You are modeling cascading probabilities for multi-step event sequences.

EVENT CHAIN DATA:
{json.dumps(payload, indent=2)}

TASK: Build mathematical model for cascading event probabilities with temporal precision.

ANALYSIS FOCUS:
1. How do initial events influence subsequent event probabilities?
2. Calculate probability decay functions for event influence
3. Model branching scenarios with conditional probabilities
4. Build mathematical framework for cascade prediction

RESPONSE FORMAT:
{{
  "cascade_model": {{
    "initial_event_influence": 0.8,
    "probability_decay_rate": 0.05,
    "branch_factor": 2.3,
    "temporal_window": 20
  }},
  "mathematical_framework": [
    "P(Event_n+1) = P(Event_n) * influence_factor * exp(-decay_rate * time_delta)",
    "Branch_probability = base_probability * conditional_factor^depth"
  ],
  "cascade_equations": [
    {{
      "equation": "P_cascade = P_initial * Π(conditional_probabilities)",
      "application": "Multi-event sequence prediction",
      "confidence": 0.8
    }}
  ],
  "confidence_score": 0.75
}}

Focus on implementable mathematical frameworks for probability cascade modeling.
'''