"""
Pattern Extractor for Grok 4 Analysis
Prepares Monte Carlo prediction vs actual data for mathematical relationship discovery
"""

import json
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class PredictionError:
    """Structure for prediction error analysis"""
    session_type: str
    predicted_value: float
    actual_value: float
    error_magnitude: float
    error_percentage: float
    session_character: str
    input_parameters: Dict

@dataclass 
class PredictionAnalysis:
    """Complete analysis package for Grok 4"""
    session_comparison: Dict
    error_patterns: List[PredictionError]
    parameter_correlations: Dict
    anomaly_detection: Dict
    mathematical_relationships: Dict

class PatternExtractor:
    """Extracts patterns from Monte Carlo results for Grok 4 analysis"""
    
    def __init__(self):
        self.prediction_errors = []
        self.parameter_space = {}
        
    def analyze_prediction_vs_actual(self, 
                                   prediction_file: str, 
                                   actual_file: str,
                                   session_type: str) -> PredictionError:
        """
        Compare single prediction vs actual result
        
        Args:
            prediction_file: Monte Carlo prediction JSON
            actual_file: Actual session results JSON
            session_type: "midnight" or "london" etc.
            
        Returns:
            PredictionError analysis
        """
        # Load prediction data
        with open(prediction_file, 'r') as f:
            prediction_data = json.load(f)
            
        # Load actual data
        with open(actual_file, 'r') as f:
            actual_data = json.load(f)
        
        # Extract prediction values
        pred_bands = prediction_data["monte_carlo_predictions"]["prediction_bands"]
        predicted_price = pred_bands["final_price_percentiles"]["50th"]
        predicted_range = pred_bands["range_percentiles"]["50th"]
        
        # Extract actual values
        actual_price_data = actual_data["original_session_data"]["price_data"]
        actual_price = actual_price_data["close"]
        actual_range = actual_price_data["range"]
        actual_character = actual_price_data["session_character"]
        
        # Calculate errors
        price_error = abs(predicted_price - actual_price)
        price_error_pct = (price_error / actual_price) * 100
        range_error = abs(predicted_range - actual_range)
        range_error_pct = (range_error / actual_range) * 100 if actual_range > 0 else 0
        
        # Extract input parameters used for prediction
        input_parameters = prediction_data["extracted_parameters"]
        
        return PredictionError(
            session_type=session_type,
            predicted_value=predicted_price,
            actual_value=actual_price,
            error_magnitude=price_error,
            error_percentage=price_error_pct,
            session_character=actual_character,
            input_parameters=input_parameters
        )
    
    def extract_event_sequences(self, prediction_file: str) -> Dict:
        """
        Extract event sequences from Monte Carlo simulation
        
        Returns:
            Event pattern analysis for Grok 4
        """
        with open(prediction_file, 'r') as f:
            data = json.load(f)
        
        event_analysis = data["monte_carlo_predictions"]["event_analysis"]
        
        # Prepare event sequence analysis
        event_sequences = {
            "dominant_patterns": {
                "most_frequent": max(event_analysis["frequencies"], key=event_analysis["frequencies"].get),
                "frequency_distribution": event_analysis["frequencies"],
                "total_events": event_analysis["total_events"],
                "events_per_path": event_analysis["avg_events_per_path"]
            },
            "pattern_insights": {
                "liquidity_vs_continuation": {
                    "liquidity_absorption": event_analysis["frequencies"]["liquidity_absorption"],
                    "level_continuation": event_analysis["frequencies"]["level_continuation"],
                    "ratio": event_analysis["frequencies"]["level_continuation"] / event_analysis["frequencies"]["liquidity_absorption"]
                },
                "rejection_vs_continuation": {
                    "level_rejection": event_analysis["frequencies"]["level_rejection"],
                    "level_continuation": event_analysis["frequencies"]["level_continuation"],
                    "market_bias": "rejection_dominant" if event_analysis["frequencies"]["level_rejection"] > event_analysis["frequencies"]["level_continuation"] else "continuation_dominant"
                }
            }
        }
        
        return event_sequences
    
    def calculate_parameter_correlations(self, prediction_errors: List[PredictionError]) -> Dict:
        """
        Calculate correlations between input parameters and prediction errors
        
        Returns:
            Parameter correlation matrix for Grok 4 analysis
        """
        if len(prediction_errors) < 2:
            return {"insufficient_data": "Need multiple sessions for correlation analysis"}
        
        # Extract parameter values and errors
        t_memory_values = [pe.input_parameters["tracker_state"]["t_memory"] for pe in prediction_errors]
        gamma_values = [pe.input_parameters["session_params"]["gamma_enhanced"] for pe in prediction_errors]
        liquidity_gradient_values = [pe.input_parameters["tracker_state"]["liquidity_gradient"] for pe in prediction_errors]
        error_magnitudes = [pe.error_magnitude for pe in prediction_errors]
        
        # Calculate correlations (simplified - would use scipy.stats.pearsonr in production)
        correlations = {
            "t_memory_vs_error": {
                "correlation": np.corrcoef(t_memory_values, error_magnitudes)[0,1] if len(set(t_memory_values)) > 1 else 0,
                "parameter_range": [min(t_memory_values), max(t_memory_values)],
                "error_range": [min(error_magnitudes), max(error_magnitudes)]
            },
            "gamma_vs_error": {
                "correlation": np.corrcoef(gamma_values, error_magnitudes)[0,1] if len(set(gamma_values)) > 1 else 0,
                "parameter_range": [min(gamma_values), max(gamma_values)],
                "error_range": [min(error_magnitudes), max(error_magnitudes)]
            },
            "liquidity_gradient_vs_error": {
                "correlation": np.corrcoef(liquidity_gradient_values, error_magnitudes)[0,1] if len(set(liquidity_gradient_values)) > 1 else 0,
                "parameter_range": [min(liquidity_gradient_values), max(liquidity_gradient_values)],
                "error_range": [min(error_magnitudes), max(error_magnitudes)]
            }
        }
        
        return correlations
    
    def detect_anomalies(self, prediction_errors: List[PredictionError]) -> Dict:
        """
        Detect anomalous predictions where model failed significantly
        
        Returns:
            Anomaly analysis for Grok 4 investigation
        """
        if not prediction_errors:
            return {"no_data": "No prediction errors to analyze"}
        
        error_magnitudes = [pe.error_magnitude for pe in prediction_errors]
        error_percentages = [pe.error_percentage for pe in prediction_errors]
        
        # Define anomaly thresholds
        magnitude_threshold = np.percentile(error_magnitudes, 75) + 1.5 * (np.percentile(error_magnitudes, 75) - np.percentile(error_magnitudes, 25))
        percentage_threshold = 5.0  # 5% error threshold
        
        anomalies = []
        for pe in prediction_errors:
            if pe.error_magnitude > magnitude_threshold or pe.error_percentage > percentage_threshold:
                anomalies.append({
                    "session_type": pe.session_type,
                    "error_magnitude": pe.error_magnitude,
                    "error_percentage": pe.error_percentage,
                    "session_character": pe.session_character,
                    "key_parameters": {
                        "t_memory": pe.input_parameters["tracker_state"]["t_memory"],
                        "gamma_enhanced": pe.input_parameters["session_params"]["gamma_enhanced"],
                        "liquidity_gradient": pe.input_parameters["tracker_state"]["liquidity_gradient"]
                    },
                    "anomaly_type": "high_magnitude" if pe.error_magnitude > magnitude_threshold else "high_percentage"
                })
        
        return {
            "anomaly_count": len(anomalies),
            "anomaly_threshold_magnitude": magnitude_threshold,
            "anomaly_threshold_percentage": percentage_threshold,
            "detected_anomalies": anomalies,
            "pattern_insights": self._analyze_anomaly_patterns(anomalies)
        }
    
    def _analyze_anomaly_patterns(self, anomalies: List[Dict]) -> Dict:
        """Analyze patterns in anomalous predictions"""
        if not anomalies:
            return {"no_anomalies": True}
        
        # Group by session character
        character_groups = {}
        for anomaly in anomalies:
            char = anomaly["session_character"]
            if char not in character_groups:
                character_groups[char] = []
            character_groups[char].append(anomaly)
        
        return {
            "session_character_patterns": character_groups,
            "most_problematic_character": max(character_groups.keys(), key=lambda x: len(character_groups[x])) if character_groups else None,
            "parameter_ranges_in_anomalies": {
                "t_memory_range": [min(a["key_parameters"]["t_memory"] for a in anomalies), max(a["key_parameters"]["t_memory"] for a in anomalies)],
                "gamma_range": [min(a["key_parameters"]["gamma_enhanced"] for a in anomalies), max(a["key_parameters"]["gamma_enhanced"] for a in anomalies)]
            }
        }
    
    def generate_mathematical_relationships(self, prediction_errors: List[PredictionError]) -> Dict:
        """
        Extract raw mathematical relationships for Grok 4 to analyze
        
        Returns:
            Mathematical relationship data for Grok 4 analysis
        """
        if len(prediction_errors) < 2:
            return {"insufficient_data": "Need multiple sessions for relationship analysis"}
        
        # Extract all numerical relationships
        relationships = {
            "parameter_interactions": {},
            "error_functions": {},
            "proposed_corrections": {}
        }
        
        # Parameter interaction analysis
        for i, pe1 in enumerate(prediction_errors):
            for j, pe2 in enumerate(prediction_errors[i+1:], i+1):
                # Calculate parameter deltas and error deltas
                t_memory_delta = pe2.input_parameters["tracker_state"]["t_memory"] - pe1.input_parameters["tracker_state"]["t_memory"]
                gamma_delta = pe2.input_parameters["session_params"]["gamma_enhanced"] - pe1.input_parameters["session_params"]["gamma_enhanced"]
                error_delta = pe2.error_magnitude - pe1.error_magnitude
                
                relationships["parameter_interactions"][f"comparison_{i}_{j}"] = {
                    "t_memory_delta": t_memory_delta,
                    "gamma_delta": gamma_delta,
                    "error_delta": error_delta,
                    "session_characters": [pe1.session_character, pe2.session_character]
                }
        
        # Error function patterns
        relationships["error_functions"] = {
            "linear_approximations": self._calculate_linear_relationships(prediction_errors),
            "exponential_patterns": self._detect_exponential_patterns(prediction_errors),
            "threshold_effects": self._detect_threshold_effects(prediction_errors)
        }
        
        return relationships
    
    def _calculate_linear_relationships(self, prediction_errors: List[PredictionError]) -> Dict:
        """Calculate linear relationship approximations"""
        # Extract parameter vs error relationships
        t_memory_vals = [pe.input_parameters["tracker_state"]["t_memory"] for pe in prediction_errors]
        gamma_vals = [pe.input_parameters["session_params"]["gamma_enhanced"] for pe in prediction_errors]
        errors = [pe.error_magnitude for pe in prediction_errors]
        
        # Simple linear fit (slope calculation)
        t_memory_slope = np.polyfit(t_memory_vals, errors, 1)[0] if len(set(t_memory_vals)) > 1 else 0
        gamma_slope = np.polyfit(gamma_vals, errors, 1)[0] if len(set(gamma_vals)) > 1 else 0
        
        return {
            "t_memory_error_slope": t_memory_slope,
            "gamma_error_slope": gamma_slope,
            "interpretation": {
                "t_memory_effect": "increases_error" if t_memory_slope > 0 else "decreases_error",
                "gamma_effect": "increases_error" if gamma_slope > 0 else "decreases_error"
            }
        }
    
    def _detect_exponential_patterns(self, prediction_errors: List[PredictionError]) -> Dict:
        """Detect exponential relationships in error patterns"""
        # Look for exponential growth/decay patterns
        errors = [pe.error_magnitude for pe in prediction_errors]
        
        if len(errors) < 3:
            return {"insufficient_data": True}
        
        # Check if errors follow exponential pattern
        log_errors = [np.log(max(e, 0.1)) for e in errors]  # Avoid log(0)
        indices = list(range(len(log_errors)))
        
        exponential_slope = np.polyfit(indices, log_errors, 1)[0] if len(log_errors) > 1 else 0
        
        return {
            "exponential_slope": exponential_slope,
            "pattern_type": "exponential_growth" if exponential_slope > 0.1 else "exponential_decay" if exponential_slope < -0.1 else "linear_or_stable",
            "growth_rate": np.exp(exponential_slope) if abs(exponential_slope) > 0.01 else 1.0
        }
    
    def _detect_threshold_effects(self, prediction_errors: List[PredictionError]) -> Dict:
        """Detect threshold effects in parameters"""
        # Group errors by parameter ranges to detect threshold effects
        t_memory_vals = [pe.input_parameters["tracker_state"]["t_memory"] for pe in prediction_errors]
        errors = [pe.error_magnitude for pe in prediction_errors]
        
        # Find potential threshold at median t_memory
        median_t_memory = np.median(t_memory_vals)
        
        low_t_memory_errors = [errors[i] for i, t in enumerate(t_memory_vals) if t < median_t_memory]
        high_t_memory_errors = [errors[i] for i, t in enumerate(t_memory_vals) if t >= median_t_memory]
        
        return {
            "t_memory_threshold": median_t_memory,
            "low_t_memory_avg_error": np.mean(low_t_memory_errors) if low_t_memory_errors else 0,
            "high_t_memory_avg_error": np.mean(high_t_memory_errors) if high_t_memory_errors else 0,
            "threshold_effect_magnitude": abs(np.mean(high_t_memory_errors) - np.mean(low_t_memory_errors)) if low_t_memory_errors and high_t_memory_errors else 0
        }
    
    def create_analysis_package(self, 
                              midnight_pred_file: str,
                              midnight_actual_file: str,
                              london_pred_file: str, 
                              london_actual_file: str) -> PredictionAnalysis:
        """
        Create complete analysis package for Grok 4
        
        Returns:
            Complete PredictionAnalysis ready for Grok 4 processing
        """
        # Analyze both sessions
        midnight_error = self.analyze_prediction_vs_actual(
            midnight_pred_file, midnight_actual_file, "midnight")
        london_error = self.analyze_prediction_vs_actual(
            london_pred_file, london_actual_file, "london")
        
        prediction_errors = [midnight_error, london_error]
        
        # Extract event sequences
        midnight_events = self.extract_event_sequences(midnight_pred_file)
        london_events = self.extract_event_sequences(london_pred_file)
        
        # Calculate correlations and detect anomalies
        correlations = self.calculate_parameter_correlations(prediction_errors)
        anomalies = self.detect_anomalies(prediction_errors)
        relationships = self.generate_mathematical_relationships(prediction_errors)
        
        return PredictionAnalysis(
            session_comparison={
                "midnight": {
                    "prediction_error": midnight_error,
                    "event_sequences": midnight_events
                },
                "london": {
                    "prediction_error": london_error,
                    "event_sequences": london_events
                }
            },
            error_patterns=prediction_errors,
            parameter_correlations=correlations,
            anomaly_detection=anomalies,
            mathematical_relationships=relationships
        )

# Example usage and testing
if __name__ == "__main__":
    extractor = PatternExtractor()
    
    # Create analysis package from our recent Monte Carlo results
    analysis = extractor.create_analysis_package(
        "midnight_monte_carlo_predictions_2025_07_22.json",
        "midnight_grokEnhanced_2025_07_22.json",
        "london_monte_carlo_predictions_2025_07_22.json",
        "london_grokEnhanced_2025_07_22.json"
    )
    
    # Convert PredictionError objects to dictionaries for JSON serialization
    def convert_prediction_error(pe):
        return {
            "session_type": pe.session_type,
            "predicted_value": pe.predicted_value,
            "actual_value": pe.actual_value,
            "error_magnitude": pe.error_magnitude,
            "error_percentage": pe.error_percentage,
            "session_character": pe.session_character,
            "input_parameters": pe.input_parameters
        }
    
    # Save analysis package for Grok 4  
    analysis_data = {
        "analysis_timestamp": datetime.now().isoformat(),
        "session_comparison": {
            "midnight": {
                "prediction_error": convert_prediction_error(analysis.session_comparison["midnight"]["prediction_error"]),
                "event_sequences": analysis.session_comparison["midnight"]["event_sequences"]
            },
            "london": {
                "prediction_error": convert_prediction_error(analysis.session_comparison["london"]["prediction_error"]),
                "event_sequences": analysis.session_comparison["london"]["event_sequences"]
            }
        },
        "error_patterns": [convert_prediction_error(pe) for pe in analysis.error_patterns],
        "parameter_correlations": analysis.parameter_correlations,
        "anomaly_detection": analysis.anomaly_detection,
        "mathematical_relationships": analysis.mathematical_relationships
    }
    
    with open("grok4_analysis_package_2025_07_22.json", "w") as f:
        json.dump(analysis_data, f, indent=2)
    
    print("✅ Analysis package created: grok4_analysis_package_2025_07_22.json")
    print("📊 Ready for Grok 4 pattern discovery and mathematical relationship analysis")