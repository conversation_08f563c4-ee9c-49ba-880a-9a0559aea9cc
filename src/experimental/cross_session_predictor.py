#!/usr/bin/env python3
"""
Cross-Session Prediction System
Uses Asia session data to predict London session outcomes, with automatic
failure analysis sent to Grok 4 when predictions miss targets.
"""

import json
import os
import sys
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Configure logging for price validation alerts
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def emergency_price_validation(predicted_price: float, reference_price: float, context: str = "") -> float:
    """
    Emergency validation to prevent impossible prices
    
    Args:
        predicted_price: The predicted price that needs validation
        reference_price: Last known good price for fallback
        context: Description of what price is being validated
        
    Returns:
        Validated price (either predicted or fallback)
    """
    if predicted_price < 0:
        logger.error(f"CRITICAL: Negative price {predicted_price} detected in {context}!")
        logger.error(f"Falling back to reference price: {reference_price}")
        return reference_price  # Fallback to last known good price
        
    # Check for extreme price gaps (>10% change)
    if abs(predicted_price - reference_price) > reference_price * 0.10:
        gap_pct = ((predicted_price - reference_price) / reference_price) * 100
        logger.warning(f"Large price gap detected in {context}: {predicted_price} vs {reference_price} ({gap_pct:.1f}%)")
        
        # If gap is extreme (>50%), use fallback
        if abs(gap_pct) > 50:
            logger.error(f"EXTREME price gap {gap_pct:.1f}% - using fallback price")
            return reference_price
    
    return predicted_price

class CrossSessionState:
    """Encapsulate all cross-session transfer logic in a single state object"""
    
    def __init__(self, from_session_data: Dict):
        """Initialize from session data"""
        # Extract price data
        price_data = from_session_data.get("price_data", {})
        if "original_session_data" in from_session_data:
            price_data = from_session_data["original_session_data"]["price_data"]
        
        self.close_price = price_data.get("close", 0)
        self.open_price = price_data.get("open", 0)
        self.high_price = price_data.get("high", 0)
        self.low_price = price_data.get("low", 0)
        self.session_character = price_data.get("session_character", "unknown")
        
        # Calculate derived values
        self.net_movement = self.close_price - self.open_price
        self.session_range = self.high_price - self.low_price
        self.momentum = abs(self.net_movement) / self.session_range if self.session_range > 0 else 0
        self.directional_bias = 1 if self.net_movement > 0 else -1
        
        # Energy and memory state (from tracker context if available)
        self.energy = abs(self.net_movement) / self.close_price if self.close_price > 0 else 0
        self.t_memory = 5.0  # Default value, can be updated from tracker data
        
    def update_from_tracker_data(self, htf_tracker: Dict, fvg_tracker: Dict, liquidity_tracker: Dict):
        """Update state with tracker context"""
        # Update T_memory from FVG tracker if available
        if "t_memory" in fvg_tracker:
            self.t_memory = fvg_tracker["t_memory"]
        
        # Additional tracker-based state updates can be added here
        self.htf_context = htf_tracker
        self.fvg_context = fvg_tracker  
        self.liquidity_context = liquidity_tracker
        
    def transfer_to_next_session(self, time_gap_hours: float = 0.5) -> Dict[str, any]:
        """
        Apply all decay and transfer logic in one place
        
        Args:
            time_gap_hours: Hours between session close and next session open
            
        Returns:
            Dictionary with all transferred parameters
        """
        import math
        
        # Price continuity - start from close price
        initial_price = self.close_price
        
        # Momentum decay with proper sign preservation
        momentum_decay_rate = 0.95 ** time_gap_hours
        carried_momentum = self.momentum * momentum_decay_rate
        carried_directional_bias = self.directional_bias  # Preserve direction
        
        # Energy decay using exponential function
        energy_decay_rate = math.exp(-0.06 * time_gap_hours)  # 6% hourly decay
        carried_energy = self.energy * energy_decay_rate
        
        # Character evolution based on momentum strength
        character_persistence = self._calculate_character_persistence()
        evolved_character = self._evolve_session_character(carried_momentum, character_persistence)
        
        # Calculate small gap adjustment (proper percentage-based)
        gap_adjustment_pct = (self.net_movement / self.close_price) * 0.1 if self.close_price > 0 else 0
        gap_adjustment_pct = max(-0.05, min(0.05, gap_adjustment_pct))  # Bound to ±5%
        
        transferred_state = {
            'initial_price': initial_price,
            'gap_adjustment_percentage': gap_adjustment_pct,
            'carried_momentum': {
                'strength': carried_momentum,
                'directional_bias': carried_directional_bias,
                'velocity': self.net_movement / 180,  # Per minute average
                'decay_factor': momentum_decay_rate
            },
            'carried_energy': carried_energy,
            'evolved_character': evolved_character,
            't_memory': self.t_memory,  # Maintain for continuity
            'transfer_metadata': {
                'time_gap_hours': time_gap_hours,
                'momentum_decay_applied': momentum_decay_rate,
                'energy_decay_applied': energy_decay_rate,
                'character_persistence': character_persistence
            }
        }
        
        return transferred_state
    
    def _calculate_character_persistence(self) -> float:
        """Calculate how likely session character persists to next session"""
        persistence_rates = {
            "trending_bullish": 0.7,
            "trending_bearish": 0.7,
            "ranging_with_liquidity_sweep": 0.5,
            "consolidation": 0.8,
            "expansion_then_consolidation": 0.4,
            "unknown": 0.3
        }
        return persistence_rates.get(self.session_character, 0.5)
    
    def _evolve_session_character(self, momentum_strength: float, persistence: float) -> str:
        """Evolve session character based on momentum and persistence"""
        character_evolution = {
            "trending_bullish": "bullish_continuation" if momentum_strength > 0.6 else "consolidation",
            "trending_bearish": "bearish_continuation" if momentum_strength > 0.6 else "consolidation",
            "ranging_with_liquidity_sweep": "range_bound_continuation",
            "consolidation": "consolidation_continuation",
            "expansion_then_consolidation": "post_expansion_ranging",
            "unknown": "neutral_continuation"
        }
        
        base_evolution = character_evolution.get(self.session_character, "neutral_continuation")
        
        # If persistence is low, default to neutral
        if persistence < 0.4:
            return "neutral_continuation"
            
        return base_evolution

def validate_cross_session_handoff(asia_close: float, london_open: float, 
                                 momentum_carryover: Dict, context: str = "") -> bool:
    """
    Comprehensive validation of cross-session mathematical handoff
    
    Args:
        asia_close: Asia session closing price
        london_open: Predicted London opening price  
        momentum_carryover: Momentum transfer calculations
        context: Description for logging
        
    Returns:
        True if handoff is mathematically valid, False otherwise
    """
    validation_passed = True
    
    # 1. London open must be positive
    if london_open <= 0:
        logger.error(f"VALIDATION FAILED {context}: London open {london_open} must be positive")
        validation_passed = False
    
    # 2. Must be within reasonable range of Asia close (max 5% gap)
    if asia_close > 0:
        max_gap = abs(asia_close * 0.05)  # Max 5% gap
        actual_gap = abs(london_open - asia_close)
        if actual_gap > max_gap:
            gap_pct = (actual_gap / asia_close) * 100
            logger.error(f"VALIDATION FAILED {context}: Gap {actual_gap:.2f} ({gap_pct:.1f}%) exceeds maximum {max_gap:.2f}")
            validation_passed = False
    
    # 3. Momentum direction must be preserved or logically transformed
    directional_bias = momentum_carryover.get("directional_bias", 0)
    momentum_strength = momentum_carryover.get("strength", 0)
    
    if directional_bias == 0:
        logger.warning(f"VALIDATION WARNING {context}: Directional bias is zero - momentum unclear")
    
    # 4. Momentum strength must be positive (decay, not negative)
    if momentum_strength < 0:
        logger.error(f"VALIDATION FAILED {context}: Momentum strength {momentum_strength} cannot be negative")
        validation_passed = False
    
    # 5. Check for mathematical continuity
    price_change = london_open - asia_close
    expected_direction = 1 if price_change > 0 else -1
    
    # If there's significant price change, it should align with momentum or be small
    if abs(price_change) > asia_close * 0.01:  # More than 1% change
        if directional_bias != 0 and expected_direction != directional_bias:
            logger.warning(f"VALIDATION WARNING {context}: Price change direction ({expected_direction}) " +
                         f"differs from momentum bias ({directional_bias})")
    
    if validation_passed:
        logger.info(f"VALIDATION PASSED {context}: Cross-session handoff mathematically valid")
    else:
        logger.error(f"VALIDATION FAILED {context}: Cross-session handoff has mathematical errors")
    
    return validation_passed

def debug_cross_session_transfer(asia_data: Dict, cross_params, london_open: float):
    """
    Debug output to trace cross-session parameter transfer calculations
    
    Args:
        asia_data: Original Asia session data
        cross_params: CrossSessionParameters object
        london_open: Calculated London opening price
    """
    print(f"\n=== CROSS-SESSION TRANSFER DEBUG ===")
    print(f"Asia Close: {cross_params.asia_close_price:.2f}")
    print(f"Gap Percentage: {cross_params.london_open_gap / cross_params.asia_close_price * 100:.4f}%")
    print(f"London Open Gap: {cross_params.london_open_gap:.2f}")
    print(f"London Open: {london_open:.2f}")
    print(f"Price Continuity: {abs(london_open - cross_params.asia_close_price):.2f} points")
    
    momentum = cross_params.momentum_carryover
    print(f"\nMomentum Transfer:")
    print(f"  Directional Bias: {momentum['directional_bias']}")
    print(f"  Strength: {momentum['strength']:.4f}")
    print(f"  Velocity: {momentum['velocity']:.6f}")
    print(f"  Decay Applied: {cross_params.temporal_decay_factors['momentum_decay']:.4f}")
    
    print(f"\nSession Character Evolution:")
    print(f"  Asia Character: {asia_data.get('price_data', {}).get('session_character', 'unknown')}")
    print(f"  London Character: {cross_params.session_character_momentum}")
    
    print(f"=== END DEBUG ===\n")

# Import existing components
try:
    from reverse_engineer import reverse_engineer_failed_prediction
    from grok_interface import GrokInterface, DataType
    from file_manager import discover_session, SessionFiles
    from monte_carlo_adapter import run_monte_carlo_from_session
    from event_chain_discovery import EventChainDiscovery, MarketEvent, EventProbabilityTree
except ImportError:
    # Fallback imports for when running as main module
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from reverse_engineer import reverse_engineer_failed_prediction
    from grok_interface import GrokInterface, DataType
    from file_manager import discover_session, SessionFiles
    from monte_carlo_adapter import run_monte_carlo_from_session
    from event_chain_discovery import EventChainDiscovery, MarketEvent, EventProbabilityTree

class CrossSessionFailureType(Enum):
    """Types of cross-session prediction failures"""
    MOMENTUM_TRANSFER_FAILURE = "momentum_transfer_failure"
    LIQUIDITY_CARRYOVER_FAILURE = "liquidity_carryover_failure"
    SESSION_CHARACTER_MISMATCH = "session_character_mismatch"
    TEMPORAL_DECAY_FAILURE = "temporal_decay_failure"
    HTF_STRUCTURE_DISCONTINUITY = "htf_structure_discontinuity"

@dataclass
class CrossSessionParameters:
    """Parameters extracted from Asia session for London prediction"""
    asia_close_price: float
    london_open_gap: float
    momentum_carryover: Dict[str, float]
    liquidity_levels_transferred: List[float]
    session_character_momentum: str
    temporal_decay_factors: Dict[str, float]
    htf_structure_continuity: Dict[str, float]
    fvg_carryover_states: Dict[str, float]
    asia_session_metadata: Dict[str, any]

@dataclass
class LondonPrediction:
    """London session prediction based on Asia data"""
    predicted_close: float
    predicted_range: Tuple[float, float]
    predicted_high: float
    predicted_low: float
    session_character_prediction: str
    confidence_level: float
    momentum_decay_timeline: Dict[int, float]  # minute -> momentum_strength
    liquidity_interaction_forecast: List[Dict[str, any]]
    prediction_metadata: Dict[str, any]

class CrossSessionParameterExtractor:
    """Extracts cross-session parameters from Asia session for London prediction"""
    
    def __init__(self):
        self.momentum_decay_rate = 0.95  # Per hour
        self.liquidity_transfer_threshold = 0.7
        self.session_gap_hours = 1.0  # Asia close to London open
        
    def extract_london_initial_conditions(self, 
                                        asia_data: Dict,
                                        htf_tracker: Dict,
                                        fvg_tracker: Dict,
                                        liquidity_tracker: Dict) -> CrossSessionParameters:
        """
        Extract parameters from Asia session to predict London outcomes
        
        Args:
            asia_data: Complete Asia session data
            htf_tracker: HTF Context tracker file
            fvg_tracker: FVG State tracker file  
            liquidity_tracker: Liquidity State tracker file
            
        Returns:
            CrossSessionParameters for London prediction
        """
        
        # Extract Asia session fundamentals
        price_data = asia_data.get("price_data", {})
        if "original_session_data" in asia_data:
            price_data = asia_data["original_session_data"]["price_data"]
            
        asia_close = price_data.get("close", 0)
        asia_open = price_data.get("open", 0)
        asia_high = price_data.get("high", 0)
        asia_low = price_data.get("low", 0)
        asia_range = asia_high - asia_low
        
        # Calculate momentum carryover from Asia session movement
        asia_net_movement = asia_close - asia_open
        asia_momentum_strength = abs(asia_net_movement) / asia_range if asia_range > 0 else 0
        
        # Apply temporal decay for gap between sessions
        momentum_decay = self.momentum_decay_rate ** self.session_gap_hours
        carried_momentum = asia_momentum_strength * momentum_decay
        
        momentum_carryover = {
            "directional_bias": 1 if asia_net_movement > 0 else -1,
            "strength": carried_momentum,
            "velocity": asia_net_movement / 180,  # per minute average
            "acceleration": self._calculate_acceleration(price_data)
        }
        
        # Extract liquidity levels that carry over to London
        untaken_liquidity = liquidity_tracker.get("untaken_liquidity", [])
        liquidity_zones = liquidity_tracker.get("liquidity_zones", [])
        
        # Filter liquidity levels that are likely to remain relevant
        asia_price_range = (asia_low * 0.999, asia_high * 1.001)
        transferred_liquidity = [
            level for level in untaken_liquidity 
            if not (asia_price_range[0] <= level <= asia_price_range[1])
        ]
        
        # Predict London opening gap
        gap_factors = self._calculate_gap_factors(asia_data, htf_tracker)
        london_open_gap = asia_close * gap_factors["gap_percentage"]
        
        # Extract session character momentum
        asia_character = price_data.get("session_character", "unknown")
        character_momentum = self._extract_character_momentum(asia_character, momentum_carryover)
        
        # Calculate temporal decay factors
        temporal_factors = {
            "momentum_decay": momentum_decay,
            "liquidity_relevance": self.liquidity_transfer_threshold,
            "character_persistence": self._calculate_character_persistence(asia_character),
            "gap_adjustment": gap_factors["temporal_adjustment"]
        }
        
        # Extract HTF structure continuity
        htf_structures = htf_tracker.get("htf_structures", [])
        htf_continuity = {
            "key_levels": htf_structures,
            "structure_strength": htf_tracker.get("structure_strength", 0.5),
            "cross_session_relevance": self._calculate_htf_relevance(htf_structures, asia_close)
        }
        
        # FVG carryover states
        fvg_states = {
            "active_fvgs": fvg_tracker.get("active_fvgs", []),
            "fvg_redelivery_probability": fvg_tracker.get("redelivery_probability", 0.3),
            "carryover_strength": self._calculate_fvg_carryover(fvg_tracker, asia_close)
        }
        
        return CrossSessionParameters(
            asia_close_price=asia_close,
            london_open_gap=london_open_gap,
            momentum_carryover=momentum_carryover,
            liquidity_levels_transferred=transferred_liquidity,
            session_character_momentum=character_momentum,
            temporal_decay_factors=temporal_factors,
            htf_structure_continuity=htf_continuity,
            fvg_carryover_states=fvg_states,
            asia_session_metadata={
                "session_range": asia_range,
                "volatility": self._calculate_volatility(price_data),
                "session_character": asia_character,
                "analysis_timestamp": datetime.now().isoformat()
            }
        )
        
    def _calculate_acceleration(self, price_data: Dict) -> float:
        """Calculate price acceleration during Asia session"""
        # Simple acceleration approximation
        open_price = price_data.get("open", 0)
        close_price = price_data.get("close", 0)
        high_price = price_data.get("high", 0)
        low_price = price_data.get("low", 0)
        
        # Estimate acceleration based on how price moved through session
        range_coverage = abs(close_price - open_price) / (high_price - low_price) if (high_price - low_price) > 0 else 0
        return range_coverage * 0.1  # Normalize acceleration factor
        
    def _calculate_gap_factors(self, asia_data: Dict, htf_tracker: Dict) -> Dict[str, float]:
        """Calculate factors affecting London opening gap"""
        # Base gap expectation (typically small for continuous markets)
        base_gap = 0.0001  # 0.01%
        
        # Adjust for Asia momentum
        price_data = asia_data.get("price_data", {})
        if "original_session_data" in asia_data:
            price_data = asia_data["original_session_data"]["price_data"]
            
        asia_close = price_data.get("close", 0)
        asia_open = price_data.get("open", 0)
        asia_movement = asia_close - asia_open
        
        # CRITICAL FIX: Convert absolute points to percentage before applying scaling
        if asia_close > 0:
            asia_movement_pct = asia_movement / asia_close  # Convert to percentage
            momentum_gap_factor = asia_movement_pct * 0.1   # Apply scaling to percentage
        else:
            momentum_gap_factor = 0.0
            
        # Add bounds checking to prevent extreme gaps
        total_gap_percentage = base_gap + momentum_gap_factor
        max_gap = 0.05  # Maximum 5% gap
        bounded_gap = max(-max_gap, min(max_gap, total_gap_percentage))
        
        return {
            "gap_percentage": bounded_gap,
            "temporal_adjustment": 0.95  # Slight decay over gap period
        }
        
    def _extract_character_momentum(self, asia_character: str, momentum: Dict) -> str:
        """Extract session character that carries to London"""
        character_mappings = {
            "trending_bullish": "bullish_continuation" if momentum["strength"] > 0.6 else "consolidation",
            "trending_bearish": "bearish_continuation" if momentum["strength"] > 0.6 else "consolidation", 
            "ranging_with_liquidity_sweep": "range_bound_continuation",
            "consolidation": "consolidation_continuation",
            "expansion_then_consolidation": "post_expansion_ranging",
            "unknown": "neutral_continuation"
        }
        
        return character_mappings.get(asia_character, "neutral_continuation")
        
    def _calculate_character_persistence(self, asia_character: str) -> float:
        """Calculate how likely Asia character persists to London"""
        persistence_rates = {
            "trending_bullish": 0.7,
            "trending_bearish": 0.7,
            "ranging_with_liquidity_sweep": 0.5,
            "consolidation": 0.8,
            "expansion_then_consolidation": 0.4,
            "unknown": 0.3
        }
        
        return persistence_rates.get(asia_character, 0.5)
        
    def _calculate_htf_relevance(self, htf_structures: List[float], asia_close: float) -> float:
        """Calculate relevance of HTF structures to London session"""
        if not htf_structures:
            return 0.3
            
        # Find closest HTF structure to Asia close
        closest_distance = min(abs(level - asia_close) for level in htf_structures)
        max_relevance_distance = 100  # points
        
        relevance = max(0.3, 1.0 - (closest_distance / max_relevance_distance))
        return min(relevance, 0.9)
        
    def _calculate_fvg_carryover(self, fvg_tracker: Dict, asia_close: float) -> float:
        """Calculate strength of FVG carryover to London"""
        active_fvgs = fvg_tracker.get("active_fvgs", [])
        if not active_fvgs:
            return 0.2
            
        # Calculate distance to nearest FVG
        fvg_distances = []
        for fvg in active_fvgs:
            if isinstance(fvg, dict):
                fvg_center = fvg.get("center", asia_close)
                fvg_distances.append(abs(fvg_center - asia_close))
        
        if not fvg_distances:
            return 0.2
            
        nearest_fvg_distance = min(fvg_distances)
        max_carryover_distance = 50
        
        carryover_strength = max(0.2, 1.0 - (nearest_fvg_distance / max_carryover_distance))
        return min(carryover_strength, 0.8)
        
    def _calculate_volatility(self, price_data: Dict) -> float:
        """Calculate session volatility"""
        high = price_data.get("high", 0)
        low = price_data.get("low", 0)
        close = price_data.get("close", 0)
        
        if close > 0:
            return (high - low) / close
        return 0.01

class LondonSessionPredictor:
    """Predicts London session outcomes using Asia session data with event-sequence intelligence"""
    
    def __init__(self):
        self.extractor = CrossSessionParameterExtractor()
        self.default_london_duration = 480  # 8 hours in minutes
        self.event_discovery = EventChainDiscovery()  # Event-sequence prediction
        
    def predict_london_from_asia(self,
                               asia_session_data: Dict,
                               tracker_files: Tuple[Dict, Dict, Dict]) -> LondonPrediction:
        """
        Predict London session outcomes based on Asia session data
        
        Args:
            asia_session_data: Complete Asia session data
            tracker_files: (htf_tracker, fvg_tracker, liquidity_tracker)
            
        Returns:
            LondonPrediction with expected outcomes
        """
        
        htf_tracker, fvg_tracker, liquidity_tracker = tracker_files
        
        # ENHANCED: Extract Asia event sequences for London prediction
        print("🔗 Extracting Asia event sequences for London prediction...")
        asia_events = self.event_discovery.extract_events_from_session(asia_session_data)
        print(f"   📊 Extracted {len(asia_events)} Asia market events")
        
        # Cross-session event analysis using Grok 4
        print("🧮 Analyzing cross-session event patterns...")
        cross_event_analysis = self.event_discovery.analyze_cross_session_events(asia_events)
        print(f"   📈 Event patterns: {cross_event_analysis['asia_event_patterns']['total_events']} events analyzed")
        print(f"   🎯 Dominant pattern: {cross_event_analysis['asia_event_patterns']['dominant_event_type']}")
        
        # Extract cross-session parameters (enhanced with event context)
        cross_params = self.extractor.extract_london_initial_conditions(
            asia_session_data, htf_tracker, fvg_tracker, liquidity_tracker
        )
        
        # Calculate London opening price with emergency validation
        london_open_raw = cross_params.asia_close_price + cross_params.london_open_gap
        london_open = emergency_price_validation(
            london_open_raw, 
            cross_params.asia_close_price, 
            "London Opening Price"
        )
        
        # Validate cross-session mathematical handoff
        handoff_valid = validate_cross_session_handoff(
            cross_params.asia_close_price,
            london_open,
            cross_params.momentum_carryover,
            "Asia→London"
        )
        
        # Debug output for tracing calculations
        debug_cross_session_transfer(asia_session_data, cross_params, london_open)
        
        # ENHANCED: Generate London event sequence predictions
        print("🎯 Generating London event sequence predictions...")
        current_london_state = {
            "time": "london_open",
            "price": london_open,
            "phase": "session_start",
            "energy": cross_params.momentum_carryover["strength"],
            "asia_event_context": cross_event_analysis
        }
        
        london_event_tree = self.event_discovery.build_probability_tree(
            current_london_state, 
            asia_events, 
            horizon_minutes=120  # First 2 hours of London session
        )
        print(f"   🌳 Generated event tree with {len(london_event_tree.probability_branches)} probability branches")
        print(f"   📊 Event forecast confidence: {london_event_tree.confidence_score:.2f}")
        
        # Display key event predictions
        for branch in london_event_tree.probability_branches[:3]:
            events_str = ", ".join(branch.get("probable_events", []))
            print(f"   T+{branch.get('time_offset_minutes', 0)}min: {events_str} ({branch.get('probability', 0):.1%})")
        
        # Predict London session movement based on carried momentum AND event sequences
        momentum = cross_params.momentum_carryover
        temporal_decay = cross_params.temporal_decay_factors["momentum_decay"]
        
        # Base movement calculation
        base_movement = momentum["velocity"] * self.default_london_duration * temporal_decay
        directional_bias = momentum["directional_bias"]
        momentum_strength = momentum["strength"]
        
        # Adjust for session character
        character = cross_params.session_character_momentum
        character_multipliers = {
            "bullish_continuation": 1.2,
            "bearish_continuation": 1.2,
            "range_bound_continuation": 0.6,
            "consolidation_continuation": 0.4,
            "post_expansion_ranging": 0.7,
            "neutral_continuation": 0.8
        }
        
        character_multiplier = character_multipliers.get(character, 0.8)
        adjusted_movement = base_movement * character_multiplier
        
        # Calculate London close prediction
        london_close = london_open + (adjusted_movement * directional_bias)
        
        # Predict London range based on volatility carryover
        asia_volatility = cross_params.asia_session_metadata["volatility"]
        london_volatility = asia_volatility * 0.8  # Typical volatility reduction
        
        predicted_range_size = london_open * london_volatility
        london_high = london_open + (predicted_range_size * 0.6)
        london_low = london_open - (predicted_range_size * 0.4)
        
        # Adjust high/low based on directional bias
        if directional_bias > 0:  # Bullish bias
            london_high = max(london_high, london_close * 1.001)
        else:  # Bearish bias
            london_low = min(london_low, london_close * 0.999)
        
        # Calculate confidence based on parameter strength
        base_confidence = 0.5
        momentum_confidence = momentum_strength * 0.3
        character_confidence = cross_params.temporal_decay_factors["character_persistence"] * 0.2
        
        total_confidence = min(0.9, base_confidence + momentum_confidence + character_confidence)
        
        # Generate momentum decay timeline
        decay_timeline = {}
        for minute in range(0, self.default_london_duration, 30):  # Every 30 minutes
            time_decay = temporal_decay ** (minute / 60)  # Hourly decay rate
            decay_timeline[minute] = momentum_strength * time_decay
        
        # Predict liquidity interactions
        liquidity_forecast = self._predict_liquidity_interactions(
            cross_params.liquidity_levels_transferred, 
            london_open, 
            london_close,
            london_high,
            london_low
        )
        
        return LondonPrediction(
            predicted_close=london_close,
            predicted_range=(london_low, london_high),
            predicted_high=london_high,
            predicted_low=london_low,
            session_character_prediction=character,
            confidence_level=total_confidence,
            momentum_decay_timeline=decay_timeline,
            liquidity_interaction_forecast=liquidity_forecast,
            prediction_metadata={
                "asia_close": cross_params.asia_close_price,
                "london_open": london_open,
                "momentum_carryover": momentum,
                "temporal_decay_applied": temporal_decay,
                "character_multiplier": character_multiplier,
                "volatility_estimate": london_volatility,
                "prediction_timestamp": datetime.now().isoformat(),
                # ENHANCED: Event-sequence prediction data
                "asia_event_analysis": {
                    "total_events": len(asia_events),
                    "event_patterns": cross_event_analysis.get("asia_event_patterns", {}),
                    "dominant_event_type": cross_event_analysis.get("asia_event_patterns", {}).get("dominant_event_type", "unknown")
                },
                "london_event_predictions": {
                    "event_tree_confidence": london_event_tree.confidence_score,
                    "probability_branches": london_event_tree.probability_branches,
                    "prediction_horizon_minutes": 120
                }
            }
        )
        
    def _predict_liquidity_interactions(self, 
                                      liquidity_levels: List[float],
                                      london_open: float,
                                      london_close: float,
                                      london_high: float,
                                      london_low: float) -> List[Dict[str, any]]:
        """Predict how London session will interact with carried liquidity"""
        
        interactions = []
        london_range = (london_low, london_high)
        
        for level in liquidity_levels:
            interaction = {
                "liquidity_level": level,
                "interaction_type": "none",
                "interaction_probability": 0.0,
                "expected_timing": None
            }
            
            # Check if level is within predicted London range
            if london_range[0] <= level <= london_range[1]:
                # Determine interaction type based on price movement
                if london_open < level < london_close:
                    interaction["interaction_type"] = "sweep_on_way_up"
                    interaction["interaction_probability"] = 0.7
                    interaction["expected_timing"] = "mid_session"
                elif london_close < level < london_open:
                    interaction["interaction_type"] = "sweep_on_way_down"
                    interaction["interaction_probability"] = 0.7
                    interaction["expected_timing"] = "mid_session"
                elif abs(level - london_high) < 10:
                    interaction["interaction_type"] = "resistance_test"
                    interaction["interaction_probability"] = 0.6
                    interaction["expected_timing"] = "late_session"
                elif abs(level - london_low) < 10:
                    interaction["interaction_type"] = "support_test"
                    interaction["interaction_probability"] = 0.6
                    interaction["expected_timing"] = "early_session"
            
            interactions.append(interaction)
        
        return interactions

class CrossSessionValidator:
    """Validates cross-session predictions against actual London outcomes"""
    
    def __init__(self, error_threshold: float = 30.0):
        self.error_threshold = error_threshold
        self.grok_interface = GrokInterface()
        
    def validate_cross_session_prediction(self,
                                        predicted_london: LondonPrediction,
                                        actual_london_file: str,
                                        asia_session_data: Dict) -> Dict[str, any]:
        """
        Validate cross-session prediction against actual London results
        
        Args:
            predicted_london: London prediction from cross-session system
            actual_london_file: Path to actual London session file
            asia_session_data: Original Asia session data used for prediction
            
        Returns:
            Validation results with failure analysis if needed
        """
        
        # Load actual London session data
        try:
            with open(actual_london_file, 'r') as f:
                actual_london = json.load(f)
        except FileNotFoundError:
            return {
                "validation_status": "failed",
                "error": f"London session file not found: {actual_london_file}",
                "cross_session_failure_analysis": None
            }
        
        # Extract actual London results
        price_data = actual_london.get("price_data", {})
        if "original_session_data" in actual_london:
            price_data = actual_london["original_session_data"]["price_data"]
        
        actual_close = price_data.get("close", 0)
        actual_high = price_data.get("high", 0)
        actual_low = price_data.get("low", 0)
        actual_open = price_data.get("open", 0)
        actual_character = price_data.get("session_character", "unknown")
        
        # Calculate prediction errors
        close_error = abs(predicted_london.predicted_close - actual_close)
        high_error = abs(predicted_london.predicted_high - actual_high)
        low_error = abs(predicted_london.predicted_low - actual_low)
        
        # Determine overall error magnitude
        overall_error = max(close_error, high_error, low_error)
        error_percentage = (overall_error / actual_close * 100) if actual_close > 0 else 0
        
        validation_results = {
            "validation_status": "passed" if overall_error <= self.error_threshold else "failed",
            "prediction_errors": {
                "close_error": close_error,
                "high_error": high_error,
                "low_error": low_error,
                "overall_error": overall_error,
                "error_percentage": error_percentage
            },
            "prediction_accuracy": {
                "predicted_close": predicted_london.predicted_close,
                "actual_close": actual_close,
                "predicted_high": predicted_london.predicted_high,
                "actual_high": actual_high,
                "predicted_low": predicted_london.predicted_low,
                "actual_low": actual_low,
                "predicted_character": predicted_london.session_character_prediction,
                "actual_character": actual_character
            },
            "cross_session_failure_analysis": None
        }
        
        # Trigger failure analysis if error exceeds threshold
        if overall_error > self.error_threshold:
            print(f"🚨 Cross-session prediction error ({overall_error:.2f}) exceeds threshold ({self.error_threshold})")
            print("Triggering Grok 4 failure analysis...")
            
            failure_analysis = self._analyze_cross_session_failure(
                predicted_london,
                actual_london,
                asia_session_data,
                validation_results["prediction_errors"]
            )
            
            validation_results["cross_session_failure_analysis"] = failure_analysis
            validation_results["validation_status"] = "failed_with_analysis"
        
        return validation_results
        
    def _analyze_cross_session_failure(self,
                                     predicted_london: LondonPrediction,
                                     actual_london: Dict,
                                     asia_session_data: Dict,
                                     error_metrics: Dict) -> Dict[str, any]:
        """Analyze cross-session prediction failure using Grok 4"""
        
        # Determine failure type
        failure_type = self._classify_cross_session_failure(error_metrics, predicted_london, actual_london)
        
        # Prepare context for Grok 4 analysis
        failure_context = {
            "failure_type": failure_type.value,
            "asia_session": {
                "close_price": asia_session_data.get("price_data", {}).get("close", 0),
                "session_character": asia_session_data.get("price_data", {}).get("session_character", "unknown"),
                "range": self._calculate_session_range(asia_session_data),
                "momentum": predicted_london.prediction_metadata.get("momentum_carryover", {})
            },
            "london_prediction": {
                "predicted_close": predicted_london.predicted_close,
                "predicted_character": predicted_london.session_character_prediction,
                "confidence": predicted_london.confidence_level,
                "momentum_decay_applied": predicted_london.prediction_metadata.get("temporal_decay_applied", 0)
            },
            "london_actual": {
                "actual_close": actual_london.get("price_data", {}).get("close", 0),
                "actual_character": actual_london.get("price_data", {}).get("session_character", "unknown"),
                "actual_range": self._calculate_session_range(actual_london)
            },
            "error_analysis": error_metrics
        }
        
        # Send to Grok 4 for relationship discovery
        try:
            grok_analysis = self._request_grok_cross_session_analysis(failure_type, failure_context)
            
            return {
                "failure_type": failure_type.value,
                "failure_context": failure_context,
                "grok_relationship_discovery": grok_analysis,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "failure_type": failure_type.value,
                "failure_context": failure_context,
                "grok_analysis_error": str(e),
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    def _classify_cross_session_failure(self,
                                      error_metrics: Dict,
                                      predicted_london: LondonPrediction,
                                      actual_london: Dict) -> CrossSessionFailureType:
        """Classify the type of cross-session prediction failure"""
        
        close_error = error_metrics["close_error"]
        high_error = error_metrics["high_error"]
        low_error = error_metrics["low_error"]
        
        # Analyze prediction vs actual character
        predicted_character = predicted_london.session_character_prediction
        actual_character = actual_london.get("price_data", {}).get("session_character", "unknown")
        
        # Character mismatch indicates session character failure
        if predicted_character != actual_character:
            return CrossSessionFailureType.SESSION_CHARACTER_MISMATCH
        
        # Large range errors suggest liquidity carryover issues
        if high_error > 20 or low_error > 20:
            return CrossSessionFailureType.LIQUIDITY_CARRYOVER_FAILURE
        
        # Close prediction error suggests momentum transfer failure
        if close_error > 25:
            return CrossSessionFailureType.MOMENTUM_TRANSFER_FAILURE
        
        # Default to temporal decay failure
        return CrossSessionFailureType.TEMPORAL_DECAY_FAILURE
    
    def _calculate_session_range(self, session_data: Dict) -> float:
        """Calculate session range from price data"""
        price_data = session_data.get("price_data", {})
        if "original_session_data" in session_data:
            price_data = session_data["original_session_data"]["price_data"]
        
        high = price_data.get("high", 0)
        low = price_data.get("low", 0)
        return high - low
    
    def _request_grok_cross_session_analysis(self,
                                           failure_type: CrossSessionFailureType,
                                           context: Dict) -> Dict[str, any]:
        """Request specialized cross-session analysis from Grok 4"""
        
        # Create specialized prompt for cross-session failure analysis
        prompt_templates = {
            CrossSessionFailureType.MOMENTUM_TRANSFER_FAILURE: 
                f"""Cross-Session Momentum Transfer Analysis:

Asia session closed at {context['asia_session']['close_price']} with character '{context['asia_session']['session_character']}'.
London was predicted to close at {context['london_prediction']['predicted_close']} but actually closed at {context['london_actual']['actual_close']}.

The momentum carryover calculation used: {context['london_prediction']['momentum_decay_applied']:.3f} decay factor.
Prediction error: {context['error_analysis']['close_error']:.2f} points.

What mathematical relationship would better model momentum transfer from Asia to London?
Consider:
1. Session gap effects on momentum persistence
2. Character-specific momentum decay rates
3. Volatility adjustments for cross-session prediction
4. Market microstructure changes between sessions

Provide specific formula improvements for cross-session momentum calculation.""",

            CrossSessionFailureType.LIQUIDITY_CARRYOVER_FAILURE:
                f"""Cross-Session Liquidity Carryover Analysis:

Asia range: {context['asia_session']['range']:.2f} points, London range: {context['london_actual']['actual_range']:.2f} points.
London predicted high: {context['london_prediction']['predicted_close'] + 50:.2f}, actual high: {context['london_actual']['actual_close'] + context['london_actual']['actual_range']/2:.2f}.

The liquidity level transfer failed to account for actual London price action.

What mathematical relationship governs liquidity level relevance across session boundaries?
Consider:
1. Distance-based liquidity relevance decay
2. Session-specific liquidity interaction patterns
3. Time-based liquidity level degradation
4. Cross-session liquidity magnetism effects

Provide improved formulas for cross-session liquidity prediction.""",

            CrossSessionFailureType.SESSION_CHARACTER_MISMATCH:
                f"""Cross-Session Character Transition Analysis:

Asia character: '{context['asia_session']['session_character']}'
Predicted London character: '{context['london_prediction']['predicted_character']}'
Actual London character: '{context['london_actual']['actual_character']}'

The session character transition model failed to predict the actual London session behavior.

What mathematical rules govern session character transitions from Asia to London?
Consider:
1. Character persistence probabilities by type
2. Momentum strength thresholds for character change
3. Market structure influence on character evolution
4. Time-decay effects on character momentum

Provide improved session character transition formulas.""",

            CrossSessionFailureType.TEMPORAL_DECAY_FAILURE:
                f"""Cross-Session Temporal Decay Analysis:

Time gap between Asia close and London prediction: ~1 hour.
Applied temporal decay: {context['london_prediction']['momentum_decay_applied']:.3f}
Prediction confidence: {context['london_prediction']['confidence']:.2f}

The temporal decay model failed to accurately reduce Asia momentum for London prediction.

What mathematical relationship better models temporal decay across session boundaries?
Consider:
1. Non-linear decay functions vs linear decay
2. Character-specific decay rates
3. Volatility-adjusted decay factors
4. Market structure persistence effects

Provide improved temporal decay formulas for cross-session prediction.""",

            CrossSessionFailureType.HTF_STRUCTURE_DISCONTINUITY:
                f"""Cross-Session HTF Structure Analysis:

Asia session interacted with HTF structures, but London prediction failed to account for structure changes.
Prediction error suggests HTF structure relationship breakdown across sessions.

What mathematical relationship governs HTF structure relevance across session boundaries?
Consider:
1. Structure strength persistence across time gaps
2. Price distance effects on HTF relevance
3. Session-specific structure interaction patterns
4. Dynamic structure weight adjustments

Provide improved HTF structure continuity formulas."""
        }
        
        prompt = prompt_templates.get(failure_type, "Generic cross-session failure analysis needed.")
        
        # Request analysis from Grok interface
        try:
            analysis_result = self.grok_interface.send_to_grok4(
                DataType.ALTERNATIVE_FORMULA_DISCOVERY,
                {
                    "failure_context": context,
                    "analysis_prompt": prompt,
                    "cross_session_analysis": True
                }
            )
            
            return analysis_result
            
        except Exception as e:
            return {
                "analysis_error": str(e),
                "prompt_used": prompt,
                "fallback_analysis": "Grok 4 analysis unavailable - manual review required"
            }

class CrossSessionPredictionSystem:
    """Complete cross-session prediction system"""
    
    def __init__(self, error_threshold: float = 30.0):
        self.predictor = LondonSessionPredictor()
        self.validator = CrossSessionValidator(error_threshold)
        self.error_threshold = error_threshold
        
    def run_cross_session_analysis(self,
                                 session: str,
                                 date: str,
                                 target_session: str = "london") -> Dict[str, any]:
        """
        Complete cross-session analysis workflow
        
        Args:
            session: Source session (e.g., "asia")
            date: Date in YYYY_MM_DD format
            target_session: Target session to predict (e.g., "london")
            
        Returns:
            Complete cross-session analysis results
        """
        
        print(f"🌏 CROSS-SESSION PREDICTION: {session.title()} → {target_session.title()}")
        print("=" * 60)
        
        try:
            # Step 1: Discover source session files
            print(f"1️⃣ Discovering {session} session files...")
            source_files = discover_session(session, date)
            print(f"   ✅ All {session} files validated for mathematical integrity")
            
            # Load source session data
            with open(source_files.session_file, 'r') as f:
                source_session_data = json.load(f)
            
            # Load tracker files
            with open(source_files.htf_context_file, 'r') as f:
                htf_tracker = json.load(f)
            with open(source_files.fvg_state_file, 'r') as f:
                fvg_tracker = json.load(f)
            with open(source_files.liquidity_state_file, 'r') as f:
                liquidity_tracker = json.load(f)
            
            # Step 2: Generate cross-session prediction
            print(f"2️⃣ Generating {target_session} prediction from {session} data...")
            london_prediction = self.predictor.predict_london_from_asia(
                source_session_data,
                (htf_tracker, fvg_tracker, liquidity_tracker)
            )
            
            print(f"   📊 Predicted {target_session.title()} Close: {london_prediction.predicted_close:.2f}")
            print(f"   📊 Predicted Range: {london_prediction.predicted_range[0]:.2f} - {london_prediction.predicted_range[1]:.2f}")
            print(f"   📊 Session Character: {london_prediction.session_character_prediction}")
            print(f"   📊 Confidence: {london_prediction.confidence_level:.2f}")
            
            # Enhanced Cross-Session Validation Dashboard
            metadata = london_prediction.prediction_metadata
            asia_close = metadata.get('asia_close', 0)
            london_open = metadata.get('london_open', 0)
            gap = abs(london_open - asia_close)
            gap_pct = (gap / asia_close * 100) if asia_close > 0 else 0
            
            print(f"\n=== CROSS-SESSION VALIDATION DASHBOARD ===")
            print(f"From: Asia Close: {asia_close:.2f}")
            print(f"To: London Open: {london_open:.2f}")
            print(f"Gap: {gap:.2f} points ({gap_pct:.3f}%)")
            
            # Validation Status
            if london_open > 0 and gap_pct < 5.0:
                status = "✓ VALID"
            else:
                status = "✗ INVALID"
            print(f"Status: {status}")
            
            # Momentum Transfer Status
            momentum_decay = metadata.get('temporal_decay_applied', 0)
            character_mult = metadata.get('character_multiplier', 0)
            print(f"Momentum Decay Applied: {momentum_decay:.3f}")
            print(f"Character Multiplier: {character_mult:.2f}")
            print(f"Mathematical Handoff: {'✓ PASSED' if london_open > 0 else '✗ FAILED'}")
            print(f"==========================================\n")
            
            # Step 3: Validate against actual target session (if available)
            print(f"3️⃣ Validating against actual {target_session} session...")
            target_session_file = f"{target_session}_grokEnhanced_{date}.json"
            
            if os.path.exists(target_session_file):
                validation_results = self.validator.validate_cross_session_prediction(
                    london_prediction,
                    target_session_file,
                    source_session_data
                )
                
                print(f"   📊 Validation Status: {validation_results['validation_status'].upper()}")
                
                if validation_results['validation_status'] == 'failed_with_analysis':
                    error = validation_results['prediction_errors']['overall_error']
                    print(f"   🚨 Prediction Error: {error:.2f} points")
                    print(f"   🔍 Grok 4 failure analysis triggered")
                    
                    failure_analysis = validation_results['cross_session_failure_analysis']
                    if failure_analysis:
                        print(f"   📋 Failure Type: {failure_analysis['failure_type']}")
                        
                elif validation_results['validation_status'] == 'passed':
                    error = validation_results['prediction_errors']['overall_error']
                    print(f"   ✅ Prediction Error: {error:.2f} points (within threshold)")
                
            else:
                print(f"   ⚠️ {target_session.title()} session file not found - prediction only mode")
                validation_results = {
                    "validation_status": "no_validation_data",
                    "prediction_only": True
                }
            
            # Step 4: Compile complete results
            complete_results = {
                "cross_session_analysis": {
                    "source_session": session,
                    "target_session": target_session,
                    "date": date,
                    "error_threshold": self.error_threshold,
                    "analysis_timestamp": datetime.now().isoformat()
                },
                "source_session_files": {
                    "session_file": os.path.basename(source_files.session_file),
                    "htf_context_file": os.path.basename(source_files.htf_context_file),
                    "fvg_state_file": os.path.basename(source_files.fvg_state_file),
                    "liquidity_state_file": os.path.basename(source_files.liquidity_state_file)
                },
                "cross_session_prediction": {
                    "predicted_close": london_prediction.predicted_close,
                    "predicted_range": london_prediction.predicted_range,
                    "predicted_high": london_prediction.predicted_high,
                    "predicted_low": london_prediction.predicted_low,
                    "session_character_prediction": london_prediction.session_character_prediction,
                    "confidence_level": london_prediction.confidence_level,
                    "momentum_decay_timeline": london_prediction.momentum_decay_timeline,
                    "liquidity_interaction_forecast": london_prediction.liquidity_interaction_forecast,
                    "prediction_metadata": london_prediction.prediction_metadata
                },
                "validation_results": validation_results
            }
            
            return complete_results
            
        except Exception as e:
            print(f"❌ Cross-session analysis failed: {str(e)}")
            return {
                "cross_session_analysis": {
                    "source_session": session,
                    "target_session": target_session,
                    "date": date,
                    "status": "failed",
                    "error": str(e)
                }
            }

def main():
    """Demonstration of cross-session prediction system"""
    
    print("🌏 CROSS-SESSION PREDICTION SYSTEM")
    print("=" * 50)
    print("Predicting London session outcomes from Asia session data")
    print("with automatic Grok 4 failure analysis\n")
    
    # Initialize system
    cross_session_system = CrossSessionPredictionSystem(error_threshold=25.0)
    
    # Run analysis on Asia → London for July 22nd
    session = "asia"
    date = "2025_07_22"
    target_session = "london"
    
    try:
        results = cross_session_system.run_cross_session_analysis(session, date, target_session)
        
        # Save results
        output_file = f"cross_session_analysis_{session}_to_{target_session}_{date}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Complete cross-session analysis saved to: {output_file}")
        
        # Display summary
        print(f"\n🎯 CROSS-SESSION ANALYSIS SUMMARY")
        print("=" * 35)
        
        if "cross_session_prediction" in results:
            prediction = results["cross_session_prediction"]
            print(f"Source: {session.title()} → Target: {target_session.title()}")
            print(f"Predicted Close: {prediction['predicted_close']:.2f}")
            print(f"Confidence: {prediction['confidence_level']:.2f}")
            
            validation = results["validation_results"]
            if validation["validation_status"] != "no_validation_data":
                print(f"Validation: {validation['validation_status'].upper()}")
                
                if "prediction_errors" in validation:
                    error = validation["prediction_errors"]["overall_error"]
                    print(f"Prediction Error: {error:.2f} points")
                    
                    if validation["validation_status"] == "failed_with_analysis":
                        print("🔍 Grok 4 failure analysis completed")
                        failure_analysis = validation.get("cross_session_failure_analysis")
                        if failure_analysis:
                            print(f"Failure Type: {failure_analysis['failure_type']}")
        
        print(f"\n🚀 Cross-session prediction system operational")
        
    except Exception as e:
        print(f"❌ System error: {str(e)}")

if __name__ == "__main__":
    main()