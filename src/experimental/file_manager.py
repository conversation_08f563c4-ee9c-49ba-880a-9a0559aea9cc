"""
File Manager for Reverse Engineering System
Handles automatic file discovery with strict validation for mathematical integrity
"""

import os
import json
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

@dataclass
class SessionFiles:
    """Complete set of files for a session with strict validation"""
    session_file: str
    htf_context_file: str
    fvg_state_file: str
    liquidity_state_file: str
    
    def validate_all_exist(self) -> bool:
        """Strict validation - all files must exist"""
        files = [self.session_file, self.htf_context_file, 
                self.fvg_state_file, self.liquidity_state_file]
        
        for file_path in files:
            if not os.path.exists(file_path):
                return False
        return True
    
    def get_missing_files(self) -> List[str]:
        """Return list of missing files for error reporting"""
        files = [
            ("session", self.session_file),
            ("htf_context", self.htf_context_file),
            ("fvg_state", self.fvg_state_file), 
            ("liquidity_state", self.liquidity_state_file)
        ]
        
        missing = []
        for file_type, file_path in files:
            if not os.path.exists(file_path):
                missing.append(f"{file_type}: {file_path}")
        
        return missing

class SessionFileManager:
    """
    Manages session file discovery and validation with strict naming conventions
    
    Enforces naming pattern:
    - Session: [session]_grokEnhanced_[YEAR]_[MONTH]_[DAY].json
    - Trackers: [TRACKER]_[Session]_grokEnhanced_[YEAR]_[MONTH]_[DAY].json
    """
    
    def __init__(self, base_directory: str = "."):
        """Initialize file manager with base directory"""
        self.base_directory = base_directory
        self.logger = logging.getLogger(__name__)
        
        # Strict naming patterns
        self.session_pattern = r"^([a-z]+)_grokEnhanced_(\d{4})_(\d{2})_(\d{2})\.json$"
        self.tracker_pattern = r"^(HTF_Context|FVG_State|Liquidity_State)_([A-Z][a-z]+)_grokEnhanced_(\d{4})_(\d{2})_(\d{2})\.json$"
        
        # Required tracker types
        self.required_trackers = ["HTF_Context", "FVG_State", "Liquidity_State"]
    
    def discover_session_files(self, session: str, date: str) -> SessionFiles:
        """
        Automatically discover all 4 files for a session with strict validation
        
        Args:
            session: Session name (e.g., "midnight", "london")
            date: Date in YYYY_MM_DD format (e.g., "2025_07_22")
            
        Returns:
            SessionFiles object with all file paths
            
        Raises:
            FileNotFoundError: If any required files are missing
            ValueError: If date format is invalid
        """
        self.logger.info(f"🔍 Discovering files for {session} session on {date}")
        
        # Validate date format
        if not self._validate_date_format(date):
            raise ValueError(f"Invalid date format: {date}. Expected YYYY_MM_DD")
        
        # Construct file paths using strict naming convention
        session_file = os.path.join(
            self.base_directory, 
            f"{session}_grokEnhanced_{date}.json"
        )
        
        session_title = session.title()  # midnight -> Midnight
        
        htf_context_file = os.path.join(
            self.base_directory,
            f"HTF_Context_{session_title}_grokEnhanced_{date}.json"
        )
        
        fvg_state_file = os.path.join(
            self.base_directory,
            f"FVG_State_{session_title}_grokEnhanced_{date}.json"
        )
        
        liquidity_state_file = os.path.join(
            self.base_directory,
            f"Liquidity_State_{session_title}_grokEnhanced_{date}.json"
        )
        
        # Create SessionFiles object
        session_files = SessionFiles(
            session_file=session_file,
            htf_context_file=htf_context_file,
            fvg_state_file=fvg_state_file,
            liquidity_state_file=liquidity_state_file
        )
        
        # STRICT VALIDATION - All files must exist
        if not session_files.validate_all_exist():
            missing_files = session_files.get_missing_files()
            error_msg = (
                f"❌ STRICT VALIDATION FAILED: Missing required files for {session} on {date}\n"
                f"Mathematical integrity requires ALL tracker files. Missing:\n" +
                "\n".join(f"   • {file}" for file in missing_files) +
                f"\n\nExpected naming convention:\n"
                f"   • Session: {session}_grokEnhanced_{date}.json\n"
                f"   • HTF Context: HTF_Context_{session_title}_grokEnhanced_{date}.json\n"
                f"   • FVG State: FVG_State_{session_title}_grokEnhanced_{date}.json\n"
                f"   • Liquidity State: Liquidity_State_{session_title}_grokEnhanced_{date}.json"
            )
            raise FileNotFoundError(error_msg)
        
        # Additional validation - check file contents are valid JSON
        self._validate_file_contents(session_files)
        
        self.logger.info(f"✅ All files discovered and validated for {session} on {date}")
        return session_files
    
    def batch_discover_sessions(self, sessions: List[str], date: str) -> Dict[str, SessionFiles]:
        """
        Discover files for multiple sessions with strict validation
        
        Args:
            sessions: List of session names
            date: Date in YYYY_MM_DD format
            
        Returns:
            Dictionary mapping session name to SessionFiles
            
        Raises:
            FileNotFoundError: If any session is missing required files
        """
        self.logger.info(f"📁 Batch discovering files for {len(sessions)} sessions on {date}")
        
        discovered_files = {}
        failed_sessions = []
        
        for session in sessions:
            try:
                session_files = self.discover_session_files(session, date)
                discovered_files[session] = session_files
                self.logger.info(f"   ✅ {session}: All files found")
            except FileNotFoundError as e:
                failed_sessions.append((session, str(e)))
                self.logger.error(f"   ❌ {session}: Files missing")
        
        # STRICT VALIDATION - Fail if ANY session is incomplete
        if failed_sessions:
            error_msg = (
                f"❌ BATCH VALIDATION FAILED: {len(failed_sessions)}/{len(sessions)} sessions missing files\n"
                f"Mathematical integrity requires complete data for all sessions.\n\n"
                f"Failed sessions:\n"
            )
            for session, error in failed_sessions:
                error_msg += f"\n{session}:\n{error}\n"
            
            raise FileNotFoundError(error_msg)
        
        self.logger.info(f"✅ Batch discovery successful: {len(discovered_files)} sessions validated")
        return discovered_files
    
    def validate_naming_convention(self, file_path: str) -> Tuple[bool, str]:
        """
        Validate file follows strict naming convention
        
        Args:
            file_path: Path to file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        filename = os.path.basename(file_path)
        
        # Check session file pattern
        session_match = re.match(self.session_pattern, filename)
        if session_match:
            return True, "Valid session file"
        
        # Check tracker file pattern
        tracker_match = re.match(self.tracker_pattern, filename)
        if tracker_match:
            tracker_type, session, year, month, day = tracker_match.groups()
            if tracker_type in self.required_trackers:
                return True, f"Valid {tracker_type} tracker file"
            else:
                return False, f"Unknown tracker type: {tracker_type}"
        
        # Invalid pattern
        return False, (
            f"Invalid naming convention: {filename}\n"
            f"Expected patterns:\n"
            f"  Session: [session]_grokEnhanced_YYYY_MM_DD.json\n"
            f"  Tracker: [TRACKER]_[Session]_grokEnhanced_YYYY_MM_DD.json"
        )
    
    def scan_directory_for_issues(self) -> Dict[str, List[str]]:
        """
        Scan directory for naming convention violations
        
        Returns:
            Dictionary with categorized issues
        """
        self.logger.info(f"🔍 Scanning {self.base_directory} for naming convention issues")
        
        issues = {
            "invalid_names": [],
            "orphaned_sessions": [],
            "missing_trackers": [],
            "valid_files": []
        }
        
        # Get all JSON files
        json_files = [f for f in os.listdir(self.base_directory) if f.endswith('.json')]
        
        for filename in json_files:
            is_valid, message = self.validate_naming_convention(filename)
            
            if is_valid:
                issues["valid_files"].append(filename)
            else:
                issues["invalid_names"].append(f"{filename}: {message}")
        
        # Check for orphaned sessions (session file without all trackers)
        session_files = [f for f in issues["valid_files"] if re.match(self.session_pattern, f)]
        
        for session_file in session_files:
            match = re.match(self.session_pattern, session_file)
            if match:
                session, year, month, day = match.groups()
                date = f"{year}_{month}_{day}"
                
                try:
                    # Try to discover all files - will fail if incomplete
                    self.discover_session_files(session, date)
                except FileNotFoundError:
                    issues["orphaned_sessions"].append(session_file)
        
        # Report findings
        self.logger.info(f"📊 Directory scan results:")
        self.logger.info(f"   Valid files: {len(issues['valid_files'])}")
        self.logger.info(f"   Invalid names: {len(issues['invalid_names'])}")
        self.logger.info(f"   Orphaned sessions: {len(issues['orphaned_sessions'])}")
        
        return issues
    
    def _validate_date_format(self, date: str) -> bool:
        """Validate date is in YYYY_MM_DD format"""
        pattern = r"^\d{4}_\d{2}_\d{2}$"
        return bool(re.match(pattern, date))
    
    def _validate_file_contents(self, session_files: SessionFiles) -> None:
        """
        Validate all files contain valid JSON
        
        Raises:
            ValueError: If any file contains invalid JSON
        """
        files_to_check = [
            ("session", session_files.session_file),
            ("htf_context", session_files.htf_context_file),
            ("fvg_state", session_files.fvg_state_file),
            ("liquidity_state", session_files.liquidity_state_file)
        ]
        
        for file_type, file_path in files_to_check:
            try:
                with open(file_path, 'r') as f:
                    json.load(f)
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in {file_type} file {file_path}: {str(e)}")
            except Exception as e:
                raise ValueError(f"Error reading {file_type} file {file_path}: {str(e)}")

# Utility functions for easy integration
def discover_session(session: str, date: str, base_dir: str = ".") -> SessionFiles:
    """
    Quick function to discover session files with strict validation
    
    Args:
        session: Session name (e.g., "midnight")
        date: Date in YYYY_MM_DD format (e.g., "2025_07_22")
        base_dir: Base directory to search in
        
    Returns:
        SessionFiles object with all paths validated
        
    Raises:
        FileNotFoundError: If any required files are missing
    """
    manager = SessionFileManager(base_dir)
    return manager.discover_session_files(session, date)

def batch_discover(sessions: List[str], date: str, base_dir: str = ".") -> Dict[str, SessionFiles]:
    """
    Quick function to discover multiple sessions with strict validation
    
    Args:
        sessions: List of session names
        date: Date in YYYY_MM_DD format
        base_dir: Base directory to search in
        
    Returns:
        Dictionary mapping session to SessionFiles
        
    Raises:
        FileNotFoundError: If any session is missing files
    """
    manager = SessionFileManager(base_dir)
    return manager.batch_discover_sessions(sessions, date)

def validate_directory(base_dir: str = ".") -> Dict[str, List[str]]:
    """
    Quick function to validate directory naming conventions
    
    Args:
        base_dir: Directory to scan
        
    Returns:
        Dictionary with validation results
    """
    manager = SessionFileManager(base_dir)
    return manager.scan_directory_for_issues()

# Example usage and testing
if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    print("🔧 SESSION FILE MANAGER - STRICT VALIDATION SYSTEM")
    print("=" * 60)
    
    # Test file discovery
    try:
        # Example: Discover midnight session files
        session_files = discover_session("midnight", "2025_07_22")
        print("✅ File discovery successful:")
        print(f"   Session: {session_files.session_file}")
        print(f"   HTF Context: {session_files.htf_context_file}")
        print(f"   FVG State: {session_files.fvg_state_file}")
        print(f"   Liquidity State: {session_files.liquidity_state_file}")
        
    except FileNotFoundError as e:
        print("❌ File discovery failed (expected if files don't exist):")
        print(f"   {str(e)}")
    
    # Test directory validation
    print(f"\n🔍 Directory Validation:")
    issues = validate_directory()
    
    print(f"   Valid files: {len(issues['valid_files'])}")
    if issues['invalid_names']:
        print(f"   ⚠️  Invalid naming: {len(issues['invalid_names'])}")
        for issue in issues['invalid_names'][:3]:  # Show first 3
            print(f"      • {issue}")
    
    print(f"\n✅ File Manager Ready for Production")
    print("   Enforces strict naming conventions")
    print("   Validates mathematical data integrity")
    print("   Automatic discovery with validation")