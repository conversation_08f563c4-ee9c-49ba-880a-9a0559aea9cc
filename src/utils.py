#!/usr/bin/env python3
"""
Utility functions for the Grok Claude Automation project
Provides centralized logging, file handling, and common operations
"""

import json
import logging
import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any

# Configure logging format
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

def setup_logger(name: str, level: int = logging.INFO, log_file: Optional[str] = None) -> logging.Logger:
    """
    Set up a logger with consistent formatting
    
    Args:
        name: Logger name (usually __name__)
        level: Logging level (default INFO)
        log_file: Optional log file path
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
        
    logger.setLevel(level)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """Get or create a logger with standard configuration"""
    return setup_logger(name)

def log_prediction_start(logger: logging.Logger, predictor_name: str, session_type: str = None):
    """Standard logging for prediction start"""
    session_info = f" for {session_type}" if session_type else ""
    logger.info(f"🚀 Starting {predictor_name} prediction{session_info}")

def log_prediction_result(logger: logging.Logger, result: Dict[str, Any], predictor_name: str):
    """Standard logging for prediction results"""
    if 'predicted_close' in result:
        logger.info(f"✅ {predictor_name} prediction complete: {result['predicted_close']:.2f}")
    elif 'prediction' in result:
        logger.info(f"✅ {predictor_name} prediction complete: {result['prediction']}")
    else:
        logger.info(f"✅ {predictor_name} prediction complete")

def log_error(logger: logging.Logger, error: Exception, context: str = ""):
    """Standard error logging"""
    context_info = f" in {context}" if context else ""
    logger.error(f"❌ Error{context_info}: {error}")

def log_warning(logger: logging.Logger, message: str):
    """Standard warning logging"""
    logger.warning(f"⚠️ {message}")

def log_import_warning(logger: logging.Logger, error: Exception):
    """Standard import warning logging"""
    logger.warning(f"⚠️ Import warning: {error}")

def log_file_operation(logger: logging.Logger, operation: str, filepath: str, success: bool = True):
    """Standard file operation logging"""
    status = "✅" if success else "❌"
    logger.info(f"{status} {operation}: {filepath}")

def ensure_directory(path: str) -> bool:
    """
    Ensure directory exists, create if needed
    
    Args:
        path: Directory path to ensure
        
    Returns:
        True if directory exists/was created successfully
    """
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        logging.getLogger(__name__).error(f"Failed to create directory {path}: {e}")
        return False

def safe_json_load(filepath: str, logger: logging.Logger = None) -> Optional[Dict]:
    """
    Safely load JSON file with error handling
    
    Args:
        filepath: Path to JSON file
        logger: Optional logger for error reporting
        
    Returns:
        Loaded JSON data or None if failed
    """
    if not logger:
        logger = get_logger(__name__)
        
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        log_file_operation(logger, "Loaded", filepath)
        return data
    except FileNotFoundError:
        log_error(logger, FileNotFoundError(f"File not found: {filepath}"))
        return None
    except json.JSONDecodeError as e:
        log_error(logger, e, f"JSON decode error in {filepath}")
        return None
    except Exception as e:
        log_error(logger, e, f"Error loading {filepath}")
        return None

def safe_json_save(data: Dict, filepath: str, logger: logging.Logger = None) -> bool:
    """
    Safely save JSON file with error handling
    
    Args:
        data: Data to save
        filepath: Output file path
        logger: Optional logger for reporting
        
    Returns:
        True if saved successfully
    """
    if not logger:
        logger = get_logger(__name__)
        
    try:
        # Ensure directory exists
        directory = os.path.dirname(filepath)
        if directory and not ensure_directory(directory):
            return False
            
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        log_file_operation(logger, "Saved", filepath)
        return True
    except Exception as e:
        log_error(logger, e, f"Error saving {filepath}")
        return False

def load_json_data(filepath: str) -> Dict:
    """
    Load JSON data with backward compatibility
    
    Args:
        filepath: Path to JSON file
        
    Returns:
        Loaded JSON data
        
    Raises:
        FileNotFoundError: If file doesn't exist
        json.JSONDecodeError: If JSON is invalid
    """
    logger = get_logger(__name__)
    
    # Handle both old and new file paths
    if not os.path.exists(filepath):
        # Try to find file in new structure
        filename = os.path.basename(filepath)
        
        # Check common migration locations (updated for new organized structure)
        search_paths = [
            # New organized structure (priority)
            f"data/sessions/level_1/{filename}",
            f"data/trackers/htf/{filename}",
            f"data/trackers/fvg/{filename}",
            f"data/trackers/liquidity/{filename}",
            f"data/trackers/{filename}",
            f"data/predictions/{filename}",
            f"data/validation/{filename}",
            f"data/htf/{filename}",
            f"data/config/{filename}",
            f"data/analysis/{filename}",
            f"data/database/{filename}",
            # Legacy paths (fallback)
            f"data/enhanced/grok_enhanced/{filename}",
            f"data/preprocessing/level_1/{filename}",
            f"data/archive/non_conforming/{filename}"
        ]
        
        for search_path in search_paths:
            if os.path.exists(search_path):
                logger.info(f"🔄 Found migrated file: {filepath} → {search_path}")
                filepath = search_path
                break
        else:
            raise FileNotFoundError(f"File not found: {filepath} (checked migration paths)")
    
    with open(filepath, 'r') as f:
        data = json.load(f)
    
    log_file_operation(logger, "Loaded", filepath)
    return data

def save_json_data(data: Dict, filepath: str) -> None:
    """
    Save JSON data with backward compatibility
    
    Args:
        data: Data to save
        filepath: Output file path
    """
    logger = get_logger(__name__)
    
    # Ensure directory exists
    directory = os.path.dirname(filepath)
    if directory:
        ensure_directory(directory)
    
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2, default=str)
    
    log_file_operation(logger, "Saved", filepath)

# Global logger for utility functions
logger = get_logger(__name__)