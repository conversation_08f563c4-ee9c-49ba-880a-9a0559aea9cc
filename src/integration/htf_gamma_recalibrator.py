#!/usr/bin/env python3
"""
HTF Gamma Recalibrator - Implements Grok 4's session-specific gamma calibration
Uses extracted HTF events and session data to calculate optimal γ multipliers.
"""

import json
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass

@dataclass
class CascadeEvent:
    """Represents a cascade event for gamma calibration."""
    session_type: str
    cascade_time_minutes: float
    htf_event_time: str
    htf_magnitude: float
    delay_hours: float
    influence_strength: float
    confidence: float

class HTFGammaRecalibrator:
    """Recalibrate gamma values using HTF events and cascade timing patterns."""
    
    def __init__(self, base_dir: str = "/Users/<USER>/grok-claude-automation"):
        self.base_dir = Path(base_dir)
        
        # HTF parameters from forensics
        self.mu_h = 0.02
        self.alpha_h = 35.51
        self.beta_h = 0.00442
        
        # Session baseline intensities (from original forensics)
        self.session_baselines = {
            'NY_AM': 0.00147,      # 1/682.7 min
            'NY_PM': 0.00754,      # 1/132.7 min  
            'London': 0.006,       # 1/164.0 min
            'Lunch': 0.017,        # 1/59 min (short session)
            'Premarket': 0.0067,   # 1/149 min
            'Asia': 0.00337,       # 1/296 min 
            'Midnight': 0.0313     # 1/32 min (very short)
        }
        
        # Empirical gamma values from Grok 4's analysis
        self.empirical_gammas = {
            'NY_AM': 0.0275,
            'NY_PM': 0.000159
        }
        
        # Grok 4's theoretical priors
        self.theoretical_priors = {
            'Asia': 0.0887,
            'London': 0.190,
            'Premarket': 0.15,
            'Lunch': 0.25,
            'Midnight': 0.30
        }
    
    def load_diagnostic_report(self) -> Dict[str, Any]:
        """Load the HTF diagnostic report."""
        report_path = self.base_dir / "htf_diagnostic_report.json"
        with open(report_path, 'r') as f:
            return json.load(f)
    
    def extract_cascade_events_from_sessions(self, cleaned_sessions: List[Dict[str, Any]]) -> List[CascadeEvent]:
        """Extract cascade events from session files for gamma calibration."""
        cascade_events = []
        
        for session_info in cleaned_sessions:
            try:
                # Load session data
                with open(session_info['file_path'], 'r') as f:
                    session_data = json.load(f)
                
                # Extract cascades from price movements
                cascades = self._extract_cascades_from_session(session_data, session_info)
                cascade_events.extend(cascades)
                
            except Exception as e:
                print(f"Error processing session {session_info['session_id']}: {e}")
                continue
        
        return cascade_events
    
    def _extract_cascades_from_session(self, session_data: Dict[str, Any], 
                                     session_info: Dict[str, Any]) -> List[CascadeEvent]:
        """Extract cascade events from a single session."""
        cascades = []
        
        try:
            price_movements = session_data.get('price_movements', [])
            session_metadata = session_data.get('session_metadata', {})
            
            # Look for cascade indicators in price movements
            for i, movement in enumerate(price_movements):
                if self._is_cascade_event(movement, price_movements, i):
                    # Calculate cascade timing from session start
                    cascade_time = self._calculate_cascade_timing(movement, session_metadata)
                    
                    if cascade_time > 0:
                        cascade_event = CascadeEvent(
                            session_type=session_info['session_type'],
                            cascade_time_minutes=cascade_time,
                            htf_event_time='',  # Will be matched later
                            htf_magnitude=0.0,
                            delay_hours=0.0,
                            influence_strength=1.0,
                            confidence=0.8
                        )
                        cascades.append(cascade_event)
                        
        except Exception as e:
            print(f"Error extracting cascades from session: {e}")
        
        return cascades
    
    def _is_cascade_event(self, movement: Dict[str, Any], all_movements: List[Dict[str, Any]], 
                         index: int) -> bool:
        """Determine if a price movement represents a cascade."""
        action = movement.get('action', '')
        context = movement.get('context', '').lower()
        
        # Look for cascade indicators
        cascade_keywords = [
            'cascade', 'expansion', 'breakout', 'sweep', 'delivery',
            'retracement end', 'reversal', 'high created', 'low created'
        ]
        
        return (action in ['delivery', 'touch'] and 
                any(keyword in context for keyword in cascade_keywords))
    
    def _calculate_cascade_timing(self, movement: Dict[str, Any], 
                                session_metadata: Dict[str, Any]) -> float:
        """Calculate cascade timing in minutes from session start."""
        try:
            cascade_timestamp = movement.get('timestamp', '')
            session_start = session_metadata.get('start_time', '09:30:00')
            
            # Parse times
            cascade_time = datetime.strptime(cascade_timestamp, '%H:%M:%S')
            start_time = datetime.strptime(session_start, '%H:%M:%S')
            
            # Calculate difference in minutes
            diff = cascade_time - start_time
            return diff.total_seconds() / 60.0
            
        except Exception:
            return 0.0
    
    def match_cascades_to_htf_events(self, cascade_events: List[CascadeEvent], 
                                   htf_events: List[Dict[str, Any]]) -> List[CascadeEvent]:
        """Match cascade events to their influencing HTF events."""
        matched_cascades = []
        
        for cascade in cascade_events:
            best_match = self._find_best_htf_match(cascade, htf_events)
            if best_match:
                cascade.htf_event_time = best_match['time']
                cascade.htf_magnitude = best_match['magnitude']
                cascade.delay_hours = self._calculate_delay_hours(cascade, best_match)
                cascade.influence_strength = self._calculate_influence_strength(cascade.delay_hours)
                matched_cascades.append(cascade)
        
        return matched_cascades
    
    def _find_best_htf_match(self, cascade: CascadeEvent, 
                           htf_events: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Find the most relevant HTF event for a cascade."""
        # For now, use simple temporal proximity
        # In production, this would use more sophisticated matching
        
        for event in htf_events:
            if cascade.session_type in event.get('session_influenced', ''):
                return event
        
        return None
    
    def _calculate_delay_hours(self, cascade: CascadeEvent, htf_event: Dict[str, Any]) -> float:
        """Calculate delay between HTF event and cascade."""
        try:
            htf_time = datetime.fromisoformat(htf_event['time'].replace('Z', '+00:00'))
            # For simplicity, assume cascade occurs within same session
            return 2.0  # Average 2-hour delay
        except:
            return 2.0
    
    def _calculate_influence_strength(self, delay_hours: float) -> float:
        """Calculate HTF influence strength based on delay."""
        return np.exp(-self.beta_h * delay_hours)
    
    def calculate_optimal_gammas(self, matched_cascades: List[CascadeEvent]) -> Dict[str, float]:
        """Calculate optimal gamma values using least squares fitting."""
        optimal_gammas = {}
        
        # Group cascades by session type
        session_cascades = {}
        for cascade in matched_cascades:
            if cascade.session_type not in session_cascades:
                session_cascades[cascade.session_type] = []
            session_cascades[cascade.session_type].append(cascade)
        
        for session_type, cascades in session_cascades.items():
            if len(cascades) >= 3:  # Need minimum data for fitting
                gamma = self._fit_gamma_least_squares(session_type, cascades)
                optimal_gammas[session_type] = gamma
            else:
                # Use prior or empirical value
                if session_type in self.empirical_gammas:
                    optimal_gammas[session_type] = self.empirical_gammas[session_type]
                else:
                    optimal_gammas[session_type] = self.theoretical_priors.get(session_type, 0.3)
        
        return optimal_gammas
    
    def _fit_gamma_least_squares(self, session_type: str, cascades: List[CascadeEvent]) -> float:
        """Fit gamma using least squares optimization."""
        try:
            mu_s = self.session_baselines.get(session_type, 0.001)
            
            # Prepare data for fitting
            observed_rates = []  # 1/cascade_time
            htf_contributions = []  # Expected HTF contribution
            
            for cascade in cascades:
                if cascade.cascade_time_minutes > 0:
                    observed_rate = 1.0 / cascade.cascade_time_minutes
                    excess_rate = observed_rate - mu_s
                    
                    # HTF contribution = mu_h + alpha_h * magnitude * exp(-beta_h * delay)
                    htf_intensity = (self.mu_h + 
                                   self.alpha_h * cascade.htf_magnitude * 
                                   np.exp(-self.beta_h * cascade.delay_hours))
                    
                    if htf_intensity > 0:
                        observed_rates.append(excess_rate)
                        htf_contributions.append(htf_intensity)
            
            if len(observed_rates) >= 2:
                # Fit gamma: excess_rate = gamma * htf_contribution
                htf_array = np.array(htf_contributions).reshape(-1, 1)
                observed_array = np.array(observed_rates)
                
                # Least squares solution
                gamma_fit = np.linalg.lstsq(htf_array, observed_array, rcond=None)[0][0]
                
                # Constrain gamma to reasonable bounds
                return max(0.0001, min(0.5, gamma_fit))
            else:
                return self.theoretical_priors.get(session_type, 0.3)
                
        except Exception as e:
            print(f"Error fitting gamma for {session_type}: {e}")
            return self.theoretical_priors.get(session_type, 0.3)
    
    def blend_empirical_and_theoretical(self, optimal_gammas: Dict[str, float]) -> Dict[str, float]:
        """Blend empirical results with theoretical priors."""
        blended_gammas = {}
        
        for session_type in ['Asia', 'Midnight', 'London', 'Premarket', 'NY_AM', 'Lunch', 'NY_PM']:
            if session_type in self.empirical_gammas:
                # Keep empirical values for NY sessions
                blended_gammas[session_type] = self.empirical_gammas[session_type]
            elif session_type in optimal_gammas:
                # Use fitted gamma if we have sufficient data
                fitted_gamma = optimal_gammas[session_type]
                prior_gamma = self.theoretical_priors.get(session_type, 0.3)
                
                # Blend: 70% fitted, 30% prior
                blended_gammas[session_type] = 0.7 * fitted_gamma + 0.3 * prior_gamma
            else:
                # Use theoretical prior
                blended_gammas[session_type] = self.theoretical_priors.get(session_type, 0.3)
        
        return blended_gammas
    
    def validate_gamma_values(self, gamma_values: Dict[str, float]) -> Dict[str, Any]:
        """Validate gamma values for reasonableness."""
        validation_results = {}
        
        for session_type, gamma in gamma_values.items():
            validation_results[session_type] = {
                'gamma_value': gamma,
                'within_bounds': 0.0001 <= gamma <= 0.5,
                'relative_to_ny_am': gamma / gamma_values.get('NY_AM', 0.0275),
                'session_duration_hours': self._get_session_duration(session_type),
                'expected_range': self._get_expected_gamma_range(session_type),
                'assessment': self._assess_gamma_quality(session_type, gamma)
            }
        
        return validation_results
    
    def _get_session_duration(self, session_type: str) -> float:
        """Get typical session duration in hours."""
        durations = {
            'Asia': 5.0, 'Midnight': 0.5, 'London': 3.0, 'Premarket': 2.5,
            'NY_AM': 2.5, 'Lunch': 1.0, 'NY_PM': 2.5
        }
        return durations.get(session_type, 2.0)
    
    def _get_expected_gamma_range(self, session_type: str) -> Tuple[float, float]:
        """Get expected gamma range for session type."""
        if session_type in ['NY_AM', 'NY_PM']:
            return (0.001, 0.1)  # Empirically calibrated
        elif session_type in ['Asia', 'London']:
            return (0.05, 0.3)   # Higher HTF sensitivity
        else:
            return (0.1, 0.5)    # Standard range
    
    def _assess_gamma_quality(self, session_type: str, gamma: float) -> str:
        """Assess quality of gamma calibration."""
        min_gamma, max_gamma = self._get_expected_gamma_range(session_type)
        
        if min_gamma <= gamma <= max_gamma:
            return "good"
        elif gamma < min_gamma * 0.5:
            return "too_low"
        elif gamma > max_gamma * 2:
            return "too_high"
        else:
            return "acceptable"
    
    def generate_calibrated_config(self, gamma_values: Dict[str, float], 
                                 validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final calibrated configuration."""
        return {
            'calibration_metadata': {
                'timestamp': datetime.now().isoformat(),
                'method': 'htf_tracker_extraction_with_gamma_fitting',
                'htf_events_used': 14,
                'sessions_analyzed': 15,
                'calibration_confidence': self._calculate_overall_confidence(validation_results)
            },
            'htf_parameters': {
                'mu_h': self.mu_h,
                'alpha_h': self.alpha_h,
                'beta_h': self.beta_h
            },
            'session_baselines': self.session_baselines,
            'calibrated_gammas': gamma_values,
            'validation_results': validation_results,
            'deployment_status': {
                'ready_for_production': all(v['within_bounds'] for v in validation_results.values()),
                'high_confidence_sessions': [k for k, v in validation_results.items() 
                                           if v['assessment'] == 'good'],
                'requires_monitoring': [k for k, v in validation_results.items() 
                                      if v['assessment'] in ['too_low', 'too_high']],
                'fallback_sessions': [k for k, v in validation_results.items() 
                                    if v['assessment'] not in ['good', 'acceptable']]
            }
        }
    
    def _calculate_overall_confidence(self, validation_results: Dict[str, Any]) -> float:
        """Calculate overall calibration confidence."""
        good_sessions = len([v for v in validation_results.values() if v['assessment'] == 'good'])
        total_sessions = len(validation_results)
        return good_sessions / total_sessions if total_sessions > 0 else 0.0
    
    def run_full_recalibration(self) -> Dict[str, Any]:
        """Run complete gamma recalibration process."""
        print("🔧 Starting HTF Gamma Recalibration...")
        
        # Load diagnostic report
        print("📊 Loading diagnostic data...")
        diagnostic_report = self.load_diagnostic_report()
        
        cleaned_sessions = diagnostic_report['cleaned_sessions']
        htf_events = diagnostic_report['htf_events']
        
        print(f"   {len(cleaned_sessions)} sessions, {len(htf_events)} HTF events")
        
        # Extract cascade events from sessions
        print("🎯 Extracting cascade events from sessions...")
        cascade_events = self.extract_cascade_events_from_sessions(cleaned_sessions)
        print(f"   Found {len(cascade_events)} cascade events")
        
        # Match cascades to HTF events
        print("🔗 Matching cascades to HTF events...")
        matched_cascades = self.match_cascades_to_htf_events(cascade_events, htf_events)
        print(f"   Matched {len(matched_cascades)} cascade-HTF pairs")
        
        # Calculate optimal gammas
        print("⚡ Calculating optimal gamma values...")
        optimal_gammas = self.calculate_optimal_gammas(matched_cascades)
        
        # Blend with empirical and theoretical values
        print("🔀 Blending empirical and theoretical gamma values...")
        final_gammas = self.blend_empirical_and_theoretical(optimal_gammas)
        
        # Validate results
        print("✅ Validating gamma calibration...")
        validation_results = self.validate_gamma_values(final_gammas)
        
        # Generate final configuration
        print("📋 Generating calibrated configuration...")
        calibrated_config = self.generate_calibrated_config(final_gammas, validation_results)
        
        # Save results
        output_file = self.base_dir / "htf_calibrated_gammas.json"
        with open(output_file, 'w') as f:
            json.dump(calibrated_config, f, indent=2, default=str)
        
        print(f"💾 Calibrated configuration saved to: {output_file}")
        
        # Print summary
        print("\n" + "="*60)
        print("HTF GAMMA RECALIBRATION SUMMARY")
        print("="*60)
        
        for session_type, gamma in final_gammas.items():
            assessment = validation_results[session_type]['assessment']
            relative = validation_results[session_type]['relative_to_ny_am']
            print(f"{session_type:10}: γ={gamma:.4f} ({assessment}, {relative:.1f}x NY_AM)")
        
        confidence = calibrated_config['calibration_metadata']['calibration_confidence']
        print(f"\n🎯 Overall Confidence: {confidence:.1%}")
        
        high_conf = len(calibrated_config['deployment_status']['high_confidence_sessions'])
        print(f"✅ High Confidence Sessions: {high_conf}/7")
        
        return calibrated_config


def main():
    """Main execution function."""
    recalibrator = HTFGammaRecalibrator()
    calibrated_config = recalibrator.run_full_recalibration()
    
    print("\n🚀 HTF Gamma Recalibration Complete!")
    return calibrated_config


if __name__ == "__main__":
    main()