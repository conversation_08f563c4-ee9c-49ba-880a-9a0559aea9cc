#!/usr/bin/env python3
"""
Options Expiry Integration for Event Timing
Integrates options expiry cycles and proximity factors for :00/:30 timing magnetism.
Events cluster around half-hour marks due to options expiry cycles.
"""

import numpy as np
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import calendar

class OptionsExpiryType(Enum):
    """Types of options expiry cycles"""
    STANDARD_MONTHLY = "standard_monthly"     # 3rd Friday monthly
    WEEKLY = "weekly"                        # Every Friday  
    DAILY = "daily"                          # Daily expirations
    QUARTERLY = "quarterly"                  # Quarterly (March, June, Sep, Dec)
    VIX = "vix"                             # VIX options (Wednesday)

@dataclass
class ExpiryTimingFactor:
    """Timing factor influence from options expiry"""
    expiry_type: OptionsExpiryType
    days_until_expiry: int
    time_magnetism_strength: float  # 0-1 strength of :00/:30 pull
    event_acceleration_factor: float  # How much events speed up
    volume_surge_expected: bool
    market_maker_hedging_pressure: float

@dataclass
class HalfHourMagnetism:
    """Magnetism effect pulling events toward :00/:30 marks"""
    current_time: datetime
    minutes_to_next_half_hour: int
    magnetism_strength: float  # 0-1 how strong the pull is
    affected_event_types: List[str]
    timing_adjustment_minutes: float  # How many minutes to adjust

class OptionsExpiryCalendar:
    """Calendar system for options expiry dates and timing effects"""
    
    def __init__(self):
        # Standard options expiry rules
        self.monthly_expiry_week = 3  # 3rd Friday of month
        self.weekly_expiry_day = 4    # Friday (0=Monday)
        self.vix_expiry_day = 2       # Wednesday
        
        # Timing magnetism parameters
        self.base_magnetism_strength = 0.7  # Base strength of :00/:30 pull
        self.expiry_magnetism_boost = 0.3   # Additional strength near expiry
        self.magnetism_decay_distance = 15  # Minutes - how far magnetism extends
        
        # Pre-calculated expiry dates cache
        self.expiry_cache = {}
        
    def get_next_expiry_dates(self, current_date: datetime) -> Dict[OptionsExpiryType, datetime]:
        """Get next expiry dates for all options types"""
        
        cache_key = current_date.strftime('%Y-%m-%d')
        if cache_key in self.expiry_cache:
            return self.expiry_cache[cache_key]
        
        expiry_dates = {}
        
        # Monthly expiry (3rd Friday)
        expiry_dates[OptionsExpiryType.STANDARD_MONTHLY] = self._get_next_monthly_expiry(current_date)
        
        # Weekly expiry (next Friday)
        expiry_dates[OptionsExpiryType.WEEKLY] = self._get_next_weekly_expiry(current_date)
        
        # Daily expiry (next trading day)
        expiry_dates[OptionsExpiryType.DAILY] = self._get_next_daily_expiry(current_date)
        
        # Quarterly expiry
        expiry_dates[OptionsExpiryType.QUARTERLY] = self._get_next_quarterly_expiry(current_date)
        
        # VIX expiry (Wednesday before monthly)
        expiry_dates[OptionsExpiryType.VIX] = self._get_next_vix_expiry(current_date)
        
        self.expiry_cache[cache_key] = expiry_dates
        return expiry_dates
    
    def _get_next_monthly_expiry(self, current_date: datetime) -> datetime:
        """Get next monthly options expiry (3rd Friday)"""
        
        year = current_date.year
        month = current_date.month
        
        # Find 3rd Friday of current month
        first_day = datetime(year, month, 1)
        first_weekday = first_day.weekday()
        
        # Calculate days to first Friday (4 = Friday)
        days_to_first_friday = (4 - first_weekday) % 7
        first_friday = first_day + timedelta(days=days_to_first_friday)
        
        # 3rd Friday is 2 weeks later
        third_friday = first_friday + timedelta(weeks=2)
        
        # If we've passed this month's expiry, get next month's
        if current_date.date() > third_friday.date():
            next_month = month + 1 if month < 12 else 1
            next_year = year if month < 12 else year + 1
            return self._get_next_monthly_expiry(datetime(next_year, next_month, 1))
        
        return third_friday
    
    def _get_next_weekly_expiry(self, current_date: datetime) -> datetime:
        """Get next weekly options expiry (Friday)"""
        
        days_ahead = 4 - current_date.weekday()  # 4 = Friday
        if days_ahead <= 0:  # Today is Friday or later
            days_ahead += 7
            
        return current_date + timedelta(days=days_ahead)
    
    def _get_next_daily_expiry(self, current_date: datetime) -> datetime:
        """Get next daily options expiry (next trading day)"""
        
        next_day = current_date + timedelta(days=1)
        
        # Skip weekends
        while next_day.weekday() >= 5:  # Saturday=5, Sunday=6
            next_day += timedelta(days=1)
            
        return next_day
    
    def _get_next_quarterly_expiry(self, current_date: datetime) -> datetime:
        """Get next quarterly expiry (March, June, September, December)"""
        
        quarterly_months = [3, 6, 9, 12]  # March, June, Sep, Dec
        current_month = current_date.month
        
        # Find next quarterly month
        next_quarterly_month = None
        for month in quarterly_months:
            if month > current_month:
                next_quarterly_month = month
                break
                
        if next_quarterly_month is None:
            # Next quarter is in next year
            next_quarterly_month = 3  # March
            year = current_date.year + 1
        else:
            year = current_date.year
            
        # Get 3rd Friday of quarterly month
        return self._get_next_monthly_expiry(datetime(year, next_quarterly_month, 1))
    
    def _get_next_vix_expiry(self, current_date: datetime) -> datetime:
        """Get next VIX expiry (Wednesday before monthly expiry)"""
        
        monthly_expiry = self._get_next_monthly_expiry(current_date)
        
        # VIX expires on Wednesday before 3rd Friday
        # Find Wednesday before monthly expiry
        days_back = (monthly_expiry.weekday() - 2) % 7  # 2 = Wednesday
        if days_back == 0:
            days_back = 7  # If monthly is Wednesday, go to previous Wednesday
            
        vix_expiry = monthly_expiry - timedelta(days=days_back)
        
        return vix_expiry

    def calculate_expiry_timing_factors(self, current_datetime: datetime) -> List[ExpiryTimingFactor]:
        """Calculate timing factors from all active options expiry cycles"""
        
        expiry_dates = self.get_next_expiry_dates(current_datetime)
        timing_factors = []
        
        for expiry_type, expiry_date in expiry_dates.items():
            days_until = (expiry_date.date() - current_datetime.date()).days
            
            # Calculate magnetism strength based on proximity to expiry
            if days_until <= 0:
                # Expiry day - maximum effect
                magnetism_strength = 1.0
                acceleration_factor = 1.5
                volume_surge = True
                hedging_pressure = 1.0
                
            elif days_until == 1:
                # Day before expiry - high effect
                magnetism_strength = 0.8
                acceleration_factor = 1.3
                volume_surge = True
                hedging_pressure = 0.8
                
            elif days_until <= 3:
                # 2-3 days before - moderate effect
                magnetism_strength = 0.6
                acceleration_factor = 1.2
                volume_surge = True
                hedging_pressure = 0.6
                
            elif days_until <= 7:
                # Within a week - low effect
                magnetism_strength = 0.4
                acceleration_factor = 1.1
                volume_surge = False
                hedging_pressure = 0.3
                
            else:
                # More than a week - minimal effect
                magnetism_strength = 0.2
                acceleration_factor = 1.0
                volume_surge = False
                hedging_pressure = 0.1
            
            # Apply expiry type weights
            type_weights = {
                OptionsExpiryType.STANDARD_MONTHLY: 1.0,    # Strongest effect
                OptionsExpiryType.WEEKLY: 0.7,              # Moderate effect
                OptionsExpiryType.DAILY: 0.3,               # Weak effect
                OptionsExpiryType.QUARTERLY: 1.2,           # Very strong effect
                OptionsExpiryType.VIX: 0.8                  # Strong effect
            }
            
            weight = type_weights.get(expiry_type, 1.0)
            
            timing_factors.append(ExpiryTimingFactor(
                expiry_type=expiry_type,
                days_until_expiry=days_until,
                time_magnetism_strength=magnetism_strength * weight,
                event_acceleration_factor=acceleration_factor,
                volume_surge_expected=volume_surge,
                market_maker_hedging_pressure=hedging_pressure * weight
            ))
        
        return timing_factors

class HalfHourMagnetismCalculator:
    """Calculates magnetism effects that pull events toward :00 and :30 marks"""
    
    def __init__(self):
        # Magnetism parameters based on empirical observations
        self.base_magnetism_strength = 0.6
        self.peak_magnetism_window = 5      # Minutes before/after :00/:30 for peak effect
        self.magnetism_falloff_window = 15  # Minutes where effect diminishes
        
        # Event types most affected by half-hour magnetism
        self.high_magnetism_events = [
            "cascade_formation", "expansion_start", "liquidity_sweep", 
            "reversal_setup", "momentum_acceleration"
        ]
        
        self.medium_magnetism_events = [
            "consolidation_begin", "fvg_interaction"
        ]
    
    def calculate_current_magnetism(self, current_datetime: datetime,
                                  expiry_factors: List[ExpiryTimingFactor] = None) -> HalfHourMagnetism:
        """Calculate current half-hour magnetism effect"""
        
        current_minute = current_datetime.minute
        
        # Calculate distance to nearest half-hour mark
        distance_to_00 = min(current_minute, 60 - current_minute)
        distance_to_30 = abs(current_minute - 30)
        
        nearest_half_hour_distance = min(distance_to_00, distance_to_30)
        minutes_to_next_half_hour = 30 - (current_minute % 30) if current_minute % 30 != 0 else 0
        
        # Calculate base magnetism strength
        if nearest_half_hour_distance <= self.peak_magnetism_window:
            # Within peak window - maximum magnetism
            base_strength = self.base_magnetism_strength
        elif nearest_half_hour_distance <= self.magnetism_falloff_window:
            # Within falloff window - diminishing magnetism
            falloff_factor = 1.0 - ((nearest_half_hour_distance - self.peak_magnetism_window) / 
                                   (self.magnetism_falloff_window - self.peak_magnetism_window))
            base_strength = self.base_magnetism_strength * falloff_factor
        else:
            # Outside magnetism window - minimal effect
            base_strength = 0.1
        
        # Apply options expiry boost
        expiry_boost = 0.0
        if expiry_factors:
            # Use strongest expiry factor
            max_expiry_magnetism = max(factor.time_magnetism_strength for factor in expiry_factors)
            expiry_boost = max_expiry_magnetism * 0.3  # 30% boost from expiry effects
        
        final_magnetism_strength = min(1.0, base_strength + expiry_boost)
        
        # Calculate timing adjustment (how many minutes to pull toward nearest half-hour)
        if nearest_half_hour_distance <= self.magnetism_falloff_window:
            # Pull toward nearest half-hour mark
            target_minute = 0 if distance_to_00 < distance_to_30 else 30
            timing_adjustment = (target_minute - current_minute) * final_magnetism_strength * 0.5
        else:
            timing_adjustment = 0.0
        
        # Determine affected event types
        affected_events = []
        if final_magnetism_strength > 0.7:
            affected_events.extend(self.high_magnetism_events)
            affected_events.extend(self.medium_magnetism_events)
        elif final_magnetism_strength > 0.4:
            affected_events.extend(self.high_magnetism_events)
        elif final_magnetism_strength > 0.2:
            affected_events.extend(["cascade_formation", "expansion_start"])
        
        return HalfHourMagnetism(
            current_time=current_datetime,
            minutes_to_next_half_hour=minutes_to_next_half_hour,
            magnetism_strength=final_magnetism_strength,
            affected_event_types=affected_events,
            timing_adjustment_minutes=timing_adjustment
        )
    
    def apply_magnetism_to_prediction(self, predicted_time: datetime, 
                                    event_type: str,
                                    magnetism: HalfHourMagnetism) -> Tuple[datetime, float]:
        """
        Apply half-hour magnetism to adjust event timing prediction
        
        Returns:
            Tuple of (adjusted_time, confidence_boost)
        """
        
        if event_type not in magnetism.affected_event_types:
            return predicted_time, 0.0
        
        # Calculate magnetism strength for this specific event type
        if event_type in self.high_magnetism_events:
            event_magnetism_multiplier = 1.0
        elif event_type in self.medium_magnetism_events:
            event_magnetism_multiplier = 0.7
        else:
            event_magnetism_multiplier = 0.4
        
        effective_magnetism = magnetism.magnetism_strength * event_magnetism_multiplier
        
        # Calculate adjustment toward nearest half-hour mark
        predicted_minute = predicted_time.minute
        
        # Find nearest half-hour target
        distance_to_00 = min(predicted_minute, 60 - predicted_minute)
        distance_to_30 = abs(predicted_minute - 30)
        
        if distance_to_00 < distance_to_30:
            target_minute = 0
        else:
            target_minute = 30
        
        # Apply magnetism pull
        minute_adjustment = (target_minute - predicted_minute) * effective_magnetism * 0.6
        
        # Bound adjustment to reasonable limits (±10 minutes)
        minute_adjustment = max(-10, min(10, minute_adjustment))
        
        adjusted_time = predicted_time + timedelta(minutes=minute_adjustment)
        
        # Calculate confidence boost (stronger magnetism = higher confidence)
        confidence_boost = effective_magnetism * 0.15  # Up to 15% confidence boost
        
        return adjusted_time, confidence_boost

class OptionsExpiryIntegration:
    """Main integration class combining expiry effects with event timing"""
    
    def __init__(self):
        self.expiry_calendar = OptionsExpiryCalendar()
        self.magnetism_calculator = HalfHourMagnetismCalculator()
        
        # Integration parameters
        self.expiry_acceleration_threshold = 0.6  # Min expiry factor to accelerate events
        self.magnetism_application_threshold = 0.3  # Min magnetism to apply adjustments
        
    def enhance_event_timing_prediction(self, base_prediction_time: datetime,
                                      event_type: str,
                                      base_confidence: float) -> Dict[str, Any]:
        """
        Enhance event timing prediction with options expiry and magnetism effects
        
        Returns:
            Enhanced prediction with adjustments and explanations
        """
        
        # Calculate current expiry factors
        expiry_factors = self.expiry_calendar.calculate_expiry_timing_factors(base_prediction_time)
        
        # Calculate current magnetism
        magnetism = self.magnetism_calculator.calculate_current_magnetism(
            base_prediction_time, expiry_factors
        )
        
        # Apply expiry acceleration effects
        accelerated_time = self._apply_expiry_acceleration(
            base_prediction_time, event_type, expiry_factors
        )
        
        # Apply half-hour magnetism
        final_time, confidence_boost = self.magnetism_calculator.apply_magnetism_to_prediction(
            accelerated_time, event_type, magnetism
        )
        
        # Calculate final confidence
        expiry_confidence_boost = max(factor.time_magnetism_strength * 0.1 for factor in expiry_factors)
        total_confidence_boost = confidence_boost + expiry_confidence_boost
        final_confidence = min(0.95, base_confidence + total_confidence_boost)
        
        # Build enhancement explanation
        enhancements = []
        
        if any(factor.days_until_expiry <= 3 for factor in expiry_factors):
            enhancements.append("options expiry proximity effect")
        
        if magnetism.magnetism_strength > 0.5:
            enhancements.append("half-hour timing magnetism")
            
        if abs((final_time - base_prediction_time).total_seconds() / 60) > 2:
            enhancements.append("significant timing adjustment applied")
        
        enhancement_explanation = "Enhanced by: " + ", ".join(enhancements) if enhancements else "No significant enhancements"
        
        return {
            'original_prediction_time': base_prediction_time,
            'enhanced_prediction_time': final_time,
            'time_adjustment_minutes': (final_time - base_prediction_time).total_seconds() / 60,
            'original_confidence': base_confidence,
            'enhanced_confidence': final_confidence,
            'confidence_boost': total_confidence_boost,
            'expiry_factors': [
                {
                    'type': factor.expiry_type.value,
                    'days_until': factor.days_until_expiry,
                    'magnetism_strength': factor.time_magnetism_strength,
                    'acceleration_factor': factor.event_acceleration_factor
                }
                for factor in expiry_factors if factor.time_magnetism_strength > 0.2
            ],
            'magnetism_effect': {
                'strength': magnetism.magnetism_strength,
                'minutes_to_half_hour': magnetism.minutes_to_next_half_hour,
                'timing_adjustment': magnetism.timing_adjustment_minutes,
                'event_affected': event_type in magnetism.affected_event_types
            },
            'enhancement_explanation': enhancement_explanation
        }
    
    def _apply_expiry_acceleration(self, base_time: datetime, event_type: str,
                                 expiry_factors: List[ExpiryTimingFactor]) -> datetime:
        """Apply options expiry acceleration effects to event timing"""
        
        # Find strongest relevant expiry factor
        relevant_factors = [f for f in expiry_factors 
                          if f.event_acceleration_factor > self.expiry_acceleration_threshold]
        
        if not relevant_factors:
            return base_time
        
        max_acceleration = max(factor.event_acceleration_factor for factor in relevant_factors)
        
        # Different event types respond differently to expiry acceleration
        event_acceleration_sensitivity = {
            "cascade_formation": 1.0,      # Highly sensitive
            "expansion_start": 0.9,        # Very sensitive
            "liquidity_sweep": 1.1,        # Most sensitive (liquidity events)
            "momentum_acceleration": 0.8,   # Moderately sensitive
            "reversal_setup": 0.6,         # Less sensitive
            "consolidation_begin": 0.3,    # Least sensitive
            "fvg_interaction": 0.5         # Moderately sensitive
        }
        
        sensitivity = event_acceleration_sensitivity.get(event_type, 0.5)
        
        # Calculate time acceleration (events happen sooner under expiry pressure)
        # Acceleration of 1.5x means events happen 1/1.5 = 67% of original time
        effective_acceleration = 1.0 + (max_acceleration - 1.0) * sensitivity
        
        if effective_acceleration > 1.0:
            # Events accelerate (happen sooner)
            minutes_from_now = (base_time - datetime.now()).total_seconds() / 60
            accelerated_minutes = minutes_from_now / effective_acceleration
            accelerated_time = datetime.now() + timedelta(minutes=accelerated_minutes)
            
            # Don't allow acceleration to push events into the past
            return max(accelerated_time, datetime.now() + timedelta(minutes=1))
        
        return base_time
    
    def get_current_market_timing_environment(self) -> Dict[str, Any]:
        """Get comprehensive view of current timing environment"""
        
        current_time = datetime.now()
        
        # Get expiry factors
        expiry_factors = self.expiry_calendar.calculate_expiry_timing_factors(current_time)
        
        # Get magnetism
        magnetism = self.magnetism_calculator.calculate_current_magnetism(current_time, expiry_factors)
        
        # Assess overall timing environment
        max_expiry_strength = max(factor.time_magnetism_strength for factor in expiry_factors)
        
        if max_expiry_strength > 0.8 or magnetism.magnetism_strength > 0.8:
            timing_environment = "high_magnetism"
        elif max_expiry_strength > 0.5 or magnetism.magnetism_strength > 0.5:
            timing_environment = "moderate_magnetism"
        else:
            timing_environment = "low_magnetism"
        
        return {
            'current_time': current_time.isoformat(),
            'timing_environment': timing_environment,
            'next_expiry_dates': {
                expiry_type.value: expiry_date.isoformat()
                for expiry_type, expiry_date in self.expiry_calendar.get_next_expiry_dates(current_time).items()
            },
            'active_expiry_factors': [
                {
                    'type': factor.expiry_type.value,
                    'days_until': factor.days_until_expiry,
                    'strength': factor.time_magnetism_strength
                }
                for factor in expiry_factors if factor.time_magnetism_strength > 0.3
            ],
            'half_hour_magnetism': {
                'strength': magnetism.magnetism_strength,
                'minutes_to_next_half_hour': magnetism.minutes_to_next_half_hour,
                'affected_events': magnetism.affected_event_types
            },
            'timing_recommendations': self._generate_timing_recommendations(expiry_factors, magnetism)
        }
    
    def _generate_timing_recommendations(self, expiry_factors: List[ExpiryTimingFactor],
                                       magnetism: HalfHourMagnetism) -> List[str]:
        """Generate timing recommendations based on current environment"""
        
        recommendations = []
        
        # Expiry-based recommendations
        max_expiry_strength = max(factor.time_magnetism_strength for factor in expiry_factors)
        
        if max_expiry_strength > 0.8:
            recommendations.append("High expiry pressure - expect accelerated event timing")
        elif max_expiry_strength > 0.5:
            recommendations.append("Moderate expiry influence - events may occur slightly earlier")
        
        # Magnetism-based recommendations
        if magnetism.magnetism_strength > 0.7:
            recommendations.append(f"Strong half-hour magnetism - events likely near :{magnetism.current_time.minute//30*30:02d} marks")
        elif magnetism.magnetism_strength > 0.4:
            recommendations.append("Moderate timing magnetism - consider half-hour proximity")
        
        # Volume and volatility expectations
        if any(factor.volume_surge_expected for factor in expiry_factors):
            recommendations.append("Volume surge expected - increased event likelihood")
        
        if magnetism.minutes_to_next_half_hour <= 5:
            recommendations.append("Approaching half-hour mark - heightened event probability")
        
        return recommendations

def main():
    """Test options expiry integration system"""
    
    print("🧪 OPTIONS EXPIRY INTEGRATION TEST")
    print("=" * 45)
    
    # Initialize integration system
    integration = OptionsExpiryIntegration()
    
    # Test current market timing environment
    environment = integration.get_current_market_timing_environment()
    
    print(f"📊 Current Market Timing Environment:")
    print(f"   Environment: {environment['timing_environment']}")
    print(f"   Half-hour magnetism: {environment['half_hour_magnetism']['strength']:.2f}")
    print(f"   Minutes to next half-hour: {environment['half_hour_magnetism']['minutes_to_next_half_hour']}")
    
    print(f"\n📅 Active Expiry Factors:")
    for factor in environment['active_expiry_factors']:
        print(f"   {factor['type']}: {factor['days_until']} days (strength: {factor['strength']:.2f})")
    
    print(f"\n💡 Timing Recommendations:")
    for rec in environment['timing_recommendations']:
        print(f"   • {rec}")
    
    # Test event timing enhancement
    print(f"\n🎯 Event Timing Enhancement Test:")
    
    # Simulate a cascade prediction for testing
    base_prediction_time = datetime.now() + timedelta(minutes=25)
    base_confidence = 0.75
    
    enhancement = integration.enhance_event_timing_prediction(
        base_prediction_time, "cascade_formation", base_confidence
    )
    
    print(f"   Original time: {enhancement['original_prediction_time'].strftime('%H:%M')}")
    print(f"   Enhanced time: {enhancement['enhanced_prediction_time'].strftime('%H:%M')}")
    print(f"   Time adjustment: {enhancement['time_adjustment_minutes']:+.1f} minutes")
    print(f"   Confidence boost: {enhancement['confidence_boost']:+.2f}")
    print(f"   Enhancement: {enhancement['enhancement_explanation']}")
    
    # Save test results
    test_results = {
        'test_metadata': {
            'test_type': 'options_expiry_integration',
            'timestamp': datetime.now().isoformat()
        },
        'market_environment': environment,
        'enhancement_test': enhancement
    }
    
    output_file = f"options_expiry_test_{datetime.now().strftime('%H%M%S')}.json"
    with open(output_file, 'w') as f:
        import json
        json.dump(test_results, f, indent=2, default=str)
    
    print(f"\n💾 Test results saved to: {output_file}")
    
    print(f"\n✅ Options expiry integration system operational")
    print(f"   Next monthly expiry: {environment['next_expiry_dates']['standard_monthly'][:10]}")
    print(f"   System ready for event timing enhancement")

if __name__ == "__main__":
    main()