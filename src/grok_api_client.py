#!/usr/bin/env python3
"""
Grok API Client with Native Structured Outputs
Uses direct REST API calls instead of CLI for better reliability and structured outputs
"""

import json
import requests
import time
from typing import Dict, Any, Type, Optional
from pydantic import BaseModel
try:
    from .schemas import UnitAResponse, UnitBResponse, UnitCResponse, UnitDResponse
    from .config import AppConfig, APIKeyError
except ImportError:
    from schemas import UnitAResponse, UnitBResponse, UnitCResponse, UnitDResponse
    from config import AppConfig, APIKeyError


class GrokAPIClient:
    """Direct API client for Grok with structured outputs support."""
    
    def __init__(self, config: AppConfig = None, api_key: str = None):
        """Initialize the Grok API client.
        
        Args:
            config: AppConfig instance for configuration management
            api_key: xAI API key for authentication. If None, reads from GROK_API_KEY env var
        """
        if config is None:
            config = AppConfig()
        
        # Store config instance for timeout access
        self.config = config
        
        try:
            self.api_key = config.get_api_key(api_key)
            self.headers = config.get_headers(api_key)
        except APIKeyError as e:
            raise ValueError(str(e))
        
        self.base_url = "https://api.x.ai/v1"
        self.model = "grok-4-latest"
    
    def execute_structured_prompt(self, prompt: str, response_schema: Type[BaseModel], 
                                timeout: int = None) -> Dict[Any, Any]:
        """Execute a prompt with structured output using the Grok API.
        
        Args:
            prompt: The prompt to send to Grok
            response_schema: Pydantic model defining the expected response structure
            timeout: Request timeout in seconds (uses default if None)
            
        Returns:
            Structured response matching the schema
            
        Raises:
            Exception: If the API request fails or returns invalid data
        """
        try:
            # Prepare the request payload with structured outputs
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {
                        "name": "calculation_response",
                        "schema": response_schema.model_json_schema()
                    }
                },
                "temperature": 0.1  # Low temperature for consistent calculations
                # No max_tokens limit - let Grok use what it needs for reasoning + output
            }
            
            # 🔧 DEBUG: Log request payload size before sending
            import json
            payload_size = len(json.dumps(payload))
            print(f"DEBUG - API Request Stats:")
            print(f"  - Payload size: {payload_size} bytes")
            print(f"  - Messages count: {len(payload.get('messages', []))}")
            if payload_size > 50000:  # 50KB warning
                print(f"  - ⚠️ LARGE PAYLOAD WARNING: {payload_size} bytes")
            
            print(f"DEBUG - Making API request to {self.base_url}/chat/completions")
            start_time = time.time()
            
            # Make the API request with shorter timeout for debugging
            try:
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload,
                    timeout=(10, 120)  # (connect timeout, read timeout) - increased for Grok latency
                )
            except requests.exceptions.Timeout as e:
                print(f"DEBUG - API REQUEST TIMEOUT: {e}")
                print(f"  - Payload was {payload_size} bytes")
                print(f"  - Timeout was (5s connect, 30s read)")
                raise
            
            processing_time = int((time.time() - start_time) * 1000)
            print(f"DEBUG - API response received in {processing_time}ms")
            
            # Check for HTTP errors
            if response.status_code != 200:
                error_msg = f"API request failed with status {response.status_code}: {response.text}"
                print(f"DEBUG - {error_msg}")
                raise Exception(error_msg)
            
            # Parse the response
            response_data = response.json()
            
            if "choices" not in response_data or not response_data["choices"]:
                raise Exception("No choices returned in API response")
            
            # Extract the structured content
            content = response_data["choices"][0]["message"]["content"]
            print(f"DEBUG - Received structured content: {len(content)} chars")
            
            # Check if content is empty
            if not content or len(content.strip()) == 0:
                print("DEBUG - Empty response content")
                print(f"DEBUG - Full response: {json.dumps(response_data, indent=2)}")
                raise Exception("API returned empty response content")
            
            # Parse the JSON response
            try:
                structured_data = json.loads(content)
                print(f"DEBUG - Successfully parsed structured JSON with keys: {list(structured_data.keys())}")
                
                # Validate against schema
                validated_response = response_schema(**structured_data)
                return validated_response.model_dump()
                
            except json.JSONDecodeError as e:
                print(f"DEBUG - JSON parsing failed: {e}")
                print(f"DEBUG - Raw content: {repr(content)}")
                raise Exception(f"Failed to parse JSON response: {e}")
                
            except Exception as e:
                print(f"DEBUG - Schema validation failed: {e}")
                # Return the raw structured data if validation fails
                return structured_data
                
        except requests.exceptions.Timeout:
            raise Exception(f"API request timed out after {timeout} seconds")
        except requests.exceptions.RequestException as e:
            raise Exception(f"API request failed: {str(e)}")
        except Exception as e:
            raise Exception(f"Failed to execute structured prompt: {str(e)}")

    def test_connection(self) -> bool:
        """Test the API connection with a simple request.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user", 
                        "content": "Return only: {\"test\": \"success\", \"status\": \"connected\"}"
                    }
                ],
                "max_tokens": 50,
                "temperature": 0
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                print(f"Connection test successful: {content}")
                return True
            else:
                print(f"Connection test failed: {response.status_code} {response.text}")
                return False
                
        except Exception as e:
            print(f"Connection test error: {e}")
            return False


class GrokClientFactory:
    """Centralized factory for creating GrokAPIClient instances with consistent configuration."""
    
    _shared_config = None
    _shared_client = None
    
    @classmethod
    def get_client(cls, api_key: str = None, force_new: bool = False) -> GrokAPIClient:
        """
        Get a GrokAPIClient instance with singleton pattern for efficiency.
        
        Args:
            api_key: Optional API key override
            force_new: Force creation of new client instead of reusing singleton
            
        Returns:
            GrokAPIClient instance
        """
        if force_new or cls._shared_client is None:
            if cls._shared_config is None:
                cls._shared_config = AppConfig()
            
            cls._shared_client = GrokAPIClient(cls._shared_config, api_key)
            print(f"🏭 GrokClientFactory: Created new API client instance")
        
        return cls._shared_client
    
    @classmethod
    def create_unit_client(cls, unit_name: str, response_schema: Type[BaseModel], 
                          api_key: str = None) -> 'StructuredComputationalUnit':
        """
        Create a StructuredComputationalUnit with consistent client configuration.
        
        Args:
            unit_name: Name of the computational unit
            response_schema: Pydantic schema for responses
            api_key: Optional API key override
            
        Returns:
            StructuredComputationalUnit instance
        """
        client = cls.get_client(api_key)
        return StructuredComputationalUnit(client, unit_name, response_schema)
    
    @classmethod
    def reset_shared_instances(cls):
        """Reset shared instances for testing or configuration changes."""
        cls._shared_config = None
        cls._shared_client = None
        print("🧹 GrokClientFactory: Reset shared instances")


class StructuredComputationalUnit:
    """Base class for computational units using structured outputs."""
    
    def __init__(self, client: GrokAPIClient, unit_name: str, response_schema: Type[BaseModel]):
        """Initialize the computational unit.
        
        Args:
            client: GrokAPIClient instance
            unit_name: Name of this unit for logging
            response_schema: Pydantic schema for the expected response
        """
        self.client = client
        self.unit_name = unit_name
        self.response_schema = response_schema
        self.processing_times = {}
    
    def _load_system_prompt(self) -> str:
        """Load the system prompt for this unit. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement _load_system_prompt")
    
    def process(self, input_data: Dict[Any, Any]) -> Dict[Any, Any]:
        """Process input through this computational unit using structured outputs.
        
        Args:
            input_data: Input data dictionary
            
        Returns:
            Structured calculation results
        """
        try:
            # Get the system prompt
            system_prompt = self._load_system_prompt()
            
            # Create the full prompt with input data
            full_prompt = f"{system_prompt}\n\nINPUT DATA:\n{json.dumps(input_data, indent=2)}"
            
            print(f"Processing {self.unit_name} with structured outputs...")
            start_time = time.time()
            
            # Execute with structured outputs using unit-specific timeout
            unit_timeout = self.client.config.get_unit_timeout(self.unit_name)
            result = self.client.execute_structured_prompt(
                full_prompt, 
                self.response_schema,
                timeout=unit_timeout
            )
            
            processing_time = int((time.time() - start_time) * 1000)
            self.processing_times[self.unit_name.lower().replace(' ', '_')] = processing_time
            
            print(f"{self.unit_name} completed in {processing_time}ms")
            
            return result
            
        except Exception as e:
            print(f"Error in {self.unit_name}: {str(e)}")
            # Return a minimal structure to keep pipeline running
            return {
                "error": str(e),
                "unit_status": "failed",
                "processing_time_ms": 0
            }