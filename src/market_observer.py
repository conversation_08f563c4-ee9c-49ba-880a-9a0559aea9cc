#!/usr/bin/env python3
"""
Market Observer - Ground Truth Recording System

Records objective market facts without mathematical interpretation.
Addresses the epistemic closure problem by capturing reality independent
of mathematical models for true out-of-sample validation.

Key Principle: Record what actually happened, not what we calculate happened.
"""

import json
import math
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data


@dataclass
class GroundTruthFacts:
    """Objective market facts recorded without interpretation"""
    session_id: str
    timestamp: str
    
    # Binary facts - no interpretation
    price_stayed_within_range: bool  # True if max-min < threshold
    expansion_occurred: bool         # True if significant move happened
    consolidation_occurred: bool     # True if price stayed tight
    
    # Raw measurements - pure numbers
    session_range: float            # high - low
    directional_distance: float     # abs(close - open)
    maximum_excursion: float        # Largest move from open
    time_to_first_major_move: Optional[float]  # Minutes to first 50+ point move
    
    # Price levels - objective facts
    session_open: float
    session_high: float
    session_low: float
    session_close: float
    
    # Timing facts
    session_duration_minutes: float
    major_move_occurred_early: bool  # Within first 25% of session
    
    # Movement characteristics - factual
    net_movement_direction: str      # "up", "down", "sideways"
    volatility_character: str        # "low", "medium", "high" based on range


class MarketObserver:
    """
    Records objective market facts without mathematical interpretation.
    
    This class addresses the epistemic closure problem by capturing
    ground truth independent of our mathematical models.
    """
    
    def __init__(self, 
                 consolidation_range_threshold: float = 20.0,
                 expansion_threshold: float = 50.0,
                 major_move_threshold: float = 50.0):
        """
        Initialize the market observer with objective thresholds.
        
        Args:
            consolidation_range_threshold: Points defining "tight range"
            expansion_threshold: Points defining "significant move"  
            major_move_threshold: Points defining "major movement"
        """
        self.consolidation_threshold = consolidation_range_threshold
        self.expansion_threshold = expansion_threshold
        self.major_move_threshold = major_move_threshold
        
        # Storage for ground truth facts
        self.recorded_facts: List[GroundTruthFacts] = []
        
    def record_session_facts(self, session_data: Dict[str, Any]) -> GroundTruthFacts:
        """
        Record objective facts about a trading session.
        
        Args:
            session_data: Raw session data (Level-1 JSON or processed)
            
        Returns:
            GroundTruthFacts object with objective observations
        """
        
        # Extract price data (handle different input formats)
        price_data = self._extract_price_data(session_data)
        
        if not price_data:
            raise ValueError("Cannot extract price data from session")
        
        # Calculate objective measurements
        session_range = price_data['high'] - price_data['low']
        directional_distance = abs(price_data['close'] - price_data['open'])
        maximum_excursion = max(
            abs(price_data['high'] - price_data['open']),
            abs(price_data['low'] - price_data['open'])
        )
        
        # Binary fact determinations - no interpretation, just thresholds
        price_stayed_within_range = session_range < self.consolidation_threshold
        expansion_occurred = directional_distance >= self.expansion_threshold
        consolidation_occurred = session_range < self.consolidation_threshold and directional_distance < (self.consolidation_threshold * 0.5)
        
        # Movement direction - purely factual
        if directional_distance < 10:
            net_direction = "sideways"
        elif price_data['close'] > price_data['open']:
            net_direction = "up"
        else:
            net_direction = "down"
            
        # Volatility character - based on range only
        if session_range < 20:
            volatility_char = "low"
        elif session_range < 80:
            volatility_char = "medium"
        else:
            volatility_char = "high"
            
        # Timing analysis (if tick data available)
        time_to_major_move = self._calculate_time_to_major_move(session_data)
        session_duration = self._extract_session_duration(session_data)
        major_move_early = False
        
        if time_to_major_move and session_duration:
            major_move_early = time_to_major_move < (session_duration * 0.25)
        
        # Create ground truth record
        facts = GroundTruthFacts(
            session_id=self._extract_session_id(session_data),
            timestamp=datetime.now().isoformat(),
            
            # Binary facts
            price_stayed_within_range=price_stayed_within_range,
            expansion_occurred=expansion_occurred,
            consolidation_occurred=consolidation_occurred,
            
            # Raw measurements
            session_range=session_range,
            directional_distance=directional_distance,
            maximum_excursion=maximum_excursion,
            time_to_first_major_move=time_to_major_move,
            
            # Price levels
            session_open=price_data['open'],
            session_high=price_data['high'],
            session_low=price_data['low'],
            session_close=price_data['close'],
            
            # Timing facts
            session_duration_minutes=session_duration or 0,
            major_move_occurred_early=major_move_early,
            
            # Movement characteristics
            net_movement_direction=net_direction,
            volatility_character=volatility_char
        )
        
        # Store the facts
        self.recorded_facts.append(facts)
        
        return facts
    
    def _extract_price_data(self, session_data: Dict[str, Any]) -> Optional[Dict[str, float]]:
        """Extract price data from various session formats"""
        
        # Try standard price_data field
        if 'price_data' in session_data:
            pd = session_data['price_data']
            if all(k in pd for k in ['open', 'high', 'low', 'close']):
                return {
                    'open': float(pd['open']),
                    'high': float(pd['high']),
                    'low': float(pd['low']),
                    'close': float(pd['close'])
                }
        
        # Try enhanced format
        if 'grok_enhanced_calculations' in session_data:
            unit_a = session_data['grok_enhanced_calculations'].get('unit_a_foundation', {})
            if 'price_data' in unit_a:
                pd = unit_a['price_data']
                if all(k in pd for k in ['open', 'high', 'low', 'close']):
                    return {
                        'open': float(pd['open']),
                        'high': float(pd['high']),
                        'low': float(pd['low']),
                        'close': float(pd['close'])
                    }
        
        # Try to extract from any nested structure
        for key, value in session_data.items():
            if isinstance(value, dict) and 'open' in value and 'close' in value:
                if all(k in value for k in ['open', 'high', 'low', 'close']):
                    return {
                        'open': float(value['open']),
                        'high': float(value['high']),
                        'low': float(value['low']),
                        'close': float(value['close'])
                    }
        
        return None
    
    def _calculate_time_to_major_move(self, session_data: Dict[str, Any]) -> Optional[float]:
        """Calculate time to first major move (if tick data available)"""
        
        # For now, return None - would need tick-level data
        # In future, this would analyze minute-by-minute price action
        return None
    
    def _extract_session_duration(self, session_data: Dict[str, Any]) -> Optional[float]:
        """Extract session duration in minutes"""
        
        # Try to find session timing info
        if 'session_metadata' in session_data:
            metadata = session_data['session_metadata']
            if 'duration_minutes' in metadata:
                return float(metadata['duration_minutes'])
        
        # Default assumption for different session types
        session_type = self._extract_session_type(session_data)
        duration_defaults = {
            'ASIA': 60,
            'LONDON': 120,
            'NYAM': 90,
            'LUNCH': 30,
            'NYPM': 90,
            'PREMARKET': 30
        }
        
        return duration_defaults.get(session_type, 60)
    
    def _extract_session_id(self, session_data: Dict[str, Any]) -> str:
        """Extract or generate session identifier"""
        
        # Try session metadata
        if 'session_metadata' in session_data:
            metadata = session_data['session_metadata']
            if 'session_name' in metadata:
                return metadata['session_name']
        
        # Generate from session type and timestamp
        session_type = self._extract_session_type(session_data)
        timestamp = datetime.now().strftime('%Y_%m_%d_%H%M')
        return f"{session_type}_{timestamp}"
    
    def _extract_session_type(self, session_data: Dict[str, Any]) -> str:
        """Extract session type from data"""
        
        if 'session_metadata' in session_data:
            metadata = session_data['session_metadata']
            if 'session_type' in metadata:
                return metadata['session_type'].upper()
        
        # Default
        return "UNKNOWN"
    
    def get_consolidation_test_result(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Opus 4's quick win: Single binary test for consolidation.
        
        Returns:
            Dictionary with ground truth vs system comparison
        """
        
        facts = self.record_session_facts(session_data)
        
        # Extract system's interpretation (if available)
        system_says_consolidation = self._extract_system_consolidation_label(session_data)
        
        return {
            'ground_truth': {
                'price_stayed_within_range': facts.price_stayed_within_range,
                'session_range': facts.session_range,
                'threshold_used': self.consolidation_threshold,
                'consolidation_occurred': facts.consolidation_occurred
            },
            'system_interpretation': {
                'system_says_consolidation': system_says_consolidation,
                'system_character': self._extract_system_character(session_data)
            },
            'validation_result': {
                'ground_truth_matches_system': facts.consolidation_occurred == system_says_consolidation,
                'range_based_accuracy': 'accurate' if facts.consolidation_occurred == system_says_consolidation else 'inaccurate',
                'potential_epistemic_closure': facts.consolidation_occurred != system_says_consolidation
            }
        }
    
    def _extract_system_consolidation_label(self, session_data: Dict[str, Any]) -> bool:
        """Extract whether system labeled this as consolidation"""
        
        # Check various places where system might store its interpretation
        system_character = self._extract_system_character(session_data)
        
        if system_character:
            return 'consolidation' in system_character.lower()
        
        return False
    
    def _extract_system_character(self, session_data: Dict[str, Any]) -> Optional[str]:
        """Extract system's character interpretation"""
        
        # Try price_data field
        if 'price_data' in session_data:
            if 'session_character' in session_data['price_data']:
                return session_data['price_data']['session_character']
        
        # Try enhanced calculations
        if 'grok_enhanced_calculations' in session_data:
            unit_a = session_data['grok_enhanced_calculations'].get('unit_a_foundation', {})
            if 'session_character' in unit_a:
                return unit_a['session_character']
        
        return None
    
    def save_ground_truth_facts(self, output_file: str) -> None:
        """Save recorded ground truth facts to file"""
        
        facts_data = {
            'ground_truth_metadata': {
                'recording_timestamp': datetime.now().isoformat(),
                'observer_version': '1.0.0',
                'thresholds': {
                    'consolidation_range': self.consolidation_threshold,
                    'expansion_threshold': self.expansion_threshold,
                    'major_move_threshold': self.major_move_threshold
                },
                'total_sessions_recorded': len(self.recorded_facts)
            },
            'recorded_facts': [
                {
                    'session_id': fact.session_id,
                    'timestamp': fact.timestamp,
                    'binary_facts': {
                        'price_stayed_within_range': fact.price_stayed_within_range,
                        'expansion_occurred': fact.expansion_occurred,
                        'consolidation_occurred': fact.consolidation_occurred,
                        'major_move_occurred_early': fact.major_move_occurred_early
                    },
                    'raw_measurements': {
                        'session_range': fact.session_range,
                        'directional_distance': fact.directional_distance,
                        'maximum_excursion': fact.maximum_excursion,
                        'time_to_first_major_move': fact.time_to_first_major_move,
                        'session_duration_minutes': fact.session_duration_minutes
                    },
                    'price_levels': {
                        'open': fact.session_open,
                        'high': fact.session_high,
                        'low': fact.session_low,
                        'close': fact.session_close
                    },
                    'characteristics': {
                        'net_movement_direction': fact.net_movement_direction,
                        'volatility_character': fact.volatility_character
                    }
                }
                for fact in self.recorded_facts
            ]
        }
        
        save_json_data(facts_data, output_file)


def create_market_observer(consolidation_threshold: float = 20.0,
                          expansion_threshold: float = 50.0) -> MarketObserver:
    """Create a market observer with specified thresholds"""
    return MarketObserver(
        consolidation_range_threshold=consolidation_threshold,
        expansion_threshold=expansion_threshold
    )


if __name__ == "__main__":
    # Quick test of the market observer
    print("🔍 MARKET OBSERVER - GROUND TRUTH RECORDING TEST")
    print("=" * 55)
    
    observer = create_market_observer()
    
    # Test with a sample session
    try:
        sample_session = load_json_data('ASIA_grokEnhanced_2025_07_25.json')
        
        print("📊 Recording ground truth facts...")
        facts = observer.record_session_facts(sample_session)
        
        print(f"✅ Ground Truth Recorded:")
        print(f"   Session Range: {facts.session_range:.1f} points")
        print(f"   Stayed in Range (<20pt): {facts.price_stayed_within_range}")
        print(f"   Expansion Occurred (>50pt): {facts.expansion_occurred}")
        print(f"   Consolidation Character: {facts.consolidation_occurred}")
        print(f"   Net Direction: {facts.net_movement_direction}")
        
        # Run Opus 4's consolidation test
        print(f"\n🎯 Opus 4 Consolidation Test:")
        test_result = observer.get_consolidation_test_result(sample_session)
        
        ground_truth = test_result['ground_truth']
        validation = test_result['validation_result']
        
        print(f"   Ground Truth: Range = {ground_truth['session_range']:.1f}pt")
        print(f"   Consolidation? {ground_truth['consolidation_occurred']}")
        print(f"   System Match: {validation['ground_truth_matches_system']}")
        print(f"   Accuracy: {validation['range_based_accuracy']}")
        
        if validation['potential_epistemic_closure']:
            print(f"   ⚠️ EPISTEMIC CLOSURE DETECTED - System vs Reality Mismatch!")
        else:
            print(f"   ✅ System interpretation matches ground truth")
        
        # Save the facts
        observer.save_ground_truth_facts('ground_truth_test_results.json')
        print(f"\n📁 Ground truth facts saved to: ground_truth_test_results.json")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("Using sample data for demonstration...")
        
        # Create sample data for testing
        sample_data = {
            'price_data': {
                'open': 23400.0,
                'high': 23415.0,
                'low': 23385.0,
                'close': 23410.0
            },
            'session_metadata': {
                'session_type': 'ASIA',
                'session_name': 'ASIA_2025_07_28'
            }
        }
        
        facts = observer.record_session_facts(sample_data)
        print(f"✅ Sample Ground Truth:")
        print(f"   Range: {facts.session_range:.1f}pt -> Consolidation: {facts.consolidation_occurred}")