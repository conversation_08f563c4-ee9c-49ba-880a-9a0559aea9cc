#!/usr/bin/env python3
"""
Unified Hawkes Cache Manager
Replaces multiple competing cache systems with a single coordinated cache.
Eliminates redundant calculations and cache thrashing.
"""

import hashlib
import time
from typing import Dict, Any, Optional, Tuple


class SimpleTTLCache:
    """Simple Time-To-Live cache implementation without external dependencies."""
    
    def __init__(self, maxsize: int = 1000, ttl: int = 3600):
        self.maxsize = maxsize
        self.ttl = ttl
        self._cache = {}
        self._timestamps = {}
    
    def __contains__(self, key):
        """Check if key exists and is not expired."""
        if key not in self._cache:
            return False
        
        if time.time() - self._timestamps[key] > self.ttl:
            # Expired, remove it
            del self._cache[key]
            del self._timestamps[key]
            return False
        
        return True
    
    def __getitem__(self, key):
        """Get item if it exists and is not expired."""
        if key in self:
            return self._cache[key]
        raise KeyError(key)
    
    def __setitem__(self, key, value):
        """Set item with current timestamp."""
        # Evict expired items and enforce size limit
        self._evict_expired()
        if len(self._cache) >= self.maxsize:
            self._evict_oldest()
        
        self._cache[key] = value
        self._timestamps[key] = time.time()
    
    def __len__(self):
        """Get current cache size."""
        self._evict_expired()
        return len(self._cache)
    
    def get(self, key, default=None):
        """Get item with default if not found."""
        try:
            return self[key]
        except KeyError:
            return default
    
    def clear(self):
        """Clear all cached items."""
        self._cache.clear()
        self._timestamps.clear()
    
    def _evict_expired(self):
        """Remove expired items."""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._timestamps.items()
            if current_time - timestamp > self.ttl
        ]
        for key in expired_keys:
            del self._cache[key]
            del self._timestamps[key]
    
    def _evict_oldest(self):
        """Remove oldest item to make space."""
        if self._timestamps:
            oldest_key = min(self._timestamps.keys(), key=lambda k: self._timestamps[k])
            del self._cache[oldest_key]
            del self._timestamps[oldest_key]
    
    def expire(self):
        """Force expiration of old items."""
        self._evict_expired()


class UnifiedHawkesCache:
    """
    Unified cache system for all Hawkes process calculations.
    Replaces both HawkesCacheManager and DynamicSyntheticVolumeCalculator._volume_cache.
    """
    
    def __init__(self, maxsize: int = 1000, ttl: int = 3600):
        """
        Initialize unified cache with LRU eviction and TTL.
        
        Args:
            maxsize: Maximum number of cached items (default: 1000)
            ttl: Time-to-live in seconds (default: 1 hour)
        """
        self.cache = SimpleTTLCache(maxsize=maxsize, ttl=ttl)
        self.stats = {
            'hits': 0,
            'misses': 0,
            'volume_calculations': 0,
            'cascade_calculations': 0,
            'evictions': 0
        }
        self._start_time = time.time()
    
    def _generate_fast_key(self, prefix: str, session_data: Dict[Any, Any], 
                          params: Optional[Dict[str, Any]] = None, 
                          timestamp: Optional[str] = None) -> str:
        """
        Generate cache key without expensive JSON serialization.
        
        Args:
            prefix: Cache key prefix (e.g., 'volume', 'cascade')
            session_data: Session data dict
            params: Optional parameters dict
            timestamp: Optional timestamp string
            
        Returns:
            Fast-generated cache key
        """
        # Use essential identifying information only
        key_parts = [
            prefix,
            session_data.get('session_metadata', {}).get('session_id', ''),
            str(session_data.get('price_data', {}).get('range', 0)),
            str(len(session_data.get('price_movements', []))),
        ]
        
        # Add parameters if provided
        if params:
            key_parts.extend([
                str(params.get('mu', '')),
                str(params.get('alpha', '')),
                str(params.get('beta', ''))
            ])
        
        # Add timestamp if provided
        if timestamp:
            key_parts.append(timestamp)
        
        # Fast hash generation
        key_string = '|'.join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()[:16]
    
    def get_volume(self, session_data: Dict[Any, Any], timestamp: str) -> Optional[float]:
        """
        Get cached volume calculation for specific timestamp.
        
        Args:
            session_data: Session data
            timestamp: Timestamp string
            
        Returns:
            Cached volume value or None if not found
        """
        key = self._generate_fast_key('volume', session_data, timestamp=timestamp)
        
        if key in self.cache:
            self.stats['hits'] += 1
            return self.cache[key]
        
        self.stats['misses'] += 1
        return None
    
    def set_volume(self, session_data: Dict[Any, Any], timestamp: str, volume: float):
        """
        Cache volume calculation result.
        
        Args:
            session_data: Session data
            timestamp: Timestamp string  
            volume: Calculated volume value
        """
        key = self._generate_fast_key('volume', session_data, timestamp=timestamp)
        self.cache[key] = volume
        self.stats['volume_calculations'] += 1
    
    def get_cascade_prediction(self, session_data: Dict[Any, Any], 
                              params: Dict[str, Any]) -> Optional[Any]:
        """
        Get cached cascade prediction.
        
        Args:
            session_data: Session data
            params: Hawkes parameters
            
        Returns:
            Cached cascade prediction or None if not found
        """
        key = self._generate_fast_key('cascade', session_data, params=params)
        
        if key in self.cache:
            self.stats['hits'] += 1
            return self.cache[key]
        
        self.stats['misses'] += 1
        return None
    
    def set_cascade_prediction(self, session_data: Dict[Any, Any], 
                              params: Dict[str, Any], prediction: Any):
        """
        Cache cascade prediction result.
        
        Args:
            session_data: Session data
            params: Hawkes parameters
            prediction: Cascade prediction result
        """
        key = self._generate_fast_key('cascade', session_data, params=params)
        self.cache[key] = prediction
        self.stats['cascade_calculations'] += 1
    
    def get_session_signature(self, session_data: Dict[Any, Any]) -> str:
        """
        Generate session signature for legacy compatibility.
        
        Args:
            session_data: Session data
            
        Returns:
            Session signature string
        """
        return self._generate_fast_key('session', session_data)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive cache statistics.
        
        Returns:
            Dictionary with cache performance metrics
        """
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
        uptime = time.time() - self._start_time
        
        return {
            **self.stats,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache),
            'max_size': self.cache.maxsize,
            'uptime_seconds': uptime,
            'requests_per_second': total_requests / uptime if uptime > 0 else 0
        }
    
    def clear(self):
        """Clear all cached items."""
        self.cache.clear()
        self.stats = {key: 0 for key in self.stats}
        self._start_time = time.time()
    
    def evict_expired(self):
        """Force eviction of expired items."""
        # TTLCache automatically handles expiration, but we can force cleanup
        self.cache.expire()
        self.stats['evictions'] += 1


# Global unified cache instance
unified_cache = UnifiedHawkesCache()


def get_unified_cache() -> UnifiedHawkesCache:
    """Get the global unified cache instance."""
    return unified_cache


# Legacy compatibility functions
def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics for monitoring."""
    return unified_cache.get_stats()


def clear_cache():
    """Clear the unified cache."""
    unified_cache.clear()