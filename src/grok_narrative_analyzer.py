#!/usr/bin/env python3
"""
Grok 4 Narrative Analysis System
Dedicated system for theoretical and experimental narrative analysis using Grok 4.

ARCHITECTURAL SEPARATION:
- This system is COMPLETELY SEPARATE from core mathematical pipeline
- Uses Grok 4 ONLY for narrative, context, and theoretical analysis
- No mathematical calculations - only narrative interpretation
- Designed for future theoretical/experimental work
- Clear separation prevents AI confusion during troubleshooting

Core Functions:
- Session narrative analysis and interpretation
- Market context and story generation
- Theoretical pattern analysis
- Experimental feature testing
- Narrative-driven insights
"""

import os
import time
import json
import subprocess
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from .config import AppConfig as Config


@dataclass
class NarrativeAnalysisResult:
    """Result structure for narrative analysis."""
    session_narrative: str
    market_context: str
    theoretical_insights: List[str]
    confidence_assessment: str
    narrative_themes: List[str]
    experimental_observations: str
    processing_time_ms: float
    methodology: str


class GrokNarrativeAnalyzer:
    """
    Grok 4 Narrative Analysis System
    
    IMPORTANT: This system is architecturally separated from mathematical pipeline.
    Used ONLY for narrative analysis, theoretical work, and experimental features.
    """
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize Grok narrative analyzer."""
        self.config = config or Config()
        self.api_key = self.config.get_api_key()
        
        # Narrative analysis configuration
        self.narrative_timeout = 45  # Shorter timeout for narrative work
        self.max_retries = 2
        
        # Experimental features flag
        self.experimental_mode = False
        
        print("📖 GROK NARRATIVE ANALYZER: Initialized for theoretical & experimental work")
        print("🔬 ARCHITECTURAL NOTE: Separated from mathematical pipeline")
    
    def analyze_session_narrative(self, 
                                session_data: Dict[Any, Any],
                                mathematical_results: Optional[Dict[str, Any]] = None) -> NarrativeAnalysisResult:
        """
        Analyze session narrative and market context using Grok 4.
        
        Args:
            session_data: Raw session data for context
            mathematical_results: Optional mathematical results for narrative context
            
        Returns:
            NarrativeAnalysisResult with narrative insights
        """
        start_time = time.time()
        
        print("📖 GROK NARRATIVE: Analyzing session story and context...")
        
        try:
            # Prepare narrative analysis prompt
            narrative_prompt = self._build_narrative_prompt(session_data, mathematical_results)
            
            # Call Grok 4 for narrative analysis
            narrative_response = self._call_grok_narrative(narrative_prompt)
            
            # Parse narrative response
            result = self._parse_narrative_response(narrative_response)
            
            processing_time = (time.time() - start_time) * 1000
            result.processing_time_ms = processing_time
            result.methodology = "grok4_narrative_analysis_separated"
            
            print(f"📖 GROK NARRATIVE completed in {processing_time:.1f}ms")
            return result
            
        except Exception as e:
            print(f"⚠️ GROK NARRATIVE failed: {e}")
            return self._create_fallback_narrative(session_data, time.time() - start_time)
    
    def _build_narrative_prompt(self, 
                              session_data: Dict[Any, Any], 
                              mathematical_results: Optional[Dict[str, Any]]) -> str:
        """Build narrative analysis prompt for Grok 4."""
        
        # Extract session context for narrative
        session_metadata = session_data.get('session_metadata', {})
        price_data = session_data.get('price_data', {})
        
        session_type = session_metadata.get('session_type', 'unknown')
        session_character = price_data.get('session_character', 'neutral')
        
        prompt = f"""
GROK 4 NARRATIVE ANALYSIS - THEORETICAL & EXPERIMENTAL SYSTEM

You are a narrative analyst providing theoretical insights and market storytelling.
This is SEPARATE from mathematical calculations - focus on narrative interpretation.

SESSION CONTEXT:
- Session Type: {session_type}
- Session Character: {session_character}
- Price Range: {price_data.get('range', 'unknown')}

NARRATIVE ANALYSIS TASKS:
1. SESSION NARRATIVE: Tell the story of this session's market behavior
2. MARKET CONTEXT: Describe the broader market environment and themes
3. THEORETICAL INSIGHTS: Provide 3-5 theoretical observations about market dynamics
4. CONFIDENCE ASSESSMENT: Assess the narrative confidence and story coherence
5. NARRATIVE THEMES: Identify key themes and patterns in the session story
6. EXPERIMENTAL OBSERVATIONS: Note any unusual or experimental patterns

RESPONSE FORMAT:
Return a JSON object with these exact fields:
{{
    "session_narrative": "The story of this session...",
    "market_context": "Broader market environment description...",
    "theoretical_insights": ["Insight 1", "Insight 2", "Insight 3"],
    "confidence_assessment": "high/medium/low confidence with explanation",
    "narrative_themes": ["Theme 1", "Theme 2", "Theme 3"],
    "experimental_observations": "Any unusual patterns or experimental findings..."
}}

Focus on NARRATIVE and THEORETICAL analysis - no mathematical calculations.
"""
        
        return prompt
    
    def _call_grok_narrative(self, prompt: str) -> str:
        """Call Grok 4 API for narrative analysis."""
        
        # Prepare Grok CLI command for narrative analysis
        cmd = [
            "grok",
            "--api-key", self.api_key,
            "--timeout", str(self.narrative_timeout),
            "--prompt", prompt
        ]
        
        try:
            # Execute Grok CLI for narrative analysis
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.narrative_timeout + 10,
                check=True
            )
            
            return result.stdout.strip()
            
        except subprocess.TimeoutExpired:
            raise Exception("Grok narrative analysis timeout")
        except subprocess.CalledProcessError as e:
            raise Exception(f"Grok narrative analysis failed: {e.stderr}")
    
    def _parse_narrative_response(self, response: str) -> NarrativeAnalysisResult:
        """Parse Grok narrative response into structured result."""
        
        try:
            # Try to parse JSON response
            narrative_data = json.loads(response)
            
            return NarrativeAnalysisResult(
                session_narrative=narrative_data.get("session_narrative", "Narrative analysis pending"),
                market_context=narrative_data.get("market_context", "Context analysis in progress"),
                theoretical_insights=narrative_data.get("theoretical_insights", ["Theoretical analysis pending"]),
                confidence_assessment=narrative_data.get("confidence_assessment", "medium confidence"),
                narrative_themes=narrative_data.get("narrative_themes", ["Standard session patterns"]),
                experimental_observations=narrative_data.get("experimental_observations", "No experimental patterns detected"),
                processing_time_ms=0.0,  # Will be set by caller
                methodology="grok4_narrative_parsed"
            )
            
        except json.JSONDecodeError:
            # Fallback parsing if JSON fails
            return NarrativeAnalysisResult(
                session_narrative=f"Raw narrative: {response[:200]}...",
                market_context="Context parsing failed - using raw response",
                theoretical_insights=["Response parsing incomplete"],
                confidence_assessment="low confidence - parsing issues",
                narrative_themes=["Narrative extraction pending"],
                experimental_observations="Experimental analysis incomplete",
                processing_time_ms=0.0,
                methodology="grok4_narrative_fallback_parsed"
            )
    
    def _create_fallback_narrative(self, session_data: Dict[Any, Any], processing_time: float) -> NarrativeAnalysisResult:
        """Create fallback narrative when Grok analysis fails."""
        
        session_metadata = session_data.get('session_metadata', {})
        session_type = session_metadata.get('session_type', 'unknown')
        
        return NarrativeAnalysisResult(
            session_narrative=f"Fallback narrative for {session_type} session - detailed analysis unavailable",
            market_context="Market context analysis failed - using fallback assessment",
            theoretical_insights=[
                "Theoretical analysis unavailable due to system limitations",
                "Session shows standard market characteristics",
                "Further narrative analysis recommended"
            ],
            confidence_assessment="low confidence - fallback mode",
            narrative_themes=["Standard session", "Analysis limitations"],
            experimental_observations="Experimental analysis unavailable in fallback mode",
            processing_time_ms=processing_time * 1000,
            methodology="fallback_narrative_analysis"
        )
    
    def analyze_theoretical_patterns(self, 
                                   session_data: Dict[Any, Any],
                                   custom_hypothesis: Optional[str] = None) -> Dict[str, Any]:
        """
        Experimental theoretical pattern analysis using Grok 4.
        
        This method is designed for future experimental work and hypothesis testing.
        """
        print("🔬 EXPERIMENTAL: Theoretical pattern analysis")
        
        if not self.experimental_mode:
            return {
                "status": "experimental_mode_disabled",
                "message": "Enable experimental mode for theoretical analysis",
                "theoretical_patterns": []
            }
        
        # This would contain experimental Grok 4 analysis
        # Placeholder for future theoretical work
        return {
            "status": "experimental_analysis_ready",
            "theoretical_patterns": ["Pattern analysis framework ready"],
            "hypothesis_testing": "Framework prepared for custom hypothesis testing",
            "experimental_features": ["Custom pattern detection", "Theoretical modeling", "Hypothesis validation"]
        }
    
    def enable_experimental_mode(self) -> None:
        """Enable experimental features for theoretical work."""
        self.experimental_mode = True
        print("🔬 EXPERIMENTAL MODE: Enabled for theoretical research")
    
    def disable_experimental_mode(self) -> None:
        """Disable experimental features."""
        self.experimental_mode = False
        print("📖 STANDARD MODE: Experimental features disabled")
    
    def get_analyzer_status(self) -> Dict[str, Any]:
        """Get narrative analyzer status and configuration."""
        return {
            "system_type": "grok4_narrative_analyzer",
            "architectural_separation": "complete_separation_from_mathematical_pipeline",
            "experimental_mode": self.experimental_mode,
            "narrative_timeout": self.narrative_timeout,
            "max_retries": self.max_retries,
            "api_configured": self.api_key is not None,
            "purpose": "narrative_analysis_and_theoretical_research",
            "mathematical_calculations": "none_performed_by_this_system"
        }


def create_grok_narrative_analyzer(config: Optional[Config] = None) -> GrokNarrativeAnalyzer:
    """Factory function to create Grok narrative analyzer."""
    return GrokNarrativeAnalyzer(config)


if __name__ == "__main__":
    # Test Grok narrative analyzer
    print("🧪 TESTING GROK NARRATIVE ANALYZER")
    print("=" * 50)
    
    # Create analyzer
    analyzer = create_grok_narrative_analyzer()
    
    # Show system status
    status = analyzer.get_analyzer_status()
    print(f"📖 System Type: {status['system_type']}")
    print(f"🏗️ Architectural Separation: {status['architectural_separation']}")
    print(f"🔬 Experimental Mode: {status['experimental_mode']}")
    print(f"🎯 Purpose: {status['purpose']}")
    print(f"🧮 Mathematical Calculations: {status['mathematical_calculations']}")
    
    # Mock session data for testing
    mock_session = {
        'session_metadata': {
            'session_type': 'london',
            'session_id': 'test_session'
        },
        'price_data': {
            'session_character': 'expansion',
            'range': 150.0
        }
    }
    
    print(f"\n📖 Testing narrative analysis...")
    
    # Note: This would normally call Grok 4, but will use fallback for testing
    try:
        result = analyzer.analyze_session_narrative(mock_session)
        
        print(f"✅ Narrative Analysis Results:")
        print(f"  Session Narrative: {result.session_narrative[:100]}...")
        print(f"  Market Context: {result.market_context[:100]}...")
        print(f"  Theoretical Insights: {len(result.theoretical_insights)} insights")
        print(f"  Confidence: {result.confidence_assessment}")
        print(f"  Processing Time: {result.processing_time_ms:.1f}ms")
        print(f"  Methodology: {result.methodology}")
        
    except Exception as e:
        print(f"⚠️ Test completed with expected fallback: {e}")
        
    print(f"\n🎯 GROK NARRATIVE ANALYZER: Ready for theoretical & experimental work")