#!/usr/bin/env python3
"""
HTF Intelligence Oversight Agent - Mission Critical System Coordinator

This agent is responsible for overseeing the complete HTF Intelligence pipeline,
ensuring data correctness, process health, and seamless coordination between:
- Level 1 session processing
- HTF Intelligence parsing and integration  
- A→B→C→D Preprocessing pipeline coordination
- HTF Hawks algorithm data feeding
- System validation and error recovery

The agent guarantees that HTF intelligence flows correctly from Level 1 natural
language through to the mathematical HTF Hawks prediction algorithm.
"""

import json
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import threading
from concurrent.futures import ThreadPoolExecutor

# Import system components
import sys
sys.path.append(str(Path(__file__).parent.parent))

try:
    from htf_intelligence_integration import HTFIntelligenceIntegration
    INTELLIGENCE_AVAILABLE = True
except ImportError:
    print("Warning: HTF Intelligence Integration not available")
    INTELLIGENCE_AVAILABLE = False

try:
    from preprocessing_agent import PreprocessingAgent
    PREPROCESSING_AVAILABLE = True
except ImportError:
    print("Warning: Preprocessing Agent not available")
    PREPROCESSING_AVAILABLE = False

try:
    from config import AppConfig
except ImportError:
    # Create minimal config class
    class AppConfig:
        def get_api_key(self):
            return None


@dataclass
class SystemHealthStatus:
    """System health monitoring data."""
    component: str
    status: str  # "healthy", "warning", "error"
    last_check: datetime
    error_count: int
    details: Dict[str, Any]


@dataclass
class ProcessingPipeline:
    """Processing pipeline coordination data."""
    session_file: str
    level_1_processed: bool
    intelligence_processed: bool  
    abcd_pipeline_processed: bool
    htf_context_updated: bool
    hawks_data_ready: bool
    errors: List[str]
    processing_time: float


class HTFIntelligenceOversightAgent:
    """
    HTF Intelligence Oversight Agent - Mission Critical System Coordinator
    
    This agent provides comprehensive oversight and coordination for the complete
    HTF intelligence processing pipeline, ensuring data integrity and system health.
    
    Key Responsibilities:
    1. Coordinate Level 1 → HTF Intelligence → HTF Hawks data flow
    2. Monitor A→B→C→D pipeline integration with HTF intelligence
    3. Validate data correctness and processing integrity
    4. Handle error recovery and system health monitoring
    5. Ensure seamless operation of all HTF components
    """
    
    def __init__(self, base_dir: str = "/Users/<USER>/grok-claude-automation"):
        self.base_dir = Path(base_dir)
        self.config = AppConfig()
        
        # Initialize system components  
        self.intelligence_integration = None
        self.preprocessing_agent = None
        
        if INTELLIGENCE_AVAILABLE:
            self.intelligence_integration = HTFIntelligenceIntegration(base_dir)
        
        if PREPROCESSING_AVAILABLE:
            self.preprocessing_agent = PreprocessingAgent(use_local_pipeline=True)
        
        # System monitoring
        self.system_health = {}
        self.processing_pipelines = {}
        self.error_recovery_queue = []
        
        # Agent state
        self.is_monitoring = False
        self.monitoring_thread = None
        self.last_health_check = None
        
        # Performance metrics
        self.metrics = {
            "sessions_processed": 0,
            "intelligence_events_created": 0,
            "abcd_pipeline_runs": 0,
            "error_recoveries": 0,
            "system_uptime": datetime.now()
        }
        
        self._setup_logging()
        self._initialize_system_health()
        
    def _setup_logging(self):
        """Setup comprehensive oversight logging."""
        self.logger = logging.getLogger("HTFOversightAgent")
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - HTF_Oversight - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
            
            # File handler for critical oversight logs
            log_dir = self.base_dir / "logs"
            log_dir.mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler(log_dir / "htf_oversight.log")
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
        
        self.logger.info("HTF Intelligence Oversight Agent initialized - Mission Critical oversight active")
    
    def _initialize_system_health(self):
        """Initialize system health monitoring."""
        components = [
            "htf_intelligence_parser",
            "htf_integration_layer", 
            "preprocessing_agent",
            "level_1_data",
            "htf_context_files",
            "hawks_algorithm_data"
        ]
        
        for component in components:
            self.system_health[component] = SystemHealthStatus(
                component=component,
                status="unknown",
                last_check=datetime.now(),
                error_count=0,
                details={}
            )
    
    def check_system_health(self) -> Dict[str, Any]:
        """Comprehensive system health check."""
        self.logger.info("Running comprehensive system health check...")
        
        health_report = {
            "overall_status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {},
            "critical_issues": [],
            "warnings": []
        }
        
        # Check Level 1 data integrity
        level_1_health = self._check_level_1_data_health()
        self.system_health["level_1_data"] = level_1_health
        health_report["components"]["level_1_data"] = level_1_health.__dict__
        
        # Check HTF Intelligence Parser health
        intelligence_health = self._check_intelligence_parser_health()
        self.system_health["htf_intelligence_parser"] = intelligence_health
        health_report["components"]["htf_intelligence_parser"] = intelligence_health.__dict__
        
        # Check HTF Context files health
        htf_context_health = self._check_htf_context_health()
        self.system_health["htf_context_files"] = htf_context_health
        health_report["components"]["htf_context_files"] = htf_context_health.__dict__
        
        # Check Preprocessing Agent health
        preprocessing_health = self._check_preprocessing_agent_health()
        self.system_health["preprocessing_agent"] = preprocessing_health
        health_report["components"]["preprocessing_agent"] = preprocessing_health.__dict__
        
        # Check Hawks Algorithm data readiness
        hawks_health = self._check_hawks_data_health()
        self.system_health["hawks_algorithm_data"] = hawks_health
        health_report["components"]["hawks_algorithm_data"] = hawks_health.__dict__
        
        # Determine overall system status
        component_statuses = [h.status for h in self.system_health.values()]
        if "error" in component_statuses:
            health_report["overall_status"] = "error"
            health_report["critical_issues"] = [
                f"{comp}: {health.details.get('error', 'Unknown error')}"
                for comp, health in self.system_health.items()
                if health.status == "error"
            ]
        elif "warning" in component_statuses:
            health_report["overall_status"] = "warning"
            health_report["warnings"] = [
                f"{comp}: {health.details.get('warning', 'Unknown warning')}"
                for comp, health in self.system_health.items()
                if health.status == "warning"
            ]
        
        self.last_health_check = datetime.now()
        
        self.logger.info(f"System health check complete: {health_report['overall_status']}")
        if health_report["critical_issues"]:
            self.logger.error(f"Critical issues found: {health_report['critical_issues']}")
        
        return health_report
    
    def _check_level_1_data_health(self) -> SystemHealthStatus:
        """Check Level 1 session data health."""
        try:
            sessions_dir = self.base_dir / "data" / "sessions" / "level_1"
            if not sessions_dir.exists():
                return SystemHealthStatus(
                    component="level_1_data",
                    status="error",
                    last_check=datetime.now(),
                    error_count=1,
                    details={"error": "Level 1 data directory not found"}
                )
            
            session_files = list(sessions_dir.glob("*_Lvl-1_*.json"))
            corrupted_files = []
            
            for session_file in session_files:
                try:
                    with open(session_file, 'r') as f:
                        session_data = json.load(f)
                    
                    # Validate required structure
                    required_fields = ["session_metadata", "price_data", "price_movements"]
                    missing_fields = [field for field in required_fields 
                                    if field not in session_data]
                    
                    if missing_fields:
                        corrupted_files.append(f"{session_file.name}: missing {missing_fields}")
                        
                except json.JSONDecodeError:
                    corrupted_files.append(f"{session_file.name}: JSON decode error")
                except Exception as e:
                    corrupted_files.append(f"{session_file.name}: {str(e)}")
            
            if corrupted_files:
                return SystemHealthStatus(
                    component="level_1_data",
                    status="warning",
                    last_check=datetime.now(),
                    error_count=len(corrupted_files),
                    details={
                        "warning": f"{len(corrupted_files)} corrupted files found",
                        "corrupted_files": corrupted_files,
                        "total_files": len(session_files)
                    }
                )
            
            return SystemHealthStatus(
                component="level_1_data",
                status="healthy",
                last_check=datetime.now(),
                error_count=0,
                details={
                    "total_files": len(session_files),
                    "all_files_valid": True
                }
            )
            
        except Exception as e:
            return SystemHealthStatus(
                component="level_1_data",
                status="error",
                last_check=datetime.now(),
                error_count=1,
                details={"error": f"Level 1 data health check failed: {str(e)}"}
            )
    
    def _check_intelligence_parser_health(self) -> SystemHealthStatus:
        """Check HTF Intelligence Parser health."""
        try:
            # Test intelligence parser with a sample session
            parser = self.intelligence_integration.parser
            
            # Verify parser database is loaded
            if not parser.session_database:
                return SystemHealthStatus(
                    component="htf_intelligence_parser",
                    status="warning",
                    last_check=datetime.now(),
                    error_count=0,
                    details={"warning": "Session database not loaded in parser"}
                )
            
            # Verify daily structures are built
            if not parser.daily_structures:
                return SystemHealthStatus(
                    component="htf_intelligence_parser",
                    status="warning", 
                    last_check=datetime.now(),
                    error_count=0,
                    details={"warning": "Daily structures not built in parser"}
                )
            
            return SystemHealthStatus(
                component="htf_intelligence_parser",
                status="healthy",
                last_check=datetime.now(),
                error_count=0,
                details={
                    "session_database_loaded": len(parser.session_database),
                    "daily_structures_built": len(parser.daily_structures)
                }
            )
            
        except Exception as e:
            return SystemHealthStatus(
                component="htf_intelligence_parser",
                status="error",
                last_check=datetime.now(),
                error_count=1,
                details={"error": f"Intelligence parser health check failed: {str(e)}"}
            )
    
    def _check_htf_context_health(self) -> SystemHealthStatus:
        """Check HTF context files health."""
        try:
            htf_dir = self.base_dir / "data" / "trackers" / "htf"
            if not htf_dir.exists():
                return SystemHealthStatus(
                    component="htf_context_files",
                    status="error",
                    last_check=datetime.now(),
                    error_count=1,
                    details={"error": "HTF context directory not found"}
                )
            
            htf_files = list(htf_dir.glob("HTF_*.json"))
            intelligence_processed_files = 0
            files_with_events = 0
            total_intelligence_events = 0
            
            for htf_file in htf_files:
                try:
                    with open(htf_file, 'r') as f:
                        htf_data = json.load(f)
                    
                    if htf_data.get("intelligence_processed", False):
                        intelligence_processed_files += 1
                    
                    htf_events = htf_data.get("htf_events", [])
                    if htf_events:
                        files_with_events += 1
                        total_intelligence_events += len(htf_events)
                        
                except Exception as e:
                    self.logger.warning(f"Could not check HTF file {htf_file.name}: {e}")
            
            return SystemHealthStatus(
                component="htf_context_files",
                status="healthy",
                last_check=datetime.now(),
                error_count=0,
                details={
                    "total_htf_files": len(htf_files),
                    "intelligence_processed_files": intelligence_processed_files,
                    "files_with_events": files_with_events,
                    "total_intelligence_events": total_intelligence_events
                }
            )
            
        except Exception as e:
            return SystemHealthStatus(
                component="htf_context_files",
                status="error",
                last_check=datetime.now(),
                error_count=1,
                details={"error": f"HTF context health check failed: {str(e)}"}
            )
    
    def _check_preprocessing_agent_health(self) -> SystemHealthStatus:
        """Check Preprocessing Agent (A→B→C→D pipeline) health."""
        try:
            # Check if preprocessing agent is properly initialized
            if not hasattr(self.preprocessing_agent, 'pipeline'):
                return SystemHealthStatus(
                    component="preprocessing_agent",
                    status="error",
                    last_check=datetime.now(),
                    error_count=1,
                    details={"error": "Preprocessing agent pipeline not initialized"}
                )
            
            # Check if using local pipeline (recommended)
            using_local = self.preprocessing_agent.use_local_pipeline
            
            return SystemHealthStatus(
                component="preprocessing_agent",
                status="healthy",
                last_check=datetime.now(),
                error_count=0,
                details={
                    "pipeline_initialized": True,
                    "using_local_pipeline": using_local,
                    "processing_state": self.preprocessing_agent.processing_state
                }
            )
            
        except Exception as e:
            return SystemHealthStatus(
                component="preprocessing_agent",
                status="error",
                last_check=datetime.now(),
                error_count=1,
                details={"error": f"Preprocessing agent health check failed: {str(e)}"}
            )
    
    def _check_hawks_data_health(self) -> SystemHealthStatus:
        """Check HTF Hawks algorithm data readiness."""
        try:
            # Check if HTF master controller files exist
            htf_master_file = self.base_dir / "src" / "htf_master_controller.py"
            if not htf_master_file.exists():
                return SystemHealthStatus(
                    component="hawks_algorithm_data",
                    status="error",
                    last_check=datetime.now(),
                    error_count=1,
                    details={"error": "HTF master controller file not found"}
                )
            
            # Check if calibration data exists (current Hawks data source)
            calibration_file = self.base_dir / "htf_calibrated_gammas_july28.json"
            static_data_exists = calibration_file.exists()
            
            # Check if HTF context files have intelligence (new data source)
            htf_dir = self.base_dir / "data" / "trackers" / "htf"
            intelligence_files = 0
            if htf_dir.exists():
                for htf_file in htf_dir.glob("HTF_*.json"):
                    try:
                        with open(htf_file, 'r') as f:
                            htf_data = json.load(f)
                        if htf_data.get("htf_events"):
                            intelligence_files += 1
                    except:
                        continue
            
            status = "healthy" if static_data_exists and intelligence_files > 0 else "warning"
            warning_msg = None
            
            if not static_data_exists:
                warning_msg = "Static calibration data missing - Hawks algorithm may fail"
            elif intelligence_files == 0:
                warning_msg = "No HTF intelligence data available - Hawks using static data only"
            
            return SystemHealthStatus(
                component="hawks_algorithm_data",
                status=status,
                last_check=datetime.now(),
                error_count=0 if status == "healthy" else 1,
                details={
                    "static_calibration_exists": static_data_exists,
                    "intelligence_files_available": intelligence_files,
                    "hawks_data_ready": static_data_exists,
                    "warning": warning_msg
                }
            )
            
        except Exception as e:
            return SystemHealthStatus(
                component="hawks_algorithm_data",
                status="error",
                last_check=datetime.now(),
                error_count=1,
                details={"error": f"Hawks data health check failed: {str(e)}"}
            )
    
    def coordinate_session_processing(self, session_file_path: str) -> ProcessingPipeline:
        """Coordinate complete session processing through all pipelines."""
        session_name = Path(session_file_path).name
        self.logger.info(f"Coordinating complete processing for {session_name}")
        
        start_time = time.time()
        pipeline = ProcessingPipeline(
            session_file=session_name,
            level_1_processed=False,
            intelligence_processed=False,
            abcd_pipeline_processed=False,
            htf_context_updated=False,
            hawks_data_ready=False,
            errors=[],
            processing_time=0.0
        )
        
        try:
            # Step 1: Validate Level 1 data
            self.logger.info(f"Step 1: Validating Level 1 data for {session_name}")
            if Path(session_file_path).exists():
                try:
                    with open(session_file_path, 'r') as f:
                        session_data = json.load(f)
                    pipeline.level_1_processed = True
                    self.logger.info(f"✅ Level 1 data valid for {session_name}")
                except Exception as e:
                    pipeline.errors.append(f"Level 1 validation failed: {str(e)}")
                    self.logger.error(f"❌ Level 1 validation failed for {session_name}: {e}")
            else:
                pipeline.errors.append("Session file not found")
                self.logger.error(f"❌ Session file not found: {session_file_path}")
            
            # Step 2: HTF Intelligence Processing
            if pipeline.level_1_processed:
                self.logger.info(f"Step 2: Processing HTF intelligence for {session_name}")
                try:
                    intelligence_result = self.intelligence_integration.process_session_with_integration(session_file_path)
                    
                    if intelligence_result["status"] in ["success", "partial_success"]:
                        pipeline.intelligence_processed = True
                        pipeline.htf_context_updated = intelligence_result.get("htf_context_updated", False)
                        self.metrics["intelligence_events_created"] += intelligence_result.get("events_processed", 0)
                        self.logger.info(f"✅ HTF intelligence processed for {session_name}: {intelligence_result.get('events_processed', 0)} events")
                    else:
                        pipeline.errors.append(f"HTF intelligence processing failed: {intelligence_result.get('error', 'Unknown')}")
                        self.logger.warning(f"⚠️ HTF intelligence processing issues for {session_name}")
                        
                except Exception as e:
                    pipeline.errors.append(f"HTF intelligence processing error: {str(e)}")
                    self.logger.error(f"❌ HTF intelligence processing error for {session_name}: {e}")
            
            # Step 3: A→B→C→D Pipeline Processing  
            if pipeline.level_1_processed:
                self.logger.info(f"Step 3: Running A→B→C→D pipeline for {session_name}")
                try:
                    abcd_result = self.preprocessing_agent.process_session(session_file_path)
                    
                    if abcd_result.get("status") == "success":
                        pipeline.abcd_pipeline_processed = True
                        self.metrics["abcd_pipeline_runs"] += 1
                        self.logger.info(f"✅ A→B→C→D pipeline completed for {session_name}")
                    else:
                        pipeline.errors.append(f"A→B→C→D pipeline failed: {abcd_result.get('error', 'Unknown')}")
                        self.logger.warning(f"⚠️ A→B→C→D pipeline issues for {session_name}")
                        
                except Exception as e:
                    pipeline.errors.append(f"A→B→C→D pipeline error: {str(e)}")
                    self.logger.error(f"❌ A→B→C→D pipeline error for {session_name}: {e}")
            
            # Step 4: Validate Hawks Data Readiness
            if pipeline.htf_context_updated:
                self.logger.info(f"Step 4: Validating Hawks data readiness for {session_name}")
                hawks_health = self._check_hawks_data_health()
                if hawks_health.status in ["healthy", "warning"]:
                    pipeline.hawks_data_ready = True
                    self.logger.info(f"✅ Hawks data ready for {session_name}")
                else:
                    pipeline.errors.append("Hawks data not ready")
                    self.logger.warning(f"⚠️ Hawks data not ready for {session_name}")
            
        except Exception as e:
            pipeline.errors.append(f"Coordination error: {str(e)}")
            self.logger.error(f"❌ Coordination error for {session_name}: {e}")
        
        pipeline.processing_time = time.time() - start_time
        self.processing_pipelines[session_name] = pipeline
        self.metrics["sessions_processed"] += 1
        
        # Log completion summary
        success_steps = sum([
            pipeline.level_1_processed,
            pipeline.intelligence_processed, 
            pipeline.abcd_pipeline_processed,
            pipeline.htf_context_updated,
            pipeline.hawks_data_ready
        ])
        
        self.logger.info(f"🎯 Processing coordination complete for {session_name}: {success_steps}/5 steps successful, {len(pipeline.errors)} errors, {pipeline.processing_time:.2f}s")
        
        return pipeline
    
    def batch_coordinate_all_sessions(self) -> Dict[str, Any]:
        """Batch coordinate all Level 1 sessions through complete pipeline."""
        self.logger.info("Starting batch coordination of all Level 1 sessions...")
        
        sessions_dir = self.base_dir / "data" / "sessions" / "level_1"
        session_files = list(sessions_dir.glob("*_Lvl-1_*.json"))
        
        batch_results = {
            "total_sessions": len(session_files),
            "processed_sessions": 0,
            "fully_successful": 0,
            "partial_success": 0,
            "failed_sessions": 0,
            "total_processing_time": 0.0,
            "session_details": [],
            "summary": {}
        }
        
        # Process sessions with thread pool for efficiency
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = {
                executor.submit(self.coordinate_session_processing, str(session_file)): session_file.name
                for session_file in session_files
            }
            
            for future in futures:
                session_name = futures[future]
                try:
                    pipeline = future.result()
                    batch_results["processed_sessions"] += 1
                    batch_results["total_processing_time"] += pipeline.processing_time
                    
                    # Classify success level
                    success_steps = sum([
                        pipeline.level_1_processed,
                        pipeline.intelligence_processed,
                        pipeline.abcd_pipeline_processed, 
                        pipeline.htf_context_updated,
                        pipeline.hawks_data_ready
                    ])
                    
                    if success_steps == 5:
                        batch_results["fully_successful"] += 1
                        success_level = "full_success"
                    elif success_steps >= 3:
                        batch_results["partial_success"] += 1
                        success_level = "partial_success"
                    else:
                        batch_results["failed_sessions"] += 1
                        success_level = "failed"
                    
                    batch_results["session_details"].append({
                        "session": session_name,
                        "success_level": success_level,
                        "steps_completed": success_steps,
                        "errors": len(pipeline.errors),
                        "processing_time": pipeline.processing_time
                    })
                    
                except Exception as e:
                    self.logger.error(f"Batch coordination failed for {session_name}: {e}")
                    batch_results["failed_sessions"] += 1
        
        # Generate summary
        batch_results["summary"] = {
            "success_rate": f"{batch_results['fully_successful']}/{batch_results['total_sessions']} ({batch_results['fully_successful']/batch_results['total_sessions']*100:.1f}%)",
            "average_processing_time": batch_results["total_processing_time"] / batch_results["processed_sessions"] if batch_results["processed_sessions"] > 0 else 0,
            "intelligence_events_created": self.metrics["intelligence_events_created"],
            "abcd_pipeline_runs": self.metrics["abcd_pipeline_runs"]
        }
        
        self.logger.info(f"Batch coordination complete: {batch_results['summary']['success_rate']} success rate, {batch_results['summary']['intelligence_events_created']} intelligence events created")
        
        return batch_results
    
    def start_monitoring(self, check_interval: int = 300):
        """Start continuous system monitoring."""
        if self.is_monitoring:
            self.logger.warning("Monitoring already active")
            return
        
        self.is_monitoring = True
        self.logger.info(f"Starting continuous monitoring with {check_interval}s intervals")
        
        def monitoring_loop():
            while self.is_monitoring:
                try:
                    health_report = self.check_system_health()
                    
                    if health_report["overall_status"] == "error":
                        self.logger.error(f"CRITICAL: System health check failed - {health_report['critical_issues']}")
                        self.metrics["error_recoveries"] += 1
                    elif health_report["overall_status"] == "warning":
                        self.logger.warning(f"System warnings detected - {health_report['warnings']}")
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    self.logger.error(f"Monitoring loop error: {e}")
                    time.sleep(60)  # Shorter retry interval on error
        
        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()
    
    def stop_monitoring(self):
        """Stop continuous system monitoring."""
        if self.is_monitoring:
            self.is_monitoring = False
            self.logger.info("Stopping continuous monitoring")
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=30)
    
    def get_oversight_report(self) -> Dict[str, Any]:
        """Generate comprehensive oversight report."""
        uptime = datetime.now() - self.metrics["system_uptime"]
        
        return {
            "agent_status": "active",
            "system_uptime": str(uptime),
            "metrics": self.metrics,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "system_health": {comp: health.__dict__ for comp, health in self.system_health.items()},
            "processing_pipelines": len(self.processing_pipelines),
            "monitoring_active": self.is_monitoring,
            "error_recovery_queue": len(self.error_recovery_queue)
        }


def main():
    """Demo HTF Intelligence Oversight Agent."""
    print("🔍 HTF Intelligence Oversight Agent - Demo")
    print("=" * 50)
    
    agent = HTFIntelligenceOversightAgent()
    
    # System health check
    print("\n📋 Running system health check...")
    health_report = agent.check_system_health()
    
    print(f"\n✅ System Health Report:")
    print(f"Overall Status: {health_report['overall_status']}")
    print(f"Components Checked: {len(health_report['components'])}")
    
    if health_report["critical_issues"]:
        print(f"❌ Critical Issues: {len(health_report['critical_issues'])}")
        for issue in health_report["critical_issues"]:
            print(f"  • {issue}")
    
    if health_report["warnings"]:
        print(f"⚠️ Warnings: {len(health_report['warnings'])}")
        for warning in health_report["warnings"]:
            print(f"  • {warning}")
    
    # Test session coordination
    test_session = "/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYAM_Lvl-1_2025_07_28.json"
    if Path(test_session).exists():
        print(f"\n🔄 Testing session coordination...")
        pipeline = agent.coordinate_session_processing(test_session)
        
        print(f"Session: {pipeline.session_file}")
        print(f"Steps Completed: {sum([pipeline.level_1_processed, pipeline.intelligence_processed, pipeline.abcd_pipeline_processed, pipeline.htf_context_updated, pipeline.hawks_data_ready])}/5")
        print(f"Processing Time: {pipeline.processing_time:.2f}s")
        print(f"Errors: {len(pipeline.errors)}")
    
    # Generate oversight report
    print(f"\n📊 Oversight Report:")
    report = agent.get_oversight_report()
    print(f"Sessions Processed: {report['metrics']['sessions_processed']}")
    print(f"Intelligence Events: {report['metrics']['intelligence_events_created']}")
    print(f"A→B→C→D Runs: {report['metrics']['abcd_pipeline_runs']}")
    print(f"System Uptime: {report['system_uptime']}")


if __name__ == "__main__":
    main()