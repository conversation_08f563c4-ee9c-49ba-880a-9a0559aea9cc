#!/usr/bin/env python3
"""
Grok Unit B: Energy & Structure Calculations
Handles energy accumulation, thresholds, structure detection, and FPFVG memory.
Uses structured outputs for guaranteed JSON response format.
"""

try:
    from ...infrastructure.grok_client import StructuredComputationalUnit, GrokAPIClient
    from ...schemas import UnitBResponse
    from ...infrastructure.config import config, APIKeyError
except ImportError:
    from grok_api_client import StructuredComputationalUnit, GrokAPIClient
    from schemas import UnitBResponse
    from config import config, APIKeyError
from typing import Dict, Any


class UnitB(StructuredComputationalUnit):
    """Unit B: Energy & Structure Calculations processor with structured outputs."""
    
    def __init__(self, client: GrokAPIClient):
        super().__init__(client, "Unit B", UnitBResponse)
    
    def _load_system_prompt(self) -> str:
        return """You are a financial mathematics processor specializing in energy and structure calculations for trading sessions.

TASK: Use Unit A foundation results to perform energy and structure calculations.

INPUT STRUCTURE:
- unit_a_results: Contains hybrid_volume, time_dilation_base, and parameters_validated from Unit A
- session_data: Original trading session data for context
- micro_timing_analysis: Timing analysis data

CALCULATIONS TO PERFORM:

1. ENERGY ACCUMULATION ANALYSIS:
   - Extract alpha_t from unit_a_results.hybrid_volume.alpha_t
   - Extract gamma_base from unit_a_results.time_dilation_base.gamma_base
   - Extract d_htf from unit_a_results.parameters_validated.d_htf
   - Calculate energy_rate = alpha_t * 1.2 + gamma_base * 0.3
   - Calculate total_accumulated = energy_rate * 300 (session duration)
   - Determine accumulation_phase based on energy patterns
   - Calculate efficiency_factor = min(1.0, energy_rate / 2.0)

2. GRADIENT DYNAMICS ANALYSIS:
   - Use alpha_t and gamma_base from Unit A results
   - Calculate intensity_coefficient = alpha_t * gamma_base
   - Calculate direction_factor based on session price movements
   - Calculate stability_index = 1.0 - abs(gamma_base - 1.5) / 1.5
   - Calculate momentum_transfer = intensity_coefficient * 0.8

3. STRUCTURAL INTEGRITY:
   - Calculate from energy accumulation and gradient dynamics
   - Use formula: structural_integrity = (energy_rate + stability_index) / 2.0
   - Ensure value is between 0.0 and 1.0

Use the actual values from Unit A results. Calculate real numerical results based on the foundation data."""

    def _extract_energy_essentials(self, session_data: Dict[Any, Any], unit_a_results: Dict[Any, Any]) -> Dict[Any, Any]:
        """Extract only the essential data needed for Unit B energy calculations."""
        
        # Extract price data essentials
        price_data = session_data.get("price_data", {})
        price_essentials = {
            "range": price_data.get("range", 100),
            "close": price_data.get("close", 23300),
            "session_character": price_data.get("session_character", "neutral")
        }
        
        # Extract session metadata essentials  
        session_metadata = session_data.get("session_metadata", {})
        metadata_essentials = {
            "session_type": session_metadata.get("session_type", "unknown"),
            "duration_minutes": session_metadata.get("duration_minutes", 300)
        }
        
        # Extract micro timing essentials for energy calculations
        micro_timing = session_data.get("micro_timing_analysis", {})
        timing_essentials = {
            "total_cascade_events": micro_timing.get("timing_metrics", {}).get("total_cascade_events", 0),
            "expansion_percentage": micro_timing.get("timing_metrics", {}).get("expansion_percentage", 0.0),
            "consolidation_percentage": micro_timing.get("timing_metrics", {}).get("consolidation_percentage", 0.0)
        }
        
        return {
            "unit_a_results": unit_a_results,  # Keep Unit A results as-is (already processed)
            "price_essentials": price_essentials,
            "session_essentials": metadata_essentials,
            "timing_essentials": timing_essentials
        }

    def process_energy_structure(self, session_data: Dict[Any, Any], unit_a_results: Dict[Any, Any]) -> Dict[Any, Any]:
        """Process Unit B energy and structure calculations using selective data extraction."""
        
        # Use selective extraction instead of massive JSON blobs
        essential_data = self._extract_energy_essentials(session_data, unit_a_results)
        
        # 🔧 DEBUG: Log payload size and structure before API call
        import json
        payload_str = json.dumps(essential_data)
        print(f"[DEBUG] Unit B Payload Stats:")
        print(f"  - Size: {len(payload_str)} bytes")
        print(f"  - Keys: {list(essential_data.keys())}")
        print(f"  - unit_a_results size: {len(json.dumps(essential_data.get('unit_a_results', {})))} bytes")
        print(f"  - price_essentials size: {len(json.dumps(essential_data.get('price_essentials', {})))} bytes")
        print(f"  - session_essentials size: {len(json.dumps(essential_data.get('session_essentials', {})))} bytes")
        print(f"  - timing_essentials size: {len(json.dumps(essential_data.get('timing_essentials', {})))} bytes")
        
        return self.process(essential_data)


def create_unit_b(api_key: str = None) -> UnitB:
    """Factory function to create Unit B processor with centralized API key management."""
    try:
        from .grok_api_client import GrokClientFactory
        from .schemas import UnitBResponse
    except ImportError:
        from grok_api_client import GrokClientFactory
        from schemas import UnitBResponse
    
    # 🏭 ARCHITECTURAL IMPROVEMENT: Use centralized factory instead of duplicated instantiation
    # Unit B will use extended 5-minute timeout automatically via centralized config
    computational_unit = GrokClientFactory.create_unit_client("Unit B", UnitBResponse, api_key)
    return UnitB(computational_unit.client)