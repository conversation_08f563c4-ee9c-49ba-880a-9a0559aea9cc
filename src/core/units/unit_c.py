#!/usr/bin/env python3
"""
Grok Unit C: Advanced Dynamics Calculations
Handles enhanced time dilation, cascade modeling, institutional flow, and timing predictions.
Uses structured outputs for guaranteed JSON response format.
"""

try:
    from ...infrastructure.grok_client import StructuredComputationalUnit, GrokAPIClient
    from ...schemas import UnitCResponse
    from ...infrastructure.config import config, APIKeyError
except ImportError:
    from grok_api_client import StructuredComputationalUnit, GrokAPIClient
    from schemas import UnitCResponse
    from config import config, APIKeyError
from typing import Dict, Any


class UnitC(StructuredComputationalUnit):
    """Unit C: Advanced Dynamics Calculations processor with structured outputs."""
    
    def __init__(self, client: GrokAPIClient):
        super().__init__(client, "Unit C", UnitCResponse)
    
    def _load_system_prompt(self) -> str:
        return """You are a financial mathematics processor specializing in advanced dynamics calculations for trading sessions.

TASK: Use results from Units A and B to perform advanced dynamics calculations.

INPUT STRUCTURE:
- unit_a_results: Foundation calculations (hybrid_volume, time_dilation_base, parameters_validated)
- unit_b_results: Energy & structure calculations (energy_accumulation, gradient_dynamics, structural_integrity)
- session_data: Original trading session data
- micro_timing_analysis: Timing analysis data

CALCULATIONS TO PERFORM:

1. TEMPORAL MOMENTUM ANALYSIS:
   - Extract gamma_base from unit_a_results.time_dilation_base.gamma_base
   - Extract energy_rate from unit_b_results.energy_accumulation.energy_rate
   - Extract intensity_coefficient from unit_b_results.gradient_dynamics.intensity_coefficient
   - Calculate momentum_strength = gamma_base * energy_rate * 0.5
   - Determine momentum_direction based on session price movements and trends
   - Calculate decay_coefficient = 1.0 / (1.0 + momentum_strength)
   - Calculate persistence_factor = min(1.0, intensity_coefficient / 2.0)

2. CONSOLIDATION ANALYSIS:
   - Extract structural_integrity from unit_b_results.structural_integrity
   - Extract stability_index from unit_b_results.gradient_dynamics.stability_index
   - Calculate consolidation_strength = (structural_integrity + stability_index) / 2.0
   - Analyze session data for consolidation patterns
   - Calculate breakout_probability = 1.0 - consolidation_strength
   - Extract consolidation duration from micro_timing_analysis (use 60 minutes default)
   - Calculate stability_rating = consolidation_strength * stability_index

3. FREQUENCY ANALYSIS:
   - Extract momentum transfer from unit_b_results.gradient_dynamics.momentum_transfer
   - Calculate dominant_freq = momentum_transfer / 2.0
   - Calculate harmonic_strength = min(1.0, dominant_freq * 1.2)  
   - Calculate frequency_stability = stability_index * 0.9

Use actual values from Units A and B results. Perform real mathematical calculations."""

    def _extract_dynamics_essentials(self, session_data: Dict[Any, Any], unit_a_results: Dict[Any, Any], 
                                   unit_b_results: Dict[Any, Any]) -> Dict[Any, Any]:
        """Extract only the essential data needed for Unit C dynamics calculations."""
        
        # Extract price data essentials for momentum calculations
        price_data = session_data.get("price_data", {})
        price_essentials = {
            "range": price_data.get("range", 100),
            "session_character": price_data.get("session_character", "neutral")
        }
        
        # Extract session metadata essentials
        session_metadata = session_data.get("session_metadata", {})
        metadata_essentials = {
            "session_type": session_metadata.get("session_type", "unknown"),
            "duration_minutes": session_metadata.get("duration_minutes", 300)
        }
        
        # Extract micro timing essentials for momentum analysis
        micro_timing = session_data.get("micro_timing_analysis", {})
        timing_essentials = {
            "phase_transitions": len(micro_timing.get("phase_transitions", [])),
            "consolidation_percentage": micro_timing.get("timing_metrics", {}).get("consolidation_percentage", 0.0),
            "expansion_percentage": micro_timing.get("timing_metrics", {}).get("expansion_percentage", 0.0),
            "average_phase_duration": micro_timing.get("timing_metrics", {}).get("average_phase_duration_minutes", 0.0)
        }
        
        return {
            "unit_a_results": unit_a_results,  # Keep Unit A results as-is (already processed)
            "unit_b_results": unit_b_results,  # Keep Unit B results as-is (already processed)
            "price_essentials": price_essentials,
            "session_essentials": metadata_essentials,
            "timing_essentials": timing_essentials
        }

    def process_advanced_dynamics(self, session_data: Dict[Any, Any], unit_a_results: Dict[Any, Any], 
                                unit_b_results: Dict[Any, Any]) -> Dict[Any, Any]:
        """Process Unit C advanced dynamics calculations using selective data extraction."""
        
        # Use selective extraction instead of massive JSON blobs
        essential_data = self._extract_dynamics_essentials(session_data, unit_a_results, unit_b_results)
        
        return self.process(essential_data)


def create_unit_c(api_key: str = None) -> UnitC:
    """Factory function to create Unit C processor with centralized API key management."""
    try:
        from .grok_api_client import GrokClientFactory
        from .schemas import UnitCResponse
    except ImportError:
        from grok_api_client import GrokClientFactory
        from schemas import UnitCResponse
    
    # 🏭 ARCHITECTURAL IMPROVEMENT: Use centralized factory instead of duplicated instantiation
    computational_unit = GrokClientFactory.create_unit_client("Unit C", UnitCResponse, api_key)
    return UnitC(computational_unit.client)