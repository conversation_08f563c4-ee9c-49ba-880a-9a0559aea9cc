#!/usr/bin/env python3
"""
Grok Unit D: Integration & Validation
Handles liquidity conservation, behavioral coherence, multi-timeframe compatibility, and final metrics.
Uses structured outputs for guaranteed JSON response format.
"""

try:
    from ...infrastructure.grok_client import StructuredComputationalUnit, GrokAPIClient
    from ...schemas import UnitDResponse
    from ...infrastructure.config import config, APIKeyError
except ImportError:
    from grok_api_client import StructuredComputationalUnit, GrokAPIClient
    from schemas import UnitDResponse
    from config import config, APIKeyError
from typing import Dict, Any


class UnitD(StructuredComputationalUnit):
    """Unit D: Integration & Validation processor with structured outputs."""
    
    def __init__(self, client: GrokAPIClient):
        super().__init__(client, "Unit D", UnitDResponse)
    
    def _load_system_prompt(self) -> str:
        return """You are a financial mathematics processor specializing in integration and validation calculations for trading sessions.

TASK: Integrate and validate results from Units A, B, and C to provide final system metrics.

INPUT STRUCTURE:
- unit_a_results: Foundation calculations (hybrid_volume, time_dilation_base, parameters_validated)
- unit_b_results: Energy & structure calculations (energy_accumulation, gradient_dynamics, structural_integrity)
- unit_c_results: Advanced dynamics (temporal_momentum, consolidation_analysis, frequency_analysis)
- session_data: Original trading session data

CALCULATIONS TO PERFORM:

1. VALIDATION RESULTS:
   - Extract alpha_t from unit_a_results.hybrid_volume.alpha_t
   - Extract gamma_base from unit_a_results.time_dilation_base.gamma_base
   - Extract energy_rate from unit_b_results.energy_accumulation.energy_rate
   - Extract structural_integrity from unit_b_results.structural_integrity
   - Extract momentum_strength from unit_c_results.temporal_momentum.momentum_strength
   - Calculate integration_score = (alpha_t + energy_rate + momentum_strength) / 3.0
   - Set consistency_check = true if all major values are within expected ranges
   - Set convergence_achieved = true if integration_score > 1.0
   - Create error_margins dictionary with percentage deviations for each unit

2. SYSTEM INTEGRATION:
   - Calculate cross_validation score from all unit results
   - Check units_synchronized = true if temporal values align
   - Set cross_validation_passed = true if all units show consistent behavior
   - Calculate final_confidence = min(1.0, integration_score * structural_integrity)
   - Generate recommendations list based on analysis results

3. QUALITY METRICS:
   - Calculate overall_accuracy = (integration_score + final_confidence) / 2.0
   - Calculate computational_efficiency based on processing times
   - Calculate data_consistency from cross-unit validation
   - Set reliability_score = min(1.0, overall_accuracy * data_consistency)

Use actual values from all previous units. Perform comprehensive integration analysis."""

    def _extract_validation_essentials(self, session_data: Dict[Any, Any], unit_a_results: Dict[Any, Any],
                                      unit_b_results: Dict[Any, Any], unit_c_results: Dict[Any, Any]) -> Dict[Any, Any]:
        """Extract only the essential data needed for Unit D validation calculations."""
        
        # Extract session metadata essentials for validation context
        session_metadata = session_data.get("session_metadata", {})
        metadata_essentials = {
            "session_type": session_metadata.get("session_type", "unknown"),
            "session_id": session_metadata.get("session_id", "unknown"),
            "date": session_metadata.get("date", "unknown")
        }
        
        # Extract price data essentials for validation bounds
        price_data = session_data.get("price_data", {})
        price_essentials = {
            "range": price_data.get("range", 100),
            "session_character": price_data.get("session_character", "neutral")
        }
        
        # Extract processing times for performance validation
        processing_times = {
            "unit_a": unit_a_results.get("processing_time_ms", 0),
            "unit_b": unit_b_results.get("processing_time_ms", 0), 
            "unit_c": unit_c_results.get("processing_time_ms", 0)
        }
        
        return {
            "unit_a_results": unit_a_results,  # Keep all unit results as-is (already processed)
            "unit_b_results": unit_b_results,
            "unit_c_results": unit_c_results,
            "session_essentials": metadata_essentials,
            "price_essentials": price_essentials,
            "pipeline_processing_times": processing_times
        }

    def process_integration_validation(self, session_data: Dict[Any, Any], unit_a_results: Dict[Any, Any],
                                     unit_b_results: Dict[Any, Any], unit_c_results: Dict[Any, Any]) -> Dict[Any, Any]:
        """Process Unit D integration and validation using selective data extraction."""
        
        # Use selective extraction instead of massive JSON blobs
        essential_data = self._extract_validation_essentials(session_data, unit_a_results, unit_b_results, unit_c_results)
        
        return self.process(essential_data)


def create_unit_d(api_key: str = None) -> UnitD:
    """Factory function to create Unit D processor with centralized API key management."""
    try:
        from .grok_api_client import GrokClientFactory
        from .schemas import UnitDResponse
    except ImportError:
        from grok_api_client import GrokClientFactory
        from schemas import UnitDResponse
    
    # 🏭 ARCHITECTURAL IMPROVEMENT: Use centralized factory instead of duplicated instantiation
    computational_unit = GrokClientFactory.create_unit_client("Unit D", UnitDResponse, api_key)
    return UnitD(computational_unit.client)