#!/usr/bin/env python3
"""
ML-Based Cascade Prediction System
Advanced machine learning extension to local pipeline for cascade timing and regime detection.

This system is INDEPENDENT of Grok 4 API - uses only local ML models:
- Hidden Markov Models for regime detection
- Gradient Boosting for cascade prediction  
- Time series analysis for pattern recognition
- Feature engineering from local pipeline variables

Architectural Note: Completely separated from Grok 4 to avoid AI confusion during troubleshooting.
"""

import numpy as np
import time
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from hmmlearn import hmm
import warnings
warnings.filterwarnings('ignore')


@dataclass
class MLPredictionResult:
    """Result structure for ML-based predictions."""
    predicted_cascade_time: float
    regime_classification: str
    regime_confidence: float
    feature_importance: Dict[str, float]
    model_confidence: float
    methodology: str
    processing_time_ms: float


@dataclass 
class RegimeState:
    """Regime detection state information."""
    current_regime: str
    regime_probability: float
    regime_stability: float
    transition_probability: float
    regime_duration: int


class HMMRegimeDetector:
    """
    Hidden Markov Model for market regime detection.
    Detects: consolidation, expansion, transition, breakout regimes.
    """
    
    def __init__(self, n_regimes: int = 4):
        self.n_regimes = n_regimes
        self.model = hmm.GaussianHMM(n_components=n_regimes, covariance_type="full")
        self.regime_labels = ["consolidation", "expansion", "transition", "breakout"]
        self.is_fitted = False
        self.feature_scaler = StandardScaler()
        
    def fit_regime_model(self, feature_sequences: List[np.ndarray]) -> None:
        """
        Fit HMM model to historical feature sequences.
        
        Args:
            feature_sequences: List of feature arrays from historical sessions
        """
        # Combine all sequences for training
        combined_features = np.vstack(feature_sequences)
        
        # Scale features
        scaled_features = self.feature_scaler.fit_transform(combined_features)
        
        # Fit HMM model
        self.model.fit(scaled_features)
        self.is_fitted = True
        
    def detect_regime(self, session_features: np.ndarray) -> RegimeState:
        """
        Detect current market regime from session features.
        
        Args:
            session_features: Feature array from current session
            
        Returns:
            RegimeState with regime classification and confidence
        """
        if not self.is_fitted:
            # Use default regime classification if model not fitted
            return self._default_regime_classification(session_features)
            
        # Scale features
        scaled_features = self.feature_scaler.transform(session_features.reshape(1, -1))
        
        # Predict regime probabilities
        regime_probs = self.model.predict_proba(scaled_features)[0]
        
        # Get most likely regime
        regime_idx = np.argmax(regime_probs)
        regime_name = self.regime_labels[regime_idx]
        regime_confidence = regime_probs[regime_idx]
        
        # Calculate regime stability (consistency of predictions)
        regime_stability = 1.0 - np.std(regime_probs)
        
        # Estimate transition probability
        transition_prob = 1.0 - regime_confidence
        
        return RegimeState(
            current_regime=regime_name,
            regime_probability=regime_confidence,
            regime_stability=regime_stability,
            transition_probability=transition_prob,
            regime_duration=1  # Would track over time in production
        )
    
    def _default_regime_classification(self, features: np.ndarray) -> RegimeState:
        """Default regime classification when model not fitted."""
        # Simple rule-based classification from features
        energy_rate = features[0] if len(features) > 0 else 1.0
        momentum_strength = features[1] if len(features) > 1 else 1.0
        
        if energy_rate > 2.0 and momentum_strength > 1.5:
            regime = "breakout"
            confidence = 0.8
        elif energy_rate > 1.5:
            regime = "expansion" 
            confidence = 0.7
        elif momentum_strength < 0.5:
            regime = "consolidation"
            confidence = 0.75
        else:
            regime = "transition"
            confidence = 0.6
            
        return RegimeState(
            current_regime=regime,
            regime_probability=confidence,
            regime_stability=0.7,
            transition_probability=1.0 - confidence,
            regime_duration=1
        )


class MLCascadePredictor:
    """
    Machine Learning Cascade Prediction System.
    Uses gradient boosting and feature engineering for cascade timing prediction.
    """
    
    def __init__(self):
        self.cascade_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        )
        self.regime_detector = HMMRegimeDetector()
        self.feature_scaler = StandardScaler()
        self.is_fitted = False
        
        # Feature importance tracking
        self.feature_names = [
            "energy_rate", "momentum_strength", "alpha_t", "gamma_base", 
            "structural_integrity", "volatility_proxy", "session_duration",
            "range_normalized", "complexity_score"
        ]
    
    def extract_ml_features(self, local_pipeline_results: Dict[str, Any]) -> np.ndarray:
        """
        Extract ML features from local pipeline results.
        
        Args:
            local_pipeline_results: Results from LocalPipeline
            
        Returns:
            Feature array for ML models
        """
        # Extract core variables from pipeline results
        unit_a = local_pipeline_results.get('unit_a', {}).calculations or {}
        unit_b = local_pipeline_results.get('unit_b', {}).calculations or {}
        unit_c = local_pipeline_results.get('unit_c', {}).calculations or {}
        
        # Core mathematical variables
        energy_rate = unit_b.get('energy_accumulation', {}).get('energy_rate', 1.0)
        momentum_strength = unit_c.get('temporal_momentum', {}).get('momentum_strength', 1.0)
        alpha_t = unit_a.get('hybrid_volume', {}).get('alpha_t', 1.0)
        gamma_base = unit_a.get('time_dilation_base', {}).get('gamma_base', 1.5)
        structural_integrity = unit_b.get('structural_integrity', 0.5)
        
        # Derived features for ML
        volatility_proxy = energy_rate * momentum_strength
        session_duration = 300.0  # Default session duration
        range_normalized = unit_a.get('time_dilation_base', {}).get('range_normalized', 1.0)
        complexity_score = unit_a.get('foundation_calculations', {}).get('complexity_score', 1.0)
        
        features = np.array([
            energy_rate, momentum_strength, alpha_t, gamma_base,
            structural_integrity, volatility_proxy, session_duration,
            range_normalized, complexity_score
        ])
        
        return features
    
    def train_cascade_model(self, training_data: List[Dict[str, Any]]) -> None:
        """
        Train cascade prediction model on historical data.
        
        Args:
            training_data: List of {"features": array, "cascade_time": float}
        """
        if not training_data:
            print("⚠️ No training data provided - using default model")
            return
            
        # Extract features and targets
        X = np.array([data["features"] for data in training_data])
        y = np.array([data["cascade_time"] for data in training_data])
        
        # Scale features
        X_scaled = self.feature_scaler.fit_transform(X)
        
        # Train cascade model
        self.cascade_model.fit(X_scaled, y)
        
        # Train regime detector with feature sequences
        feature_sequences = [data["features"].reshape(-1, len(self.feature_names)) for data in training_data]
        self.regime_detector.fit_regime_model(feature_sequences)
        
        self.is_fitted = True
        print(f"✅ ML models trained on {len(training_data)} samples")
    
    def predict_cascade(self, local_pipeline_results: Dict[str, Any]) -> MLPredictionResult:
        """
        Predict cascade timing using ML models.
        
        Args:
            local_pipeline_results: Results from LocalPipeline
            
        Returns:
            MLPredictionResult with cascade prediction and regime analysis
        """
        start_time = time.time()
        
        # Extract features
        features = self.extract_ml_features(local_pipeline_results)
        
        # Detect market regime
        regime_state = self.regime_detector.detect_regime(features)
        
        # Predict cascade timing
        if self.is_fitted:
            # Use trained model
            features_scaled = self.feature_scaler.transform(features.reshape(1, -1))
            predicted_time = self.cascade_model.predict(features_scaled)[0]
            
            # Get feature importance
            feature_importance = dict(zip(
                self.feature_names, 
                self.cascade_model.feature_importances_
            ))
            
            model_confidence = 0.85
            
        else:
            # Fallback prediction based on regime and features
            predicted_time = self._fallback_cascade_prediction(features, regime_state)
            
            # Default feature importance
            feature_importance = {
                "energy_rate": 0.25,
                "momentum_strength": 0.20,
                "structural_integrity": 0.15,
                "alpha_t": 0.12,
                "gamma_base": 0.10,
                "volatility_proxy": 0.08,
                "session_duration": 0.05,
                "range_normalized": 0.03,
                "complexity_score": 0.02
            }
            
            model_confidence = 0.7
        
        processing_time = (time.time() - start_time) * 1000
        
        return MLPredictionResult(
            predicted_cascade_time=predicted_time,
            regime_classification=regime_state.current_regime,
            regime_confidence=regime_state.regime_probability,
            feature_importance=feature_importance,
            model_confidence=model_confidence,
            methodology="ml_cascade_prediction_with_hmm_regime_detection",
            processing_time_ms=processing_time
        )
    
    def _fallback_cascade_prediction(self, features: np.ndarray, regime_state: RegimeState) -> float:
        """Fallback cascade prediction when model not trained."""
        energy_rate = features[0]
        momentum_strength = features[1]
        structural_integrity = features[4]
        
        # Regime-based cascade timing
        regime_factors = {
            "breakout": 0.5,      # Fast cascade in breakout
            "expansion": 0.7,     # Moderate cascade in expansion
            "transition": 1.0,    # Standard cascade in transition
            "consolidation": 1.5  # Slower cascade in consolidation
        }
        
        regime_factor = regime_factors.get(regime_state.current_regime, 1.0)
        
        # Base prediction from mathematical variables
        base_prediction = 5.0 - (energy_rate * 2.0) + (structural_integrity * 3.0)
        
        # Apply regime adjustment
        cascade_time = base_prediction * regime_factor
        
        # Clamp to reasonable range
        return max(0.5, min(15.0, cascade_time))
    
    def get_model_status(self) -> Dict[str, Any]:
        """Get ML model training and performance status."""
        return {
            "cascade_model_fitted": self.is_fitted,
            "regime_detector_fitted": self.regime_detector.is_fitted,
            "feature_count": len(self.feature_names),
            "supported_regimes": self.regime_detector.regime_labels,
            "model_type": "gradient_boosting_with_hmm",
            "ready_for_prediction": True
        }


def create_ml_cascade_predictor() -> MLCascadePredictor:
    """Factory function to create ML cascade predictor."""
    return MLCascadePredictor()


if __name__ == "__main__":
    # Test ML cascade predictor
    print("🧪 TESTING ML CASCADE PREDICTOR")
    print("=" * 50)
    
    # Create predictor
    ml_predictor = create_ml_cascade_predictor()
    
    # Mock local pipeline results for testing
    mock_results = {
        'unit_a': type('obj', (object,), {
            'calculations': {
                'hybrid_volume': {'alpha_t': 0.578},
                'time_dilation_base': {'gamma_base': 1.17, 'range_normalized': 1.2},
                'foundation_calculations': {'complexity_score': 1.5}
            }
        })(),
        'unit_b': type('obj', (object,), {
            'calculations': {
                'energy_accumulation': {'energy_rate': 1.045},
                'structural_integrity': 0.91
            }
        })(),
        'unit_c': type('obj', (object,), {
            'calculations': {
                'temporal_momentum': {'momentum_strength': 0.612}
            }
        })()
    }
    
    # Test prediction
    result = ml_predictor.predict_cascade(mock_results)
    
    print(f"🎯 ML Prediction Results:")
    print(f"  Cascade Time: {result.predicted_cascade_time:.2f} minutes")
    print(f"  Regime: {result.regime_classification}")
    print(f"  Regime Confidence: {result.regime_confidence:.2f}")
    print(f"  Model Confidence: {result.model_confidence:.2f}")
    print(f"  Processing Time: {result.processing_time_ms:.2f}ms")
    
    print(f"\n📊 Feature Importance:")
    for feature, importance in sorted(result.feature_importance.items(), 
                                    key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {feature}: {importance:.3f}")
    
    # Model status
    status = ml_predictor.get_model_status()
    print(f"\n🔧 Model Status:")
    print(f"  Cascade Model Fitted: {status['cascade_model_fitted']}")
    print(f"  Regime Detector Fitted: {status['regime_detector_fitted']}")
    print(f"  Supported Regimes: {', '.join(status['supported_regimes'])}")