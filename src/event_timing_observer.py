#!/usr/bin/env python3
"""
Event Timing Observer - Ground Truth Recording System (Event-Focused)

Records objective EVENT TIMING facts without mathematical interpretation.
Addresses the epistemic closure problem by capturing WHEN events occurred
independent of mathematical models for true out-of-sample validation.

Key Principle: Record WHEN events actually happened, not what we calculated would happen.

Paradigm Shift: FROM price prediction TO event timing prediction validation
"""

import json
import math
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data


@dataclass
class EventTimingFacts:
    """Objective event timing facts recorded without interpretation"""
    session_id: str
    timestamp: str
    
    # Event Timing Facts - WHEN events occurred (binary)
    cascade_occurred: bool                    # True if major cascade happened
    expansion_occurred: bool                  # True if expansion phase occurred
    consolidation_break_occurred: bool        # True if consolidation was broken
    liquidity_sweep_occurred: bool           # True if liquidity sweep happened
    
    # Timing Measurements - pure minutes from session start
    time_to_first_cascade: Optional[float]    # Minutes to first major cascade
    time_to_expansion_phase: Optional[float]  # Minutes to expansion initiation
    time_to_consolidation_break: Optional[float]  # Minutes to range break
    time_to_session_high: Optional[float]     # Minutes to session high formation
    time_to_session_low: Optional[float]      # Minutes to session low formation
    
    # Event Sequence Facts - what order events occurred
    first_event_type: str                     # "cascade", "expansion", "consolidation_break"
    dominant_session_phase: str               # "cascade_dominant", "expansion_dominant", "consolidation_dominant"
    event_sequence: List[str]                 # Chronological order of events
    
    # Session Duration and Structure
    session_duration_minutes: float
    early_action_phase: bool                  # Major events in first 25% of session
    
    # Price Reference Points (for timing validation only)
    session_open: float
    session_high: float
    session_low: float
    session_close: float
    session_range: float                      # For threshold validation


@dataclass
class SystemTimingPredictions:
    """System's event timing predictions for comparison"""
    predicted_cascade_timing: Optional[float]
    predicted_expansion_timing: Optional[float]
    predicted_consolidation_break_timing: Optional[float]
    predicted_dominant_phase: str
    prediction_confidence: float
    prediction_method: str


class EventTimingObserver:
    """
    Records objective event timing facts without mathematical interpretation.
    
    This class addresses the epistemic closure problem by capturing
    WHEN events actually occurred independent of our mathematical models.
    
    Focus: Event timing validation, not price prediction validation
    """
    
    def __init__(self, 
                 cascade_threshold_points: float = 50.0,
                 expansion_threshold_points: float = 30.0,
                 major_move_threshold_points: float = 25.0,
                 timing_precision_minutes: float = 1.0):
        """
        Initialize the event timing observer with objective thresholds.
        
        Args:
            cascade_threshold_points: Points defining "major cascade"
            expansion_threshold_points: Points defining "expansion phase"
            major_move_threshold_points: Points defining "major movement"
            timing_precision_minutes: Precision for timing measurements
        """
        self.cascade_threshold = cascade_threshold_points
        self.expansion_threshold = expansion_threshold_points
        self.major_move_threshold = major_move_threshold_points
        self.timing_precision = timing_precision_minutes
        
        # Storage for event timing facts
        self.recorded_timing_facts: List[EventTimingFacts] = []
        
    def record_session_event_timing(self, session_data: Dict[str, Any]) -> EventTimingFacts:
        """
        Record objective event timing facts about a trading session.
        
        Args:
            session_data: Raw session data with price movements and timestamps
            
        Returns:
            EventTimingFacts object with objective timing observations
        """
        
        # Extract session metadata and price data
        session_info = self._extract_session_info(session_data)
        price_movements = self._extract_price_movements(session_data)
        
        if not session_info or not price_movements:
            raise ValueError("Cannot extract session timing data")
        
        # Analyze event timing from price movements
        timing_analysis = self._analyze_event_timing(price_movements, session_info, session_data)
        
        # Create event timing facts record
        facts = EventTimingFacts(
            session_id=self._extract_session_id(session_data),
            timestamp=datetime.now().isoformat(),
            
            # Event occurrence facts
            cascade_occurred=timing_analysis['cascade_occurred'],
            expansion_occurred=timing_analysis['expansion_occurred'],
            consolidation_break_occurred=timing_analysis['consolidation_break_occurred'],
            liquidity_sweep_occurred=timing_analysis['liquidity_sweep_occurred'],
            
            # Timing measurements
            time_to_first_cascade=timing_analysis['time_to_first_cascade'],
            time_to_expansion_phase=timing_analysis['time_to_expansion_phase'],
            time_to_consolidation_break=timing_analysis['time_to_consolidation_break'],
            time_to_session_high=timing_analysis['time_to_session_high'],
            time_to_session_low=timing_analysis['time_to_session_low'],
            
            # Event sequence analysis
            first_event_type=timing_analysis['first_event_type'],
            dominant_session_phase=timing_analysis['dominant_session_phase'],
            event_sequence=timing_analysis['event_sequence'],
            
            # Session structure
            session_duration_minutes=session_info['duration_minutes'],
            early_action_phase=timing_analysis['early_action_phase'],
            
            # Price reference (for validation only)
            session_open=session_info['open'],
            session_high=session_info['high'],
            session_low=session_info['low'],
            session_close=session_info['close'],
            session_range=session_info['range']
        )
        
        # Store the facts
        self.recorded_timing_facts.append(facts)
        
        return facts
    
    def _extract_session_info(self, session_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract basic session information for timing analysis"""
        
        # Try price_data field
        if 'price_data' in session_data:
            pd = session_data['price_data']
            if all(k in pd for k in ['open', 'high', 'low', 'close']):
                return {
                    'open': float(pd['open']),
                    'high': float(pd['high']),
                    'low': float(pd['low']),
                    'close': float(pd['close']),
                    'range': float(pd.get('range', pd['high'] - pd['low'])),
                    'duration_minutes': self._extract_session_duration(session_data)
                }
        
        return None
    
    def _extract_price_movements(self, session_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract price movements with timestamps for timing analysis"""
        
        # Look for price_movements array
        if 'price_movements' in session_data:
            return session_data['price_movements']
        
        # If no explicit movements, try to infer from OHLC
        price_data = session_data.get('price_data', {})
        if price_data:
            # Create synthetic movements for basic analysis
            movements = []
            
            # Session open (time 0)
            movements.append({
                'timestamp': '00:00:00',  # Session start
                'price': price_data.get('open', 0),
                'action': 'open',
                'context': 'session_open'
            })
            
            # Session high (estimate mid-session)
            movements.append({
                'timestamp': '30:00:00',  # Estimate
                'price': price_data.get('high', 0),
                'action': 'touch',
                'context': 'session_high_formation'
            })
            
            # Session low (estimate)
            movements.append({
                'timestamp': '45:00:00',  # Estimate
                'price': price_data.get('low', 0),
                'action': 'touch',
                'context': 'session_low_formation'
            })
            
            return movements
        
        return []
    
    def _analyze_event_timing(self, price_movements: List[Dict[str, Any]], session_info: Dict[str, Any], session_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze timing of market events from price movements"""
        
        session_range = session_info['range']
        session_open = session_info['open']
        session_high = session_info['high']
        session_low = session_info['low']
        duration = session_info['duration_minutes']
        
        analysis = {
            'cascade_occurred': False,
            'expansion_occurred': False,
            'consolidation_break_occurred': False,
            'liquidity_sweep_occurred': False,
            'time_to_first_cascade': None,
            'time_to_expansion_phase': None,
            'time_to_consolidation_break': None,
            'time_to_session_high': None,
            'time_to_session_low': None,
            'first_event_type': 'none',
            'dominant_session_phase': 'consolidation_dominant',
            'event_sequence': [],
            'early_action_phase': False
        }
        
        # Analyze each price movement for timing
        events_timeline = []
        
        # Get session start time once for all calculations
        session_start_time = self._extract_session_start_time(session_data)
        
        for movement in price_movements:
            timestamp = movement.get('timestamp', '00:00:00')
            price = movement.get('price', 0)
            action = movement.get('action', '')
            context = movement.get('context', '')
            
            # Calculate minutes from session start  
            minutes_from_start = self._timestamp_to_minutes(timestamp, session_start_time)
            
            # Detect event types
            price_move_size = abs(price - session_open)
            
            # Cascade detection
            if price_move_size >= self.cascade_threshold and not analysis['cascade_occurred']:
                analysis['cascade_occurred'] = True
                analysis['time_to_first_cascade'] = minutes_from_start
                events_timeline.append(('cascade', minutes_from_start))
                analysis['event_sequence'].append('cascade')
            
            # Expansion detection
            if price_move_size >= self.expansion_threshold and not analysis['expansion_occurred']:
                analysis['expansion_occurred'] = True
                analysis['time_to_expansion_phase'] = minutes_from_start
                events_timeline.append(('expansion', minutes_from_start))
                analysis['event_sequence'].append('expansion')
            
            # Consolidation break detection
            if 'break' in action.lower():
                analysis['consolidation_break_occurred'] = True
                if analysis['time_to_consolidation_break'] is None:
                    analysis['time_to_consolidation_break'] = minutes_from_start
                    events_timeline.append(('consolidation_break', minutes_from_start))
                    analysis['event_sequence'].append('consolidation_break')
            
            # Liquidity sweep detection
            if 'sweep' in context.lower() or 'liquidity' in context.lower():
                analysis['liquidity_sweep_occurred'] = True
                events_timeline.append(('liquidity_sweep', minutes_from_start))
                analysis['event_sequence'].append('liquidity_sweep')
            
            # Session high/low timing
            if abs(price - session_high) < 1.0:  # Within 1 point of session high
                analysis['time_to_session_high'] = minutes_from_start
            
            if abs(price - session_low) < 1.0:  # Within 1 point of session low
                analysis['time_to_session_low'] = minutes_from_start
        
        # Determine first event type
        if events_timeline:
            events_timeline.sort(key=lambda x: x[1])  # Sort by time
            analysis['first_event_type'] = events_timeline[0][0]
        
        # Determine dominant session phase
        if session_range >= self.cascade_threshold:
            analysis['dominant_session_phase'] = 'cascade_dominant'
        elif session_range >= self.expansion_threshold:
            analysis['dominant_session_phase'] = 'expansion_dominant'
        else:
            analysis['dominant_session_phase'] = 'consolidation_dominant'
        
        # Early action detection
        early_threshold = duration * 0.25  # First 25% of session
        early_events = [event for event in events_timeline if event[1] <= early_threshold]
        analysis['early_action_phase'] = len(early_events) > 0
        
        return analysis
    
    def _timestamp_to_minutes(self, timestamp: str, session_start: str = "09:30:00") -> float:
        """Convert timestamp to minutes from session start"""
        try:
            # Handle different timestamp formats
            if ':' in timestamp:
                # Parse event timestamp
                event_parts = timestamp.split(':')
                if len(event_parts) >= 2:
                    event_hours = int(event_parts[0])
                    event_minutes = int(event_parts[1])
                    event_total_minutes = event_hours * 60 + event_minutes
                
                # Parse session start timestamp
                start_parts = session_start.split(':')
                if len(start_parts) >= 2:
                    start_hours = int(start_parts[0])
                    start_minutes = int(start_parts[1])
                    start_total_minutes = start_hours * 60 + start_minutes
                    
                    # Calculate minutes from session start
                    minutes_from_start = event_total_minutes - start_total_minutes
                    
                    # Handle case where event crosses midnight (rare but possible)
                    if minutes_from_start < 0:
                        minutes_from_start += 24 * 60  # Add 24 hours
                    
                    return float(minutes_from_start)
            
            return 0.0
        except:
            return 0.0
    
    def _extract_session_start_time_from_info(self, session_info: Dict[str, Any]) -> str:
        """Extract session start time from session_info dict"""
        # Default NYAM start time
        return "09:30:00"
    
    def _extract_session_start_time(self, session_data: Dict[str, Any]) -> str:
        """Extract session start time for relative timing calculations"""
        
        # Try to get actual session start time from metadata
        if 'session_metadata' in session_data:
            metadata = session_data['session_metadata']
            if 'start_time' in metadata:
                start_time = metadata['start_time']
                # Handle different formats like "09:30:00 ET" vs "09:30:00"
                if ' ' in start_time:
                    start_time = start_time.split(' ')[0]  # Remove timezone
                return start_time
        
        # Default NYAM start time if not found
        return "09:30:00"
    
    def _extract_session_duration(self, session_data: Dict[str, Any]) -> float:
        """Extract session duration in minutes"""
        
        # Try session metadata
        if 'session_metadata' in session_data:
            metadata = session_data['session_metadata']
            if 'duration_minutes' in metadata:
                return float(metadata['duration_minutes'])
        
        # Default assumptions
        return 90.0  # Default NYAM session duration
    
    def _extract_session_id(self, session_data: Dict[str, Any]) -> str:
        """Extract or generate session identifier"""
        
        # Try session metadata
        if 'session_metadata' in session_data:
            metadata = session_data['session_metadata']
            if 'session_id' in metadata:
                return metadata['session_id']
        
        # Generate default
        timestamp = datetime.now().strftime('%Y_%m_%d_%H%M')
        return f"session_{timestamp}"
    
    def get_event_timing_test_result(self, 
                                   session_data: Dict[str, Any],
                                   system_predictions: Optional[SystemTimingPredictions] = None) -> Dict[str, Any]:
        """
        Opus 4's timing-focused test: Binary event timing validation.
        
        Returns:
            Dictionary with ground truth vs system timing comparison
        """
        
        facts = self.record_session_event_timing(session_data)
        
        # Extract system timing predictions if available
        system_cascade_timing = None
        system_expansion_timing = None
        system_dominant_phase = "unknown"
        
        if system_predictions:
            system_cascade_timing = system_predictions.predicted_cascade_timing
            system_expansion_timing = system_predictions.predicted_expansion_timing
            system_dominant_phase = system_predictions.predicted_dominant_phase
        else:
            # Try to extract from session data
            system_data = self._extract_system_timing_predictions(session_data)
            if system_data:
                system_cascade_timing = system_data.get('cascade_timing')
                system_expansion_timing = system_data.get('expansion_timing')
                system_dominant_phase = system_data.get('dominant_phase', 'unknown')
        
        return {
            'ground_truth_timing': {
                'cascade_occurred': facts.cascade_occurred,
                'time_to_first_cascade': facts.time_to_first_cascade,
                'expansion_occurred': facts.expansion_occurred,
                'time_to_expansion_phase': facts.time_to_expansion_phase,
                'consolidation_break_occurred': facts.consolidation_break_occurred,
                'dominant_session_phase': facts.dominant_session_phase,
                'first_event_type': facts.first_event_type,
                'early_action_phase': facts.early_action_phase
            },
            'system_timing_predictions': {
                'predicted_cascade_timing': system_cascade_timing,
                'predicted_expansion_timing': system_expansion_timing,
                'predicted_dominant_phase': system_dominant_phase
            },
            'timing_validation_result': {
                'cascade_timing_accuracy': self._calculate_timing_accuracy(
                    facts.time_to_first_cascade, system_cascade_timing
                ),
                'expansion_timing_accuracy': self._calculate_timing_accuracy(
                    facts.time_to_expansion_phase, system_expansion_timing
                ),
                'dominant_phase_accuracy': facts.dominant_session_phase == system_dominant_phase,
                'overall_timing_quality': self._assess_overall_timing_quality(facts, system_predictions),
                'potential_timing_epistemic_closure': self._detect_timing_epistemic_closure(facts, system_predictions)
            }
        }
    
    def _extract_system_timing_predictions(self, session_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract system timing predictions from session data"""
        
        # Look for existing timing predictions in enhanced data
        if 'grok_enhanced_calculations' in session_data:
            # This would contain system's timing predictions
            # For now, return None as we'll implement this based on actual data structure
            pass
        
        return None
    
    def _calculate_timing_accuracy(self, actual_timing: Optional[float], predicted_timing: Optional[float]) -> str:
        """Calculate timing prediction accuracy"""
        
        if actual_timing is None or predicted_timing is None:
            return 'unable_to_validate'
        
        timing_error = abs(actual_timing - predicted_timing)
        
        if timing_error <= 2.0:  # Within 2 minutes
            return 'excellent'
        elif timing_error <= 5.0:  # Within 5 minutes
            return 'good'
        elif timing_error <= 10.0:  # Within 10 minutes
            return 'moderate'
        else:
            return 'poor'
    
    def _assess_overall_timing_quality(self, facts: EventTimingFacts, predictions: Optional[SystemTimingPredictions]) -> str:
        """Assess overall timing prediction quality"""
        
        if not predictions:
            return 'no_predictions_available'
        
        # Calculate overall accuracy based on multiple timing factors
        accuracy_scores = []
        
        if facts.time_to_first_cascade and predictions.predicted_cascade_timing:
            cascade_accuracy = self._calculate_timing_accuracy(facts.time_to_first_cascade, predictions.predicted_cascade_timing)
            accuracy_scores.append(cascade_accuracy)
        
        if facts.time_to_expansion_phase and predictions.predicted_expansion_timing:
            expansion_accuracy = self._calculate_timing_accuracy(facts.time_to_expansion_phase, predictions.predicted_expansion_timing)
            accuracy_scores.append(expansion_accuracy)
        
        if not accuracy_scores:
            return 'insufficient_data'
        
        # Determine overall quality
        excellent_count = accuracy_scores.count('excellent')
        good_count = accuracy_scores.count('good')
        
        if excellent_count / len(accuracy_scores) >= 0.7:
            return 'excellent'
        elif (excellent_count + good_count) / len(accuracy_scores) >= 0.6:
            return 'good'
        else:
            return 'needs_improvement'
    
    def _detect_timing_epistemic_closure(self, facts: EventTimingFacts, predictions: Optional[SystemTimingPredictions]) -> bool:
        """Detect timing-based epistemic closure issues"""
        
        if not predictions:
            return False
        
        # Check for major timing discrepancies that suggest epistemic closure
        major_discrepancies = 0
        
        if facts.time_to_first_cascade and predictions.predicted_cascade_timing:
            error = abs(facts.time_to_first_cascade - predictions.predicted_cascade_timing)
            if error > 15.0:  # More than 15 minutes off
                major_discrepancies += 1
        
        if facts.time_to_expansion_phase and predictions.predicted_expansion_timing:
            error = abs(facts.time_to_expansion_phase - predictions.predicted_expansion_timing)
            if error > 15.0:  # More than 15 minutes off
                major_discrepancies += 1
        
        # Phase mismatch
        if facts.dominant_session_phase != predictions.predicted_dominant_phase:
            major_discrepancies += 1
        
        return major_discrepancies >= 2  # Multiple major discrepancies suggest epistemic closure
    
    def save_event_timing_facts(self, output_file: str) -> None:
        """Save recorded event timing facts to file"""
        
        facts_data = {
            'event_timing_metadata': {
                'recording_timestamp': datetime.now().isoformat(),
                'observer_version': '2.0.0_event_timing_focused',
                'paradigm': 'event_timing_prediction_validation',
                'thresholds': {
                    'cascade_threshold_points': self.cascade_threshold,
                    'expansion_threshold_points': self.expansion_threshold,
                    'major_move_threshold_points': self.major_move_threshold,
                    'timing_precision_minutes': self.timing_precision
                },
                'total_sessions_recorded': len(self.recorded_timing_facts)
            },
            'recorded_timing_facts': [
                {
                    'session_id': fact.session_id,
                    'timestamp': fact.timestamp,
                    'event_timing_facts': {
                        'cascade_occurred': fact.cascade_occurred,
                        'time_to_first_cascade': fact.time_to_first_cascade,
                        'expansion_occurred': fact.expansion_occurred,
                        'time_to_expansion_phase': fact.time_to_expansion_phase,
                        'consolidation_break_occurred': fact.consolidation_break_occurred,
                        'time_to_consolidation_break': fact.time_to_consolidation_break,
                        'liquidity_sweep_occurred': fact.liquidity_sweep_occurred
                    },
                    'session_structure': {
                        'first_event_type': fact.first_event_type,
                        'dominant_session_phase': fact.dominant_session_phase,
                        'event_sequence': fact.event_sequence,
                        'early_action_phase': fact.early_action_phase,
                        'session_duration_minutes': fact.session_duration_minutes
                    },
                    'reference_prices': {
                        'open': fact.session_open,
                        'high': fact.session_high,
                        'low': fact.session_low,
                        'close': fact.session_close,
                        'range': fact.session_range
                    }
                }
                for fact in self.recorded_timing_facts
            ]
        }
        
        save_json_data(facts_data, output_file)


def create_event_timing_observer(cascade_threshold: float = 50.0,
                                expansion_threshold: float = 30.0,
                                major_move_threshold: float = 25.0) -> EventTimingObserver:
    """Create an event timing observer with specified thresholds"""
    return EventTimingObserver(
        cascade_threshold_points=cascade_threshold,
        expansion_threshold_points=expansion_threshold,
        major_move_threshold_points=major_move_threshold
    )


if __name__ == "__main__":
    # Quick test of the event timing observer
    print("⏰ EVENT TIMING OBSERVER - GROUND TRUTH RECORDING TEST")
    print("=" * 60)
    
    observer = create_event_timing_observer()
    
    # Test with NYAM session
    try:
        sample_session = load_json_data('NYAM_Lvl-1_2025_07_25.json')
        
        print("📊 Recording event timing facts...")
        facts = observer.record_session_event_timing(sample_session)
        
        print(f"✅ Event Timing Facts Recorded:")
        print(f"   Cascade Occurred: {facts.cascade_occurred}")
        print(f"   Time to First Cascade: {facts.time_to_first_cascade} minutes")
        print(f"   Expansion Occurred: {facts.expansion_occurred}")
        print(f"   Time to Expansion: {facts.time_to_expansion_phase} minutes")
        print(f"   Dominant Phase: {facts.dominant_session_phase}")
        print(f"   First Event Type: {facts.first_event_type}")
        print(f"   Early Action Phase: {facts.early_action_phase}")
        
        # Run timing validation test
        print(f"\n🎯 Event Timing Validation Test:")
        test_result = observer.get_event_timing_test_result(sample_session)
        
        timing_validation = test_result['timing_validation_result']
        print(f"   Overall Timing Quality: {timing_validation['overall_timing_quality']}")
        print(f"   Timing Epistemic Closure: {timing_validation['potential_timing_epistemic_closure']}")
        
        # Save the facts
        observer.save_event_timing_facts('event_timing_ground_truth_test.json')
        print(f"\n📁 Event timing facts saved to: event_timing_ground_truth_test.json")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ Event timing observer test complete")