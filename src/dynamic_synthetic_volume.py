#!/usr/bin/env python3
"""
Dynamic Synthetic Volume Calculator
Replaces static v_synthetic formula with ICT event-responsive calculation
implementing Grok 4's recommendation: v_s = Δp × (1 + w_FVG + m_sweep)
"""

import json
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import sys
sys.path.append('.')
from src.utils import load_json_data
from src.cache_manager import get_unified_cache

@dataclass
class MarketEvent:
    """Individual market event for synthetic volume calculation"""
    timestamp: str
    price: float
    action: str
    context: str
    minutes_from_start: float
    event_type: str
    magnitude: float

@dataclass
class FVGEvent:
    """Fair Value Gap event details"""
    timestamp: str
    gap_width: float
    gap_type: str
    significance: str
    normalized_width: float  # w_FVG = gap_width / session_range

@dataclass
class LiquiditySweep:
    """Liquidity sweep event details"""
    timestamp: str
    sweep_magnitude: float
    direction: str
    liquidity_type: str
    price_displacement: float

@dataclass
class SyntheticVolumeComponents:
    """Components of dynamic synthetic volume calculation"""
    price_displacement: float  # Δp
    fvg_weight: float         # w_FVG
    sweep_magnitude: float    # m_sweep
    calculated_volume: float  # Final v_s
    base_volume_static: float # Original static volume for comparison
    improvement_factor: float # Dynamic vs static ratio

class DynamicSyntheticVolumeCalculator:
    """
    Calculate event-responsive synthetic volume using ICT market events
    instead of static mathematical formula.
    """
    
    def __init__(self):
        # Static formula for comparison
        self.static_volume = 100.0 * (1 + 0.35 * np.exp(-10.0/50.0))  # ≈128.66
        
        # Event type classifications for ICT analysis
        self.fvg_keywords = ['fpfvg', 'fvg', 'gap', 'imbalance', 'void']
        self.liquidity_keywords = ['liquidity', 'sweep', 'taken', 'break', 'cleared']
        self.cascade_keywords = ['cascade', 'break', 'formation', 'low', 'high']
        
        # 🚀 UNIFIED CACHE: Replace local cache with unified system
        self.unified_cache = get_unified_cache()
        
    def _timestamp_to_minutes(self, timestamp_str: str, session_start: str = "09:30:00") -> float:
        """Convert timestamp to minutes from session start"""
        
        # Clean session_start time (remove timezone if present)
        clean_session_start = session_start.split(' ')[0]
        
        # Parse the timestamps
        event_time = datetime.strptime(timestamp_str, "%H:%M:%S").time()
        start_time = datetime.strptime(clean_session_start, "%H:%M:%S").time()
        
        # Convert to total minutes from midnight
        event_total_minutes = event_time.hour * 60 + event_time.minute + event_time.second / 60.0
        start_total_minutes = start_time.hour * 60 + start_time.minute + start_time.second / 60.0
        
        # Return minutes from session start
        return event_total_minutes - start_total_minutes
    
    def extract_market_events(self, session_data: dict) -> List[MarketEvent]:
        """Extract and classify market events from session data"""
        
        price_movements = session_data.get('price_movements', [])
        session_metadata = session_data.get('session_metadata', {})
        session_start = session_metadata.get('start_time', '09:30:00')
        
        events = []
        
        for movement in price_movements:
            timestamp = movement['timestamp']
            minutes_from_start = self._timestamp_to_minutes(timestamp, session_start)
            context = movement['context'].lower()
            price = movement['price']
            action = movement['action']
            
            # Classify event type and calculate magnitude
            event_type = 'general'
            magnitude = 0.0
            
            if any(keyword in context for keyword in self.fvg_keywords):
                event_type = 'fvg_event'
                magnitude = 1.0  # Base FVG magnitude
            elif any(keyword in context for keyword in self.liquidity_keywords):
                event_type = 'liquidity_sweep'
                magnitude = 2.0  # Higher magnitude for liquidity events
            elif any(keyword in context for keyword in self.cascade_keywords):
                event_type = 'cascade_trigger'
                magnitude = 3.0  # Highest magnitude for cascade events
            
            events.append(MarketEvent(
                timestamp=timestamp,
                price=price,
                action=action,
                context=movement['context'],
                minutes_from_start=minutes_from_start,
                event_type=event_type,
                magnitude=magnitude
            ))
        
        # Sort events by time
        events.sort(key=lambda x: x.minutes_from_start)
        
        return events
    
    def extract_fvg_events(self, session_data: dict, events: List[MarketEvent]) -> List[FVGEvent]:
        """Extract Fair Value Gap events for w_FVG calculation"""
        
        session_range = session_data.get('price_data', {}).get('range', 100.0)
        fvg_events = []
        
        for event in events:
            if event.event_type == 'fvg_event':
                # Estimate FVG width from context or use default
                # In real implementation, this would parse actual FVG data
                if 'premium' in event.context.lower():
                    gap_width = session_range * 0.15  # Estimate 15% of range for premium FVG
                else:
                    gap_width = session_range * 0.10  # Default 10% of range
                
                normalized_width = gap_width / session_range
                
                fvg_events.append(FVGEvent(
                    timestamp=event.timestamp,
                    gap_width=gap_width,
                    gap_type='bearish' if 'low' in event.context else 'bullish',
                    significance='high' if 'formation' in event.context else 'medium',
                    normalized_width=normalized_width
                ))
        
        return fvg_events
    
    def extract_liquidity_sweeps(self, session_data: dict, events: List[MarketEvent]) -> List[LiquiditySweep]:
        """Extract liquidity sweep events for m_sweep calculation"""
        
        session_open = session_data.get('price_data', {}).get('open', 0.0)
        liquidity_sweeps = []
        
        for event in events:
            if event.event_type == 'liquidity_sweep' or event.event_type == 'cascade_trigger':
                # Calculate price displacement from session open
                price_displacement = abs(event.price - session_open)
                
                # Determine sweep magnitude based on context
                if 'session_low' in event.context or 'session_high' in event.context:
                    sweep_magnitude = price_displacement * 1.5  # High magnitude for session extremes
                else:
                    sweep_magnitude = price_displacement
                
                liquidity_sweeps.append(LiquiditySweep(
                    timestamp=event.timestamp,
                    sweep_magnitude=sweep_magnitude,
                    direction='bearish' if event.action == 'break' and 'low' in event.context else 'bullish',
                    liquidity_type='major' if 'session' in event.context else 'minor',
                    price_displacement=price_displacement
                ))
        
        return liquidity_sweeps
    
    def calculate_dynamic_synthetic_volume(self, session_data: dict, 
                                         target_timestamp: Optional[str] = None) -> SyntheticVolumeComponents:
        """
        Calculate dynamic synthetic volume using Grok 4's formula:
        v_s = Δp × (1 + w_FVG + m_sweep)
        """
        
        # Reduced debug output to prevent spam during bulk calculations
        # print("🔬 DYNAMIC SYNTHETIC VOLUME CALCULATION")
        # print("=" * 50)
        
        # Extract all market events
        events = self.extract_market_events(session_data)
        # Reduced verbosity: print(f"1️⃣ Extracted {len(events)} market events")
        
        # Extract FVG and liquidity sweep events
        fvg_events = self.extract_fvg_events(session_data, events)
        liquidity_sweeps = self.extract_liquidity_sweeps(session_data, events)
        
        # Reduced verbosity: print(f"2️⃣ Found {len(fvg_events)} FVG events, {len(liquidity_sweeps)} liquidity sweeps")
        
        # Calculate components for the most significant event or target timestamp
        if target_timestamp:
            # Find event closest to target timestamp
            if not events:
                raise ValueError("No market events found in session data for target timestamp analysis")
            target_minutes = self._timestamp_to_minutes(target_timestamp, 
                                                       session_data.get('session_metadata', {}).get('start_time', '09:30:00'))
            target_event = min(events, key=lambda x: abs(x.minutes_from_start - target_minutes))
        else:
            # Use the most significant cascade/liquidity event
            if not events:
                raise ValueError("No market events found in session data - cannot calculate dynamic synthetic volume")
            cascade_events = [e for e in events if e.event_type in ['cascade_trigger', 'liquidity_sweep']]
            target_event = max(cascade_events, key=lambda x: x.magnitude) if cascade_events else events[0]
        
        # Reduced verbosity: print(f"3️⃣ Target event: {target_event.timestamp} - {target_event.context}")
        
        # Calculate Δp (price displacement)
        session_open = session_data.get('price_data', {}).get('open', target_event.price)
        price_displacement = abs(target_event.price - session_open)
        
        # Calculate w_FVG (FVG weight) - sum of normalized FVG widths up to target time
        fvg_weight = 0.0
        target_minutes = target_event.minutes_from_start
        for fvg in fvg_events:
            fvg_minutes = self._timestamp_to_minutes(fvg.timestamp, 
                                                    session_data.get('session_metadata', {}).get('start_time', '09:30:00'))
            if fvg_minutes <= target_minutes:
                fvg_weight += fvg.normalized_width
        
        # Calculate m_sweep (sweep magnitude) - largest sweep up to target time
        sweep_magnitude = 0.0
        for sweep in liquidity_sweeps:
            sweep_minutes = self._timestamp_to_minutes(sweep.timestamp,
                                                      session_data.get('session_metadata', {}).get('start_time', '09:30:00'))
            if sweep_minutes <= target_minutes:
                sweep_magnitude = max(sweep_magnitude, sweep.sweep_magnitude)
        
        # Apply Grok 4's formula: v_s = Δp × (1 + w_FVG + m_sweep)
        # Note: Normalize sweep_magnitude to prevent extremely large values
        normalized_sweep = min(sweep_magnitude / 100.0, 2.0)  # Cap at 2.0 for stability
        
        calculated_volume = price_displacement * (1 + fvg_weight + normalized_sweep)
        
        # Ensure reasonable bounds (similar to static volume range)
        calculated_volume = max(50.0, min(calculated_volume, 500.0))
        
        improvement_factor = calculated_volume / self.static_volume
        
        # Reduced verbosity during bulk calculations
        # print(f"4️⃣ Components:")
        # print(f"   Δp (price displacement): {price_displacement:.2f}")
        # print(f"   w_FVG (FVG weight): {fvg_weight:.3f}")
        # print(f"   m_sweep (sweep magnitude): {sweep_magnitude:.2f} → normalized: {normalized_sweep:.3f}")
        # print(f"   Static v_synthetic: {self.static_volume:.2f}")
        # print(f"   Dynamic v_s: {calculated_volume:.2f}")
        # print(f"   Improvement factor: {improvement_factor:.2f}x")
        
        return SyntheticVolumeComponents(
            price_displacement=price_displacement,
            fvg_weight=fvg_weight,
            sweep_magnitude=sweep_magnitude,
            calculated_volume=calculated_volume,
            base_volume_static=self.static_volume,
            improvement_factor=improvement_factor
        )
    
    def get_volume_for_timestamp(self, session_data: dict, timestamp: str) -> float:
        """Get dynamic synthetic volume for a specific timestamp with unified caching."""
        
        # 🚀 UNIFIED CACHE: Check unified cache first
        cached_volume = self.unified_cache.get_volume(session_data, timestamp)
        
        if cached_volume is not None:
            print(f"🎯 UNIFIED CACHE HIT: Retrieved volume {cached_volume:.2f} for {timestamp}")
            return cached_volume
        
        # Calculate if not cached
        components = self.calculate_dynamic_synthetic_volume(session_data, timestamp)
        volume = components.calculated_volume
        
        # 🚀 UNIFIED CACHE: Store in unified cache
        self.unified_cache.set_volume(session_data, timestamp, volume)
        
        print(f"💾 UNIFIED CACHE STORED: Cached volume {volume:.2f} for {timestamp}")
        
        return volume

def test_dynamic_synthetic_volume():
    """Test dynamic synthetic volume calculation on NYAM session"""
    
    print("🧪 TESTING DYNAMIC SYNTHETIC VOLUME CALCULATOR")
    print("=" * 60)
    
    # Load NYAM session data
    nyam_data = load_json_data('NYAM_Lvl-1_2025_07_25.json')
    
    calculator = DynamicSyntheticVolumeCalculator()
    
    # Calculate for cascade event at 09:38:00
    cascade_components = calculator.calculate_dynamic_synthetic_volume(
        nyam_data, target_timestamp="09:38:00"
    )
    
    print(f"\n📊 CASCADE EVENT ANALYSIS (09:38:00):")
    print(f"   Static Formula Result: {cascade_components.base_volume_static:.2f}")
    print(f"   Dynamic ICT Result: {cascade_components.calculated_volume:.2f}")
    print(f"   Event Responsiveness: {cascade_components.improvement_factor:.2f}x")
    
    # Calculate for FVG formation at 09:37:00
    fvg_components = calculator.calculate_dynamic_synthetic_volume(
        nyam_data, target_timestamp="09:37:00"
    )
    
    print(f"\n📊 FVG FORMATION ANALYSIS (09:37:00):")
    print(f"   Static Formula Result: {fvg_components.base_volume_static:.2f}")
    print(f"   Dynamic ICT Result: {fvg_components.calculated_volume:.2f}")
    print(f"   Event Responsiveness: {fvg_components.improvement_factor:.2f}x")
    
    # Test general calculation (most significant event)
    general_components = calculator.calculate_dynamic_synthetic_volume(nyam_data)
    
    print(f"\n📊 MOST SIGNIFICANT EVENT ANALYSIS:")
    print(f"   Static Formula Result: {general_components.base_volume_static:.2f}")
    print(f"   Dynamic ICT Result: {general_components.calculated_volume:.2f}")
    print(f"   Event Responsiveness: {general_components.improvement_factor:.2f}x")
    
    return cascade_components, fvg_components, general_components

if __name__ == "__main__":
    test_dynamic_synthetic_volume()