#!/usr/bin/env python3
"""
DEPRECATED: Legacy pipeline module - use src.core.pipeline instead
This module provides backward compatibility during refactoring
"""

# Import from new location for backward compatibility
try:
    from .core.pipeline import *
except ImportError:
    # If new structure not ready, fall back to original
    from .pipeline_original import *

import warnings
warnings.warn(
    "src.pipeline is deprecated. Use src.core.pipeline instead.",
    DeprecationWarning,
    stacklevel=2
)
