#!/usr/bin/env python3
"""
Shadow Validation System
Runs parallel validation of Hawkes process predictions against multiple
methods without interrupting production pipeline. Provides continuous
accuracy monitoring and model performance tracking.
"""

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
from src.hawkes_cascade_predictor import HawkesCascadePredictor
from src.micro_timing_analysis import MicroTimingAnalyzer
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import threading
import time
import logging

@dataclass
class ValidationMethod:
    """Single validation method configuration"""
    name: str
    predictor_function: callable
    weight: float
    active: bool
    last_accuracy: Optional[float] = None
    prediction_history: List[Dict] = None
    
    def __post_init__(self):
        if self.prediction_history is None:
            self.prediction_history = []

@dataclass
class ShadowValidationResult:
    """Result from shadow validation comparison"""
    session_file: str
    ground_truth_cascade_time: float
    hawkes_prediction: float
    hawkes_error: float
    alternative_predictions: Dict[str, float]
    alternative_errors: Dict[str, float]
    best_performing_method: str
    hawkes_rank: int
    validation_timestamp: str
    confidence_intervals: Dict[str, Tuple[float, float]]

@dataclass
class ContinuousMonitoringStats:
    """Continuous monitoring statistics"""
    total_predictions: int
    hawkes_win_rate: float
    average_hawkes_error: float
    best_alternative_method: str
    performance_trend: str  # improving, declining, stable
    last_update: str

class ShadowValidator:
    """
    Shadow validation system for continuous Hawkes process performance monitoring.
    Runs alternative prediction methods in parallel without affecting production.
    """
    
    def __init__(self):
        self.hawkes_predictor = HawkesCascadePredictor()
        self.micro_timing_analyzer = MicroTimingAnalyzer()
        
        # Initialize validation methods
        self.validation_methods = [
            ValidationMethod(
                name="static_monte_carlo",
                predictor_function=self._static_monte_carlo_prediction,
                weight=1.0,
                active=True
            ),
            ValidationMethod(
                name="hmm_baseline",
                predictor_function=self._hmm_baseline_prediction,
                weight=1.0,
                active=True
            ),
            ValidationMethod(
                name="session_character_heuristic",
                predictor_function=self._session_character_heuristic,
                weight=0.8,
                active=True
            ),
            ValidationMethod(
                name="volume_momentum_predictor",
                predictor_function=self._volume_momentum_prediction,
                weight=0.9,
                active=True
            )
        ]
        
        # Monitoring configuration
        self.validation_history = []
        self.performance_tracking = []
        self.continuous_monitoring_active = False
        self.monitoring_thread = None
        
        # Logging setup
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def validate_hawkes_prediction(self, session_file: str, 
                                 ground_truth_cascade_time: float,
                                 run_alternatives: bool = True) -> ShadowValidationResult:
        """
        Validate Hawkes prediction against ground truth and alternative methods.
        
        Args:
            session_file: Path to session data file
            ground_truth_cascade_time: Actual cascade timing in minutes
            run_alternatives: Whether to run alternative prediction methods
            
        Returns:
            Complete validation result with method comparison
        """
        
        print(f"🔍 SHADOW VALIDATION: {session_file}")
        print("=" * 60)
        
        # Load session data
        session_data = load_json_data(session_file)
        
        # 1. Get Hawkes prediction
        hawkes_prediction_obj = self.hawkes_predictor.predict_cascade_timing(session_data)
        hawkes_prediction = hawkes_prediction_obj.predicted_cascade_time
        hawkes_error = abs(hawkes_prediction - ground_truth_cascade_time)
        
        print(f"🎯 HAWKES PREDICTION: {hawkes_prediction:.1f} min (error: {hawkes_error:.2f})")
        
        # 2. Run alternative methods if requested
        alternative_predictions = {}
        alternative_errors = {}
        confidence_intervals = {}
        
        if run_alternatives:
            print(f"🔄 Running {len([m for m in self.validation_methods if m.active])} alternative methods...")
            
            for method in self.validation_methods:
                if method.active:
                    try:
                        prediction = method.predictor_function(session_data)
                        error = abs(prediction - ground_truth_cascade_time)
                        
                        alternative_predictions[method.name] = prediction
                        alternative_errors[method.name] = error
                        
                        # Calculate confidence interval (simple ±2 minute window)
                        confidence_intervals[method.name] = (prediction - 2.0, prediction + 2.0)
                        
                        print(f"   {method.name}: {prediction:.1f} min (error: {error:.2f})")
                        
                        # Update method history
                        method.prediction_history.append({
                            "session": session_file,
                            "prediction": prediction,
                            "error": error,
                            "timestamp": datetime.now().isoformat()
                        })
                        method.last_accuracy = error
                        
                    except Exception as e:
                        print(f"   ⚠️ {method.name} failed: {e}")
                        alternative_predictions[method.name] = None
                        alternative_errors[method.name] = float('inf')
        
        # 3. Determine best performing method
        all_errors = {"hawkes_process": hawkes_error, **alternative_errors}
        best_method = min(all_errors.keys(), key=lambda k: all_errors[k])
        
        # 4. Calculate Hawkes rank
        sorted_methods = sorted(all_errors.items(), key=lambda x: x[1])
        hawkes_rank = next(i for i, (method, _) in enumerate(sorted_methods, 1) if method == "hawkes_process")
        
        print(f"🏆 BEST METHOD: {best_method} (error: {all_errors[best_method]:.2f})")
        print(f"📊 HAWKES RANK: #{hawkes_rank} out of {len(all_errors)}")
        
        # 5. Create validation result
        result = ShadowValidationResult(
            session_file=session_file,
            ground_truth_cascade_time=ground_truth_cascade_time,
            hawkes_prediction=hawkes_prediction,
            hawkes_error=hawkes_error,
            alternative_predictions=alternative_predictions,
            alternative_errors=alternative_errors,
            best_performing_method=best_method,
            hawkes_rank=hawkes_rank,
            validation_timestamp=datetime.now().isoformat(),
            confidence_intervals=confidence_intervals
        )
        
        # 6. Add to validation history
        self.validation_history.append(result)
        
        return result
    
    def _static_monte_carlo_prediction(self, session_data: Dict[str, Any]) -> float:
        """Static Monte Carlo prediction method (baseline comparison)."""
        
        # Simple static formula: session range influences cascade timing
        price_data = session_data.get("price_data", {})
        session_range = price_data.get("range", 50.0)
        
        # Basic timing: larger ranges = earlier cascades
        base_timing = 15.0  # Base 15 minutes
        range_factor = max(0.5, min(2.0, session_range / 50.0))
        
        return base_timing / range_factor
    
    def _hmm_baseline_prediction(self, session_data: Dict[str, Any]) -> float:
        """HMM baseline prediction (previous method)."""
        
        # Simplified HMM logic based on session character
        price_data = session_data.get("price_data", {})
        session_character = price_data.get("session_character", "neutral").lower()
        
        if "expansion" in session_character:
            return 12.0  # Expansion sessions cascade earlier
        elif "consolidation" in session_character:
            return 25.0  # Consolidation takes longer
        else:
            return 18.0  # Neutral timing
    
    def _session_character_heuristic(self, session_data: Dict[str, Any]) -> float:
        """Session character-based heuristic prediction."""
        
        price_data = session_data.get("price_data", {})
        session_range = price_data.get("range", 50.0)
        session_character = price_data.get("session_character", "neutral").lower()
        
        # Combine character and range
        if "expansion" in session_character and session_range > 40:
            return 8.0  # High volatility expansion
        elif "consolidation" in session_character and session_range < 30:
            return 30.0  # Low volatility consolidation
        else:
            return 15.0  # Mixed conditions
    
    def _volume_momentum_prediction(self, session_data: Dict[str, Any]) -> float:
        """Volume/momentum-based prediction method."""
        
        # Extract price movements for momentum analysis
        price_movements = session_data.get("price_movements", [])
        
        if not price_movements:
            return 20.0  # Default if no movement data
        
        # Calculate average movement size
        total_movement = 0
        for movement in price_movements:
            if isinstance(movement, dict) and "price" in movement:
                total_movement += abs(float(str(movement["price"]).replace(',', '')))
        
        avg_movement = total_movement / len(price_movements) if price_movements else 0
        
        # Higher average movement = earlier cascade
        if avg_movement > 100:
            return 10.0
        elif avg_movement > 50:
            return 15.0
        else:
            return 25.0
    
    def generate_validation_report(self, output_file: str = None) -> Dict[str, Any]:
        """Generate comprehensive validation report."""
        
        if not self.validation_history:
            return {"error": "No validation history available"}
        
        # Calculate overall statistics
        total_validations = len(self.validation_history)
        hawkes_wins = sum(1 for v in self.validation_history if v.best_performing_method == "hawkes_process")
        hawkes_win_rate = hawkes_wins / total_validations
        
        avg_hawkes_error = sum(v.hawkes_error for v in self.validation_history) / total_validations
        
        # Method performance comparison
        method_stats = {}
        for method in self.validation_methods:
            method_errors = [h["error"] for h in method.prediction_history]
            if method_errors:
                method_stats[method.name] = {
                    "average_error": sum(method_errors) / len(method_errors),
                    "total_predictions": len(method_errors),
                    "best_error": min(method_errors),
                    "worst_error": max(method_errors)
                }
        
        # Recent performance trend
        recent_validations = self.validation_history[-5:] if len(self.validation_history) >= 5 else self.validation_history
        recent_hawkes_wins = sum(1 for v in recent_validations if v.best_performing_method == "hawkes_process")
        recent_win_rate = recent_hawkes_wins / len(recent_validations)
        
        if recent_win_rate > hawkes_win_rate + 0.1:
            trend = "improving"
        elif recent_win_rate < hawkes_win_rate - 0.1:
            trend = "declining"
        else:
            trend = "stable"
        
        # Generate report
        report = {
            "shadow_validation_report": {
                "report_timestamp": datetime.now().isoformat(),
                "total_validations": total_validations,
                "hawkes_performance": {
                    "win_rate": hawkes_win_rate,
                    "average_error_minutes": avg_hawkes_error,
                    "rank_distribution": self._calculate_rank_distribution(),
                    "performance_trend": trend
                },
                "method_comparison": method_stats,
                "recent_performance": {
                    "last_5_validations": recent_win_rate,
                    "trend_direction": trend
                },
                "validation_history_summary": [
                    {
                        "session": v.session_file,
                        "hawkes_error": v.hawkes_error,
                        "best_method": v.best_performing_method,
                        "hawkes_rank": v.hawkes_rank
                    }
                    for v in self.validation_history[-10:]  # Last 10 validations
                ]
            }
        }
        
        # Save report if output file specified
        if output_file:
            save_json_data(report, output_file)
            print(f"📊 Validation report saved: {output_file}")
        
        return report
    
    def _calculate_rank_distribution(self) -> Dict[str, int]:
        """Calculate distribution of Hawkes ranking positions."""
        
        rank_counts = {}
        for validation in self.validation_history:
            rank = validation.hawkes_rank
            rank_counts[f"rank_{rank}"] = rank_counts.get(f"rank_{rank}", 0) + 1
        
        return rank_counts
    
    def start_continuous_monitoring(self, monitor_interval_minutes: int = 60):
        """Start continuous monitoring of validation performance."""
        
        if self.continuous_monitoring_active:
            print("⚠️ Continuous monitoring already active")
            return
        
        self.continuous_monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._continuous_monitoring_loop,
            args=(monitor_interval_minutes,),
            daemon=True
        )
        self.monitoring_thread.start()
        
        print(f"🔄 Started continuous monitoring (interval: {monitor_interval_minutes} min)")
    
    def stop_continuous_monitoring(self):
        """Stop continuous monitoring."""
        
        self.continuous_monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        print("⏹️ Stopped continuous monitoring")
    
    def _continuous_monitoring_loop(self, interval_minutes: int):
        """Continuous monitoring loop (runs in separate thread)."""
        
        while self.continuous_monitoring_active:
            try:
                # Generate monitoring stats
                stats = self._generate_monitoring_stats()
                
                # Log performance update
                self.logger.info(f"Shadow Validation Stats: Win Rate {stats.hawkes_win_rate:.2f}, "
                               f"Avg Error {stats.average_hawkes_error:.2f}min, "
                               f"Trend: {stats.performance_trend}")
                
                # Sleep for interval
                time.sleep(interval_minutes * 60)
                
            except Exception as e:
                self.logger.error(f"Continuous monitoring error: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def _generate_monitoring_stats(self) -> ContinuousMonitoringStats:
        """Generate current monitoring statistics."""
        
        if not self.validation_history:
            return ContinuousMonitoringStats(
                total_predictions=0,
                hawkes_win_rate=0.0,
                average_hawkes_error=0.0,
                best_alternative_method="none",
                performance_trend="no_data",
                last_update=datetime.now().isoformat()
            )
        
        total_predictions = len(self.validation_history)
        hawkes_wins = sum(1 for v in self.validation_history if v.best_performing_method == "hawkes_process")
        hawkes_win_rate = hawkes_wins / total_predictions
        avg_hawkes_error = sum(v.hawkes_error for v in self.validation_history) / total_predictions
        
        # Find best alternative method
        method_performance = {}
        for validation in self.validation_history:
            for method, error in validation.alternative_errors.items():
                if method not in method_performance:
                    method_performance[method] = []
                method_performance[method].append(error)
        
        best_alternative = "none"
        if method_performance:
            avg_errors = {method: sum(errors)/len(errors) for method, errors in method_performance.items()}
            best_alternative = min(avg_errors, key=avg_errors.get)
        
        # Determine trend
        recent_win_rate = hawkes_win_rate  # Simplified for now
        if recent_win_rate > 0.8:
            trend = "excellent"
        elif recent_win_rate > 0.6:
            trend = "good"
        elif recent_win_rate > 0.4:
            trend = "moderate"
        else:
            trend = "needs_attention"
        
        return ContinuousMonitoringStats(
            total_predictions=total_predictions,
            hawkes_win_rate=hawkes_win_rate,
            average_hawkes_error=avg_hawkes_error,
            best_alternative_method=best_alternative,
            performance_trend=trend,
            last_update=datetime.now().isoformat()
        )

def test_shadow_validation():
    """Test shadow validation system."""
    
    print("🧪 TESTING SHADOW VALIDATION SYSTEM")
    print("=" * 60)
    
    # Initialize validator
    validator = ShadowValidator()
    
    # Test validation on NYAM session (known cascade at 8.0 minutes)
    result = validator.validate_hawkes_prediction(
        'NYAM_Lvl-1_2025_07_25.json',
        ground_truth_cascade_time=8.0,
        run_alternatives=True
    )
    
    print(f"✅ SHADOW VALIDATION TEST RESULTS:")
    print(f"   Hawkes Error: {result.hawkes_error:.2f} minutes")
    print(f"   Best Method: {result.best_performing_method}")
    print(f"   Hawkes Rank: #{result.hawkes_rank}")
    
    # Generate validation report
    report = validator.generate_validation_report('shadow_validation_test_report.json')
    
    return validator, result

if __name__ == "__main__":
    test_shadow_validation()