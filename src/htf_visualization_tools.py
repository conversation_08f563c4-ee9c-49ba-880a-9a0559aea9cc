#!/usr/bin/env python3
"""
HTF Visualization and Analysis Tools
Implements Grok's visualization system for HTF algorithm analysis and monitoring.

Visualization Features:
- HTF intensity timeline plots with session overlay
- Multi-scale Hawkes process visualization
- Adaptive γ(t) coupling parameter plots
- Pattern detection confidence heatmaps
- Cross-session correlation matrices
- Performance dashboard with real-time metrics

Output Formats:
- Line charts for NYPM_Lvl-1_2025_07_25.json analysis
- Statistical plots for validation results
- Interactive dashboards for system monitoring
- Export capabilities (PNG, SVG, JSON data)
"""

import numpy as np
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Import plotting libraries with fallbacks
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.patches import Rectangle
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
    
    # Set style for professional plots
    plt.style.use('seaborn-v0_8' if 'seaborn-v0_8' in plt.style.available else 'default')
    sns.set_palette("husl")
    
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib/seaborn not available - using data-only visualization")

try:
    import plotly.graph_objects as go
    import plotly.subplots as sp
    from plotly.offline import plot as plotly_plot
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️ plotly not available - using matplotlib for visualization")


@dataclass
class VisualizationConfig:
    """Configuration for HTF visualizations."""
    figure_size: Tuple[int, int] = (12, 8)
    dpi: int = 300
    color_scheme: str = 'professional'  # 'professional', 'vibrant', 'minimal'
    export_format: str = 'png'  # 'png', 'svg', 'pdf', 'html'
    interactive: bool = True
    show_confidence_bands: bool = True
    timeline_resolution: int = 180  # Minutes


@dataclass
class PlotResult:
    """Result structure for plot generation."""
    plot_type: str
    title: str
    file_path: Optional[str]
    data_summary: Dict[str, Any]
    generation_time_ms: float
    interactive_available: bool


class HTFVisualizationTools:
    """
    HTF Visualization and Analysis Tools
    
    Provides comprehensive visualization capabilities for HTF algorithm
    analysis, monitoring, and presentation.
    """
    
    def __init__(self, config: Optional[VisualizationConfig] = None):
        """Initialize visualization tools with configuration."""
        
        self.config = config or VisualizationConfig()
        
        # Color schemes
        self.color_schemes = {
            'professional': {
                'htf_intensity': '#2E86AB',
                'session_intensity': '#A23B72', 
                'coupled_intensity': '#F18F01',
                'gamma_timeline': '#C73E1D',
                'pattern_detection': '#7209B7',
                'background': '#F8F9FA',
                'grid': '#E9ECEF'
            },
            'vibrant': {
                'htf_intensity': '#FF6B6B',
                'session_intensity': '#4ECDC4',
                'coupled_intensity': '#45B7D1',
                'gamma_timeline': '#96CEB4',
                'pattern_detection': '#FFEAA7',
                'background': '#FFFFFF',
                'grid': '#F1F2F6'
            },
            'minimal': {
                'htf_intensity': '#2C3E50',
                'session_intensity': '#34495E',
                'coupled_intensity': '#7F8C8D',
                'gamma_timeline': '#95A5A6',
                'pattern_detection': '#BDC3C7',
                'background': '#FFFFFF',
                'grid': '#ECF0F1'
            }
        }
        
        self.colors = self.color_schemes.get(self.config.color_scheme, self.color_schemes['professional'])
        
        # Plot tracking
        self.generated_plots = []
        self.plot_counter = 0
        
        print("📊 HTF VISUALIZATION TOOLS: Advanced plotting system initialized")
        print(f"   Libraries: matplotlib={MATPLOTLIB_AVAILABLE}, plotly={PLOTLY_AVAILABLE}")
        print(f"   Config: {self.config.figure_size[0]}x{self.config.figure_size[1]} @ {self.config.dpi}dpi")
    
    def create_htf_intensity_timeline(self, session_data: Dict[Any, Any],
                                    htf_coupling_data: Dict[str, Any],
                                    save_path: Optional[str] = None) -> PlotResult:
        """
        Create HTF intensity timeline visualization.
        
        Shows HTF intensity, session intensity, and coupled intensity over time
        with pattern detection markers and confidence bands.
        """
        start_time = time.time()
        
        print("📊 CREATING HTF INTENSITY TIMELINE")
        
        # Extract data
        htf_intensity = htf_coupling_data.get('htf_intensity_timeline', [])
        session_intensity = htf_coupling_data.get('session_intensity_timeline', [])
        coupled_intensity = htf_coupling_data.get('coupled_intensity_timeline', [])
        gamma_timeline = htf_coupling_data.get('gamma_adaptive', [])
        
        timeline_length = len(htf_intensity)
        time_points = np.linspace(0, self.config.timeline_resolution, timeline_length)
        
        if MATPLOTLIB_AVAILABLE:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.config.figure_size, 
                                         height_ratios=[3, 1], dpi=self.config.dpi)
            
            # Main intensity plot
            ax1.plot(time_points, htf_intensity, 
                    color=self.colors['htf_intensity'], linewidth=2.5, 
                    label='HTF Intensity λ_HTF(t)', alpha=0.8)
            ax1.plot(time_points, session_intensity, 
                    color=self.colors['session_intensity'], linewidth=2.0, 
                    label='Session Intensity λ_session(t)', alpha=0.7)
            ax1.plot(time_points, coupled_intensity, 
                    color=self.colors['coupled_intensity'], linewidth=3.0, 
                    label='Coupled Intensity λ_total(t)', alpha=0.9)
            
            # Confidence bands (if enabled)
            if self.config.show_confidence_bands and len(htf_intensity) > 10:
                htf_std = np.std(htf_intensity)
                ax1.fill_between(time_points, 
                               np.array(htf_intensity) - htf_std,
                               np.array(htf_intensity) + htf_std,
                               color=self.colors['htf_intensity'], alpha=0.2)
            
            # Add pattern detection markers
            self._add_pattern_markers(ax1, session_data, time_points)
            
            ax1.set_ylabel('Intensity', fontsize=12, fontweight='bold')
            ax1.set_title('HTF Multi-Scale Hawkes Process - Intensity Timeline', 
                         fontsize=14, fontweight='bold', pad=20)
            ax1.legend(loc='upper right', fontsize=10)
            ax1.grid(True, alpha=0.3, color=self.colors['grid'])
            ax1.set_facecolor(self.colors['background'])
            
            # Gamma subplot
            if gamma_timeline:
                ax2.plot(time_points[:len(gamma_timeline)], gamma_timeline, 
                        color=self.colors['gamma_timeline'], linewidth=2.0, 
                        label='Adaptive Coupling γ(t)')
                ax2.fill_between(time_points[:len(gamma_timeline)], gamma_timeline, 
                               alpha=0.3, color=self.colors['gamma_timeline'])
            
            ax2.set_xlabel('Time (minutes)', fontsize=12, fontweight='bold')
            ax2.set_ylabel('γ(t)', fontsize=12, fontweight='bold')
            ax2.legend(loc='upper right', fontsize=10)
            ax2.grid(True, alpha=0.3, color=self.colors['grid'])
            ax2.set_facecolor(self.colors['background'])
            
            plt.tight_layout()
            
            # Save plot
            if save_path:
                plt.savefig(save_path, dpi=self.config.dpi, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                file_path = save_path
            else:
                file_path = f'htf_intensity_timeline_{self.plot_counter:03d}.{self.config.export_format}'
                plt.savefig(file_path, dpi=self.config.dpi, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
            
            if self.config.interactive:
                plt.show()
            else:
                plt.close()
        
        else:
            # Data-only visualization
            file_path = None
            print("   📊 Data Summary (matplotlib not available):")
            print(f"      HTF Intensity: μ={np.mean(htf_intensity):.3f}, σ={np.std(htf_intensity):.3f}")
            print(f"      Coupled Intensity: max={max(coupled_intensity):.3f}")
            print(f"      Gamma Range: [{min(gamma_timeline):.3f}, {max(gamma_timeline):.3f}]")
        
        processing_time = (time.time() - start_time) * 1000
        self.plot_counter += 1
        
        result = PlotResult(
            plot_type='htf_intensity_timeline',
            title='HTF Multi-Scale Hawkes Process - Intensity Timeline',
            file_path=file_path,
            data_summary={
                'timeline_points': timeline_length,
                'htf_intensity_stats': {
                    'mean': np.mean(htf_intensity) if htf_intensity else 0,
                    'std': np.std(htf_intensity) if htf_intensity else 0,
                    'max': max(htf_intensity) if htf_intensity else 0
                },
                'coupled_intensity_max': max(coupled_intensity) if coupled_intensity else 0,
                'gamma_range': [min(gamma_timeline), max(gamma_timeline)] if gamma_timeline else [0, 0]
            },
            generation_time_ms=processing_time,
            interactive_available=MATPLOTLIB_AVAILABLE
        )
        
        self.generated_plots.append(result)
        print(f"✅ HTF intensity timeline created in {processing_time:.1f}ms")
        
        return result
    
    def create_pattern_detection_heatmap(self, validation_results: List[Dict[str, Any]],
                                       save_path: Optional[str] = None) -> PlotResult:
        """Create pattern detection confidence heatmap."""
        start_time = time.time()
        
        print("📊 CREATING PATTERN DETECTION HEATMAP")
        
        # Prepare heatmap data
        pattern_types = ['friday_trap', 'monday_sweep', 'tuesday_cascade', 'friday_completion']
        session_count = len(validation_results)
        
        # Create confidence matrix
        confidence_matrix = np.zeros((len(pattern_types), session_count))
        
        for i, result in enumerate(validation_results):
            # Mock confidence data (in real implementation, extract from results)
            for j, pattern in enumerate(pattern_types):
                confidence_matrix[j, i] = np.random.beta(2, 1)  # Mock confidence scores
        
        if MATPLOTLIB_AVAILABLE:
            fig, ax = plt.subplots(figsize=self.config.figure_size, dpi=self.config.dpi)
            
            # Create heatmap
            im = ax.imshow(confidence_matrix, cmap='YlOrRd', aspect='auto', vmin=0, vmax=1)
            
            # Add colorbar
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Detection Confidence', fontsize=12, fontweight='bold')
            
            # Set labels
            ax.set_xticks(range(session_count))
            ax.set_xticklabels([f'S{i+1}' for i in range(session_count)], rotation=45)
            ax.set_yticks(range(len(pattern_types)))
            ax.set_yticklabels([p.replace('_', ' ').title() for p in pattern_types])
            
            ax.set_xlabel('Session Number', fontsize=12, fontweight='bold')
            ax.set_ylabel('Pattern Type', fontsize=12, fontweight='bold')
            ax.set_title('HTF Pattern Detection Confidence Heatmap', 
                        fontsize=14, fontweight='bold', pad=20)
            
            # Add confidence values as text
            for i in range(len(pattern_types)):
                for j in range(session_count):
                    text = ax.text(j, i, f'{confidence_matrix[i, j]:.2f}',
                                 ha="center", va="center", color="black", fontsize=8)
            
            plt.tight_layout()
            
            # Save plot
            if save_path:
                plt.savefig(save_path, dpi=self.config.dpi, bbox_inches='tight')
                file_path = save_path
            else:
                file_path = f'pattern_detection_heatmap_{self.plot_counter:03d}.{self.config.export_format}'
                plt.savefig(file_path, dpi=self.config.dpi, bbox_inches='tight')
            
            if self.config.interactive:
                plt.show()
            else:
                plt.close()
        
        else:
            file_path = None
            print("   📊 Heatmap Summary (matplotlib not available):")
            print(f"      Pattern Types: {len(pattern_types)}")
            print(f"      Sessions: {session_count}")
            print(f"      Average Confidence: {np.mean(confidence_matrix):.3f}")
        
        processing_time = (time.time() - start_time) * 1000
        self.plot_counter += 1
        
        result = PlotResult(
            plot_type='pattern_detection_heatmap',
            title='HTF Pattern Detection Confidence Heatmap',
            file_path=file_path,
            data_summary={
                'pattern_types': pattern_types,
                'sessions_analyzed': session_count,
                'average_confidence': float(np.mean(confidence_matrix)),
                'max_confidence': float(np.max(confidence_matrix)),
                'min_confidence': float(np.min(confidence_matrix))
            },
            generation_time_ms=processing_time,
            interactive_available=MATPLOTLIB_AVAILABLE
        )
        
        self.generated_plots.append(result)
        print(f"✅ Pattern detection heatmap created in {processing_time:.1f}ms")
        
        return result
    
    def create_validation_dashboard(self, validation_metrics: Dict[str, Any],
                                  save_path: Optional[str] = None) -> PlotResult:
        """Create comprehensive validation dashboard."""
        start_time = time.time()
        
        print("📊 CREATING VALIDATION DASHBOARD")
        
        if MATPLOTLIB_AVAILABLE:
            fig = plt.figure(figsize=(16, 12), dpi=self.config.dpi)
            
            # Create 2x3 subplot grid
            gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], width_ratios=[1, 1])
            
            # 1. Requirements radar chart
            ax1 = fig.add_subplot(gs[0, 0], projection='polar')
            self._create_requirements_radar(ax1, validation_metrics)
            
            # 2. Performance metrics bar chart
            ax2 = fig.add_subplot(gs[0, 1])
            self._create_performance_bars(ax2, validation_metrics)
            
            # 3. Timing precision distribution
            ax3 = fig.add_subplot(gs[1, 0])
            self._create_timing_distribution(ax3, validation_metrics)
            
            # 4. Cross-session correlation matrix
            ax4 = fig.add_subplot(gs[1, 1])
            self._create_correlation_matrix(ax4, validation_metrics)
            
            # 5. Safeguard activation timeline
            ax5 = fig.add_subplot(gs[2, :])
            self._create_safeguard_timeline(ax5, validation_metrics)
            
            plt.suptitle('HTF Algorithm Validation Dashboard', 
                        fontsize=16, fontweight='bold', y=0.98)
            plt.tight_layout()
            
            # Save dashboard
            if save_path:
                plt.savefig(save_path, dpi=self.config.dpi, bbox_inches='tight')
                file_path = save_path
            else:
                file_path = f'validation_dashboard_{self.plot_counter:03d}.{self.config.export_format}'
                plt.savefig(file_path, dpi=self.config.dpi, bbox_inches='tight')
            
            if self.config.interactive:
                plt.show()
            else:
                plt.close()
        
        else:
            file_path = None
            print("   📊 Dashboard Summary (matplotlib not available):")
            print(f"      Validation Metrics: {len(validation_metrics)} categories")
        
        processing_time = (time.time() - start_time) * 1000
        self.plot_counter += 1
        
        result = PlotResult(
            plot_type='validation_dashboard',
            title='HTF Algorithm Validation Dashboard',
            file_path=file_path,
            data_summary={
                'metrics_included': list(validation_metrics.keys()),
                'dashboard_sections': 5,
                'comprehensive_analysis': True
            },
            generation_time_ms=processing_time,
            interactive_available=MATPLOTLIB_AVAILABLE
        )
        
        self.generated_plots.append(result)
        print(f"✅ Validation dashboard created in {processing_time:.1f}ms")
        
        return result
    
    def create_interactive_htf_explorer(self, session_data: Dict[Any, Any],
                                      htf_coupling_data: Dict[str, Any],
                                      save_path: Optional[str] = None) -> PlotResult:
        """Create interactive HTF explorer using Plotly."""
        start_time = time.time()
        
        print("📊 CREATING INTERACTIVE HTF EXPLORER")
        
        if PLOTLY_AVAILABLE:
            # Extract data
            htf_intensity = htf_coupling_data.get('htf_intensity_timeline', [])
            session_intensity = htf_coupling_data.get('session_intensity_timeline', [])
            coupled_intensity = htf_coupling_data.get('coupled_intensity_timeline', [])
            gamma_timeline = htf_coupling_data.get('gamma_adaptive', [])
            
            timeline_length = len(htf_intensity)
            time_points = list(range(timeline_length))
            
            # Create subplots
            fig = sp.make_subplots(
                rows=2, cols=1,
                subplot_titles=('HTF Multi-Scale Intensity', 'Adaptive Coupling γ(t)'),
                vertical_spacing=0.1,
                row_heights=[0.7, 0.3]
            )
            
            # Add intensity traces
            fig.add_trace(
                go.Scatter(x=time_points, y=htf_intensity, name='HTF Intensity',
                          line=dict(color='#2E86AB', width=3), opacity=0.8),
                row=1, col=1
            )
            
            fig.add_trace(
                go.Scatter(x=time_points, y=session_intensity, name='Session Intensity',
                          line=dict(color='#A23B72', width=2), opacity=0.7),
                row=1, col=1
            )
            
            fig.add_trace(
                go.Scatter(x=time_points, y=coupled_intensity, name='Coupled Intensity',
                          line=dict(color='#F18F01', width=4), opacity=0.9),
                row=1, col=1
            )
            
            # Add gamma trace
            if gamma_timeline:
                fig.add_trace(
                    go.Scatter(x=time_points[:len(gamma_timeline)], y=gamma_timeline, 
                              name='γ(t)', line=dict(color='#C73E1D', width=3),
                              fill='tonexty', opacity=0.6),
                    row=2, col=1
                )
            
            # Update layout
            fig.update_layout(
                title='Interactive HTF Multi-Scale Hawkes Process Explorer',
                height=800,
                showlegend=True,
                hovermode='x unified'
            )
            
            fig.update_xaxes(title_text="Time (minutes)", row=2, col=1)
            fig.update_yaxes(title_text="Intensity", row=1, col=1)
            fig.update_yaxes(title_text="γ(t)", row=2, col=1)
            
            # Save interactive plot
            if save_path:
                plotly_plot(fig, filename=save_path, auto_open=False)
                file_path = save_path
            else:
                file_path = f'interactive_htf_explorer_{self.plot_counter:03d}.html'
                plotly_plot(fig, filename=file_path, auto_open=False)
        
        else:
            file_path = None
            print("   📊 Interactive Explorer (plotly not available)")
            print("   📊 Using matplotlib fallback...")
            return self.create_htf_intensity_timeline(session_data, htf_coupling_data, save_path)
        
        processing_time = (time.time() - start_time) * 1000
        self.plot_counter += 1
        
        result = PlotResult(
            plot_type='interactive_htf_explorer',
            title='Interactive HTF Multi-Scale Hawkes Process Explorer',
            file_path=file_path,
            data_summary={
                'interactive': True,
                'plotly_enabled': PLOTLY_AVAILABLE,
                'timeline_points': len(htf_intensity),
                'traces_included': 4
            },
            generation_time_ms=processing_time,
            interactive_available=PLOTLY_AVAILABLE
        )
        
        self.generated_plots.append(result)
        print(f"✅ Interactive HTF explorer created in {processing_time:.1f}ms")
        
        return result
    
    def _add_pattern_markers(self, ax, session_data: Dict[Any, Any], time_points: np.ndarray):
        """Add pattern detection markers to intensity plot."""
        
        # Mock pattern detection events
        patterns = [
            {'time': 45, 'type': 'friday_trap', 'confidence': 0.85},
            {'time': 90, 'type': 'tuesday_cascade', 'confidence': 0.92},
            {'time': 135, 'type': 'completion', 'confidence': 0.78}
        ]
        
        marker_styles = {
            'friday_trap': {'marker': 'v', 'color': '#FF6B6B', 'size': 12},
            'tuesday_cascade': {'marker': '^', 'color': '#4ECDC4', 'size': 15},
            'completion': {'marker': 's', 'color': '#45B7D1', 'size': 10}
        }
        
        for pattern in patterns:
            if pattern['time'] < len(time_points):
                style = marker_styles.get(pattern['type'], {'marker': 'o', 'color': 'red', 'size': 8})
                ax.scatter(pattern['time'], 0.5, marker=style['marker'], 
                          color=style['color'], s=style['size']**2, alpha=0.8,
                          label=f"{pattern['type']} ({pattern['confidence']:.2f})")
    
    def _create_requirements_radar(self, ax, validation_metrics: Dict[str, Any]):
        """Create requirements fulfillment radar chart."""
        
        categories = ['Pattern\nAccuracy', 'Timing\nPrecision', 'Intensity\nR²',
                     'Cross-Session\nCorrelation', 'Safeguard\nReliability']
        
        # Mock values (in real implementation, extract from validation_metrics)
        values = [0.87, 0.72, 0.83, 0.78, 0.95]  # Example scores
        requirements = [0.85, 0.78, 0.80, 0.70, 0.95]  # Thresholds
        
        # Number of variables
        N = len(categories)
        
        # Compute angle for each axis
        angles = [n / float(N) * 2 * np.pi for n in range(N)]
        angles += angles[:1]  # Complete the circle
        
        # Add values to complete the circle
        values += values[:1]
        requirements += requirements[:1]
        
        # Plot actual values
        ax.plot(angles, values, 'o-', linewidth=2, label='Actual', color='#2E86AB')
        ax.fill(angles, values, alpha=0.25, color='#2E86AB')
        
        # Plot requirements
        ax.plot(angles, requirements, 'o--', linewidth=2, label='Required', color='#C73E1D')
        
        # Add category labels
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories, fontsize=9)
        ax.set_ylim(0, 1)
        ax.set_title('Requirements Fulfillment', fontsize=12, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)
    
    def _create_performance_bars(self, ax, validation_metrics: Dict[str, Any]):
        """Create performance metrics bar chart."""
        
        metrics = ['Pattern Acc.', 'Timing Prec.', 'Intensity R²', 'Reliability']
        values = [0.87, 0.72, 0.83, 0.95]  # Mock values
        thresholds = [0.85, 0.78, 0.80, 0.95]
        
        x = np.arange(len(metrics))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, values, width, label='Actual', color='#2E86AB', alpha=0.8)
        bars2 = ax.bar(x + width/2, thresholds, width, label='Required', color='#C73E1D', alpha=0.6)
        
        ax.set_ylabel('Score')
        ax.set_title('Performance Metrics', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics, rotation=15)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar in bars1:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.2f}', ha='center', va='bottom', fontsize=9)
    
    def _create_timing_distribution(self, ax, validation_metrics: Dict[str, Any]):
        """Create timing precision distribution."""
        
        # Mock timing error data
        timing_errors = np.random.exponential(35, 100)  # Mock timing errors
        
        ax.hist(timing_errors, bins=20, alpha=0.7, color='#4ECDC4', edgecolor='black')
        ax.axvline(78, color='#C73E1D', linestyle='--', linewidth=2, label='Threshold (78 min)')
        ax.axvline(np.mean(timing_errors), color='#2E86AB', linestyle='-', linewidth=2, 
                  label=f'Mean ({np.mean(timing_errors):.1f} min)')
        
        ax.set_xlabel('Timing Error (minutes)')
        ax.set_ylabel('Frequency')
        ax.set_title('Timing Precision Distribution', fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_correlation_matrix(self, ax, validation_metrics: Dict[str, Any]):
        """Create cross-session correlation matrix."""
        
        sessions = ['Asia', 'London', 'NY_AM', 'NY_PM']
        correlation_matrix = np.random.uniform(0.6, 0.95, (4, 4))  # Mock correlations
        np.fill_diagonal(correlation_matrix, 1.0)
        
        im = ax.imshow(correlation_matrix, cmap='Blues', vmin=0, vmax=1)
        
        # Add correlation values
        for i in range(len(sessions)):
            for j in range(len(sessions)):
                text = ax.text(j, i, f'{correlation_matrix[i, j]:.2f}',
                             ha="center", va="center", color="black", fontsize=10)
        
        ax.set_xticks(range(len(sessions)))
        ax.set_yticks(range(len(sessions)))
        ax.set_xticklabels(sessions)
        ax.set_yticklabels(sessions)
        ax.set_title('Cross-Session Correlations', fontweight='bold')
    
    def _create_safeguard_timeline(self, ax, validation_metrics: Dict[str, Any]):
        """Create safeguard activation timeline."""
        
        # Mock safeguard activations
        time_points = np.arange(0, 30)  # 30 time periods
        safeguards = ['Gamma Cap', 'HTF Fallback', 'Param Stability', 'Timeout Guard']
        
        # Create mock activation data
        activation_data = np.random.choice([0, 1], size=(len(safeguards), len(time_points)), p=[0.85, 0.15])
        
        # Plot as stacked areas
        bottom = np.zeros(len(time_points))
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        
        for i, (safeguard, color) in enumerate(zip(safeguards, colors)):
            ax.fill_between(time_points, bottom, bottom + activation_data[i], 
                           label=safeguard, color=color, alpha=0.7)
            bottom += activation_data[i]
        
        ax.set_xlabel('Time Period')
        ax.set_ylabel('Safeguard Activations')
        ax.set_title('Safeguard Activation Timeline', fontweight='bold')
        ax.legend(loc='upper left', ncol=2)
        ax.grid(True, alpha=0.3)
    
    def export_plot_data(self, plot_result: PlotResult, format: str = 'json') -> str:
        """Export plot data in specified format."""
        
        export_data = {
            'plot_metadata': {
                'plot_type': plot_result.plot_type,
                'title': plot_result.title,
                'generation_time_ms': plot_result.generation_time_ms,
                'timestamp': datetime.now().isoformat()
            },
            'data_summary': plot_result.data_summary,
            'config': {
                'figure_size': self.config.figure_size,
                'color_scheme': self.config.color_scheme,
                'export_format': self.config.export_format
            }
        }
        
        if format == 'json':
            filename = f"{plot_result.plot_type}_data.json"
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            return filename
        
        return ""
    
    def generate_plot_summary_report(self) -> Dict[str, Any]:
        """Generate summary report of all generated plots."""
        
        return {
            'visualization_summary': {
                'total_plots_generated': len(self.generated_plots),
                'generation_timestamp': datetime.now().isoformat(),
                'libraries_available': {
                    'matplotlib': MATPLOTLIB_AVAILABLE,
                    'plotly': PLOTLY_AVAILABLE
                }
            },
            'plot_breakdown': {
                plot_type: len([p for p in self.generated_plots if p.plot_type == plot_type])
                for plot_type in set(p.plot_type for p in self.generated_plots)
            },
            'performance_metrics': {
                'average_generation_time_ms': np.mean([p.generation_time_ms for p in self.generated_plots]) if self.generated_plots else 0,
                'total_generation_time_ms': sum(p.generation_time_ms for p in self.generated_plots),
                'interactive_plots': sum(1 for p in self.generated_plots if p.interactive_available)
            },
            'configuration': {
                'figure_size': self.config.figure_size,
                'dpi': self.config.dpi,
                'color_scheme': self.config.color_scheme,
                'export_format': self.config.export_format
            },
            'generated_plots': [
                {
                    'plot_type': p.plot_type,
                    'title': p.title,
                    'file_path': p.file_path,
                    'generation_time_ms': p.generation_time_ms
                }
                for p in self.generated_plots
            ]
        }
    
    def get_visualization_status(self) -> Dict[str, Any]:
        """Get visualization tools status."""
        return {
            'tools_version': '1.0.0',
            'libraries_available': {
                'matplotlib': MATPLOTLIB_AVAILABLE,
                'seaborn': MATPLOTLIB_AVAILABLE,  # Bundled with matplotlib check
                'plotly': PLOTLY_AVAILABLE
            },
            'configuration': {
                'figure_size': self.config.figure_size,
                'dpi': self.config.dpi,
                'color_scheme': self.config.color_scheme,
                'export_format': self.config.export_format,
                'interactive': self.config.interactive
            },
            'plots_generated': len(self.generated_plots),
            'color_schemes_available': list(self.color_schemes.keys())
        }


def create_htf_visualization_tools(**kwargs) -> HTFVisualizationTools:
    """Factory function to create HTF visualization tools."""
    return HTFVisualizationTools(**kwargs)


if __name__ == "__main__":
    # Test HTF visualization tools
    print("🧪 TESTING HTF VISUALIZATION TOOLS")
    print("=" * 60)
    
    viz_tools = create_htf_visualization_tools()
    
    # Mock session data for visualization testing
    mock_session = {
        'session_metadata': {
            'date': '2025-07-25',
            'session_type': 'NY_PM',
            'duration_minutes': 159
        },
        'price_data': {
            'high': 23459.75,
            'low': 23407.0,
            'range': 52.75,
            'session_character': 'expansion_consolidation_major_downward_movement'
        }
    }
    
    # Mock HTF coupling data
    timeline_length = 159
    mock_htf_coupling = {
        'htf_intensity_timeline': (np.random.normal(0.3, 0.05, timeline_length) + 
                                  0.1 * np.sin(np.linspace(0, 4*np.pi, timeline_length))).tolist(),
        'session_intensity_timeline': (np.random.normal(0.4, 0.1, timeline_length) + 
                                      0.2 * np.cos(np.linspace(0, 2*np.pi, timeline_length))).tolist(),
        'coupled_intensity_timeline': (np.random.normal(0.7, 0.15, timeline_length) + 
                                      0.3 * np.sin(np.linspace(0, 3*np.pi, timeline_length))).tolist(),
        'gamma_adaptive': np.random.uniform(0.2, 0.6, timeline_length).tolist(),
        'htf_events_detected': 3
    }
    
    # Mock validation metrics
    mock_validation_metrics = {
        'pattern_accuracy': 0.87,
        'timing_precision_mae': 42.3,
        'intensity_r_squared': 0.83,
        'cross_session_correlation': 0.78,
        'safeguard_reliability': 0.95
    }
    
    # Test 1: HTF Intensity Timeline
    print("\n📊 TEST 1: HTF Intensity Timeline")
    timeline_result = viz_tools.create_htf_intensity_timeline(
        mock_session, mock_htf_coupling
    )
    print(f"   Plot Type: {timeline_result.plot_type}")
    print(f"   Generation Time: {timeline_result.generation_time_ms:.1f}ms")
    print(f"   File Path: {timeline_result.file_path}")
    
    # Test 2: Pattern Detection Heatmap
    print("\n📊 TEST 2: Pattern Detection Heatmap")
    mock_validation_results = [{'session': f'session_{i:03d}'} for i in range(10)]
    heatmap_result = viz_tools.create_pattern_detection_heatmap(mock_validation_results)
    print(f"   Plot Type: {heatmap_result.plot_type}")
    print(f"   Generation Time: {heatmap_result.generation_time_ms:.1f}ms")
    
    # Test 3: Validation Dashboard
    print("\n📊 TEST 3: Validation Dashboard")
    dashboard_result = viz_tools.create_validation_dashboard(mock_validation_metrics)
    print(f"   Plot Type: {dashboard_result.plot_type}")
    print(f"   Generation Time: {dashboard_result.generation_time_ms:.1f}ms")
    print(f"   Dashboard Sections: {dashboard_result.data_summary.get('dashboard_sections', 0)}")
    
    # Test 4: Interactive Explorer (if Plotly available)
    print("\n📊 TEST 4: Interactive HTF Explorer")
    interactive_result = viz_tools.create_interactive_htf_explorer(
        mock_session, mock_htf_coupling
    )
    print(f"   Plot Type: {interactive_result.plot_type}")
    print(f"   Interactive: {interactive_result.data_summary.get('interactive', False)}")
    print(f"   Plotly Enabled: {interactive_result.data_summary.get('plotly_enabled', False)}")
    
    # Generate summary report
    summary_report = viz_tools.generate_plot_summary_report()
    print(f"\n📊 VISUALIZATION SUMMARY:")
    print(f"   Total Plots Generated: {summary_report['visualization_summary']['total_plots_generated']}")
    print(f"   Average Generation Time: {summary_report['performance_metrics']['average_generation_time_ms']:.1f}ms")
    print(f"   Interactive Plots: {summary_report['performance_metrics']['interactive_plots']}")
    
    # Show tool status
    status = viz_tools.get_visualization_status()
    print(f"\n🔧 Visualization Tools Status:")
    print(f"   Tools Version: {status['tools_version']}")
    print(f"   Matplotlib Available: {status['libraries_available']['matplotlib']}")
    print(f"   Plotly Available: {status['libraries_available']['plotly']}")
    print(f"   Color Schemes: {', '.join(status['color_schemes_available'])}")
    
    print(f"\n✅ HTF Visualization Tools testing completed!")