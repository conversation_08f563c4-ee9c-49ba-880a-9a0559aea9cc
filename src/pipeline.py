#!/usr/bin/env python3
"""
Grok Computational Pipeline Orchestrator
Manages the sequential execution of Units A → B → C → D
"""

import json
import time
import signal
from typing import Dict, Any, Optional
from .unit_a import create_unit_a
from .unit_b import create_unit_b
from .unit_c import create_unit_c
from .unit_d import create_unit_d
from .tracker_state import TrackerStateManager
from .config import AppConfig, APIKeyError
from .utils import load_json_data, save_json_data


class TimeoutError(Exception):
    """Custom timeout exception for unit processing."""
    pass


def timeout_handler(signum, frame):
    """Signal handler for timeout."""
    raise TimeoutError("Unit processing timed out")


def execute_with_timeout(func, timeout_seconds: int, *args, **kwargs):
    """Execute function with timeout protection."""
    # Set up timeout handler
    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(timeout_seconds)
    
    try:
        result = func(*args, **kwargs)
        signal.alarm(0)  # Cancel alarm
        return result
    except TimeoutError:
        print(f"⏰ TIMEOUT: Function timed out after {timeout_seconds}s")
        raise
    finally:
        signal.signal(signal.SIGALRM, old_handler)  # Restore old handler


class PipelineErrorHandler:
    """Centralized error handling for pipeline operations."""
    
    @staticmethod
    def handle_unit_error(unit_name: str, error_result: Dict[Any, Any], 
                         processing_times: Dict[str, int]) -> Dict[Any, Any]:
        """Handle errors from individual units with consistent formatting."""
        error_details = error_result.get("error", "Unknown error")
        
        # Log the error for monitoring
        print(f"❌ {unit_name} FAILED: {error_details}")
        
        return {
            "pipeline_error": {
                "failed_unit": unit_name,
                "error_type": "unit_processing_error",
                "error_details": error_details,
                "pipeline_status": "failed",
                "processing_times": processing_times,
                "error_severity": "high" if "CRITICAL FAILURE" in str(error_details) else "medium"
            }
        }
    
    @staticmethod
    def handle_pipeline_error(error_message: str, elapsed_time: float, 
                            processing_times: Dict[str, int]) -> Dict[Any, Any]:
        """Handle general pipeline errors with consistent formatting."""
        print(f"💥 PIPELINE ERROR: {error_message}")
        
        return {
            "pipeline_error": {
                "error_type": "pipeline_system_error",
                "error_message": error_message,
                "pipeline_status": "failed",
                "elapsed_time_ms": int(elapsed_time * 1000),
                "processing_times": processing_times,
                "error_severity": "critical"
            }
        }
    
    @staticmethod
    def handle_configuration_error(error_message: str) -> Dict[Any, Any]:
        """Handle configuration-related errors."""
        print(f"⚙️ CONFIGURATION ERROR: {error_message}")
        
        return {
            "pipeline_error": {
                "error_type": "configuration_error",
                "error_message": error_message,
                "pipeline_status": "failed",
                "error_severity": "critical",
                "resolution_hint": "Check API key configuration and environment variables"
            }
        }


class GrokPipeline:
    """Main orchestrator for the 4-unit Grok computational pipeline."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize pipeline with all computational units."""
        self.error_handler = PipelineErrorHandler()
        
        try:
            config = AppConfig()
            self.api_key = config.get_api_key(api_key)
        except APIKeyError as e:
            # Use centralized error handling for configuration issues
            error_result = self.error_handler.handle_configuration_error(str(e))
            raise ValueError(f"Pipeline initialization failed: {e}")
            
        self.unit_a = create_unit_a(self.api_key)
        self.unit_b = create_unit_b(self.api_key)
        self.unit_c = create_unit_c(self.api_key)
        self.unit_d = create_unit_d(self.api_key)
        self.tracker_manager = TrackerStateManager()
        
        self.results = {}
        self.processing_times = {}
        self.tracker_context = None
    
    def process_session(self, session_data: Dict[Any, Any], 
                       htf_context: Dict[Any, Any] = None,
                       fvg_state: Dict[Any, Any] = None, 
                       liquidity_state: Dict[Any, Any] = None) -> Dict[Any, Any]:
        """
        Process a complete session through all 4 units with tracker context.
        
        Args:
            session_data: Base session JSON data
            htf_context: Higher timeframe context data
            fvg_state: FVG state tracking data
            liquidity_state: Liquidity state registry
            
        Returns:
            Complete results from all units with timing information
        """
        start_time = time.time()
        
        # Extract micro timing from session data if not provided separately
        micro_timing = session_data.get("micro_timing_analysis", {})
        
        try:
            # Extract and process tracker context before Unit A
            print("Processing tracker context...")
            if htf_context or fvg_state or liquidity_state:
                # Extract full tracker context
                full_tracker_context = self.tracker_manager.extract_tracker_context(
                    htf_context or {},
                    fvg_state or {},
                    liquidity_state or {}
                )
                
                # Extract minimal essentials per unit to prevent API timeouts
                self.tracker_essentials = self.tracker_manager.extract_tracker_essentials(
                    full_tracker_context, session_data
                )
                
                # Keep full context for template population
                self.tracker_context = full_tracker_context
                
                print(f"Tracker essentials extracted: T_memory={self.tracker_essentials['unit_a']['t_memory']:.2f}")
                print(f"HTF distance={self.tracker_essentials['unit_a']['nearest_htf_distance']:.1f}, Structures={self.tracker_essentials['unit_c']['htf_structure_count']}")
            else:
                # Create empty tracker context for backward compatibility
                self.tracker_context = {
                    "t_memory": 15.0,
                    "active_structures": [],
                    "untaken_liquidity": [],
                    "liquidity_gradient": {"gradient_strength": 0.0},
                    "htf_influence_factor": 0.0
                }
                self.tracker_essentials = {
                    "unit_a": {"t_memory": 15.0, "nearest_htf_distance": 100.0, "liquidity_gradient_strength": 0.0},
                    "unit_b": {"energy_threshold_adjustment": 800.0, "regime_state": "neutral"},
                    "unit_c": {"htf_structure_count": 0, "liquidity_imbalance_direction": "balanced"},
                    "unit_d": {"tracker_validation_score": 0.0, "context_completeness": "minimal"}
                }
                print("No tracker context provided, using defaults")
            
            # Unit A: Foundation Calculations with tracker context and timeout
            print("🔧 DEBUG: Starting Unit A processing...")
            print("Processing Unit A: Foundation Calculations... (timeout: 180s)")
            unit_a_start = time.time()
            try:
                print("🔧 DEBUG: Calling Unit A process_foundation_calculations_with_essentials...")
                foundation_result = execute_with_timeout(
                    self.unit_a.process_foundation_calculations_with_essentials,
                    180,  # 3 minute timeout
                    session_data, 
                    micro_timing,
                    tracker_essentials=self.tracker_essentials['unit_a']
                )
                self.processing_times['unit_a'] = int((time.time() - unit_a_start) * 1000)
                print(f"🔧 DEBUG: Unit A COMPLETED successfully in {self.processing_times['unit_a']}ms")
                print(f"Unit A completed in {self.processing_times['unit_a']}ms")
            except TimeoutError:
                self.processing_times['unit_a'] = int((time.time() - unit_a_start) * 1000)
                return self.error_handler.handle_pipeline_error(
                    "Unit A timed out after 180 seconds", time.time() - start_time, self.processing_times
                )
            
            if "error" in foundation_result.get("foundation_calculations", {}):
                return self.error_handler.handle_unit_error("Unit A", foundation_result, self.processing_times)
            
            # Unit B: Energy & Structure with timeout
            print("🔧 DEBUG: Starting Unit B processing...")
            print("Processing Unit B: Energy & Structure... (timeout: 300s)")
            unit_b_start = time.time()
            try:
                print("🔧 DEBUG: Calling Unit B process_energy_structure...")
                energy_result = execute_with_timeout(
                    self.unit_b.process_energy_structure,
                    300,  # 5 minute timeout
                    session_data, foundation_result
                )
                self.processing_times['unit_b'] = int((time.time() - unit_b_start) * 1000)
                print(f"🔧 DEBUG: Unit B COMPLETED successfully in {self.processing_times['unit_b']}ms")
                print(f"Unit B completed in {self.processing_times['unit_b']}ms")
            except TimeoutError:
                self.processing_times['unit_b'] = int((time.time() - unit_b_start) * 1000)
                return self.error_handler.handle_pipeline_error(
                    "Unit B timed out after 300 seconds", time.time() - start_time, self.processing_times
                )
            
            if "error" in energy_result.get("energy_structure_calculations", {}):
                return self.error_handler.handle_unit_error("Unit B", energy_result, self.processing_times)
            
            # Unit C: Advanced Dynamics with timeout
            print("Processing Unit C: Advanced Dynamics... (timeout: 180s)")
            unit_c_start = time.time()
            try:
                dynamics_result = execute_with_timeout(
                    self.unit_c.process_advanced_dynamics,
                    180,  # 3 minute timeout
                    session_data, foundation_result, energy_result
                )
                self.processing_times['unit_c'] = int((time.time() - unit_c_start) * 1000)
                print(f"Unit C completed in {self.processing_times['unit_c']}ms")
            except TimeoutError:
                self.processing_times['unit_c'] = int((time.time() - unit_c_start) * 1000)
                return self.error_handler.handle_pipeline_error(
                    "Unit C timed out after 180 seconds", time.time() - start_time, self.processing_times
                )
            
            if "error" in dynamics_result.get("advanced_dynamics", {}):
                return self.error_handler.handle_unit_error("Unit C", dynamics_result, self.processing_times)
            
            # Unit D: Integration & Validation with timeout
            print("Processing Unit D: Integration & Validation... (timeout: 120s)")
            unit_d_start = time.time()
            try:
                validation_result = execute_with_timeout(
                    self.unit_d.process_integration_validation,
                    120,  # 2 minute timeout
                    session_data, foundation_result, energy_result, dynamics_result
                )
                self.processing_times['unit_d'] = int((time.time() - unit_d_start) * 1000)
                print(f"Unit D completed in {self.processing_times['unit_d']}ms")
            except TimeoutError:
                self.processing_times['unit_d'] = int((time.time() - unit_d_start) * 1000)
                return self.error_handler.handle_pipeline_error(
                    "Unit D timed out after 120 seconds", time.time() - start_time, self.processing_times
                )
            
            if "error" in validation_result.get("integration_validation", {}):
                return self.error_handler.handle_unit_error("Unit D", validation_result, self.processing_times)
            
            # Compile final results
            total_processing_time = int((time.time() - start_time) * 1000)
            
            print(f"Compiling final results...")
            print(f"Foundation result keys: {list(foundation_result.keys())}")
            print(f"Energy result keys: {list(energy_result.keys())}")
            print(f"Dynamics result keys: {list(dynamics_result.keys())}")
            print(f"Validation result keys: {list(validation_result.keys())}")
            
            final_result = {
                "grok_enhanced_calculations": {
                    "unit_a_foundation": foundation_result,  # Preserve complete unit results
                    "unit_b_energy_structure": energy_result,  # Preserve complete unit results
                    "unit_c_advanced_dynamics": dynamics_result,  # Preserve complete unit results
                    "unit_d_integration_validation": validation_result  # Preserve complete unit results
                },
                "pipeline_metadata": {
                    "total_processing_time_ms": total_processing_time,
                    "individual_unit_times": self.processing_times,
                    "pipeline_status": "complete",
                    "equations_processed": 78,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                },
                "original_session_data": session_data,
                "tracker_context": self.tracker_context
            }
            
            self.results = final_result
            print(f"Pipeline completed successfully in {total_processing_time}ms")
            
            # Generate output tracker files for next session (MISSION CRITICAL)
            self._generate_output_tracker_files(session_data, final_result)
            
            return final_result
            
        except Exception as e:
            return self.error_handler.handle_pipeline_error(str(e), time.time() - start_time, self.processing_times)
    
    def process_session_legacy(self, session_data: Dict[Any, Any], micro_timing: Dict[Any, Any]) -> Dict[Any, Any]:
        """
        Legacy method for backward compatibility with existing code.
        
        Args:
            session_data: Base session JSON data
            micro_timing: Micro timing analysis data
            
        Returns:
            Complete results from all units with timing information
        """
        # Use the new method with empty tracker contexts
        return self.process_session(session_data, None, None, None)
    
    # 🏭 ARCHITECTURAL IMPROVEMENT: Removed obsolete error handling methods
    # Error handling is now centralized in PipelineErrorHandler
    
    def save_results(self, filepath: str) -> None:
        """Save pipeline results to JSON file."""
        if self.results:
            save_json_data(self.results, filepath)
            print(f"Results saved to {filepath}")
        else:
            print("No results to save")
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the last pipeline execution."""
        if not self.results:
            return {"status": "no_results"}
        
        metadata = self.results.get("pipeline_metadata", {})
        
        return {
            "status": metadata.get("pipeline_status", "unknown"),
            "total_time_ms": metadata.get("total_processing_time_ms", 0),
            "equations_processed": metadata.get("equations_processed", 0),
            "unit_times": metadata.get("individual_unit_times", {}),
            "timestamp": metadata.get("timestamp", "unknown")
        }
    
    def _generate_output_tracker_files(self, session_data: Dict[Any, Any], pipeline_results: Dict[Any, Any]) -> None:
        """Generate output tracker files for the next session (MISSION CRITICAL)."""
        
        try:
            # Extract session info for file naming
            session_metadata = session_data.get("session_metadata", {})
            session_type = session_metadata.get("session_type", "unknown")
            date = session_metadata.get("date", "unknown")
            
            print(f"🔄 Generating output tracker files for {session_type}...")
            
            # Generate HTF Context output
            htf_output = self.tracker_manager.generate_output_htf_context(pipeline_results)
            htf_filename = f"HTF_Context_{session_type}_grokEnhanced_{date}.json"
            save_json_data(htf_output, htf_filename)
            print(f"📤 Generated: {htf_filename}")
            
            # Generate FVG State output  
            fvg_output = self.tracker_manager.generate_output_fvg_state(pipeline_results)
            fvg_filename = f"FVG_State_{session_type}_grokEnhanced_{date}.json"
            save_json_data(fvg_output, fvg_filename)
            print(f"📤 Generated: {fvg_filename}")
            
            # Generate Liquidity State output
            liquidity_output = self.tracker_manager.generate_output_liquidity_state(pipeline_results)
            liquidity_filename = f"Liquidity_State_{session_type}_grokEnhanced_{date}.json"
            save_json_data(liquidity_output, liquidity_filename)
            print(f"📤 Generated: {liquidity_filename}")
            
            print(f"✅ All tracker files generated successfully for {session_type}")
            
        except Exception as e:
            print(f"❌ Error generating tracker files: {e}")
            # Don't fail the entire pipeline if tracker generation fails
            print("⚠️ Pipeline will continue despite tracker generation error")


# Convenience functions for session processing
def process_single_session_with_trackers(session_file: str, 
                                        htf_context_file: str = None,
                                        fvg_state_file: str = None,
                                        liquidity_state_file: str = None,
                                        output_file: str = None, 
                                        api_key: str = None) -> Dict[Any, Any]:
    """
    Process a single session with tracker context through the complete pipeline.
    
    Args:
        session_file: Path to session JSON file
        htf_context_file: Optional path to HTF context JSON file
        fvg_state_file: Optional path to FVG state JSON file
        liquidity_state_file: Optional path to liquidity state JSON file
        output_file: Optional path to save results
        api_key: Optional Grok API key
        
    Returns:
        Complete pipeline results with tracker integration
    """
    # Load input data
    session_data = load_json_data(session_file)
    
    htf_context = {}
    fvg_state = {}
    liquidity_state = {}
    
    if htf_context_file:
        try:
            htf_context = load_json_data(htf_context_file)
        except (FileNotFoundError, json.JSONDecodeError):
            print(f"Warning: Could not load HTF context from {htf_context_file}")
    
    if fvg_state_file:
        try:
            fvg_state = load_json_data(fvg_state_file)
        except (FileNotFoundError, json.JSONDecodeError):
            print(f"Warning: Could not load FVG state from {fvg_state_file}")
    
    if liquidity_state_file:
        try:
            liquidity_state = load_json_data(liquidity_state_file)
        except (FileNotFoundError, json.JSONDecodeError):
            print(f"Warning: Could not load liquidity state from {liquidity_state_file}")
    
    # Create and run pipeline with tracker context
    pipeline = GrokPipeline(api_key)
    results = pipeline.process_session(session_data, htf_context, fvg_state, liquidity_state)
    
    # Save results if output file specified
    if output_file:
        pipeline.save_results(output_file)
    
    return results


def process_single_session(session_file: str, micro_timing_file: str = None, 
                          output_file: str = None, api_key: str = None) -> Dict[Any, Any]:
    """
    Process a single session through the complete pipeline.
    
    Args:
        session_file: Path to session JSON file
        micro_timing_file: Optional path to micro timing analysis JSON file (if None, uses data from session file)
        output_file: Optional path to save results
        api_key: Optional Grok API key
        
    Returns:
        Complete pipeline results
    """
    # Load input data
    session_data = load_json_data(session_file)
    
    if micro_timing_file:
        micro_timing = load_json_data(micro_timing_file)
    else:
        # Extract micro timing from session data
        micro_timing = session_data.get("micro_timing_analysis", {})
    
    # Create and run pipeline (legacy method)
    pipeline = GrokPipeline(api_key)
    results = pipeline.process_session_legacy(session_data, micro_timing)
    
    # Save results if output file specified
    if output_file:
        pipeline.save_results(output_file)
    
    return results


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python pipeline.py <session_file> <micro_timing_file> [output_file]")
        sys.exit(1)
    
    session_file = sys.argv[1]
    micro_timing_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    results = process_single_session(session_file, micro_timing_file, output_file)
    
    # Print summary
    pipeline = GrokPipeline()
    pipeline.results = results
    summary = pipeline.get_summary()
    
    print("\nPipeline Summary:")
    print(f"Status: {summary['status']}")
    print(f"Total Processing Time: {summary['total_time_ms']}ms")
    print(f"Equations Processed: {summary['equations_processed']}")
    print(f"Individual Unit Times: {summary['unit_times']}")