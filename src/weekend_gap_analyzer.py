#!/usr/bin/env python3
"""
Weekend Gap Analyzer for Post-Weekend Asia Session Prediction
Analyzes Friday close vs Sunday/Monday New Week Opening Gap (NWOG) 
for enhanced Asia session predictions with gap magnitude and news integration.
"""

import json
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

@dataclass
class WeekendGapAnalysis:
    """Analysis results for weekend gap characteristics."""
    gap_magnitude: float           # Absolute gap size in points
    gap_percentage: float          # Gap as percentage of Friday close
    gap_direction: str            # 'bullish_gap_up', 'bearish_gap_down', 'minimal_gap'
    gap_severity: str             # 'major', 'moderate', 'minor'
    friday_close: float           # Previous week's closing price
    sunday_open: float            # New week opening price (NWOG)
    liquidity_gap_zones: List[Dict[str, Any]]  # Price zones requiring fill
    weekend_news_impact: Dict[str, Any]        # News accumulation analysis
    asia_prediction_multipliers: Dict[str, float]  # Prediction adjustment factors

@dataclass
class WeekendNewsAccumulation:
    """Weekend news accumulation with severity scoring."""
    total_news_events: int
    high_impact_count: int        # Central bank, major economic data
    medium_impact_count: int      # Regional developments, corporate news
    geopolitical_events: int      # Weekend geopolitical developments
    news_severity_score: float    # 0.0-1.0 cumulative impact score
    dominant_sentiment: str       # 'bullish', 'bearish', 'neutral', 'mixed'
    news_multiplier: float        # Timing prediction adjustment (0.2x-1.0x)

class WeekendGapAnalyzer:
    """
    Analyzes weekend gaps and news accumulation for enhanced Asia session predictions.
    Integrates with existing Monte Carlo and Hawkes systems.
    """
    
    def __init__(self):
        self.logger = logging.getLogger('WeekendGapAnalyzer')
        
        # Gap severity thresholds (in points)
        self.gap_thresholds = {
            'major': 50.0,      # >50 points = major weekend development
            'moderate': 20.0,   # 20-50 points = moderate gap
            'minor': 5.0        # 5-20 points = minor gap
        }
        
        # News impact multipliers for prediction timing
        self.news_impact_multipliers = {
            'extreme_weekend': 0.2,     # Major weekend crisis/breakthrough
            'high_weekend': 0.4,        # Important weekend developments  
            'moderate_weekend': 0.6,    # Standard weekend news
            'minimal_weekend': 0.8,     # Quiet weekend
            'no_weekend_news': 1.0      # No significant news
        }
    
    def analyze_weekend_gap(self, friday_close: float, sunday_open: float, 
                          weekend_news: Optional[Dict[str, Any]] = None) -> WeekendGapAnalysis:
        """
        Analyze weekend gap characteristics and implications for Asia session.
        
        Args:
            friday_close: Previous week's closing price
            sunday_open: New Week Opening Gap (NWOG) price
            weekend_news: Optional news accumulation data
            
        Returns:
            Comprehensive weekend gap analysis
        """
        # Calculate gap metrics
        gap_magnitude = abs(sunday_open - friday_close)
        gap_percentage = (gap_magnitude / friday_close) * 100
        gap_direction = self._determine_gap_direction(friday_close, sunday_open)
        gap_severity = self._classify_gap_severity(gap_magnitude)
        
        # Analyze liquidity gap zones
        liquidity_zones = self._identify_liquidity_gap_zones(friday_close, sunday_open)
        
        # Process weekend news if provided
        news_analysis = self._analyze_weekend_news(weekend_news) if weekend_news else None
        
        # Calculate prediction multipliers
        prediction_multipliers = self._calculate_prediction_multipliers(
            gap_severity, gap_direction, news_analysis
        )
        
        self.logger.info(f"🌏 Weekend Gap Analysis: {gap_magnitude:.1f}pts ({gap_percentage:.2f}%) {gap_direction} - {gap_severity}")
        
        return WeekendGapAnalysis(
            gap_magnitude=gap_magnitude,
            gap_percentage=gap_percentage,
            gap_direction=gap_direction,
            gap_severity=gap_severity,
            friday_close=friday_close,
            sunday_open=sunday_open,
            liquidity_gap_zones=liquidity_zones,
            weekend_news_impact=news_analysis.__dict__ if news_analysis else {},
            asia_prediction_multipliers=prediction_multipliers
        )
    
    def _determine_gap_direction(self, friday_close: float, sunday_open: float) -> str:
        """Classify gap direction and bias."""
        gap_size = sunday_open - friday_close
        gap_percentage = abs(gap_size / friday_close) * 100
        
        if gap_percentage < 0.1:  # Less than 0.1% gap
            return 'minimal_gap'
        elif gap_size > 0:
            return 'bullish_gap_up'
        else:
            return 'bearish_gap_down'
    
    def _classify_gap_severity(self, gap_magnitude: float) -> str:
        """Classify gap severity based on point magnitude."""
        if gap_magnitude >= self.gap_thresholds['major']:
            return 'major'
        elif gap_magnitude >= self.gap_thresholds['moderate']:
            return 'moderate'
        elif gap_magnitude >= self.gap_thresholds['minor']:
            return 'minor'
        else:
            return 'minimal'
    
    def _identify_liquidity_gap_zones(self, friday_close: float, sunday_open: float) -> List[Dict[str, Any]]:
        """
        Identify price zones that represent liquidity gaps requiring potential fill.
        
        Returns:
            List of liquidity gap zones with fill probability and timing estimates
        """
        gap_magnitude = abs(sunday_open - friday_close)
        gap_direction = 'up' if sunday_open > friday_close else 'down'
        
        if gap_magnitude < 5.0:  # Minimal gap
            return []
        
        # Create gap zones based on gap size
        zones = []
        
        if gap_direction == 'up':
            # Bullish gap - zones below Sunday open that may get filled
            zones.append({
                'zone_type': 'gap_fill_zone',
                'price_low': friday_close,
                'price_high': sunday_open,
                'fill_probability': min(0.9, gap_magnitude / 100.0),  # Higher prob for larger gaps
                'expected_fill_timing': 'asia_session_early' if gap_magnitude > 30 else 'asia_session_mid'
            })
        else:
            # Bearish gap - zones above Sunday open that may get filled
            zones.append({
                'zone_type': 'gap_fill_zone', 
                'price_low': sunday_open,
                'price_high': friday_close,
                'fill_probability': min(0.9, gap_magnitude / 100.0),
                'expected_fill_timing': 'asia_session_early' if gap_magnitude > 30 else 'asia_session_mid'
            })
        
        return zones
    
    def _analyze_weekend_news(self, weekend_news: Dict[str, Any]) -> WeekendNewsAccumulation:
        """
        Analyze weekend news accumulation and calculate impact scores.
        
        Args:
            weekend_news: Dictionary with news events and classifications
            
        Returns:
            Weekend news analysis with severity scoring
        """
        # Extract news counts by impact level
        high_impact = weekend_news.get('high_impact_events', [])
        medium_impact = weekend_news.get('medium_impact_events', [])
        geopolitical = weekend_news.get('geopolitical_events', [])
        
        total_events = len(high_impact) + len(medium_impact) + len(geopolitical)
        
        # Calculate severity score (0.0-1.0)
        severity_score = (
            len(high_impact) * 0.5 +      # High impact events
            len(medium_impact) * 0.2 +    # Medium impact events  
            len(geopolitical) * 0.3       # Geopolitical events
        )
        severity_score = min(1.0, severity_score)  # Cap at 1.0
        
        # Determine dominant sentiment
        sentiment_scores = weekend_news.get('sentiment_analysis', {})
        dominant_sentiment = max(sentiment_scores, key=sentiment_scores.get) if sentiment_scores else 'neutral'
        
        # Calculate timing multiplier based on severity
        if severity_score >= 0.8:
            news_multiplier = self.news_impact_multipliers['extreme_weekend']
        elif severity_score >= 0.5:
            news_multiplier = self.news_impact_multipliers['high_weekend']
        elif severity_score >= 0.2:
            news_multiplier = self.news_impact_multipliers['moderate_weekend']
        elif severity_score > 0:
            news_multiplier = self.news_impact_multipliers['minimal_weekend']
        else:
            news_multiplier = self.news_impact_multipliers['no_weekend_news']
        
        return WeekendNewsAccumulation(
            total_news_events=total_events,
            high_impact_count=len(high_impact),
            medium_impact_count=len(medium_impact),
            geopolitical_events=len(geopolitical),
            news_severity_score=severity_score,
            dominant_sentiment=dominant_sentiment,
            news_multiplier=news_multiplier
        )
    
    def _calculate_prediction_multipliers(self, gap_severity: str, gap_direction: str, 
                                        news_analysis: Optional[WeekendNewsAccumulation]) -> Dict[str, float]:
        """
        Calculate prediction adjustment multipliers for different forecasting methods.
        
        Returns:
            Dictionary of multipliers for Monte Carlo, Hawkes, and other prediction methods
        """
        base_multipliers = {
            'monte_carlo_timing': 1.0,
            'hawkes_cascade': 1.0, 
            'hmm_state_transition': 1.0,
            'volume_momentum': 1.0
        }
        
        # Gap severity adjustments
        gap_adjustments = {
            'major': {'monte_carlo_timing': 0.3, 'hawkes_cascade': 0.4, 'hmm_state_transition': 0.5},
            'moderate': {'monte_carlo_timing': 0.6, 'hawkes_cascade': 0.7, 'hmm_state_transition': 0.8}, 
            'minor': {'monte_carlo_timing': 0.8, 'hawkes_cascade': 0.9, 'hmm_state_transition': 0.9},
            'minimal': {'monte_carlo_timing': 1.0, 'hawkes_cascade': 1.0, 'hmm_state_transition': 1.0}
        }
        
        # Apply gap severity adjustments
        if gap_severity in gap_adjustments:
            for method, adjustment in gap_adjustments[gap_severity].items():
                base_multipliers[method] *= adjustment
        
        # Apply news impact if available
        if news_analysis:
            news_factor = news_analysis.news_multiplier
            for method in base_multipliers:
                base_multipliers[method] *= news_factor
        
        # Gap direction bias (affects certain methods more)
        if gap_direction != 'minimal_gap':
            # HMM and volume methods more sensitive to directional bias
            base_multipliers['hmm_state_transition'] *= 0.8
            base_multipliers['volume_momentum'] *= 0.7
        
        return base_multipliers
    
    def generate_asia_session_context(self, gap_analysis: WeekendGapAnalysis) -> Dict[str, Any]:
        """
        Generate enhanced context for Asia session prediction systems.
        
        Args:
            gap_analysis: Weekend gap analysis results
            
        Returns:
            Context dictionary for use with existing prediction systems
        """
        return {
            'weekend_gap_context': {
                'gap_characteristics': {
                    'magnitude_points': gap_analysis.gap_magnitude,
                    'percentage': gap_analysis.gap_percentage,
                    'direction': gap_analysis.gap_direction,
                    'severity': gap_analysis.gap_severity
                },
                'liquidity_implications': {
                    'gap_zones': gap_analysis.liquidity_gap_zones,
                    'fill_expectations': len(gap_analysis.liquidity_gap_zones) > 0
                },
                'prediction_adjustments': gap_analysis.asia_prediction_multipliers,
                'enhanced_monte_carlo': {
                    'weekend_multiplier': gap_analysis.asia_prediction_multipliers.get('monte_carlo_timing', 1.0),
                    'gap_magnetism_factor': min(2.0, gap_analysis.gap_magnitude / 25.0)  # Stronger pull for larger gaps
                },
                'enhanced_hawkes': {
                    'weekend_intensity_adjustment': gap_analysis.asia_prediction_multipliers.get('hawkes_cascade', 1.0),
                    'gap_threshold_modifier': 1.2 if gap_analysis.gap_severity in ['major', 'moderate'] else 1.0
                }
            },
            'weekend_news_impact': gap_analysis.weekend_news_impact,
            'session_bias_expectation': self._determine_session_bias(gap_analysis)
        }
    
    def _determine_session_bias(self, gap_analysis: WeekendGapAnalysis) -> str:
        """Determine expected Asia session bias based on gap analysis."""
        if gap_analysis.gap_severity == 'minimal':
            return 'neutral_range_bound'
        elif gap_analysis.gap_direction == 'bullish_gap_up':
            if gap_analysis.gap_severity == 'major':
                return 'gap_fill_bearish_bias'  # Large gaps often get filled
            else:
                return 'continuation_bullish_bias'
        elif gap_analysis.gap_direction == 'bearish_gap_down':
            if gap_analysis.gap_severity == 'major':
                return 'gap_fill_bullish_bias'  # Large gaps often get filled
            else:
                return 'continuation_bearish_bias'
        else:
            return 'neutral_range_bound'

def test_weekend_gap_analyzer():
    """Test weekend gap analyzer with sample data."""
    analyzer = WeekendGapAnalyzer()
    
    # Test scenario: Major bullish weekend gap with high-impact news
    friday_close = 23250.0
    sunday_open = 23320.0  # 70-point gap up
    
    weekend_news = {
        'high_impact_events': [
            {'event': 'Fed Chair Weekend Speech - Dovish Signals', 'impact': 'high', 'sentiment': 'bullish'},
            {'event': 'China Economic Support Package Announced', 'impact': 'high', 'sentiment': 'bullish'}
        ],
        'medium_impact_events': [
            {'event': 'European PMI Preliminary Strong', 'impact': 'medium', 'sentiment': 'bullish'}
        ],
        'geopolitical_events': [],
        'sentiment_analysis': {'bullish': 0.8, 'bearish': 0.1, 'neutral': 0.1}
    }
    
    gap_analysis = analyzer.analyze_weekend_gap(friday_close, sunday_open, weekend_news)
    asia_context = analyzer.generate_asia_session_context(gap_analysis)
    
    print("🌏 WEEKEND GAP ANALYSIS TEST")
    print("=" * 50)
    print(f"Gap Analysis: {gap_analysis.gap_magnitude:.1f}pts ({gap_analysis.gap_percentage:.2f}%) {gap_analysis.gap_direction}")
    print(f"Severity: {gap_analysis.gap_severity}")
    print(f"News Impact: {gap_analysis.weekend_news_impact}")
    print(f"Prediction Multipliers: {gap_analysis.asia_prediction_multipliers}")
    print("\n🔮 Asia Session Context:")
    print(json.dumps(asia_context, indent=2))
    
    return gap_analysis, asia_context

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_weekend_gap_analyzer()