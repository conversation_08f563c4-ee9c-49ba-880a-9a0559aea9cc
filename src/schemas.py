#!/usr/bin/env python3
"""
Pydantic schemas for Grok structured outputs
Defines the JSON response format for each computational unit
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime


class HybridVolumeCalculations(BaseModel):
    """Schema for hybrid volume calculations in Unit A"""
    alpha_t: float = Field(description="Alpha coefficient for volume hybrid calculation")
    v_synthetic: float = Field(description="Synthetic volume value")
    v_traditional: float = Field(description="Traditional volume value")  
    v_hybrid: float = Field(description="Final hybrid volume calculation")
    q_t: float = Field(description="Q coefficient from rate per minute")
    r_t: float = Field(description="R coefficient from intensity trend")


class TimeDilationBase(BaseModel):
    """Schema for time dilation base calculations"""
    gamma_base: float = Field(description="Base gamma coefficient")
    alpha_grad_scaled: float = Field(description="Scaled gradient alpha")
    volume_factor: float = Field(description="Volume factor in dilation calculation")
    h_score: float = Field(description="Session harmonic score")


class ParametersValidated(BaseModel):
    """Schema for parameter validation results"""
    all_within_bounds: bool = Field(description="Whether all parameters are within valid bounds")
    beta_HTF: float = Field(description="Beta HTF parameter value")
    sigma_HTF: float = Field(description="Sigma HTF parameter value")
    alpha_grad: float = Field(description="Alpha gradient parameter value")
    d_htf: float = Field(description="D HTF parameter value")


class UnitAResponse(BaseModel):
    """Complete response schema for Unit A Foundation Calculations"""
    foundation_calculations: Dict[str, Any] = Field(description="Foundation calculation results")
    hybrid_volume: HybridVolumeCalculations = Field(description="Hybrid volume calculations")
    time_dilation_base: TimeDilationBase = Field(description="Time dilation base calculations")
    parameters_validated: ParametersValidated = Field(description="Parameter validation results")
    unit_status: str = Field(description="Processing status", default="complete")
    processing_time_ms: int = Field(description="Processing time in milliseconds")


class EnergyAccumulation(BaseModel):
    """Schema for energy accumulation calculations in Unit B"""
    energy_rate: float = Field(description="Energy accumulation rate")
    total_accumulated: float = Field(description="Total energy accumulated")
    accumulation_phase: str = Field(description="Current accumulation phase")
    efficiency_factor: float = Field(description="Energy accumulation efficiency")


class GradientDynamics(BaseModel):
    """Schema for gradient dynamics calculations"""
    intensity_coefficient: float = Field(description="Gradient intensity coefficient")
    direction_factor: float = Field(description="Gradient direction factor")
    stability_index: float = Field(description="Gradient stability index")
    momentum_transfer: float = Field(description="Momentum transfer coefficient")


class UnitBResponse(BaseModel):
    """Complete response schema for Unit B Energy & Structure"""
    energy_structure_calculations: Dict[str, Any] = Field(description="Energy structure calculation results")
    energy_accumulation: EnergyAccumulation = Field(description="Energy accumulation results")
    gradient_dynamics: GradientDynamics = Field(description="Gradient dynamics results")
    structural_integrity: float = Field(description="Structural integrity coefficient")
    unit_status: str = Field(description="Processing status", default="complete")
    processing_time_ms: int = Field(description="Processing time in milliseconds")


class TemporalMomentum(BaseModel):
    """Schema for temporal momentum calculations in Unit C"""
    momentum_strength: float = Field(description="Temporal momentum strength")
    momentum_direction: str = Field(description="Momentum direction classification")
    decay_coefficient: float = Field(description="Momentum decay coefficient")
    persistence_factor: float = Field(description="Momentum persistence factor")


class ConsolidationAnalysis(BaseModel):
    """Schema for consolidation analysis results"""
    consolidation_strength: float = Field(description="Consolidation strength coefficient")
    breakout_probability: float = Field(description="Breakout probability assessment")
    consolidation_duration: int = Field(description="Consolidation duration in minutes")
    stability_rating: float = Field(description="Consolidation stability rating")


class UnitCResponse(BaseModel):
    """Complete response schema for Unit C Advanced Dynamics"""
    advanced_dynamics: Dict[str, Any] = Field(description="Advanced dynamics calculation results")
    temporal_momentum: TemporalMomentum = Field(description="Temporal momentum analysis")
    consolidation_analysis: ConsolidationAnalysis = Field(description="Consolidation analysis results")
    frequency_analysis: Dict[str, float] = Field(description="Frequency analysis results")
    unit_status: str = Field(description="Processing status", default="complete")
    processing_time_ms: int = Field(description="Processing time in milliseconds")


class ValidationResults(BaseModel):
    """Schema for integration validation results in Unit D"""
    integration_score: float = Field(description="Overall integration score")
    consistency_check: bool = Field(description="Whether calculations are consistent")
    convergence_achieved: bool = Field(description="Whether convergence was achieved")
    error_margins: Dict[str, float] = Field(description="Error margins for each calculation")


class SystemIntegration(BaseModel):
    """Schema for system integration analysis"""
    units_synchronized: bool = Field(description="Whether all units are synchronized")
    cross_validation_passed: bool = Field(description="Whether cross-validation passed")
    final_confidence: float = Field(description="Final confidence score for results")
    recommendations: List[str] = Field(description="System recommendations")


class UnitDResponse(BaseModel):
    """Complete response schema for Unit D Integration & Validation"""
    integration_validation: Dict[str, Any] = Field(description="Integration validation results")
    validation_results: ValidationResults = Field(description="Validation analysis results")
    system_integration: SystemIntegration = Field(description="System integration analysis")
    quality_metrics: Dict[str, float] = Field(description="Quality metrics for the entire pipeline")
    unit_status: str = Field(description="Processing status", default="complete")
    processing_time_ms: int = Field(description="Processing time in milliseconds")


# Tracker State Schemas

class HTFStructure(BaseModel):
    """Schema for Higher Timeframe structure definition"""
    structure_id: str = Field(description="Unique identifier for the structure")
    structure_type: str = Field(description="Type of structure (e.g., support, resistance, trendline)")
    level: float = Field(description="Price level of the structure")
    strength: float = Field(description="Structure strength coefficient")
    recency_factor: float = Field(description="Recency factor for structure relevance")
    formation_time: str = Field(description="Time when structure was formed")
    validation_strength: Optional[float] = Field(description="Post-validation strength", default=None)


class LiquidityLevel(BaseModel):
    """Schema for untaken liquidity level definition"""
    level_id: str = Field(description="Unique identifier for the liquidity level")
    level: float = Field(description="Price level of liquidity")
    weight: float = Field(description="Weight/importance of the liquidity level")
    level_type: str = Field(description="Type of liquidity (e.g., stop_hunt, gap_fill, institutional)")
    time_created: str = Field(description="Time when level was identified")
    interactions: int = Field(description="Number of previous interactions", default=0)


class FVGCarryoverMetadata(BaseModel):
    """Schema for FVG carryover metadata"""
    t_memory_final: float = Field(description="Final T_memory value from previous session")
    last_interaction_time: str = Field(description="Time of last FVG interaction")
    carryover_strength: float = Field(description="Strength of carryover effect")
    decay_applied: bool = Field(description="Whether decay has been applied", default=False)


class HTFContextSchema(BaseModel):
    """Schema for Higher Timeframe context input"""
    context_id: str = Field(description="Unique identifier for HTF context")
    active_structures: List[HTFStructure] = Field(description="List of active HTF structures")
    context_timestamp: str = Field(description="Timestamp of context creation")
    precedence_weight: float = Field(description="Overall HTF precedence weight", default=0.35)


class FVGStateSchema(BaseModel):
    """Schema for FVG state tracking input"""
    state_id: str = Field(description="Unique identifier for FVG state")
    fpfvg_carryover_metadata: FVGCarryoverMetadata = Field(description="FVG carryover metadata")
    active_gaps: List[Dict[str, Any]] = Field(description="Currently active FVG levels")
    state_timestamp: str = Field(description="Timestamp of state creation")


class LiquidityStateSchema(BaseModel):
    """Schema for liquidity state registry input"""
    registry_id: str = Field(description="Unique identifier for liquidity registry")
    untaken_liquidity_registry: List[LiquidityLevel] = Field(description="Registry of untaken liquidity levels")
    registry_timestamp: str = Field(description="Timestamp of registry creation")
    gradient_strength: float = Field(description="Overall liquidity gradient strength", default=0.0)


class TrackerContextSchema(BaseModel):
    """Schema for processed tracker context"""
    t_memory: float = Field(description="Current T_memory value")
    active_structures: List[HTFStructure] = Field(description="Active HTF structures")
    untaken_liquidity: List[LiquidityLevel] = Field(description="Untaken liquidity levels")
    liquidity_gradient: Dict[str, float] = Field(description="Liquidity gradient metrics")
    htf_influence_factor: float = Field(description="HTF influence factor")
    tracker_timestamp: str = Field(description="Timestamp of tracker context creation")


class UpdatedTrackerStatesSchema(BaseModel):
    """Schema for updated tracker states after processing"""
    updated_t_memory: float = Field(description="Updated T_memory value after processing")
    updated_liquidity_registry: List[LiquidityLevel] = Field(description="Updated liquidity registry")
    updated_htf_structures: List[HTFStructure] = Field(description="Updated HTF structures")
    updated_liquidity_gradient: Dict[str, float] = Field(description="Updated liquidity gradient metrics")
    processing_metadata: Dict[str, Any] = Field(description="Metadata about the update process")
    update_timestamp: str = Field(description="Timestamp of tracker state update")


class EnhancedGrokTemplateResponse(BaseModel):
    """Schema for enhanced grokEnhanced template with tracker updates"""
    grok_enhanced_template: Dict[str, Any] = Field(description="Complete grokEnhanced template")
    updated_tracker_states: UpdatedTrackerStatesSchema = Field(description="Updated tracker states")
    template_metadata: Dict[str, Any] = Field(description="Template generation metadata")
    generation_timestamp: str = Field(description="Timestamp of template generation")