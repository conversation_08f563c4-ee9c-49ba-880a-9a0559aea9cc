#!/usr/bin/env python3
"""
Tracker State Management System
Handles FVG state, HTF context, and liquidity state for enhanced pipeline processing.
"""

import math
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

try:
    from .utils import load_json_data, save_json_data
except ImportError:
    from utils import load_json_data, save_json_data


class TrackerStateManager:
    """Manages tracker states for enhanced context-aware processing."""
    
    def __init__(self):
        """Initialize tracker state manager with constants."""
        # Tracker decay constants
        self.LAMBDA_MEM = 0.06  # Memory decay constant
        self.PHI_LIQUIDITY = 0.11  # Liquidity gradient constant
        self.T_MEMORY_MIN = 5.0  # Minimum T_memory value (minutes)
        self.T_MEMORY_MAX = 180.0  # Maximum T_memory value (minutes)
        
        # Distance calculation parameters
        self.DISTANCE_SCALE_FACTOR = 0.1  # Scale factor for liquidity gradient
        self.HTF_PRECEDENCE_WEIGHT = 0.35  # HTF structure weight
        
    def extract_tracker_context(self, htf_context: Dict[Any, Any], 
                               fvg_state: Dict[Any, Any], 
                               liquidity_state: Dict[Any, Any]) -> Dict[str, Any]:
        """
        Extract and process tracker context for pipeline consumption.
        
        Args:
            htf_context: Higher timeframe context data
            fvg_state: FVG state tracking data
            liquidity_state: Liquidity state registry
            
        Returns:
            Processed tracker context for pipeline units
        """
        # Extract T_memory from FVG carryover metadata
        t_memory_raw = self._extract_t_memory(fvg_state)
        
        # Extract active HTF structures
        active_structures = self._extract_htf_structures(htf_context)
        
        # Extract untaken liquidity registry
        untaken_liquidity = self._extract_untaken_liquidity(liquidity_state)
        
        # Calculate derived metrics
        htf_influence_factor = self._calculate_htf_influence(active_structures)
        
        # Calculate comprehensive liquidity gradient with a default current price
        # (In real usage, this would come from the session data)
        default_current_price = 23300.0  # Default for testing
        liquidity_gradient = self.calculate_liquidity_gradient_distances(default_current_price, untaken_liquidity)
        
        return {
            "t_memory": t_memory_raw,
            "active_structures": active_structures,
            "untaken_liquidity": untaken_liquidity,
            "liquidity_gradient": liquidity_gradient,
            "htf_influence_factor": htf_influence_factor,
            "tracker_timestamp": datetime.now().isoformat()
        }
    
    def extract_tracker_essentials(self, tracker_context: Dict[str, Any], 
                                  session_data: Dict[Any, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Extract minimal tracker data needed per unit to prevent API timeouts.
        
        Args:
            tracker_context: Full tracker context from extract_tracker_context()
            session_data: Session data for price reference
            
        Returns:
            Dictionary with minimal data per unit
        """
        current_price = session_data.get('price_data', {}).get('close', 23300.0)
        
        # Extract minimal data for Unit A
        unit_a_essentials = {
            "t_memory": tracker_context.get('t_memory', 15.0),
            "nearest_htf_distance": self._calculate_nearest_htf_distance(
                tracker_context.get('active_structures', []), current_price
            ),
            "liquidity_gradient_strength": tracker_context.get('liquidity_gradient', {}).get('gradient_strength', 0.0)
        }
        
        # Extract minimal data for Unit B  
        unit_b_essentials = {
            "energy_threshold_adjustment": self.calculate_energy_threshold_adjustment(800.0, tracker_context.get('t_memory', 15.0)),
            "regime_state": self._extract_regime_state(tracker_context.get('active_structures', []))
        }
        
        # Extract minimal data for Unit C
        unit_c_essentials = {
            "htf_structure_count": len(tracker_context.get('active_structures', [])),
            "liquidity_imbalance_direction": tracker_context.get('liquidity_gradient', {}).get('liquidity_bias', 'balanced')
        }
        
        # Extract minimal data for Unit D
        unit_d_essentials = {
            "tracker_validation_score": self._calculate_tracker_validation_score(tracker_context),
            "context_completeness": self._assess_context_completeness(tracker_context)
        }
        
        return {
            "unit_a": unit_a_essentials,
            "unit_b": unit_b_essentials,
            "unit_c": unit_c_essentials,
            "unit_d": unit_d_essentials
        }
    
    def _calculate_nearest_htf_distance(self, htf_structures: List[Dict[str, Any]], current_price: float) -> float:
        """Calculate distance to nearest HTF structure."""
        if not htf_structures:
            return 100.0  # Default large distance
        
        distances = []
        for structure in htf_structures:
            level = structure.get('level', current_price)
            distance = abs(current_price - level)
            distances.append(distance)
        
        return min(distances) if distances else 100.0
    
    def _extract_regime_state(self, htf_structures: List[Dict[str, Any]]) -> str:
        """Extract simplified regime state from HTF structures."""
        if not htf_structures:
            return "neutral"
        
        # Count structure types
        support_count = sum(1 for s in htf_structures if s.get('structure_type') == 'support')
        resistance_count = len(htf_structures) - support_count
        
        if support_count > resistance_count * 1.5:
            return "bullish_regime"
        elif resistance_count > support_count * 1.5:
            return "bearish_regime"
        else:
            return "balanced_regime"
    
    def _calculate_tracker_validation_score(self, tracker_context: Dict[str, Any]) -> float:
        """Calculate overall tracker validation score."""
        t_memory = tracker_context.get('t_memory', 15.0)
        htf_count = len(tracker_context.get('active_structures', []))
        liquidity_count = len(tracker_context.get('untaken_liquidity', []))
        
        # Simple scoring: more context = higher score
        score = min(1.0, (t_memory / 30.0) * 0.4 + (htf_count / 10.0) * 0.3 + (liquidity_count / 10.0) * 0.3)
        return score
    
    def _assess_context_completeness(self, tracker_context: Dict[str, Any]) -> str:
        """Assess completeness of tracker context."""
        t_memory = tracker_context.get('t_memory', 0)
        htf_count = len(tracker_context.get('active_structures', []))
        liquidity_count = len(tracker_context.get('untaken_liquidity', []))
        
        if t_memory > 0 and htf_count > 0 and liquidity_count > 0:
            return "complete"
        elif t_memory > 0 or htf_count > 0 or liquidity_count > 0:
            return "partial"
        else:
            return "minimal"
    
    def calculate_t_memory_decay(self, t_memory_prev: float, hours_elapsed: float) -> float:
        """
        Calculate T_memory decay using exponential formula.
        
        Formula: T_memory(t) = T_memory_prev × e^(-0.06 × hours_elapsed)
        
        Args:
            t_memory_prev: Previous T_memory value
            hours_elapsed: Hours elapsed since last calculation
            
        Returns:
            Decayed T_memory value with bounds applied
        """
        if t_memory_prev <= 0 or hours_elapsed < 0:
            return self.T_MEMORY_MIN
            
        # Apply exponential decay
        t_memory_decayed = t_memory_prev * math.exp(-self.LAMBDA_MEM * hours_elapsed)
        
        # Apply bounds
        return max(self.T_MEMORY_MIN, min(self.T_MEMORY_MAX, t_memory_decayed))
    
    def calculate_energy_threshold_adjustment(self, e_threshold_base: float, t_memory: float) -> float:
        """
        Calculate energy threshold adjustment using T_memory.
        
        Formula: E_threshold_adj = E_threshold × (1 - 0.11 × T_memory)
        
        Args:
            e_threshold_base: Base energy threshold
            t_memory: Current T_memory value
            
        Returns:
            Adjusted energy threshold
        """
        # Normalize T_memory for calculation (0-1 range)
        t_memory_normalized = min(1.0, t_memory / 60.0)  # Normalize to hour scale
        
        # Apply adjustment formula
        adjustment_factor = 1.0 - (self.PHI_LIQUIDITY * t_memory_normalized)
        adjustment_factor = max(0.1, adjustment_factor)  # Prevent negative values
        
        return e_threshold_base * adjustment_factor
    
    def calculate_liquidity_gradient_distances(self, current_price: float, 
                                             untaken_levels: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Calculate distance-based liquidity gradient effects.
        
        Args:
            current_price: Current market price
            untaken_levels: List of untaken liquidity levels
            
        Returns:
            Liquidity gradient metrics
        """
        if not untaken_levels:
            return {
                "nearest_distance": float('inf'),
                "weighted_pull": 0.0,
                "gradient_strength": 0.0,
                "liquidity_bias": "neutral"
            }
        
        distances = []
        weighted_pulls = []
        
        for level in untaken_levels:
            level_price = level.get('level', current_price)
            distance = abs(current_price - level_price)
            weight = level.get('weight', 1.0)
            
            distances.append(distance)
            # Inverse distance weighting
            pull_strength = (weight / (distance + 1.0)) if distance > 0 else weight
            weighted_pulls.append(pull_strength)
        
        nearest_distance = min(distances) if distances else float('inf')
        total_pull = sum(weighted_pulls)
        gradient_strength = total_pull * self.DISTANCE_SCALE_FACTOR
        
        # Determine liquidity bias
        above_levels = sum(1 for level in untaken_levels if level.get('level', current_price) > current_price)
        below_levels = len(untaken_levels) - above_levels
        
        if above_levels > below_levels * 1.5:
            liquidity_bias = "upward_pull"
        elif below_levels > above_levels * 1.5:
            liquidity_bias = "downward_pull"
        else:
            liquidity_bias = "balanced"
        
        return {
            "nearest_distance": nearest_distance,
            "weighted_pull": total_pull,
            "gradient_strength": gradient_strength,
            "liquidity_bias": liquidity_bias
        }
    
    def update_tracker_states_post_processing(self, 
                                            unit_results: Dict[str, Any],
                                            session_data: Dict[Any, Any],
                                            original_trackers: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update tracker states after pipeline processing.
        
        Args:
            unit_results: Results from Units A-D
            session_data: Processed session data
            original_trackers: Original tracker context
            
        Returns:
            Updated tracker states
        """
        # Calculate new T_memory based on session processing
        session_duration = session_data.get('session_metadata', {}).get('duration_minutes', 300)
        hours_processed = session_duration / 60.0
        
        current_t_memory = original_trackers.get('t_memory', 15.0)
        updated_t_memory = self.calculate_t_memory_decay(current_t_memory, hours_processed)
        
        # Update liquidity registry based on session interactions
        updated_liquidity = self._update_liquidity_registry(
            original_trackers.get('untaken_liquidity', []),
            session_data
        )
        
        # Update HTF structure states
        updated_htf_structures = self._update_htf_structures(
            original_trackers.get('active_structures', []),
            unit_results,
            session_data
        )
        
        # Calculate updated gradient metrics
        current_price = session_data.get('price_data', {}).get('close', 0)
        updated_gradient = self.calculate_liquidity_gradient_distances(current_price, updated_liquidity)
        
        return {
            "updated_t_memory": updated_t_memory,
            "updated_liquidity_registry": updated_liquidity,
            "updated_htf_structures": updated_htf_structures,
            "updated_liquidity_gradient": updated_gradient,
            "processing_metadata": {
                "session_duration_hours": hours_processed,
                "t_memory_decay_applied": current_t_memory - updated_t_memory,
                "structures_modified": len(updated_htf_structures),
                "liquidity_levels_updated": len(updated_liquidity)
            },
            "update_timestamp": datetime.now().isoformat()
        }
    
    def _extract_t_memory(self, fvg_state: Dict[Any, Any]) -> float:
        """Extract T_memory from FVG state metadata."""
        try:
            carryover = fvg_state.get('fpfvg_carryover_metadata', {})
            t_memory = carryover.get('t_memory_final', 15.0)
            return max(self.T_MEMORY_MIN, min(self.T_MEMORY_MAX, float(t_memory)))
        except (TypeError, ValueError):
            return 15.0  # Default fallback
    
    def _extract_htf_structures(self, htf_context: Dict[Any, Any]) -> List[Dict[str, Any]]:
        """Extract active HTF structures."""
        try:
            return htf_context.get('active_structures', [])
        except (TypeError, AttributeError):
            return []
    
    def _extract_untaken_liquidity(self, liquidity_state: Dict[Any, Any]) -> List[Dict[str, Any]]:
        """Extract untaken liquidity registry."""
        try:
            return liquidity_state.get('untaken_liquidity_registry', [])
        except (TypeError, AttributeError):
            return []
    
    def _calculate_liquidity_gradient(self, untaken_liquidity: List[Dict[str, Any]]) -> float:
        """Calculate overall liquidity gradient strength."""
        if not untaken_liquidity:
            return 0.0
        
        total_weight = sum(level.get('weight', 1.0) for level in untaken_liquidity)
        return min(2.0, total_weight * self.DISTANCE_SCALE_FACTOR)
    
    def _calculate_htf_influence(self, active_structures: List[Dict[str, Any]]) -> float:
        """Calculate HTF influence factor."""
        if not active_structures:
            return 0.0
        
        # Weight by structure strength and recency
        influence = 0.0
        for structure in active_structures:
            strength = structure.get('strength', 1.0)
            recency = structure.get('recency_factor', 1.0)
            influence += strength * recency * self.HTF_PRECEDENCE_WEIGHT
        
        return min(1.5, influence)
    
    def _update_liquidity_registry(self, original_liquidity: List[Dict[str, Any]], 
                                 session_data: Dict[Any, Any]) -> List[Dict[str, Any]]:
        """Update liquidity registry based on session interactions."""
        updated_registry = original_liquidity.copy()
        
        # Check for liquidity interactions in session
        level_interactions = session_data.get('level_interactions', [])
        
        for interaction in level_interactions:
            level_price = interaction.get('level', 0)
            interaction_type = interaction.get('interaction_type', 'test')
            result = interaction.get('result', 'respected')
            
            # Update or remove levels based on interaction results
            if result == 'broken' and interaction_type == 'sweep':
                # Remove taken liquidity
                updated_registry = [
                    level for level in updated_registry 
                    if abs(level.get('level', 0) - level_price) > 0.5
                ]
        
        return updated_registry
    
    def _update_htf_structures(self, original_structures: List[Dict[str, Any]], 
                             unit_results: Dict[str, Any],
                             session_data: Dict[Any, Any]) -> List[Dict[str, Any]]:
        """Update HTF structure states based on processing results."""
        updated_structures = []
        
        for structure in original_structures:
            # Update structure based on session interactions
            structure_copy = structure.copy()
            
            # Decay recency factor
            current_recency = structure_copy.get('recency_factor', 1.0)
            structure_copy['recency_factor'] = max(0.1, current_recency * 0.9)
            
            # Update based on validation results
            validation = unit_results.get('unit_d_integration_validation', {})
            if validation.get('validation_results', {}).get('convergence_achieved', False):
                structure_copy['validation_strength'] = structure_copy.get('strength', 1.0) * 1.1
            
            updated_structures.append(structure_copy)
        
        return updated_structures
    
    def generate_output_htf_context(self, pipeline_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate HTF context output for the next session."""
        
        # Extract session data for structure creation
        session_data = pipeline_results.get("original_session_data", {})
        structures_identified = session_data.get("structures_identified", {})
        price_data = session_data.get("price_data", {})
        
        # Get session price reference for fallbacks
        session_close = price_data.get("close", 23300)
        session_high = price_data.get("high", session_close + 50)
        session_low = price_data.get("low", session_close - 50)
        
        # Create active structures from session analysis
        active_structures = []
        
        # Add session levels as HTF structures
        session_levels = structures_identified.get("session_levels", [])
        for level in session_levels:
            structure = {
                "level": level.get("level", session_close),
                "structure_type": level.get("type", "support"),
                "strength": 0.8 if level.get("holds", False) else 0.5,
                "recency_factor": 0.95,  # Recent session structures
                "validation_strength": 1.0,
                "created_timestamp": datetime.now().isoformat()
            }
            active_structures.append(structure)
        
        # Add FVG structures  
        fvgs = structures_identified.get("fair_value_gaps", [])
        for fvg in fvgs:
            # Add premium level
            structure_premium = {
                "level": fvg.get("premium_high", session_high),
                "structure_type": "fpfvg_premium",
                "strength": 0.9 if fvg.get("delivery_status") == "delivered" else 0.7,
                "recency_factor": 0.9,
                "validation_strength": 1.0,
                "created_timestamp": datetime.now().isoformat()
            }
            active_structures.append(structure_premium)
            
            # Add discount level
            structure_discount = {
                "level": fvg.get("discount_low", session_low),
                "structure_type": "fpfvg_discount", 
                "strength": 0.8 if fvg.get("delivery_status") == "delivered" else 0.6,
                "recency_factor": 0.9,
                "validation_strength": 1.0,
                "created_timestamp": datetime.now().isoformat()
            }
            active_structures.append(structure_discount)
        
        # If no structures found, create basic support/resistance from price data
        if not active_structures:
            # Create session high as resistance
            high_structure = {
                "level": session_high,
                "structure_type": "session_high",
                "strength": 0.7,
                "recency_factor": 0.95,
                "validation_strength": 1.0,
                "created_timestamp": datetime.now().isoformat()
            }
            active_structures.append(high_structure)
            
            # Create session low as support
            low_structure = {
                "level": session_low,
                "structure_type": "session_low", 
                "strength": 0.7,
                "recency_factor": 0.95,
                "validation_strength": 1.0,
                "created_timestamp": datetime.now().isoformat()
            }
            active_structures.append(low_structure)
        
        # Calculate HTF influence from Unit D validation
        unit_d = pipeline_results.get("grok_enhanced_calculations", {}).get("unit_d_integration_validation", {})
        extracted_values = unit_d.get("extracted_values", {})
        htf_influence = extracted_values.get("structural_integrity", 0.35) * 0.9
        
        return {
            "active_structures": active_structures,
            "htf_influence_factor": htf_influence,
            "last_update": datetime.now().isoformat(),
            "session_continuity": "maintained"
        }
    
    def generate_output_fvg_state(self, pipeline_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate FVG state output for the next session."""
        
        # Extract energy data from Unit D extracted values (where the real enhanced data is)
        unit_d = pipeline_results.get("grok_enhanced_calculations", {}).get("unit_d_integration_validation", {})
        extracted_values = unit_d.get("extracted_values", {})
        
        # Get energy_rate from extracted values with robust fallback
        current_energy_rate = extracted_values.get("energy_rate", 1.0)
        
        # If no extracted values, try to derive from session characteristics
        if current_energy_rate == 1.0 and not extracted_values:
            session_data = pipeline_results.get("original_session_data", {})
            session_metadata = session_data.get("session_metadata", {})
            session_character = session_metadata.get("session_character", "")
            
            # Derive energy rate from session character
            if "expansion" in session_character.lower():
                current_energy_rate = 1.3  # Higher energy for expansion sessions
            elif "consolidation" in session_character.lower():
                current_energy_rate = 0.8  # Lower energy for consolidation
            else:
                # Derive from price range as last resort
                price_data = session_data.get("price_data", {})
                price_range = price_data.get("range", 50)
                # Normalize range to energy rate (50 points = 1.0 baseline)
                current_energy_rate = max(0.5, min(2.0, price_range / 50.0))
        
        # Calculate session duration factor
        session_data = pipeline_results.get("original_session_data", {})
        duration = session_data.get("session_metadata", {}).get("duration_minutes", 300)
        duration_factor = min(1.2, duration / 300.0)  # Normalize around 5-hour sessions
        
        # T_memory based on energy processing and session duration
        base_t_memory = 15.0  # Default
        energy_factor = min(1.5, current_energy_rate / 1.0)  # Normalize around 1.0
        updated_t_memory = base_t_memory * energy_factor * duration_factor
        
        # Clamp to bounds
        updated_t_memory = max(self.T_MEMORY_MIN, min(self.T_MEMORY_MAX, updated_t_memory))
        
        # Calculate FVG density based on session structures
        fvg_count = len(session_data.get("structures_identified", {}).get("fair_value_gaps", []))
        fvg_density = max(1, fvg_count)  # At least 1
        
        return {
            "t_memory": updated_t_memory,
            "energy_carryover": current_energy_rate * 0.7,  # 70% carryover to next session
            "fvg_density": fvg_density,
            "last_update": datetime.now().isoformat(),
            "session_continuity": "maintained"
        }
    
    def generate_output_liquidity_state(self, pipeline_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate liquidity state output for the next session."""
        
        # Extract liquidity analysis from session data
        session_data = pipeline_results.get("original_session_data", {})
        liquidity_analysis = session_data.get("liquidity_analysis", {})
        price_data = session_data.get("price_data", {})
        
        # Get session prices for fallbacks
        session_close = price_data.get("close", 23300)
        session_high = price_data.get("high", session_close + 50)
        session_low = price_data.get("low", session_close - 50)
        
        # Create liquidity registry from session analysis
        untaken_liquidity_registry = []
        
        # Add untaken liquidity from session analysis
        untaken_liquidity_levels = liquidity_analysis.get("untaken_liquidity", [])
        for level_data in untaken_liquidity_levels:
            liquidity_entry = {
                "level": level_data.get("level", session_close),
                "weight": 2.0 if level_data.get("significance") == "high" else 1.0,
                "side": level_data.get("side", "balanced"),
                "significance": level_data.get("significance", "medium"),
                "session_origin": "current_session",
                "created_timestamp": datetime.now().isoformat()
            }
            untaken_liquidity_registry.append(liquidity_entry)
        
        # Add liquidity sweeps as historical context (reduced weight)
        liquidity_sweeps = liquidity_analysis.get("liquidity_sweeps", [])
        for sweep in liquidity_sweeps:
            sweep_entry = {
                "level": sweep.get("swept_level", session_close),
                "weight": 0.5,  # Reduced weight for swept levels
                "side": "neutral",
                "significance": "low",
                "session_origin": "swept_liquidity",
                "created_timestamp": datetime.now().isoformat()
            }
            untaken_liquidity_registry.append(sweep_entry)
        
        # If no liquidity analysis, create basic liquidity zones from price action
        if not untaken_liquidity_registry:
            # Create liquidity zone at session high (potential resistance)
            high_liquidity = {
                "level": session_high,
                "weight": 1.5,
                "side": "sell",
                "significance": "medium",
                "session_origin": "price_action_derived",
                "created_timestamp": datetime.now().isoformat()
            }
            untaken_liquidity_registry.append(high_liquidity)
            
            # Create liquidity zone at session low (potential support)
            low_liquidity = {
                "level": session_low,
                "weight": 1.5,
                "side": "buy",
                "significance": "medium", 
                "session_origin": "price_action_derived",
                "created_timestamp": datetime.now().isoformat()
            }
            untaken_liquidity_registry.append(low_liquidity)
        
        # Calculate liquidity gradient
        total_weight = sum(zone.get("weight", 1.0) for zone in untaken_liquidity_registry)
        gradient_strength = min(2.0, total_weight * self.DISTANCE_SCALE_FACTOR)
        
        # Determine liquidity bias from session character
        session_character = session_data.get("session_metadata", {}).get("session_character", "neutral")
        if "expansion" in session_character.lower():
            liquidity_bias = "expansion_driven"
        elif "consolidation" in session_character.lower():
            liquidity_bias = "consolidation_focused" 
        else:
            # Derive bias from price movement
            price_range = price_data.get("range", 50)
            if price_range > 75:  # High volatility sessions
                liquidity_bias = "volatility_driven"
            else:
                liquidity_bias = "balanced"
        
        return {
            "untaken_liquidity_registry": untaken_liquidity_registry,
            "liquidity_gradient": {
                "gradient_strength": gradient_strength,
                "liquidity_bias": liquidity_bias
            },
            "last_update": datetime.now().isoformat(),
            "session_continuity": "maintained"
        }


def create_tracker_context_from_inputs(htf_context_file: str = None,
                                      fvg_state_file: str = None,
                                      liquidity_state_file: str = None) -> Dict[str, Any]:
    """
    Create tracker context from input files.
    
    Args:
        htf_context_file: Path to HTF context JSON file
        fvg_state_file: Path to FVG state JSON file  
        liquidity_state_file: Path to liquidity state JSON file
        
    Returns:
        Processed tracker context
    """
    manager = TrackerStateManager()
    
    # Load input files
    htf_context = {}
    fvg_state = {}
    liquidity_state = {}
    
    if htf_context_file:
        try:
            # 🔧 STANDARDIZED: Use migration-safe JSON loading
            htf_context = load_json_data(htf_context_file)
        except (FileNotFoundError, Exception):  # Broader exception for JSON errors
            pass
    
    if fvg_state_file:
        try:
            # 🔧 STANDARDIZED: Use migration-safe JSON loading
            fvg_state = load_json_data(fvg_state_file)
        except (FileNotFoundError, Exception):  # Broader exception for JSON errors
            pass
    
    if liquidity_state_file:
        try:
            # 🔧 STANDARDIZED: Use migration-safe JSON loading
            liquidity_state = load_json_data(liquidity_state_file)
        except (FileNotFoundError, Exception):  # Broader exception for JSON errors
            pass
    
    return manager.extract_tracker_context(htf_context, fvg_state, liquidity_state)


if __name__ == "__main__":
    # Example usage and testing
    manager = TrackerStateManager()
    
    # Test T_memory decay
    t_memory_prev = 30.0
    hours_elapsed = 2.0
    decayed = manager.calculate_t_memory_decay(t_memory_prev, hours_elapsed)
    print(f"T_memory decay: {t_memory_prev} → {decayed:.2f} (after {hours_elapsed}h)")
    
    # Test energy threshold adjustment
    e_threshold_base = 1000.0
    t_memory = 25.0
    adjusted = manager.calculate_energy_threshold_adjustment(e_threshold_base, t_memory)
    print(f"Energy threshold adjustment: {e_threshold_base} → {adjusted:.2f} (T_memory: {t_memory})")
    
    print("TrackerStateManager initialized and tested successfully!")