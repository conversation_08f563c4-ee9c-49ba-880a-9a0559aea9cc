#!/usr/bin/env python3
"""
Preprocessing Agent - Mission Critical Session Processing Manager

This agent is responsible for:
- Session preprocessing with selective JSON extraction
- A→B→C→D pipeline execution with timeout management
- Tracker file generation and validation
- Enhanced data extraction and validation
- Error recovery and fallback handling
- Processing state management and reporting

The agent ensures MISSION CRITICAL requirements:
- Real enhanced data (no fallbacks) for every session
- Automatic tracker file generation (HTF, FVG, Liquidity)
- Selective JSON preprocessing to prevent API timeouts
- Robust processing across all session types
"""

import os
import time
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

from .pipeline import GrokPipeline
from .local_pipeline_adapter import LocalPipelineAdapter
from .tracker_state import TrackerStateManager
from .config import AppConfig as Config
from .utils import load_json_data, save_json_data


class PreprocessingAgentError(Exception):
    """Custom exception for preprocessing agent errors."""
    pass


class PreprocessingAgent:
    """
    Mission Critical Preprocessing Agent
    
    Manages the complete preprocessing workflow:
    1. Session validation and preparation
    2. Tracker context loading and validation
    3. Selective JSON preprocessing 
    4. A→B→C→D pipeline execution
    5. Enhanced data extraction and validation
    6. Tracker file generation and persistence
    7. Processing metrics and reporting
    """
    
    def __init__(self, config: Optional[Config] = None, use_local_pipeline: bool = True):
        """Initialize the preprocessing agent."""
        self.config = config or Config()
        self.use_local_pipeline = use_local_pipeline
        
        # Initialize pipeline - LOCAL by default for 1000x+ speedup
        if use_local_pipeline:
            self.pipeline = LocalPipelineAdapter()
            print("🚀 LOCAL PIPELINE: Initialized with NumPy acceleration (1000x+ speedup)")
        else:
            # Legacy Grok API pipeline (for compatibility)
            api_key = self.config.get_api_key()
            self.pipeline = GrokPipeline(api_key)
            print("⚠️ GROK API: Using legacy pipeline (slower)")
            
        self.tracker_manager = TrackerStateManager()
        
        # Agent state tracking
        self.processing_state = "idle"
        self.current_session = None
        self.processing_metrics = {}
        self.last_error = None
        
        # Setup logging
        self._setup_logging()
        
        # Processing requirements
        self.REQUIRED_ENHANCED_FIELDS = [
            "energy_rate", "momentum_strength", "alpha_t", "gamma_base", "structural_integrity"
        ]
        self.REQUIRED_TRACKER_FILES = ["HTF_Context", "FVG_State", "Liquidity_State"]
        
        self.logger.info("Preprocessing Agent initialized - Ready for mission critical operations")
    
    def _setup_logging(self):
        """Setup comprehensive logging for the agent."""
        self.logger = logging.getLogger("PreprocessingAgent")
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def process_session(self, 
                       session_file: str,
                       htf_context_file: Optional[str] = None,
                       fvg_state_file: Optional[str] = None,
                       liquidity_state_file: Optional[str] = None,
                       output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute complete session preprocessing workflow.
        
        Args:
            session_file: Path to session JSON file
            htf_context_file: Optional HTF context tracker file
            fvg_state_file: Optional FVG state tracker file  
            liquidity_state_file: Optional liquidity state tracker file
            output_file: Optional output file path (auto-generated if not provided)
            
        Returns:
            Complete processing results with validation status
            
        Raises:
            PreprocessingAgentError: If mission critical requirements not met
        """
        start_time = time.time()
        self.processing_state = "processing"
        self.current_session = session_file
        
        try:
            self.logger.info(f"🚀 Starting mission critical preprocessing for: {session_file}")
            
            # Phase 1: Session Validation and Preparation
            session_info = self._validate_and_prepare_session(session_file)
            self.logger.info(f"✅ Session validated: {session_info['session_id']}")
            
            # Phase 2: Tracker Context Loading
            tracker_context = self._load_and_validate_trackers(
                htf_context_file, fvg_state_file, liquidity_state_file
            )
            self.logger.info(f"✅ Tracker context loaded: {len(tracker_context.get('active_structures', []))} structures")
            
            # Phase 3: Pipeline Execution with Selective Preprocessing
            enhanced_results = self._execute_pipeline_with_preprocessing(
                session_info['session_data'], tracker_context
            )
            self.logger.info(f"✅ Pipeline completed in {enhanced_results.get('pipeline_metadata', {}).get('total_processing_time_ms', 0)}ms")
            
            # Phase 4: Enhanced Data Validation
            validation_results = self._validate_enhanced_data(enhanced_results)
            if not validation_results['is_valid']:
                raise PreprocessingAgentError(f"Enhanced data validation failed: {validation_results['errors']}")
            self.logger.info(f"✅ Enhanced data validated: {validation_results['extracted_fields']}")
            
            # Phase 5: Tracker File Generation
            tracker_files = self._generate_and_save_tracker_files(enhanced_results, session_info)
            self.logger.info(f"✅ Tracker files generated: {list(tracker_files.keys())}")
            
            # Phase 6: Output File Management
            output_path = self._save_enhanced_results(enhanced_results, session_info, output_file)
            self.logger.info(f"✅ Enhanced results saved: {output_path}")
            
            # Phase 7: Processing Metrics and Reporting
            processing_time = time.time() - start_time
            metrics = self._generate_processing_metrics(
                session_info, enhanced_results, tracker_files, processing_time
            )
            
            self.processing_state = "completed"
            self.logger.info(f"🎯 Mission critical preprocessing COMPLETED in {processing_time:.1f}s")
            
            return {
                "status": "success",
                "session_info": session_info,
                "enhanced_results": enhanced_results,
                "tracker_files": tracker_files,
                "output_file": output_path,
                "validation_results": validation_results,
                "processing_metrics": metrics,
                "processing_time_seconds": processing_time
            }
            
        except Exception as e:
            self.processing_state = "error"
            self.last_error = str(e)
            self.logger.error(f"❌ Preprocessing failed: {str(e)}")
            
            # Generate error report
            error_report = self._generate_error_report(session_file, str(e), time.time() - start_time)
            
            raise PreprocessingAgentError(f"Mission critical preprocessing failed: {str(e)}") from e
    
    def _validate_and_prepare_session(self, session_file: str) -> Dict[str, Any]:
        """Validate session file and extract metadata."""
        if not os.path.exists(session_file):
            raise PreprocessingAgentError(f"Session file not found: {session_file}")
        
        # 🔧 STANDARDIZED: Use migration-safe JSON loading
        session_data = load_json_data(session_file)
        
        # Extract session metadata
        session_metadata = session_data.get('session_metadata', {})
        session_id = session_metadata.get('session_id', 'unknown_session')
        session_type = session_metadata.get('session_type', 'unknown')
        session_date = session_metadata.get('date', 'unknown_date')
        
        # Validate required fields
        required_fields = ['price_data']
        missing_fields = [field for field in required_fields if field not in session_data]
        if missing_fields:
            raise PreprocessingAgentError(f"Missing required session fields: {missing_fields}")
        
        return {
            "session_file": session_file,
            "session_data": session_data,
            "session_id": session_id,
            "session_type": session_type,
            "session_date": session_date,
            "data_size_kb": len(str(session_data)) / 1024  # Approximate size without json import
        }
    
    def _load_and_validate_trackers(self, 
                                   htf_context_file: Optional[str],
                                   fvg_state_file: Optional[str], 
                                   liquidity_state_file: Optional[str]) -> Dict[str, Any]:
        """Load and validate tracker context files."""
        tracker_files = {
            "htf_context_file": htf_context_file,
            "fvg_state_file": fvg_state_file,
            "liquidity_state_file": liquidity_state_file
        }
        
        # Load tracker context (graceful fallback to defaults)
        try:
            from .tracker_state import create_tracker_context_from_inputs
            tracker_context = create_tracker_context_from_inputs(
                htf_context_file, fvg_state_file, liquidity_state_file
            )
            self.logger.info("📁 Tracker context loaded from files")
        except Exception as e:
            self.logger.warning(f"⚠️ Tracker loading failed, using defaults: {str(e)}")
            tracker_context = self.tracker_manager.extract_tracker_context({}, {}, {})
        
        return {
            "tracker_context": tracker_context,
            "tracker_files_used": tracker_files,
            "tracker_loaded": any(tracker_files.values())
        }
    
    def _execute_pipeline_with_preprocessing(self, 
                                           session_data: Dict[Any, Any],
                                           tracker_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute A→B→C→D pipeline with selective JSON preprocessing."""
        
        # Use the pipeline's process_session method with proper tracker context unpacking
        try:
            tracker_ctx = tracker_context.get("tracker_context", {})
            
            enhanced_results = self.pipeline.process_session(
                session_data,
                htf_context=tracker_ctx.get("htf_context", {}),
                fvg_state=tracker_ctx.get("fvg_state", {}),
                liquidity_state=tracker_ctx.get("liquidity_state", {})
            )
            
            return enhanced_results
            
        except Exception as e:
            self.logger.error(f"Pipeline execution failed: {str(e)}")
            raise PreprocessingAgentError(f"Pipeline execution failed: {str(e)}") from e
    
    def _validate_enhanced_data(self, enhanced_results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that real enhanced data was generated (no fallbacks)."""
        
        # Handle both local pipeline and legacy Grok format
        if self.use_local_pipeline:
            # Local pipeline format - direct access to synthesis results
            extracted_values = {
                "energy_rate": enhanced_results.get("energy_accumulation", {}).get("energy_rate", 0),
                "momentum_strength": enhanced_results.get("temporal_momentum", {}).get("momentum_strength", 0),
                "alpha_t": enhanced_results.get("hybrid_volume", {}).get("alpha_t", 0),
                "gamma_base": enhanced_results.get("time_dilation_base", {}).get("gamma_base", 0),
                "structural_integrity": enhanced_results.get("structural_integrity", 0)
            }
        else:
            # Legacy Grok format
            unit_d = enhanced_results.get("grok_enhanced_calculations", {}).get("unit_d_integration_validation", {})
            extracted_values = unit_d.get("extracted_values", {})
        
        # Check for required enhanced fields
        missing_fields = []
        extracted_fields = {}
        
        for field in self.REQUIRED_ENHANCED_FIELDS:
            if field in extracted_values:
                extracted_fields[field] = extracted_values[field]
            else:
                missing_fields.append(field)
        
        # Validate that values are not default fallbacks
        validation_errors = []
        
        # Check energy_rate is not default 1.0
        energy_rate = extracted_values.get("energy_rate", 1.0)
        if abs(energy_rate - 1.0) < 0.001:
            validation_errors.append("energy_rate appears to be default fallback (1.0)")
        
        # Check alpha_t is reasonable
        alpha_t = extracted_values.get("alpha_t", 0.5)
        if alpha_t < 0.1 or alpha_t > 1.0:
            validation_errors.append(f"alpha_t value unreasonable: {alpha_t}")
        
        # Check processing time indicates real computation
        processing_time = enhanced_results.get("pipeline_metadata", {}).get("total_processing_time_ms", 0)
        
        # Adjust validation for local vs Grok pipeline
        if self.use_local_pipeline:
            # Local pipeline should be ultra-fast (<100ms) 
            if processing_time > 1000:  # More than 1 second indicates problem
                validation_errors.append(f"Local processing time too slow ({processing_time}ms) - performance degradation")
        else:
            # Legacy Grok pipeline should take 30+ seconds
            if processing_time < 30000:  # Less than 30 seconds indicates possible fallback
                validation_errors.append(f"Grok processing time too short ({processing_time}ms) - possible fallback")
        
        is_valid = len(missing_fields) == 0 and len(validation_errors) == 0
        
        return {
            "is_valid": is_valid,
            "extracted_fields": extracted_fields,
            "missing_fields": missing_fields,
            "errors": validation_errors,
            "field_count": len(extracted_fields),
            "processing_time_ms": processing_time
        }
    
    def _generate_and_save_tracker_files(self, 
                                        enhanced_results: Dict[str, Any],
                                        session_info: Dict[str, Any]) -> Dict[str, str]:
        """Generate and save all required tracker files."""
        
        session_id = session_info["session_id"]
        session_date = session_info["session_date"]
        
        # Generate tracker data
        htf_context = self.tracker_manager.generate_output_htf_context(enhanced_results)
        fvg_state = self.tracker_manager.generate_output_fvg_state(enhanced_results)
        liquidity_state = self.tracker_manager.generate_output_liquidity_state(enhanced_results)
        
        # Generate file paths
        tracker_files = {}
        
        for tracker_type, tracker_data in [
            ("HTF_Context", htf_context),
            ("FVG_State", fvg_state), 
            ("Liquidity_State", liquidity_state)
        ]:
            filename = f"{tracker_type}_{session_id}_grokEnhanced_{session_date}.json"
            filepath = os.path.abspath(filename)
            
            # 🔧 STANDARDIZED: Use migration-safe JSON saving
            save_json_data(tracker_data, filepath)
            
            tracker_files[tracker_type] = filepath
            self.logger.info(f"💾 Saved {tracker_type}: {filename}")
        
        return tracker_files
    
    def _save_enhanced_results(self, 
                              enhanced_results: Dict[str, Any],
                              session_info: Dict[str, Any],
                              output_file: Optional[str]) -> str:
        """Save enhanced results to output file."""
        
        if output_file is None:
            session_id = session_info["session_id"]
            session_date = session_info["session_date"]
            output_file = f"{session_id}_grokEnhanced_{session_date}_NEW.json"
        
        output_path = os.path.abspath(output_file)
        
        # 🔧 STANDARDIZED: Use migration-safe JSON saving
        save_json_data(enhanced_results, output_path)
        
        return output_path
    
    def _generate_processing_metrics(self,
                                   session_info: Dict[str, Any],
                                   enhanced_results: Dict[str, Any],
                                   tracker_files: Dict[str, str],
                                   total_processing_time: float) -> Dict[str, Any]:
        """Generate comprehensive processing metrics."""
        
        pipeline_metadata = enhanced_results.get("pipeline_metadata", {})
        unit_times = pipeline_metadata.get("individual_unit_times", {})
        
        return {
            "session_processing": {
                "session_id": session_info["session_id"],
                "session_type": session_info["session_type"],
                "input_data_size_kb": session_info["data_size_kb"],
                "total_processing_time_seconds": total_processing_time,
                "pipeline_processing_time_ms": pipeline_metadata.get("total_processing_time_ms", 0),
                "equations_processed": pipeline_metadata.get("equations_processed", 0)
            },
            "unit_performance": {
                "unit_a_time_ms": unit_times.get("unit_a", 0),
                "unit_b_time_ms": unit_times.get("unit_b", 0),
                "unit_c_time_ms": unit_times.get("unit_c", 0), 
                "unit_d_time_ms": unit_times.get("unit_d", 0)
            },
            "enhanced_data_quality": {
                "enhanced_fields_extracted": len(enhanced_results.get("grok_enhanced_calculations", {}).get("unit_d_integration_validation", {}).get("extracted_values", {})),
                "energy_rate": enhanced_results.get("grok_enhanced_calculations", {}).get("unit_d_integration_validation", {}).get("extracted_values", {}).get("energy_rate", 0),
                "momentum_strength": enhanced_results.get("grok_enhanced_calculations", {}).get("unit_d_integration_validation", {}).get("extracted_values", {}).get("momentum_strength", 0)
            },
            "tracker_generation": {
                "tracker_files_created": len(tracker_files),
                "htf_structures_generated": len(enhanced_results.get("grok_enhanced_calculations", {}).get("unit_d_integration_validation", {}).get("extracted_values", {})) > 0,
                "tracker_files": list(tracker_files.keys())
            },
            "agent_performance": {
                "processing_state": self.processing_state,
                "error_occurred": self.last_error is not None,
                "last_error": self.last_error,
                "timestamp": datetime.now().isoformat()
            }
        }
    
    def _generate_error_report(self, session_file: str, error_message: str, processing_time: float) -> Dict[str, Any]:
        """Generate detailed error report for debugging."""
        return {
            "error_report": {
                "session_file": session_file,
                "error_message": error_message,
                "processing_time_seconds": processing_time,
                "processing_state": self.processing_state,
                "timestamp": datetime.now().isoformat(),
                "agent_version": "1.0.0"
            }
        }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status and metrics."""
        return {
            "processing_state": self.processing_state,
            "current_session": self.current_session,
            "last_error": self.last_error,
            "processing_metrics": self.processing_metrics,
            "agent_ready": self.processing_state in ["idle", "completed"],
            "tracker_manager_ready": self.tracker_manager is not None,
            "pipeline_ready": self.pipeline is not None
        }
    
    def batch_process_sessions(self, session_files: List[str]) -> Dict[str, Any]:
        """Process multiple sessions in batch with error recovery."""
        batch_start_time = time.time()
        results = {}
        errors = {}
        
        self.logger.info(f"🔄 Starting batch processing of {len(session_files)} sessions")
        
        for i, session_file in enumerate(session_files, 1):
            try:
                self.logger.info(f"📊 Processing session {i}/{len(session_files)}: {session_file}")
                result = self.process_session(session_file)
                results[session_file] = result
                self.logger.info(f"✅ Session {i} completed successfully")
                
            except Exception as e:
                self.logger.error(f"❌ Session {i} failed: {str(e)}")
                errors[session_file] = str(e)
                continue  # Continue with next session
        
        batch_time = time.time() - batch_start_time
        
        return {
            "batch_results": {
                "total_sessions": len(session_files),
                "successful_sessions": len(results),
                "failed_sessions": len(errors),
                "success_rate": len(results) / len(session_files) if session_files else 0,
                "total_batch_time_seconds": batch_time,
                "average_time_per_session": batch_time / len(session_files) if session_files else 0
            },
            "session_results": results,
            "session_errors": errors,
            "timestamp": datetime.now().isoformat()
        }


def create_preprocessing_agent(config: Optional[Config] = None, use_local_pipeline: bool = True) -> PreprocessingAgent:
    """
    Factory function to create a preprocessing agent.
    
    Args:
        config: Optional configuration object
        use_local_pipeline: Use local NumPy pipeline (True) or legacy Grok API (False)
                           Default True for 1000x+ performance improvement
    """
    return PreprocessingAgent(config, use_local_pipeline)


if __name__ == "__main__":
    # Example usage and testing
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python preprocessing_agent.py <session_file> [htf_context] [fvg_state] [liquidity_state]")
        sys.exit(1)
    
    # Create agent
    agent = create_preprocessing_agent()
    
    # Process single session
    session_file = sys.argv[1]
    htf_context = sys.argv[2] if len(sys.argv) > 2 else None
    fvg_state = sys.argv[3] if len(sys.argv) > 3 else None
    liquidity_state = sys.argv[4] if len(sys.argv) > 4 else None
    
    try:
        result = agent.process_session(
            session_file=session_file,
            htf_context_file=htf_context,
            fvg_state_file=fvg_state,
            liquidity_state_file=liquidity_state
        )
        
        print("🎯 Preprocessing Agent - Mission Critical Success!")
        print(f"✅ Enhanced data extracted: {result['validation_results']['field_count']} fields")
        print(f"✅ Tracker files generated: {len(result['tracker_files'])}")
        print(f"✅ Processing time: {result['processing_time_seconds']:.1f}s")
        print(f"✅ Output file: {result['output_file']}")
        
    except PreprocessingAgentError as e:
        print(f"❌ Preprocessing Agent Failed: {str(e)}")
        sys.exit(1)