#!/usr/bin/env python3
"""
HTF Intelligence Integration - Production Ready Integration

This module provides production integration for the HTF Session Intelligence Parser
with the existing HTF pipeline and context files.

Features:
- Seamless integration with existing HTF context files
- Automatic context file creation if missing
- Integration with HTF Master Controller
- Production-ready error handling and logging
- Session-to-HTF context mapping
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from htf_session_intelligence_parser import HTFSessionIntelligenceParser, HTFEvent


class HTFIntelligenceIntegration:
    """
    Production integration layer for HTF Session Intelligence Parser.
    
    Provides seamless integration with existing HTF infrastructure while
    maintaining backward compatibility and production stability.
    """
    
    def __init__(self, base_dir: str = "/Users/<USER>/grok-claude-automation"):
        self.base_dir = Path(base_dir)
        self.parser = HTFSessionIntelligenceParser(base_dir)
        
        # HTF context mapping for session types
        self.session_htf_mapping = {
            "Asia": "Asia",
            "Midnight": "Midnight", 
            "London": "London",
            "Premarket": "Premarket",
            "NY_AM": "NYAM",
            "Lunch": "Lunch",
            "NY_PM": "NY_PM",
            "PM": "NY_PM"
        }
        
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup integration logging."""
        self.logger = logging.getLogger("HTFIntelligenceIntegration")
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - HTF_Integration - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def find_htf_context_file(self, session_type: str, date: str) -> Optional[Path]:
        """Find existing HTF context file with flexible naming."""
        htf_dir = self.base_dir / "data" / "trackers" / "htf"
        
        # Map session type to HTF file naming
        mapped_session = self.session_htf_mapping.get(session_type, session_type)
        
        # Try various naming patterns
        patterns = [
            f"HTF_Context_{mapped_session}_*{date}*.json",
            f"HTF_Context_{session_type}_*{date}*.json",
            f"HTF_{mapped_session}_{date}.json",
            f"HTF_Tracker_{mapped_session}_{date.replace('-', '_')}.json"
        ]
        
        for pattern in patterns:
            matches = list(htf_dir.glob(pattern))
            if matches:
                return matches[0]  # Return first match
                
        return None
    
    def create_htf_context_file(self, session_type: str, date: str) -> Path:
        """Create new HTF context file if missing."""
        htf_dir = self.base_dir / "data" / "trackers" / "htf"
        htf_dir.mkdir(parents=True, exist_ok=True)
        
        mapped_session = self.session_htf_mapping.get(session_type, session_type)
        filename = f"HTF_Context_{mapped_session}_grokEnhanced_{date}.json"
        htf_file = htf_dir / filename
        
        # Create basic HTF context structure
        htf_context = {
            "active_structures": [],
            "htf_influence_factor": 0.0,
            "last_update": datetime.now().isoformat(),
            "session_continuity": "maintained",
            "htf_events": [],
            "created_by": "HTF_Intelligence_Integration",
            "session_type": session_type,
            "date": date
        }
        
        with open(htf_file, 'w') as f:
            json.dump(htf_context, f, indent=2)
            
        self.logger.info(f"Created new HTF context file: {filename}")
        return htf_file
    
    def update_htf_context_with_intelligence(self, htf_events: List[HTFEvent], 
                                           session_type: str, date: str) -> bool:
        """Update HTF context file with intelligent events."""
        # Find or create HTF context file
        htf_file = self.find_htf_context_file(session_type, date)
        
        if not htf_file:
            htf_file = self.create_htf_context_file(session_type, date)
        
        try:
            # Load existing context
            with open(htf_file, 'r') as f:
                htf_context = json.load(f)
                
        except Exception as e:
            self.logger.error(f"Could not load HTF context {htf_file}: {e}")
            return False
        
        # Ensure htf_events section exists
        if "htf_events" not in htf_context:
            htf_context["htf_events"] = []
        
        # Add intelligence events
        events_added = 0
        for event in htf_events:
            # Check for duplicates based on htf_significance
            existing = [e for e in htf_context["htf_events"] 
                       if e.get("htf_significance") == event.htf_significance]
            
            if not existing:
                event_record = {
                    "event_type": event.event_type,
                    "level": event.level,
                    "htf_significance": event.htf_significance,
                    "reference_date": event.reference_date,
                    "violated_on": event.violated_on,
                    "origin_session": event.origin_session,
                    "taken_by_session": event.taken_by_session,
                    "session_details": event.session_details,
                    "timestamp": event.timestamp.isoformat(),
                    "processed_by": "HTF_Intelligence_Integration",
                    "confidence": 0.95
                }
                
                htf_context["htf_events"].append(event_record)
                events_added += 1
        
        # Update metadata
        htf_context["last_update"] = datetime.now().isoformat()
        htf_context["intelligence_processed"] = True
        htf_context["intelligence_events_added"] = events_added
        
        # Save updated context
        try:
            with open(htf_file, 'w') as f:
                json.dump(htf_context, f, indent=2)
                
            self.logger.info(f"Updated HTF context {htf_file.name} with {events_added} intelligence events")
            return True
            
        except Exception as e:
            self.logger.error(f"Could not save HTF context {htf_file}: {e}")
            return False
    
    def process_session_with_integration(self, session_file_path: str) -> Dict[str, Any]:
        """Process session with full HTF integration."""
        self.logger.info(f"Processing session with HTF integration: {Path(session_file_path).name}")
        
        # Parse HTF events using intelligence parser
        htf_events = self.parser.parse_level_1_session(session_file_path)
        
        if not htf_events:
            return {
                "status": "no_htf_events",
                "session_file": Path(session_file_path).name,
                "events_processed": 0
            }
        
        # Extract session metadata
        try:
            with open(session_file_path, 'r') as f:
                session_data = json.load(f)
                
            metadata = session_data.get("session_metadata", {})
            session_type = metadata.get("session_type")
            date = metadata.get("date")
            
        except Exception as e:
            self.logger.error(f"Could not extract session metadata: {e}")
            return {"status": "error", "error": str(e)}
        
        # Update HTF context with intelligence
        success = self.update_htf_context_with_intelligence(htf_events, session_type, date)
        
        return {
            "status": "success" if success else "partial_success",
            "session_file": Path(session_file_path).name,
            "session_type": session_type,
            "date": date,
            "events_processed": len(htf_events),
            "htf_context_updated": success,
            "htf_events": [
                {
                    "htf_significance": event.htf_significance,
                    "level": event.level,
                    "reference_date": event.reference_date,
                    "origin_session": event.origin_session
                }
                for event in htf_events
            ]
        }
    
    def batch_integrate_all_sessions(self) -> Dict[str, Any]:
        """Batch integrate all Level 1 sessions with HTF context."""
        self.logger.info("Starting batch HTF intelligence integration...")
        
        sessions_dir = self.base_dir / "data" / "sessions" / "level_1"
        
        results = {
            "total_sessions": 0,
            "processed_sessions": 0,
            "total_htf_events": 0,
            "htf_files_updated": 0,
            "session_results": [],
            "errors": []
        }
        
        for session_file in sessions_dir.glob("*_Lvl-1_*.json"):
            results["total_sessions"] += 1
            
            try:
                result = self.process_session_with_integration(str(session_file))
                
                if result["status"] in ["success", "partial_success"]:
                    results["processed_sessions"] += 1
                    results["total_htf_events"] += result["events_processed"]
                    
                    if result.get("htf_context_updated"):
                        results["htf_files_updated"] += 1
                
                results["session_results"].append(result)
                
            except Exception as e:
                error_msg = f"Error processing {session_file.name}: {str(e)}"
                self.logger.error(error_msg)
                results["errors"].append(error_msg)
        
        self.logger.info(f"Batch integration complete: {results['processed_sessions']}/{results['total_sessions']} sessions, {results['total_htf_events']} HTF events, {results['htf_files_updated']} files updated")
        
        return results
    
    def validate_integration(self) -> Dict[str, Any]:
        """Validate HTF intelligence integration."""
        validation_results = {
            "htf_files_with_intelligence": 0,
            "total_intelligence_events": 0,
            "files_checked": 0,
            "validation_details": []
        }
        
        htf_dir = self.base_dir / "data" / "trackers" / "htf"
        
        for htf_file in htf_dir.glob("HTF_*.json"):
            validation_results["files_checked"] += 1
            
            try:
                with open(htf_file, 'r') as f:
                    htf_data = json.load(f)
                
                intelligence_events = htf_data.get("htf_events", [])
                intelligence_processed = htf_data.get("intelligence_processed", False)
                
                if intelligence_events:
                    validation_results["htf_files_with_intelligence"] += 1
                    validation_results["total_intelligence_events"] += len(intelligence_events)
                
                validation_results["validation_details"].append({
                    "file": htf_file.name,
                    "intelligence_processed": intelligence_processed,
                    "intelligence_events": len(intelligence_events),
                    "sample_events": intelligence_events[:2] if intelligence_events else []
                })
                
            except Exception as e:
                self.logger.warning(f"Could not validate {htf_file.name}: {e}")
        
        return validation_results


def main():
    """Demo HTF Intelligence Integration."""
    integration = HTFIntelligenceIntegration()
    
    print("🔗 HTF Intelligence Integration - Demo")
    print("=" * 50)
    
    # Test single session integration
    test_session = "/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYAM_Lvl-1_2025_07_28.json"
    
    if Path(test_session).exists():
        print(f"\n📋 Testing integration with: {Path(test_session).name}")
        result = integration.process_session_with_integration(test_session)
        
        print(f"\n✅ Integration Results:")
        print(f"Status: {result['status']}")
        print(f"Events Processed: {result.get('events_processed', 0)}")
        print(f"HTF Context Updated: {result.get('htf_context_updated', False)}")
        
        if result.get('htf_events'):
            print(f"\n🎯 HTF Events Integrated:")
            for event in result['htf_events']:
                print(f"  • {event['htf_significance']} at {event['level']}")
    
    # Batch integration
    print(f"\n🔄 Running batch integration...")
    batch_results = integration.batch_integrate_all_sessions()
    
    print(f"\n📊 Batch Integration Results:")
    print(f"Sessions Processed: {batch_results['processed_sessions']}/{batch_results['total_sessions']}")
    print(f"HTF Events: {batch_results['total_htf_events']}")
    print(f"HTF Files Updated: {batch_results['htf_files_updated']}")
    
    # Validation
    print(f"\n🔍 Validating integration...")
    validation = integration.validate_integration()
    
    print(f"\n📈 Validation Results:")
    print(f"Files with Intelligence: {validation['htf_files_with_intelligence']}/{validation['files_checked']}")
    print(f"Total Intelligence Events: {validation['total_intelligence_events']}")


if __name__ == "__main__":
    main()