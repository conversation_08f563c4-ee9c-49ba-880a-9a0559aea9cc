"""
Grok-Claude Automation Package
Automated pipeline for Grok computational units A→B→C→D
"""

from .pipeline import GrokPipeline, process_single_session
from .unit_a import create_unit_a
from .unit_b import create_unit_b  
from .unit_c import create_unit_c
from .unit_d import create_unit_d
from .grok_client import GrokClient, ComputationalUnit
from .utils import get_logger, setup_logger

__version__ = "1.0.0"
__all__ = [
    "GrokPipeline", 
    "process_single_session",
    "create_unit_a", 
    "create_unit_b", 
    "create_unit_c", 
    "create_unit_d",
    "GrokClient", 
    "ComputationalUnit",
    "get_logger",
    "setup_logger"
]