#!/usr/bin/env python3
"""
Local Pipeline Adapter
Production-ready adapter that replaces Grok API calls with local NumPy computations.
Maintains compatibility with existing preprocessing agent and CLI interfaces.
"""

import time
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import Dict, Any, Optional
from src.local_units import create_local_pipeline
from src.hawkes_cascade_predictor import HawkesCascadePredictor
from src.ml_cascade_predictor import create_ml_cascade_predictor
from src.grok_narrative_analyzer import create_grok_narrative_analyzer
from src.cache_manager import get_unified_cache


class LocalPipelineAdapter:
    """
    Adapter that provides Grok-compatible interface using local pipeline.
    Drop-in replacement for existing Grok pipeline with 1000x+ speedup.
    """
    
    def __init__(self, enable_ml_features: bool = True, enable_narrative_analysis: bool = False):
        # Core local pipeline (always enabled)
        self.local_pipeline = create_local_pipeline()
        self.hawkes_predictor = HawkesCascadePredictor()
        self.unified_cache = get_unified_cache()
        
        # Phase 3: ML-based features (optional)
        self.enable_ml_features = enable_ml_features
        if enable_ml_features:
            self.ml_predictor = create_ml_cascade_predictor()
            print("🤖 ML FEATURES: Enabled (cascade prediction + HMM regime detection)")
        else:
            self.ml_predictor = None
            
        # Phase 4: Grok narrative analysis (optional, separated)
        self.enable_narrative_analysis = enable_narrative_analysis
        if enable_narrative_analysis:
            self.narrative_analyzer = create_grok_narrative_analyzer()
            print("📖 NARRATIVE ANALYSIS: Enabled (theoretical & experimental)")
        else:
            self.narrative_analyzer = None
        
        self.processing_stats = {
            'sessions_processed': 0,
            'total_time_ms': 0,
            'average_time_ms': 0,
            'grok_api_calls_avoided': 0,
            'ml_predictions_made': 0,
            'narrative_analyses_performed': 0
        }
    
    def process_session(self, session_data: Dict[Any, Any], 
                       htf_context: Dict[Any, Any] = None,
                       fvg_state: Dict[Any, Any] = None,
                       liquidity_state: Dict[Any, Any] = None) -> Dict[str, Any]:
        """
        Process session using local pipeline - compatible with existing interface.
        
        Args:
            session_data: Base session JSON data
            htf_context: Higher timeframe context (optional, for compatibility)
            fvg_state: FVG state tracking (optional, for compatibility)
            liquidity_state: Liquidity state (optional, for compatibility)
            
        Returns:
            Results in same format as original Grok pipeline
        """
        start_time = time.time()
        
        print("🚀 LOCAL PIPELINE: Processing session with NumPy acceleration...")
        
        # Use local pipeline instead of Grok API calls
        local_results = self.local_pipeline.process_session(session_data)
        
        # Integrate Hawkes prediction (already optimized)
        hawkes_prediction = self._integrate_hawkes_prediction(session_data)
        
        # Phase 3: ML-based cascade prediction and regime detection
        ml_prediction = self._integrate_ml_prediction(local_results) if self.enable_ml_features else None
        
        # Phase 4: Grok narrative analysis (architecturally separated)  
        narrative_analysis = self._integrate_narrative_analysis(session_data, local_results) if self.enable_narrative_analysis else None
        
        # Convert to Grok-compatible format
        compatible_results = self._convert_to_grok_format(
            local_results, hawkes_prediction, ml_prediction, narrative_analysis
        )
        
        # Update processing stats
        processing_time = (time.time() - start_time) * 1000
        self._update_stats(processing_time)
        
        print(f"✅ LOCAL PIPELINE completed in {processing_time:.1f}ms")
        print(f"🚀 Avoided {4} Grok API calls (estimated time saved: {67.5:.1f}s)")
        
        return compatible_results
    
    def _integrate_hawkes_prediction(self, session_data: Dict[Any, Any]) -> Dict[str, Any]:
        """Integrate optimized Hawkes cascade prediction."""
        try:
            prediction = self.hawkes_predictor.predict_cascade_timing(session_data)
            return {
                'cascade_time': prediction.predicted_cascade_time,
                'confidence': prediction.prediction_confidence,
                'methodology': prediction.methodology,
                'iterations_used': len(prediction.intensity_buildup)
            }
        except Exception as e:
            print(f"⚠️ Hawkes prediction failed: {e}")
            return {
                'cascade_time': 5.0,  # Default fallback
                'confidence': 0.7,
                'methodology': 'fallback',
                'iterations_used': 0
            }
    
    def _integrate_ml_prediction(self, local_results: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Integrate ML-based cascade prediction and regime detection."""
        if not self.ml_predictor:
            return None
            
        try:
            ml_result = self.ml_predictor.predict_cascade(local_results)
            self.processing_stats['ml_predictions_made'] += 1
            
            return {
                'predicted_cascade_time': ml_result.predicted_cascade_time,
                'regime_classification': ml_result.regime_classification,
                'regime_confidence': ml_result.regime_confidence,
                'model_confidence': ml_result.model_confidence,
                'feature_importance': ml_result.feature_importance,
                'methodology': ml_result.methodology,
                'processing_time_ms': ml_result.processing_time_ms
            }
        except Exception as e:
            print(f"⚠️ ML prediction failed: {e}")
            return {
                'predicted_cascade_time': 5.0,
                'regime_classification': 'unknown',
                'regime_confidence': 0.5,
                'model_confidence': 0.5,
                'feature_importance': {},
                'methodology': 'ml_prediction_fallback',
                'processing_time_ms': 0.0
            }
    
    def _integrate_narrative_analysis(self, session_data: Dict[Any, Any], 
                                    local_results: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Integrate Grok narrative analysis (architecturally separated)."""
        if not self.narrative_analyzer:
            return None
            
        try:
            narrative_result = self.narrative_analyzer.analyze_session_narrative(
                session_data, local_results
            )
            self.processing_stats['narrative_analyses_performed'] += 1
            
            return {
                'session_narrative': narrative_result.session_narrative,
                'market_context': narrative_result.market_context,
                'theoretical_insights': narrative_result.theoretical_insights,
                'confidence_assessment': narrative_result.confidence_assessment,
                'narrative_themes': narrative_result.narrative_themes,
                'experimental_observations': narrative_result.experimental_observations,
                'methodology': narrative_result.methodology,
                'processing_time_ms': narrative_result.processing_time_ms
            }
        except Exception as e:
            print(f"⚠️ Narrative analysis failed: {e}")
            return {
                'session_narrative': 'Narrative analysis unavailable',
                'market_context': 'Context analysis failed', 
                'theoretical_insights': ['Analysis unavailable'],
                'confidence_assessment': 'low confidence - system error',
                'narrative_themes': ['Analysis failed'],
                'experimental_observations': 'Experimental analysis unavailable',
                'methodology': 'narrative_analysis_fallback',
                'processing_time_ms': 0.0
            }
    
    def _convert_to_grok_format(self, local_results: Dict[str, Any], 
                               hawkes_prediction: Dict[str, Any],
                               ml_prediction: Optional[Dict[str, Any]] = None,
                               narrative_analysis: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Convert local pipeline results to Grok-compatible format."""
        
        # Extract results from each unit
        unit_a = local_results['unit_a'].calculations
        unit_b = local_results['unit_b'].calculations
        unit_c = local_results['unit_c'].calculations
        unit_d = local_results['unit_d'].calculations
        pipeline_meta = local_results['pipeline_metadata']
        
        return {
            # Unit A results (Foundation Calculations)
            'foundation_calculations': {
                'extracted_values': unit_a['foundation_calculations']['extracted_values'],
                'session_type': unit_a['foundation_calculations']['session_type'],
                'complexity_score': unit_a['foundation_calculations']['complexity_score']
            },
            'hybrid_volume': unit_a['hybrid_volume'],
            'time_dilation_base': unit_a['time_dilation_base'],
            'parameters_validated': unit_a['parameters_validated'],
            
            # Unit B results (Energy & Structure)
            'energy_structure_calculations': unit_b['energy_structure_calculations'],
            'energy_accumulation': unit_b['energy_accumulation'],
            'gradient_dynamics': unit_b['gradient_dynamics'],
            'structural_integrity': unit_b['structural_integrity'],
            
            # Unit C results (Temporal Dynamics)
            'advanced_dynamics': unit_c['advanced_dynamics'],
            'temporal_momentum': unit_c['temporal_momentum'],
            'consolidation_analysis': unit_c['consolidation_analysis'],
            'frequency_analysis': unit_c['frequency_analysis'],
            
            # Unit D results (Integration & Synthesis)
            'integration_synthesis': unit_d['integration_synthesis'],
            'validation_results': unit_d['validation_results'],
            'synthesis_results': unit_d['synthesis_results'],
            'quality_metrics': unit_d['quality_metrics'],
            
            # Hawkes integration
            'hawkes_cascade_prediction': hawkes_prediction,
            
            # Phase 3: ML-based predictions (optional)
            'ml_cascade_prediction': ml_prediction,
            
            # Phase 4: Narrative analysis (architecturally separated, optional)
            'grok_narrative_analysis': narrative_analysis,
            
            # Pipeline metadata (enhanced with local info)
            'pipeline_metadata': {
                **pipeline_meta,
                'computation_method': 'local_numpy',
                'api_calls_made': 0,
                'api_calls_avoided': 4,
                'estimated_time_saved_ms': 67500 - pipeline_meta['total_processing_time_ms'],
                'performance_class': 'excellent' if pipeline_meta['total_processing_time_ms'] < 1.0 else 'good'
            },
            
            # Compatibility fields for existing code
            'unit_status': 'completed',
            'local_computation': True,
            'grok_compatible': True
        }
    
    def _update_stats(self, processing_time_ms: float) -> None:
        """Update processing statistics."""
        self.processing_stats['sessions_processed'] += 1
        self.processing_stats['total_time_ms'] += processing_time_ms
        self.processing_stats['average_time_ms'] = (
            self.processing_stats['total_time_ms'] / self.processing_stats['sessions_processed']
        )
        self.processing_stats['grok_api_calls_avoided'] += 4  # 4 units per session
        
        # Track additional features usage
        if self.enable_ml_features and hasattr(self, 'ml_predictor'):
            pass  # ML stats already tracked in _integrate_ml_prediction
        if self.enable_narrative_analysis and hasattr(self, 'narrative_analyzer'):
            pass  # Narrative stats already tracked in _integrate_narrative_analysis
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        stats = self.processing_stats.copy()
        
        if stats['sessions_processed'] > 0:
            estimated_grok_time = stats['sessions_processed'] * 67500  # ms
            actual_time = stats['total_time_ms']
            
            stats.update({
                'estimated_grok_total_time_ms': estimated_grok_time,
                'actual_total_time_ms': actual_time,
                'time_saved_ms': estimated_grok_time - actual_time,
                'speedup_factor': estimated_grok_time / actual_time if actual_time > 0 else float('inf'),
                'efficiency_rating': 'excellent' if stats['average_time_ms'] < 1.0 else 'good'
            })
        
        return stats
    
    def reset_stats(self) -> None:
        """Reset performance statistics."""
        self.processing_stats = {
            'sessions_processed': 0,
            'total_time_ms': 0,
            'average_time_ms': 0,
            'grok_api_calls_avoided': 0
        }


def create_local_pipeline_adapter(enable_ml_features: bool = True, 
                                  enable_narrative_analysis: bool = False) -> LocalPipelineAdapter:
    """
    Factory function to create local pipeline adapter.
    
    Args:
        enable_ml_features: Enable ML cascade prediction and HMM regime detection
        enable_narrative_analysis: Enable Grok narrative analysis (separated system)
    """
    return LocalPipelineAdapter(enable_ml_features, enable_narrative_analysis)


# Compatibility function for existing code
def create_pipeline_replacement() -> LocalPipelineAdapter:
    """
    Create pipeline replacement that can be used as drop-in substitute
    for existing GrokPipeline instances.
    """
    adapter = create_local_pipeline_adapter()
    
    # Add method aliases for compatibility
    adapter.process_session_legacy = adapter.process_session
    
    return adapter


if __name__ == "__main__":
    # Test the adapter with real session data
    print("🧪 TESTING LOCAL PIPELINE ADAPTER")
    print("=" * 50)
    
    from src.utils import load_json_data
    
    # Test with actual session files
    test_sessions = [
        'ASIA_Lvl-1_2025_07_29.json.json',
        'LONDON_Lvl-1_2025_07_28.json',
        'NYAM_Lvl-1_2025_07_29.json'
    ]
    
    adapter = create_local_pipeline_adapter()
    
    for session_file in test_sessions:
        try:
            print(f"\n📁 Testing: {session_file}")
            session_data = load_json_data(session_file)
            
            # Process with local adapter
            results = adapter.process_session(session_data)
            
            # Show key results
            final_confidence = results['synthesis_results']['final_confidence']
            processing_time = results['pipeline_metadata']['total_processing_time_ms']
            time_saved = results['pipeline_metadata']['estimated_time_saved_ms']
            
            print(f"  🏆 Final confidence: {final_confidence:.3f}")
            print(f"  ⏱️ Processing time: {processing_time:.2f}ms")
            print(f"  💰 Time saved: {time_saved:.0f}ms")
            
        except Exception as e:
            print(f"  ❌ Failed: {e}")
    
    # Show overall statistics
    stats = adapter.get_performance_stats()
    print(f"\n📊 OVERALL PERFORMANCE:")
    print(f"  Sessions processed: {stats['sessions_processed']}")
    print(f"  Average time: {stats['average_time_ms']:.2f}ms")
    print(f"  API calls avoided: {stats['grok_api_calls_avoided']}")
    print(f"  Speedup factor: {stats.get('speedup_factor', 0):.0f}x")
    print(f"  Efficiency: {stats.get('efficiency_rating', 'unknown')}")