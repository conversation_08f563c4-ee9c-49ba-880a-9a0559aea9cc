#!/usr/bin/env python3
"""
Base Grok API client for computational unit automation.
Simplified approach using subprocess calls to existing Grok CLI.
"""

import subprocess
import json
import os
import re
from typing import Dict, Any, Optional
from pydantic import BaseModel
try:
    from .config import config, APIKeyError
except ImportError:
    from config import config, APIKeyError


class GrokClient:
    """Base client for interacting with Grok CLI."""
    
    def __init__(self, api_key: Optional[str] = None):
        try:
            self.api_key = config.get_api_key(api_key)
        except APIKeyError as e:
            raise ValueError(str(e))
    
    def execute_prompt(self, prompt: str, model: str = "grok-4-latest") -> str:
        """Execute a prompt using Grok CLI."""
        try:
            cmd = [
                "grok", 
                "--model", model,
                "--prompt", prompt
            ]
            
            # Set environment variable for the subprocess using centralized config
            env = config.get_subprocess_env()
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                env=env,
                timeout=config.timeout_config.SUBPROCESS_TIMEOUT  # Centralized subprocess timeout
            )
            
            if result.returncode != 0:
                raise Exception(f"Grok CLI error: {result.stderr}")
                
            return result.stdout.strip()
            
        except subprocess.TimeoutExpired:
            raise Exception("Grok request timed out")
        except Exception as e:
            raise Exception(f"Failed to execute Grok prompt: {str(e)}")
    
    def extract_json_from_response(self, response: str) -> Dict[Any, Any]:
        """Extract JSON from Grok's conversational response format."""
        
        # Debug logging - simplified
        print(f"DEBUG - Response length: {len(response)} chars")
        if len(response) > 100:
            print(f"DEBUG - Last 200 chars: {response[-200:]}")
        
        # Method 0: Look for calculations in the final portion first
        calculation_keys = ['"foundation_calculations"', '"energy_structure_calculations"', 
                          '"advanced_dynamics"', '"integration_validation"']
        found_key = None
        for key in calculation_keys:
            if key in response[-1000:]:
                found_key = key
                print(f"DEBUG - Found {key} near end of response")
                break
        
        if found_key:
            # Extract just the end portion that likely contains the result
            end_portion = response[-1000:]
            # Try to find the complete JSON structure first
            json_start_patterns = [
                '{"foundation_calculations"', '{"energy_structure_calculations"',
                '{"advanced_dynamics"', '{"integration_validation"'
            ]
            start_idx = -1
            for pattern in json_start_patterns:
                start_idx = end_portion.find(pattern)
                if start_idx != -1:
                    break
            
            if start_idx == -1:
                # Fall back to finding just the key and building backwards
                start_idx = end_portion.find(found_key)
                if start_idx != -1:
                    # Find the start of the JSON object
                    while start_idx > 0 and end_portion[start_idx-1] != '{':
                        start_idx -= 1
                    if start_idx > 0:
                        start_idx -= 1
            
            if start_idx != -1:
                candidate = end_portion[start_idx:]
                # Try to extract a complete JSON object
                brace_count = 0
                end_idx = 0
                for i, char in enumerate(candidate):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_idx = i + 1
                            break
                
                if end_idx > 0:
                    json_candidate = candidate[:end_idx]
                    try:
                        result = json.loads(json_candidate)
                        print(f"DEBUG - End extraction successful: {list(result.keys())}")
                        return result
                    except json.JSONDecodeError:
                        print(f"DEBUG - End extraction failed, candidate: {json_candidate[:100]}...")
        
        # Method 1: Try direct JSON parsing first
        try:
            result = json.loads(response.strip())
            print(f"DEBUG - Direct JSON parsing successful: {list(result.keys())}")
            return result
        except json.JSONDecodeError:
            print("DEBUG - Direct JSON parsing failed")
            pass
        
        # Method 2: Remove Grok's emoji and processing indicators
        cleaned = response
        
        # Remove Grok's processing indicators
        cleaned = re.sub(r'🤖 Processing prompt\.{3}\n\n', '', cleaned)
        cleaned = re.sub(r'> .*?\n\n', '', cleaned, flags=re.DOTALL)
        
        # Try parsing cleaned response
        try:
            return json.loads(cleaned.strip())
        except json.JSONDecodeError:
            pass
        
        # Method 3: Find JSON blocks in response
        json_patterns = [
            # Look for complete JSON objects
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',
            # Look for JSON within code blocks
            r'```json\s*(.*?)\s*```',
            r'```\s*(.*?)\s*```'
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            for match in matches:
                try:
                    # Clean up the match
                    json_str = match.strip()
                    if not json_str.startswith('{'):
                        continue
                    
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    continue
        
        # Method 4: Line-by-line extraction
        lines = response.split('\n')
        json_lines = []
        in_json = False
        brace_count = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Skip Grok's conversational elements
            if '🤖' in line or line.startswith('>') or 'Processing prompt' in line:
                continue
                
            # Start collecting when we see opening brace
            if line.startswith('{'):
                in_json = True
                json_lines = []
                brace_count = 0
            
            if in_json:
                json_lines.append(line)
                brace_count += line.count('{') - line.count('}')
                
                # End collecting when braces are balanced
                if brace_count == 0 and len(json_lines) > 0:
                    try:
                        json_str = '\n'.join(json_lines)
                        result = json.loads(json_str)
                        print(f"DEBUG - Line-by-line extraction successful: {list(result.keys())}")
                        return result
                    except json.JSONDecodeError:
                        pass
                    in_json = False
                    json_lines = []
        
        # Method 5: Look for complete calculation structures (most important)
        # Priority order: calculations at end of response are more likely to be results
        calculation_patterns = [
            # Look for complete foundation_calculations structure
            r'\{\s*"foundation_calculations":\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\}',
            r'\{\s*"energy_structure_calculations":\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\}',
            r'\{\s*"advanced_dynamics":\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\}',
            r'\{\s*"integration_validation":\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\}'
        ]
        
        # Search from end of response backwards (more likely to find calculations)
        for pattern in calculation_patterns:
            matches = list(re.finditer(pattern, response, re.DOTALL))
            if matches:
                # Take the last match (closest to end of response)
                match = matches[-1]
                try:
                    json_str = match.group(0)
                    result = json.loads(json_str)
                    print(f"DEBUG - Calculation pattern extraction successful: {list(result.keys())}")
                    return result
                except json.JSONDecodeError:
                    continue
        
        # Method 6: Extract just the calculation content without outer braces
        result_patterns = [
            r'"foundation_calculations":\s*(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
            r'"energy_structure_calculations":\s*(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
            r'"advanced_dynamics":\s*(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
            r'"integration_validation":\s*(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})'
        ]
        
        for i, pattern in enumerate(result_patterns):
            matches = list(re.finditer(pattern, response, re.DOTALL))
            if matches:
                # Take the last match
                match = matches[-1]
                try:
                    calc_content = match.group(1)
                    # Determine the key name
                    key_names = ["foundation_calculations", "energy_structure_calculations", 
                               "advanced_dynamics", "integration_validation"]
                    key_name = key_names[i]
                    
                    # Wrap in proper JSON structure
                    json_content = f'{{"{key_name}": {calc_content}}}'
                    result = json.loads(json_content)
                    print(f"DEBUG - Pattern extraction successful: {list(result.keys())}")
                    return result
                except json.JSONDecodeError:
                    continue
        
        # If all methods fail, return error structure
        return {
            "calculations": {
                "error": "json_extraction_failed",
                "raw_response": response[:500] + "..." if len(response) > 500 else response,
                "unit_status": "failed"
            }
        }
    
    def execute_json_prompt(self, prompt: str, expected_schema: str = None) -> Dict[Any, Any]:
        """Execute a prompt expecting JSON response with robust extraction."""
        
        # Enhanced prompt with direct JSON enforcement
        enhanced_prompt = f"""{prompt}

CRITICAL: Return only the JSON structure specified above. Begin immediately with {{. No other text."""
        
        response = self.execute_prompt(enhanced_prompt)
        return self.extract_json_from_response(response)


class ComputationalUnit:
    """Base class for Grok computational units."""
    
    def __init__(self, client: GrokClient, unit_name: str):
        self.client = client
        self.unit_name = unit_name
        self.system_prompt = self._load_system_prompt()
    
    def _load_system_prompt(self) -> str:
        """Load system prompt for this unit."""
        # This will be overridden by each unit
        return f"You are {self.unit_name}. Process the input and return structured JSON."
    
    def process(self, input_data: Dict[Any, Any]) -> Dict[Any, Any]:
        """Process input through this computational unit."""
        try:
            # Prepare the full prompt
            prompt = f"{self.system_prompt}\n\nINPUT:\n{json.dumps(input_data, indent=2)}"
            
            # Execute and get JSON response
            result = self.client.execute_json_prompt(prompt)
            
            return result
            
        except Exception as e:
            # Return error structure compatible with unit output
            unit_key = f"{self.unit_name.lower().replace(' ', '_')}_calculations"
            return {
                unit_key: {
                    "error": "processing_failed",
                    "error_message": str(e),
                    "unit_status": "failed"
                }
            }