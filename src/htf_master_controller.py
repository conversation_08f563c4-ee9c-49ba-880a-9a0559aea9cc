#!/usr/bin/env python3
"""
HTF Master Controller - Fractal Architecture Implementation
Implements the Higher Timeframe process that serves as master activation controller
for subordinate session-level Hawkes cascade predictions.
"""

import json
import math
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

@dataclass
class HTFEvent:
    """Represents an HTF event for intensity calculation."""
    time: datetime
    event_type: str
    price: float
    magnitude: float
    structure_info: str
    source_file: str

@dataclass
class ActivationSignal:
    """Represents an activation signal for subordinate processes."""
    target_sessions: List[str]
    activation_window: str
    cascade_type: str
    param_adjustments: Dict[str, float]
    confidence_boost: float
    htf_intensity: float
    activation_time: datetime

class HTFActivationController:
    """
    HTF Master Controller implementing fractal cascade architecture.
    
    This controller monitors HTF intensity and generates activation signals
    for subordinate session-level processes when thresholds are exceeded.
    """
    
    def __init__(self, base_dir: str = "/Users/<USER>/grok-claude-automation"):
        self.base_dir = Path(base_dir)
        
        # Load HTF parameters from calibration
        self.htf_params = self._load_htf_parameters()
        self.session_gammas = self._load_session_gammas()
        
        # HTF activation threshold based on calibration confidence (0.891)
        self.threshold_h = 0.5
        
        # Session targeting matrix for temporal markers
        self.temporal_marker_matrix = self._initialize_temporal_matrix()
        
        # Current HTF events cache
        self.htf_events_cache = []
        self.last_cache_update = None
        
    def _load_htf_parameters(self) -> Dict[str, float]:
        """Load HTF parameters from calibration file."""
        try:
            calibration_file = self.base_dir / "htf_calibrated_gammas_july28.json"
            with open(calibration_file, 'r') as f:
                data = json.load(f)
            return data['htf_parameters']
        except Exception as e:
            print(f"Warning: Could not load HTF parameters: {e}")
            # Fallback parameters
            return {
                "mu_h": 0.02,
                "alpha_h": 35.51,
                "beta_h": 0.00442
            }
    
    def _load_session_gammas(self) -> Dict[str, float]:
        """Load session gamma values from calibration."""
        try:
            calibration_file = self.base_dir / "htf_calibrated_gammas_july28.json"
            with open(calibration_file, 'r') as f:
                data = json.load(f)
            return data['calibrated_gammas_july28']
        except Exception as e:
            print(f"Warning: Could not load session gammas: {e}")
            # Fallback gammas
            return {
                "Asia": 0.0895,
                "Midnight": 0.2987,
                "London": 0.1934,
                "Premarket": 0.1523,
                "NY_AM": 0.0278,
                "Lunch": 0.2534,
                "NY_PM": 0.000163
            }
    
    def _initialize_temporal_matrix(self) -> Dict[str, Dict[str, Any]]:
        """Initialize temporal marker matrix for session targeting."""
        return {
            "session_high_htf": {
                "target_sessions": ["NY_PM", "Asia"],
                "cascade_type": "expansion_lower",
                "confidence_factor": 0.85
            },
            "session_low_htf": {
                "target_sessions": ["NY_PM", "London"],
                "cascade_type": "expansion_higher",
                "confidence_factor": 0.80
            },
            "htf_fvg_formation": {
                "target_sessions": ["NY_PM", "NY_AM"],
                "cascade_type": "redelivery_expansion",
                "confidence_factor": 0.90
            },
            "friday_weekly_setup": {
                "target_sessions": ["Asia", "London"],
                "cascade_type": "weekly_influence",
                "confidence_factor": 0.95
            },
            "friday_close_htf": {
                "target_sessions": ["Asia", "Midnight", "London"],
                "cascade_type": "weekend_carryover",
                "confidence_factor": 0.92
            }
        }
    
    def load_htf_events(self, force_reload: bool = False) -> List[HTFEvent]:
        """Load HTF events from diagnostic report."""
        current_time = datetime.now()
        
        # Check cache validity (refresh every 5 minutes)
        if (not force_reload and self.htf_events_cache and 
            self.last_cache_update and 
            (current_time - self.last_cache_update).total_seconds() < 300):
            return self.htf_events_cache
        
        try:
            diagnostic_file = self.base_dir / "htf_diagnostic_report_july28.json"
            with open(diagnostic_file, 'r') as f:
                data = json.load(f)
            
            htf_events = []
            
            # Extract events from July 28 sessions analyzed
            for session_data in data.get('july_28_sessions_analyzed', []):
                for event_data in session_data.get('key_events', []):
                    try:
                        event_time = datetime.fromisoformat(event_data['time'].replace('Z', '+00:00'))
                        
                        htf_event = HTFEvent(
                            time=event_time,
                            event_type=event_data['type'],
                            price=event_data['price'],
                            magnitude=event_data['magnitude'],
                            structure_info=event_data['structure_info'],
                            source_file=session_data['file']
                        )
                        htf_events.append(htf_event)
                        
                    except Exception as e:
                        print(f"Error parsing HTF event: {e}")
                        continue
            
            self.htf_events_cache = htf_events
            self.last_cache_update = current_time
            return htf_events
            
        except Exception as e:
            print(f"Error loading HTF events: {e}")
            return []
    
    def compute_htf_intensity(self, current_time: datetime, 
                            htf_events: List[HTFEvent] = None) -> float:
        """
        Compute current HTF intensity using Hawkes process.
        
        Args:
            current_time: Time to compute intensity for
            htf_events: HTF events to use (loads from cache if None)
            
        Returns:
            HTF intensity value
        """
        if htf_events is None:
            htf_events = self.load_htf_events()
        
        # Base intensity
        intensity = self.htf_params['mu_h']
        
        # Add contributions from recent HTF events
        for event in htf_events:
            # Calculate time difference in hours (HTF scale)
            delta_t = (current_time - event.time).total_seconds() / 3600.0
            
            # Only consider events in the past that are still influential
            if delta_t >= 0 and delta_t <= 168:  # 1 week max influence
                # HTF excitation: α_h * exp(-β_h * Δt) * magnitude
                excitation = (self.htf_params['alpha_h'] * 
                            math.exp(-self.htf_params['beta_h'] * delta_t) * 
                            event.magnitude)
                intensity += excitation
        
        return intensity
    
    def detect_activation(self, current_time: datetime = None) -> Optional[ActivationSignal]:
        """
        Detect if HTF intensity exceeds threshold and generate activation signal.
        
        Args:
            current_time: Time to check activation for (defaults to now)
            
        Returns:
            ActivationSignal if activation detected, None otherwise
        """
        if current_time is None:
            current_time = datetime.now()
        
        # Load recent HTF events
        htf_events = self.load_htf_events()
        
        # Compute current HTF intensity
        htf_intensity = self.compute_htf_intensity(current_time, htf_events)
        
        # Check if intensity exceeds activation threshold
        if htf_intensity > self.threshold_h:
            return self.generate_activation_signal(
                htf_intensity, htf_events, current_time
            )
        
        return None
    
    def generate_activation_signal(self, htf_intensity: float, 
                                 htf_events: List[HTFEvent], 
                                 current_time: datetime) -> ActivationSignal:
        """
        Generate activation signal for subordinate processes.
        
        Args:
            htf_intensity: Current HTF intensity
            htf_events: Recent HTF events
            current_time: Current time
            
        Returns:
            ActivationSignal with target sessions and parameters
        """
        # Determine target sessions based on recent events and current time
        target_sessions = self._determine_target_sessions(htf_events, current_time)
        
        # Determine cascade type based on liquidity bias and event patterns
        cascade_type = self._determine_cascade_type(htf_events, current_time)
        
        # Calculate parameter adjustments
        baseline_boost = htf_intensity / self.threshold_h
        
        # Get appropriate gamma for primary target session
        primary_session = target_sessions[0] if target_sessions else "NY_PM"
        decay_gamma = self._get_effective_gamma(primary_session)
        
        # Calculate confidence boost
        confidence_boost = min(1.5, baseline_boost)  # Cap at 1.5x
        
        # Determine activation window based on target session
        activation_window = self._get_activation_window(primary_session, current_time)
        
        return ActivationSignal(
            target_sessions=target_sessions,
            activation_window=activation_window,
            cascade_type=cascade_type,
            param_adjustments={
                "baseline_boost": baseline_boost,
                "decay_gamma": decay_gamma,
                "confidence_factor": confidence_boost
            },
            confidence_boost=confidence_boost,
            htf_intensity=htf_intensity,
            activation_time=current_time
        )
    
    def _determine_target_sessions(self, htf_events: List[HTFEvent], 
                                 current_time: datetime) -> List[str]:
        """Determine target sessions based on HTF events and timing."""
        current_hour = current_time.hour + current_time.minute / 60.0
        
        # Current session determination (ET timezone)
        if 0.0 <= current_hour < 0.5:
            current_session = "Midnight"
        elif 2.0 <= current_hour < 5.0:
            current_session = "London"
        elif 7.0 <= current_hour < 9.5:
            current_session = "Premarket"
        elif 9.5 <= current_hour < 12.0:
            current_session = "NY_AM"
        elif 12.0 <= current_hour < 13.0:
            current_session = "Lunch"
        elif 13.5 <= current_hour < 16.25:
            current_session = "NY_PM"
        elif 19.0 <= current_hour <= 23.99:
            current_session = "Asia"
        else:
            current_session = "NY_PM"  # Default
        
        # Get targets from most recent significant events
        target_sessions = []
        for event in sorted(htf_events, key=lambda x: x.time, reverse=True)[:3]:
            event_targets = self.temporal_marker_matrix.get(
                event.event_type, {}
            ).get("target_sessions", [])
            target_sessions.extend(event_targets)
        
        # Remove duplicates and prioritize current session
        unique_targets = list(set(target_sessions))
        if current_session in unique_targets:
            unique_targets.remove(current_session)
            unique_targets.insert(0, current_session)
        
        return unique_targets[:2]  # Return top 2 targets
    
    def _determine_cascade_type(self, htf_events: List[HTFEvent], 
                              current_time: datetime) -> str:
        """Determine cascade type based on HTF event patterns."""
        # Analyze recent events for directional bias
        recent_events = [e for e in htf_events 
                        if (current_time - e.time).total_seconds() < 14400]  # 4 hours
        
        if not recent_events:
            return "expansion_lower"  # Default based on untaken liquidity
        
        # Count event types
        highs = len([e for e in recent_events if "high" in e.event_type])
        lows = len([e for e in recent_events if "low" in e.event_type])
        
        if lows > highs:
            return "expansion_higher"  # Lows create upward bias
        else:
            return "expansion_lower"   # Highs create downward bias
    
    def _get_effective_gamma(self, session: str) -> float:
        """Get effective gamma for session, handling 'too_low' cases."""
        gamma = self.session_gammas.get(session, 0.1)
        
        # Handle NY_PM "too_low" gamma with fallback
        if session == "NY_PM" and gamma < 0.001:
            # Use average of high-confidence sessions
            high_confidence_gammas = [
                self.session_gammas.get("Asia", 0.0895),
                self.session_gammas.get("London", 0.1934),
                self.session_gammas.get("Premarket", 0.1523),
                self.session_gammas.get("NY_AM", 0.0278),
                self.session_gammas.get("Lunch", 0.2534)
            ]
            gamma = sum(high_confidence_gammas) / len(high_confidence_gammas)
        
        return gamma
    
    def _get_activation_window(self, session: str, current_time: datetime) -> str:
        """Get activation window for target session."""
        session_windows = {
            "Asia": "19:00-23:59 ET",
            "Midnight": "00:00-00:29 ET",
            "London": "02:00-04:59 ET",
            "Premarket": "07:00-09:29 ET",
            "NY_AM": "09:30-11:59 ET",
            "Lunch": "12:00-12:59 ET",
            "NY_PM": "13:30-16:09 ET"
        }
        
        return session_windows.get(session, "13:30-16:09 ET")
    
    def get_activation_status(self, current_time: datetime = None) -> Dict[str, Any]:
        """Get current activation status and HTF intensity."""
        if current_time is None:
            current_time = datetime.now()
        
        htf_events = self.load_htf_events()
        htf_intensity = self.compute_htf_intensity(current_time, htf_events)
        activation_signal = self.detect_activation(current_time)
        
        return {
            "timestamp": current_time.isoformat(),
            "htf_intensity": htf_intensity,
            "threshold": self.threshold_h,
            "activation_status": "ACTIVE" if activation_signal else "DORMANT",
            "activation_signal": activation_signal.__dict__ if activation_signal else None,
            "htf_events_count": len(htf_events),
            "recent_events": [
                {
                    "time": e.time.isoformat(),
                    "type": e.event_type,
                    "magnitude": e.magnitude
                } for e in htf_events[-5:]  # Last 5 events
            ]
        }


def main():
    """Test the HTF Master Controller."""
    controller = HTFActivationController()
    
    # Test current activation status
    print("🔍 Testing HTF Master Controller...")
    status = controller.get_activation_status()
    
    print(f"\n📊 HTF ACTIVATION STATUS:")
    print(f"HTF Intensity: {status['htf_intensity']:.4f}")
    print(f"Threshold: {status['threshold']}")
    print(f"Status: {status['activation_status']}")
    print(f"HTF Events: {status['htf_events_count']}")
    
    if status['activation_signal']:
        signal = status['activation_signal']
        print(f"\n🚀 ACTIVATION SIGNAL:")
        print(f"Target Sessions: {signal['target_sessions']}")
        print(f"Cascade Type: {signal['cascade_type']}")
        print(f"Baseline Boost: {signal['param_adjustments']['baseline_boost']:.3f}")
        print(f"Decay Gamma: {signal['param_adjustments']['decay_gamma']:.6f}")
        print(f"Confidence Boost: {signal['confidence_boost']:.3f}")
    
    print(f"\n📋 Recent HTF Events:")
    for event in status['recent_events']:
        print(f"  {event['time']}: {event['type']} (mag: {event['magnitude']})")


if __name__ == "__main__":
    main()