#!/usr/bin/env python3
"""
Timing Agent Competition System
Multiple specialized timing agents compete to predict market event timing.
Each agent has different strengths and methodologies for timing prediction.
"""

import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time
from abc import ABC, abstractmethod

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
try:
    from market_state_hmm import MarketStateHMM, MarketState
    from options_expiry_integration import OptionsExpiryIntegration
    from event_timing_monte_carlo import EventTimingMonteCarloSimulator
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

class AgentType(Enum):
    """Types of timing prediction agents"""
    MOMENTUM_TIMER = "momentum_timer"
    REVERSION_TIMER = "reversion_timer"
    CASCADE_TIMER = "cascade_timer"
    ENERGY_TIMER = "energy_timer"
    PATTERN_TIMER = "pattern_timer"
    HYBRID_TIMER = "hybrid_timer"

@dataclass
class TimingPrediction:
    """Individual timing prediction from an agent"""
    agent_id: str
    agent_type: AgentType
    event_type: str
    predicted_time: datetime
    confidence: float
    methodology: str
    supporting_evidence: Dict[str, float]
    prediction_timestamp: datetime

@dataclass
class AgentPerformance:
    """Performance tracking for timing agents"""
    agent_id: str
    total_predictions: int
    successful_predictions: int
    average_timing_error_minutes: float
    confidence_calibration: float
    best_event_types: List[str]
    recent_performance_trend: str
    specialty_strength: float

@dataclass
class CompetitionResult:
    """Result from agent competition"""
    winning_agent: str
    winning_prediction: TimingPrediction
    competition_scores: Dict[str, float]
    consensus_prediction: Optional[TimingPrediction]
    confidence_in_winner: float
    competition_timestamp: datetime

class TimingAgent(ABC):
    """Abstract base class for timing prediction agents"""
    
    def __init__(self, agent_id: str, agent_type: AgentType):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.performance_history = []
        self.specialties = []
        
    @abstractmethod
    def predict_event_timing(self, session_data: dict, event_type: str,
                           tracker_context: tuple = None) -> TimingPrediction:
        """Make a timing prediction for the specified event type"""
        pass
    
    @abstractmethod
    def get_agent_strengths(self) -> List[str]:
        """Return list of event types this agent specializes in"""
        pass
    
    def update_performance(self, prediction: TimingPrediction, actual_time: datetime):
        """Update agent performance based on prediction accuracy"""
        
        timing_error = abs((actual_time - prediction.predicted_time).total_seconds() / 60)
        
        performance_record = {
            'prediction_id': f"{prediction.agent_id}_{prediction.prediction_timestamp.strftime('%H%M%S')}",
            'event_type': prediction.event_type,
            'timing_error_minutes': timing_error,
            'confidence': prediction.confidence,
            'successful': timing_error <= 10,  # Within 10 minutes
            'timestamp': datetime.now()
        }
        
        self.performance_history.append(performance_record)
        
        # Keep only last 50 predictions
        if len(self.performance_history) > 50:
            self.performance_history = self.performance_history[-50:]

class MomentumTimerAgent(TimingAgent):
    """Agent specializing in momentum-based timing predictions"""
    
    def __init__(self):
        super().__init__("momentum_timer", AgentType.MOMENTUM_TIMER)
        self.specialties = ["expansion_start", "momentum_acceleration", "cascade_start"]
        
    def predict_event_timing(self, session_data: dict, event_type: str,
                           tracker_context: tuple = None) -> TimingPrediction:
        
        # Extract momentum indicators
        grok_data = session_data.get('grok_enhanced_calculations', {})
        unit_c = grok_data.get('unit_c_advanced_dynamics', {})
        momentum_data = unit_c.get('temporal_momentum', {})
        momentum_strength = momentum_data.get('momentum_strength', 0.5)
        
        # Extract energy rate
        unit_b = grok_data.get('unit_b_energy_structure', {})
        energy_accumulation = unit_b.get('energy_accumulation', {})
        energy_rate = energy_accumulation.get('energy_rate', 0.5)
        
        # Momentum-based timing calculation
        base_timing_minutes = {
            "expansion_start": 20,
            "momentum_acceleration": 15,
            "cascade_start": 25,
            "liquidity_sweep": 18
        }.get(event_type, 30)
        
        # Adjust timing based on momentum strength
        if momentum_strength > 1.0:
            # High momentum = events happen sooner
            timing_adjustment = -(momentum_strength - 1.0) * 8
            confidence_boost = 0.15
        elif momentum_strength < 0.5:
            # Low momentum = events delayed
            timing_adjustment = (0.5 - momentum_strength) * 12
            confidence_boost = -0.1
        else:
            timing_adjustment = 0
            confidence_boost = 0
        
        # Energy rate influence
        if energy_rate > 1.0:
            timing_adjustment -= (energy_rate - 1.0) * 5
            confidence_boost += 0.1
        
        final_timing = max(5, base_timing_minutes + timing_adjustment)
        predicted_time = datetime.now() + timedelta(minutes=final_timing)
        
        base_confidence = 0.7
        final_confidence = min(0.95, max(0.3, base_confidence + confidence_boost))
        
        return TimingPrediction(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            event_type=event_type,
            predicted_time=predicted_time,
            confidence=final_confidence,
            methodology="momentum_strength_analysis",
            supporting_evidence={
                'momentum_strength': momentum_strength,
                'energy_rate': energy_rate,
                'timing_adjustment': timing_adjustment
            },
            prediction_timestamp=datetime.now()
        )
    
    def get_agent_strengths(self) -> List[str]:
        return self.specialties

class ReversionTimerAgent(TimingAgent):
    """Agent specializing in mean reversion timing predictions"""
    
    def __init__(self):
        super().__init__("reversion_timer", AgentType.REVERSION_TIMER)
        self.specialties = ["consolidation_begin", "energy_exhaustion", "reversal_setup"]
        
    def predict_event_timing(self, session_data: dict, event_type: str,
                           tracker_context: tuple = None) -> TimingPrediction:
        
        # Extract consolidation indicators
        grok_data = session_data.get('grok_enhanced_calculations', {})
        unit_c = grok_data.get('unit_c_advanced_dynamics', {})
        consolidation_data = unit_c.get('consolidation_analysis', {})
        consolidation_strength = consolidation_data.get('consolidation_strength', 0.5)
        
        # Extract energy data
        unit_b = grok_data.get('unit_b_energy_structure', {})
        energy_accumulation = unit_b.get('energy_accumulation', {})
        energy_rate = energy_accumulation.get('energy_rate', 0.5)
        
        # Reversion-based timing calculation
        base_timing_minutes = {
            "consolidation_begin": 45,
            "energy_exhaustion": 60,
            "reversal_setup": 35,
            "fvg_interaction": 40
        }.get(event_type, 50)
        
        # Adjust timing based on consolidation strength
        if consolidation_strength > 0.7:
            # Strong consolidation = reversion happens sooner
            timing_adjustment = -(consolidation_strength - 0.7) * 15
            confidence_boost = 0.2
        else:
            # Weak consolidation = reversion delayed
            timing_adjustment = (0.7 - consolidation_strength) * 10
            confidence_boost = -0.1
        
        # Energy exhaustion influence
        if energy_rate < 0.3:
            # Low energy supports reversion
            timing_adjustment -= (0.3 - energy_rate) * 20
            confidence_boost += 0.15
        
        final_timing = max(10, base_timing_minutes + timing_adjustment)
        predicted_time = datetime.now() + timedelta(minutes=final_timing)
        
        base_confidence = 0.65
        final_confidence = min(0.9, max(0.25, base_confidence + confidence_boost))
        
        return TimingPrediction(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            event_type=event_type,
            predicted_time=predicted_time,
            confidence=final_confidence,
            methodology="consolidation_reversion_analysis",
            supporting_evidence={
                'consolidation_strength': consolidation_strength,
                'energy_rate': energy_rate,
                'timing_adjustment': timing_adjustment
            },
            prediction_timestamp=datetime.now()
        )
    
    def get_agent_strengths(self) -> List[str]:
        return self.specialties

class CascadeTimerAgent(TimingAgent):
    """Agent specializing in cascade event timing (based on 12:22 pattern discovery)"""
    
    def __init__(self):
        super().__init__("cascade_timer", AgentType.CASCADE_TIMER)
        self.specialties = ["cascade_start", "expansion_phase", "liquidity_sweep"]
        self.cascade_lead_time = 8  # 8-minute lead time from 12:22 pattern
        
    def predict_event_timing(self, session_data: dict, event_type: str,
                           tracker_context: tuple = None) -> TimingPrediction:
        
        # Extract T_memory from tracker context
        t_memory = 5.0
        fvg_cluster_density = 0.0
        
        if tracker_context:
            try:
                htf_tracker, fvg_tracker, liquidity_tracker = tracker_context
                t_memory = fvg_tracker.get('t_memory_calculations', {}).get('t_memory_current', 5.0)
                fvg_clusters = fvg_tracker.get('active_fvg_clusters', [])
                fvg_cluster_density = len(fvg_clusters)
            except Exception:
                pass
        
        # Extract energy data
        grok_data = session_data.get('grok_enhanced_calculations', {})
        unit_b = grok_data.get('unit_b_energy_structure', {})
        energy_accumulation = unit_b.get('energy_accumulation', {})
        energy_rate = energy_accumulation.get('energy_rate', 0.5)
        
        # Cascade timing calculation using discovered pattern
        if event_type in ["cascade_start", "expansion_phase"]:
            # Use 8-minute lead time discovery
            base_timing = self.cascade_lead_time
            
            # T_memory enhancement (higher T_memory = more cascade potential)
            if t_memory > 5.0:
                timing_adjustment = -(t_memory - 5.0) * 1.2  # Earlier cascade
                confidence_boost = min(0.25, (t_memory - 5.0) * 0.05)
            else:
                timing_adjustment = (5.0 - t_memory) * 2.0  # Later cascade
                confidence_boost = -0.1
            
            # FVG cluster density influence
            if fvg_cluster_density >= 3.0:
                timing_adjustment -= 3  # FVG clusters accelerate cascades
                confidence_boost += 0.15
            
            # Energy rate influence
            if energy_rate > 0.8:
                timing_adjustment -= 2  # High energy = immediate cascade
                confidence_boost += 0.1
                
        else:
            # Other event types
            base_timing = 20
            timing_adjustment = 0
            confidence_boost = 0
        
        # Apply half-hour magnetism (cascades drawn to :22, :52 patterns)
        current_minute = datetime.now().minute
        target_minutes = [22, 52]  # Cascade magnetism points
        
        closest_target = min(target_minutes, key=lambda x: abs(x - current_minute))
        minutes_to_target = (closest_target - current_minute) % 60
        
        if minutes_to_target <= 15:  # Within 15 minutes of target
            magnetism_adjustment = -(15 - minutes_to_target) * 0.5
            timing_adjustment += magnetism_adjustment
            confidence_boost += 0.1
        
        final_timing = max(2, base_timing + timing_adjustment)
        predicted_time = datetime.now() + timedelta(minutes=final_timing)
        
        base_confidence = 0.8  # High confidence due to discovered pattern
        final_confidence = min(0.95, max(0.4, base_confidence + confidence_boost))
        
        return TimingPrediction(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            event_type=event_type,
            predicted_time=predicted_time,
            confidence=final_confidence,
            methodology="cascade_pattern_discovery_8min_lead",
            supporting_evidence={
                't_memory': t_memory,
                'fvg_cluster_density': fvg_cluster_density,
                'energy_rate': energy_rate,
                'cascade_lead_time': self.cascade_lead_time,
                'magnetism_target': closest_target
            },
            prediction_timestamp=datetime.now()
        )
    
    def get_agent_strengths(self) -> List[str]:
        return self.specialties

class EnergyTimerAgent(TimingAgent):
    """Agent specializing in energy-based timing predictions"""
    
    def __init__(self):
        super().__init__("energy_timer", AgentType.ENERGY_TIMER)
        self.specialties = ["energy_exhaustion", "momentum_acceleration", "expansion_start"]
        
    def predict_event_timing(self, session_data: dict, event_type: str,
                           tracker_context: tuple = None) -> TimingPrediction:
        
        # Extract energy metrics
        grok_data = session_data.get('grok_enhanced_calculations', {})
        unit_b = grok_data.get('unit_b_energy_structure', {})
        energy_accumulation = unit_b.get('energy_accumulation', {})
        
        energy_rate = energy_accumulation.get('energy_rate', 0.5)
        total_accumulated = energy_accumulation.get('total_accumulated', 150.0)
        e_threshold_adj = energy_accumulation.get('e_threshold_adj', 1000.0)
        
        # Energy-based timing calculations
        energy_level = min(1.0, energy_rate / 2.0)  # Normalize to 0-1
        energy_saturation = total_accumulated / e_threshold_adj
        
        base_timing_minutes = {
            "energy_exhaustion": 90 - (energy_level * 60),  # High energy = sooner exhaustion
            "momentum_acceleration": 20 - (energy_level * 10),  # High energy = sooner acceleration
            "expansion_start": 25 - (energy_level * 15),  # High energy = sooner expansion
            "consolidation_begin": 40 + (energy_level * 20)  # High energy = later consolidation
        }.get(event_type, 40)
        
        # Energy saturation effects
        if energy_saturation > 0.8:
            # Near threshold - events imminent
            timing_adjustment = -15
            confidence_boost = 0.2
        elif energy_saturation < 0.3:
            # Low saturation - events delayed
            timing_adjustment = 10
            confidence_boost = -0.1
        else:
            timing_adjustment = 0
            confidence_boost = 0
        
        # Energy rate trend (simulated - would be calculated from history)
        energy_trend = "stable"  # Would analyze energy_rate changes over time
        
        if energy_trend == "accelerating":
            timing_adjustment -= 5
            confidence_boost += 0.1
        elif energy_trend == "decelerating":
            timing_adjustment += 5
            confidence_boost -= 0.05
        
        final_timing = max(3, base_timing_minutes + timing_adjustment)
        predicted_time = datetime.now() + timedelta(minutes=final_timing)
        
        base_confidence = 0.75
        final_confidence = min(0.95, max(0.3, base_confidence + confidence_boost))
        
        return TimingPrediction(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            event_type=event_type,
            predicted_time=predicted_time,
            confidence=final_confidence,
            methodology="energy_accumulation_threshold_analysis",
            supporting_evidence={
                'energy_rate': energy_rate,
                'energy_level': energy_level,
                'energy_saturation': energy_saturation,
                'timing_adjustment': timing_adjustment
            },
            prediction_timestamp=datetime.now()
        )
    
    def get_agent_strengths(self) -> List[str]:
        return self.specialties

class PatternTimerAgent(TimingAgent):
    """Agent specializing in pattern-based timing predictions using session character"""
    
    def __init__(self):
        super().__init__("pattern_timer", AgentType.PATTERN_TIMER)
        self.specialties = ["all_events"]  # Generalist based on patterns
        
        # Pattern-based timing profiles
        self.session_patterns = {
            "expansion_consolidation_final_expansion": {
                "cascade_start": 22,
                "expansion_phase": 18,
                "consolidation_begin": 55,
                "final_expansion": 85
            },
            "trending_bullish": {
                "expansion_start": 15,
                "momentum_acceleration": 25,
                "liquidity_sweep": 35
            },
            "consolidation": {
                "consolidation_begin": 20,
                "fvg_interaction": 30,
                "reversal_setup": 45
            }
        }
        
    def predict_event_timing(self, session_data: dict, event_type: str,
                           tracker_context: tuple = None) -> TimingPrediction:
        
        # Extract session character
        price_data = session_data.get('price_data', {})
        if 'original_session_data' in session_data:
            price_data = session_data['original_session_data']['price_data']
            
        session_character = price_data.get('session_character', 'unknown')
        
        # Get pattern-based timing
        pattern_timings = self.session_patterns.get(session_character, {})
        base_timing = pattern_timings.get(event_type, 35)  # Default 35 minutes
        
        # Confidence based on pattern match quality
        if session_character in self.session_patterns:
            if event_type in pattern_timings:
                pattern_confidence = 0.85  # High confidence for known patterns
            else:
                pattern_confidence = 0.6   # Medium confidence for known session type
        else:
            pattern_confidence = 0.4       # Low confidence for unknown patterns
        
        # Apply random variation to avoid deterministic predictions
        timing_variation = np.random.normal(0, 3)  # ±3 minute random variation
        final_timing = max(5, base_timing + timing_variation)
        
        predicted_time = datetime.now() + timedelta(minutes=final_timing)
        
        return TimingPrediction(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            event_type=event_type,
            predicted_time=predicted_time,
            confidence=pattern_confidence,
            methodology="session_character_pattern_matching",
            supporting_evidence={
                'session_character': session_character,
                'pattern_timing': base_timing,
                'timing_variation': timing_variation,
                'pattern_match_quality': pattern_confidence
            },
            prediction_timestamp=datetime.now()
        )
    
    def get_agent_strengths(self) -> List[str]:
        return ["pattern_recognition", "session_character_analysis"]

class HybridTimerAgent(TimingAgent):
    """Agent that combines multiple timing methodologies"""
    
    def __init__(self):
        super().__init__("hybrid_timer", AgentType.HYBRID_TIMER)
        self.specialties = ["all_events"]
        
        # Initialize component agents for ensemble approach
        self.momentum_agent = MomentumTimerAgent()
        self.reversion_agent = ReversionTimerAgent()
        self.cascade_agent = CascadeTimerAgent()
        self.energy_agent = EnergyTimerAgent()
        
    def predict_event_timing(self, session_data: dict, event_type: str,
                           tracker_context: tuple = None) -> TimingPrediction:
        
        # Get predictions from all component agents
        predictions = []
        
        try:
            momentum_pred = self.momentum_agent.predict_event_timing(session_data, event_type, tracker_context)
            predictions.append(momentum_pred)
        except Exception as e:
            pass
            
        try:
            reversion_pred = self.reversion_agent.predict_event_timing(session_data, event_type, tracker_context)
            predictions.append(reversion_pred)
        except Exception as e:
            pass
            
        try:
            cascade_pred = self.cascade_agent.predict_event_timing(session_data, event_type, tracker_context)
            predictions.append(cascade_pred)
        except Exception as e:
            pass
            
        try:
            energy_pred = self.energy_agent.predict_event_timing(session_data, event_type, tracker_context)
            predictions.append(energy_pred)
        except Exception as e:
            pass
        
        if not predictions:
            # Fallback prediction
            predicted_time = datetime.now() + timedelta(minutes=30)
            return TimingPrediction(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                event_type=event_type,
                predicted_time=predicted_time,
                confidence=0.5,
                methodology="fallback_default",
                supporting_evidence={},
                prediction_timestamp=datetime.now()
            )
        
        # Ensemble prediction using confidence-weighted average
        total_weight = sum(pred.confidence for pred in predictions)
        
        if total_weight > 0:
            # Calculate weighted average timing
            weighted_minutes = sum(
                pred.confidence * (pred.predicted_time - datetime.now()).total_seconds() / 60
                for pred in predictions
            ) / total_weight
            
            predicted_time = datetime.now() + timedelta(minutes=weighted_minutes)
            
            # Average confidence with slight boost for ensemble diversity
            ensemble_confidence = (sum(pred.confidence for pred in predictions) / len(predictions)) + 0.05
            ensemble_confidence = min(0.95, ensemble_confidence)
        else:
            # All predictions have zero confidence - use simple average
            avg_minutes = sum(
                (pred.predicted_time - datetime.now()).total_seconds() / 60
                for pred in predictions
            ) / len(predictions)
            
            predicted_time = datetime.now() + timedelta(minutes=avg_minutes)
            ensemble_confidence = 0.4
        
        # Collect supporting evidence from all agents
        if len(predictions) > 1:
            prediction_times = [(pred.predicted_time - datetime.now()).total_seconds() / 60 for pred in predictions]
            prediction_spread = max(prediction_times) - min(prediction_times)
        else:
            prediction_spread = 0
            
        supporting_evidence = {
            'component_predictions': len(predictions),
            'prediction_spread_minutes': prediction_spread,
            'consensus_strength': 1.0 - (prediction_spread / 60)
        }
        
        return TimingPrediction(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            event_type=event_type,
            predicted_time=predicted_time,
            confidence=ensemble_confidence,
            methodology="hybrid_ensemble_weighted_average",
            supporting_evidence=supporting_evidence,
            prediction_timestamp=datetime.now()
        )
    
    def get_agent_strengths(self) -> List[str]:
        return ["ensemble_prediction", "methodology_diversity", "robust_fallback"]

class TimingAgentCompetition:
    """Main competition system coordinating multiple timing agents"""
    
    def __init__(self):
        # Initialize all timing agents
        self.agents = {
            "momentum_timer": MomentumTimerAgent(),
            "reversion_timer": ReversionTimerAgent(),
            "cascade_timer": CascadeTimerAgent(),
            "energy_timer": EnergyTimerAgent(),
            "pattern_timer": PatternTimerAgent(),
            "hybrid_timer": HybridTimerAgent()
        }
        
        # Competition parameters
        self.competition_history = []
        self.agent_performance_tracking = {}
        
    def run_timing_competition(self, session_data: dict, event_type: str,
                             tracker_context: tuple = None) -> CompetitionResult:
        """Run competition between all agents for event timing prediction"""
        
        print(f"🏁 TIMING AGENT COMPETITION")
        print(f"=" * 35)
        print(f"Event Type: {event_type}")
        print(f"Competitors: {len(self.agents)}")
        
        # Get predictions from all agents
        predictions = {}
        competition_scores = {}
        
        print(f"\n🤖 Agent Predictions:")
        
        for agent_id, agent in self.agents.items():
            try:
                prediction = agent.predict_event_timing(session_data, event_type, tracker_context)
                predictions[agent_id] = prediction
                
                # Calculate competition score (confidence + specialization bonus)
                specialization_bonus = 0.1 if event_type in agent.get_agent_strengths() else 0
                competition_scores[agent_id] = prediction.confidence + specialization_bonus
                
                time_str = prediction.predicted_time.strftime('%H:%M:%S')
                print(f"   {agent_id}: {time_str} (confidence: {prediction.confidence:.2f})")
                
            except Exception as e:
                print(f"   {agent_id}: ❌ FAILED ({e})")
                competition_scores[agent_id] = 0.0
        
        if not predictions:
            print("❌ No valid predictions - competition failed")
            return None
        
        # Determine winner
        winning_agent_id = max(competition_scores, key=competition_scores.get)
        winning_prediction = predictions[winning_agent_id]
        
        print(f"\n🏆 Competition Winner: {winning_agent_id}")
        print(f"   Score: {competition_scores[winning_agent_id]:.2f}")
        print(f"   Predicted Time: {winning_prediction.predicted_time.strftime('%H:%M:%S')}")
        print(f"   Methodology: {winning_prediction.methodology}")
        
        # Generate consensus prediction (weighted by scores)
        consensus_prediction = self._generate_consensus_prediction(predictions, competition_scores, event_type)
        
        if consensus_prediction:
            print(f"\n🤝 Consensus Prediction: {consensus_prediction.predicted_time.strftime('%H:%M:%S')}")
            print(f"   Consensus Confidence: {consensus_prediction.confidence:.2f}")
        
        # Calculate confidence in winner
        winner_score = competition_scores[winning_agent_id]
        avg_score = np.mean(list(competition_scores.values()))
        confidence_in_winner = min(0.95, winner_score / max(avg_score, 0.1))
        
        result = CompetitionResult(
            winning_agent=winning_agent_id,
            winning_prediction=winning_prediction,
            competition_scores=competition_scores,
            consensus_prediction=consensus_prediction,
            confidence_in_winner=confidence_in_winner,
            competition_timestamp=datetime.now()
        )
        
        # Store competition history
        self.competition_history.append(result)
        if len(self.competition_history) > 100:
            self.competition_history = self.competition_history[-100:]
        
        return result
    
    def _generate_consensus_prediction(self, predictions: Dict[str, TimingPrediction],
                                     scores: Dict[str, float], event_type: str) -> Optional[TimingPrediction]:
        """Generate consensus prediction from all agent predictions"""
        
        if len(predictions) < 2:
            return None
        
        # Calculate weighted average timing
        total_weight = sum(scores.values())
        if total_weight <= 0:
            return None
        
        weighted_minutes = sum(
            scores[agent_id] * (pred.predicted_time - datetime.now()).total_seconds() / 60
            for agent_id, pred in predictions.items()
        ) / total_weight
        
        consensus_time = datetime.now() + timedelta(minutes=weighted_minutes)
        
        # Calculate consensus confidence
        confidence_values = [pred.confidence for pred in predictions.values()]
        consensus_confidence = np.mean(confidence_values)
        
        # Boost confidence if predictions are closely aligned
        prediction_times = [(pred.predicted_time - datetime.now()).total_seconds() / 60 
                          for pred in predictions.values()]
        time_spread = max(prediction_times) - min(prediction_times)
        
        if time_spread <= 10:  # Within 10 minutes
            consensus_confidence += 0.1
        elif time_spread <= 20:  # Within 20 minutes
            consensus_confidence += 0.05
        
        consensus_confidence = min(0.95, consensus_confidence)
        
        # Collect consensus supporting evidence
        supporting_evidence = {
            'participating_agents': len(predictions),
            'prediction_spread_minutes': time_spread,
            'score_distribution': scores,
            'methodology_diversity': len(set(pred.methodology for pred in predictions.values()))
        }
        
        return TimingPrediction(
            agent_id="consensus",
            agent_type=AgentType.HYBRID_TIMER,
            event_type=event_type,
            predicted_time=consensus_time,
            confidence=consensus_confidence,
            methodology="multi_agent_consensus",
            supporting_evidence=supporting_evidence,
            prediction_timestamp=datetime.now()
        )
    
    def get_agent_performance_summary(self) -> Dict[str, AgentPerformance]:
        """Get performance summary for all agents"""
        
        performance_summary = {}
        
        for agent_id, agent in self.agents.items():
            if agent.performance_history:
                total_predictions = len(agent.performance_history)
                successful_predictions = sum(1 for p in agent.performance_history if p['successful'])
                
                timing_errors = [p['timing_error_minutes'] for p in agent.performance_history]
                avg_timing_error = np.mean(timing_errors)
                
                # Calculate confidence calibration (how well confidence matches accuracy)
                confidences = [p['confidence'] for p in agent.performance_history]
                success_rates = [1 if p['successful'] else 0 for p in agent.performance_history]
                
                if len(confidences) > 1:
                    confidence_calibration = 1.0 - abs(np.mean(confidences) - np.mean(success_rates))
                else:
                    confidence_calibration = 0.5
                
                # Find best event types
                event_performance = {}
                for p in agent.performance_history:
                    event_type = p['event_type']
                    if event_type not in event_performance:
                        event_performance[event_type] = []
                    event_performance[event_type].append(p['successful'])
                
                best_events = [event for event, successes in event_performance.items()
                             if np.mean(successes) > 0.6]
                
                # Recent performance trend
                recent_performances = agent.performance_history[-10:]
                if len(recent_performances) >= 5:
                    recent_success_rate = np.mean([p['successful'] for p in recent_performances])
                    older_success_rate = np.mean([p['successful'] for p in agent.performance_history[-20:-10]]) if len(agent.performance_history) >= 20 else recent_success_rate
                    
                    if recent_success_rate > older_success_rate + 0.1:
                        trend = "improving"
                    elif recent_success_rate < older_success_rate - 0.1:
                        trend = "declining"
                    else:
                        trend = "stable"
                else:
                    trend = "insufficient_data"
                
                # Specialty strength
                agent_strengths = agent.get_agent_strengths()
                if agent_strengths and agent_strengths[0] != "all_events":
                    specialty_predictions = [p for p in agent.performance_history 
                                           if p['event_type'] in agent_strengths]
                    if specialty_predictions:
                        specialty_strength = np.mean([p['successful'] for p in specialty_predictions])
                    else:
                        specialty_strength = 0.5
                else:
                    specialty_strength = np.mean(success_rates)
                
                performance_summary[agent_id] = AgentPerformance(
                    agent_id=agent_id,
                    total_predictions=total_predictions,
                    successful_predictions=successful_predictions,
                    average_timing_error_minutes=avg_timing_error,
                    confidence_calibration=confidence_calibration,
                    best_event_types=best_events,
                    recent_performance_trend=trend,
                    specialty_strength=specialty_strength
                )
        
        return performance_summary
    
    def get_competition_statistics(self) -> Dict[str, Any]:
        """Get overall competition statistics"""
        
        if not self.competition_history:
            return {"message": "No competition history available"}
        
        # Winner frequency
        winners = [comp.winning_agent for comp in self.competition_history]
        from collections import Counter
        winner_counts = Counter(winners)
        
        # Average confidence in winners
        avg_winner_confidence = np.mean([comp.confidence_in_winner for comp in self.competition_history])
        
        # Consensus vs winner comparison
        consensus_available = sum(1 for comp in self.competition_history if comp.consensus_prediction)
        
        return {
            'total_competitions': len(self.competition_history),
            'winner_frequency': dict(winner_counts),
            'most_successful_agent': winner_counts.most_common(1)[0][0] if winner_counts else None,
            'average_winner_confidence': avg_winner_confidence,
            'consensus_prediction_rate': consensus_available / len(self.competition_history),
            'competition_diversity': len(set(winners)) / len(self.agents)
        }

def main():
    """Test timing agent competition system"""
    
    print("🧪 TIMING AGENT COMPETITION TEST")
    print("=" * 45)
    
    # Load test data
    try:
        with open('ny_pm_grokEnhanced_2025_07_23.json', 'r') as f:
            session_data = json.load(f)
        with open('HTF_Context_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            htf_tracker = json.load(f)
        with open('FVG_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            fvg_tracker = json.load(f)
        with open('Liquidity_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            liquidity_tracker = json.load(f)
            
        tracker_context = (htf_tracker, fvg_tracker, liquidity_tracker)
        
    except FileNotFoundError as e:
        print(f"❌ Test data not found: {e}")
        return
    
    # Initialize competition
    competition = TimingAgentCompetition()
    
    # Test multiple event types
    test_events = ["cascade_start", "expansion_phase", "consolidation_begin", "energy_exhaustion"]
    
    results = []
    
    for event_type in test_events:
        print(f"\n" + "="*60)
        result = competition.run_timing_competition(session_data, event_type, tracker_context)
        if result:
            results.append(result)
    
    # Display overall statistics
    print(f"\n📊 COMPETITION SUMMARY")
    print(f"=" * 30)
    
    stats = competition.get_competition_statistics()
    print(f"Total Competitions: {stats['total_competitions']}")
    print(f"Most Successful Agent: {stats['most_successful_agent']}")
    print(f"Average Winner Confidence: {stats['average_winner_confidence']:.2f}")
    
    print(f"\n🏆 Winner Frequency:")
    for agent, wins in stats['winner_frequency'].items():
        print(f"   {agent}: {wins} wins")
    
    # Save results
    results_data = {
        'test_metadata': {
            'test_type': 'timing_agent_competition',
            'timestamp': datetime.now().isoformat(),
            'events_tested': test_events
        },
        'competition_results': [
            {
                'event_type': result.winning_prediction.event_type,
                'winning_agent': result.winning_agent,
                'winning_time': result.winning_prediction.predicted_time.strftime('%H:%M:%S'),
                'confidence': result.winning_prediction.confidence,
                'scores': result.competition_scores
            }
            for result in results
        ],
        'competition_statistics': stats
    }
    
    output_file = f"timing_agent_competition_{datetime.now().strftime('%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results_data, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    print(f"\n✅ Agent competition system operational")
    print(f"   {len(competition.agents)} agents competing")
    print(f"   Multiple specialized timing methodologies")

if __name__ == "__main__":
    main()