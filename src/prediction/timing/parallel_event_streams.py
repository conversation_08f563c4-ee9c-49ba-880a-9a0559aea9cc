#!/usr/bin/env python3
"""
Parallel Event Streams Architecture
Converges HMM state detection, energy monitoring, and FVG cluster analysis
for unified event timing predictions with high confidence thresholds.
"""

import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
try:
    from market_state_hmm import MarketStateHMM, MarketState, TimingPrediction
    from fvg_enhanced_event_engine import FVGEnhancedEventEngine
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

@dataclass
class StreamResult:
    """Result from an individual event stream"""
    stream_name: str
    event_type: str
    predicted_time: datetime
    confidence: float
    supporting_metrics: Dict[str, float]
    state_evidence: Dict[str, Any]
    timestamp: datetime

@dataclass
class ConvergedPrediction:
    """Result from converged parallel streams"""
    unified_event_type: str
    consensus_time_window: Tuple[datetime, datetime]
    overall_confidence: float
    stream_agreement: float  # How well streams agree (0-1)
    contributing_streams: List[str]
    convergence_evidence: Dict[str, Any]
    countdown_timer: str
    alert_level: str  # low, medium, high, critical

class EnergyMonitor:
    """Dedicated energy stream for real-time energy accumulation tracking"""
    
    def __init__(self):
        self.energy_history = []
        self.energy_rate_threshold = 0.7
        self.energy_acceleration_threshold = 0.1  # Rate of change
        self.last_energy_level = 0.0
        self.energy_trend = "stable"
        
    def analyze_energy_stream(self, session_data: dict, tracker_context: tuple = None) -> StreamResult:
        """Analyze current energy stream for event timing signals"""
        
        # Extract current energy level
        grok_data = session_data.get('grok_enhanced_calculations', {})
        unit_b = grok_data.get('unit_b_energy_structure', {})
        energy_accumulation = unit_b.get('energy_accumulation', {})
        current_energy = energy_accumulation.get('energy_rate', 0.5)
        
        # Normalize energy (0-2 range to 0-1)
        normalized_energy = min(1.0, current_energy / 2.0)
        
        # Calculate energy acceleration
        energy_change = normalized_energy - self.last_energy_level
        self.energy_history.append(normalized_energy)
        
        # Keep only last 10 readings for trend analysis
        if len(self.energy_history) > 10:
            self.energy_history = self.energy_history[-10:]
        
        # Determine energy trend
        if len(self.energy_history) >= 3:
            recent_avg = np.mean(self.energy_history[-3:])
            older_avg = np.mean(self.energy_history[-6:-3]) if len(self.energy_history) >= 6 else recent_avg
            
            if recent_avg > older_avg + 0.1:
                self.energy_trend = "accelerating"
            elif recent_avg < older_avg - 0.1:
                self.energy_trend = "decelerating"
            else:
                self.energy_trend = "stable"
        
        # Predict energy-based events
        event_type = "energy_monitoring"
        predicted_time = datetime.now() + timedelta(minutes=15)  # Default
        confidence = 0.5
        
        if normalized_energy >= self.energy_rate_threshold and self.energy_trend == "accelerating":
            event_type = "energy_cascade_imminent"
            # High energy + acceleration = cascade within 5-10 minutes
            predicted_time = datetime.now() + timedelta(minutes=7)
            confidence = min(0.9, normalized_energy + 0.2)
            
        elif normalized_energy >= 0.5 and energy_change > self.energy_acceleration_threshold:
            event_type = "energy_building"
            # Energy is building = cascade within 10-15 minutes
            predicted_time = datetime.now() + timedelta(minutes=12)
            confidence = normalized_energy * 0.8
            
        elif normalized_energy <= 0.2 and self.energy_trend == "decelerating":
            event_type = "energy_exhaustion"
            # Energy exhausted = consolidation phase imminent
            predicted_time = datetime.now() + timedelta(minutes=5)
            confidence = (1.0 - normalized_energy) * 0.7
        
        self.last_energy_level = normalized_energy
        
        return StreamResult(
            stream_name="energy_monitor",
            event_type=event_type,
            predicted_time=predicted_time,
            confidence=confidence,
            supporting_metrics={
                'current_energy': normalized_energy,
                'energy_change': energy_change,
                'energy_trend': self.energy_trend,
                'threshold_exceeded': normalized_energy >= self.energy_rate_threshold
            },
            state_evidence={
                'energy_history': self.energy_history[-5:],  # Last 5 readings
                'acceleration_detected': abs(energy_change) > self.energy_acceleration_threshold
            },
            timestamp=datetime.now()
        )

class FVGClusterAnalyzer:
    """Dedicated FVG stream for cluster-based timing analysis"""
    
    def __init__(self):
        self.cluster_density_threshold = 3.0
        self.proximity_threshold = 0.8
        self.cascade_lead_time = 8  # Minutes before cascade when clusters form
        
    def analyze_fvg_stream(self, session_data: dict, tracker_context: tuple = None) -> StreamResult:
        """Analyze FVG cluster patterns for cascade timing"""
        
        fvg_cluster_density = 0.0
        proximity_score = 0.0
        
        # Extract FVG data from tracker context
        if tracker_context:
            try:
                htf_tracker, fvg_tracker, liquidity_tracker = tracker_context
                
                # Get active FVG clusters
                active_clusters = fvg_tracker.get('active_fvg_clusters', [])
                fvg_cluster_density = len(active_clusters)
                
                # Calculate proximity to price action
                current_price = session_data.get('original_session_data', {}).get('price_data', {}).get('close', 23300)
                
                if active_clusters:
                    # Calculate weighted proximity
                    total_proximity = 0.0
                    for cluster in active_clusters:
                        cluster_price = cluster.get('level', current_price)
                        distance = abs(current_price - cluster_price)
                        weight = cluster.get('weight', 1.0)
                        proximity = (weight / (distance + 1.0)) if distance > 0 else weight
                        total_proximity += proximity
                    
                    proximity_score = min(1.0, total_proximity / 10.0)  # Normalize
                    
            except Exception as e:
                print(f"⚠️ FVG stream analysis failed: {e}")
        
        # Predict FVG-based events
        event_type = "fvg_monitoring"
        predicted_time = datetime.now() + timedelta(minutes=20)
        confidence = 0.3
        
        if fvg_cluster_density >= self.cluster_density_threshold and proximity_score >= self.proximity_threshold:
            event_type = "fvg_cascade_setup"
            # High cluster density + proximity = cascade within 8 minutes (12:22 pattern)
            predicted_time = datetime.now() + timedelta(minutes=self.cascade_lead_time)
            confidence = min(0.85, (fvg_cluster_density / 5.0) + proximity_score)
            
        elif fvg_cluster_density >= 2.0:
            event_type = "fvg_clustering"
            # Moderate clustering = potential cascade within 15 minutes
            predicted_time = datetime.now() + timedelta(minutes=15)
            confidence = fvg_cluster_density / 5.0
            
        elif proximity_score >= 0.5:
            event_type = "fvg_proximity_alert"
            # Close to clusters = interaction likely within 10 minutes
            predicted_time = datetime.now() + timedelta(minutes=10)
            confidence = proximity_score * 0.6
        
        return StreamResult(
            stream_name="fvg_cluster_analyzer",
            event_type=event_type,
            predicted_time=predicted_time,
            confidence=confidence,
            supporting_metrics={
                'cluster_density': fvg_cluster_density,
                'proximity_score': proximity_score,
                'density_threshold_met': fvg_cluster_density >= self.cluster_density_threshold,
                'proximity_threshold_met': proximity_score >= self.proximity_threshold
            },
            state_evidence={
                'active_cluster_count': int(fvg_cluster_density),
                'high_density_detected': fvg_cluster_density >= self.cluster_density_threshold
            },
            timestamp=datetime.now()
        )

class ParallelEventStreams:
    """Main coordinator for parallel event stream analysis"""
    
    def __init__(self):
        # Initialize individual stream analyzers
        self.hmm_stream = MarketStateHMM()
        self.energy_stream = EnergyMonitor()  
        self.fvg_stream = FVGClusterAnalyzer()
        
        # Convergence parameters
        self.consensus_threshold = 0.7  # Agreement level needed for high confidence
        self.time_window_tolerance = 10  # Minutes - predictions must be within this window
        self.min_confidence_threshold = 0.5  # Minimum individual stream confidence
        
        # Real-time tracking
        self.last_convergence_time = datetime.now()
        self.convergence_history = []
        
    def analyze_all_streams(self, session_data: dict, tracker_context: tuple = None) -> List[StreamResult]:
        """Run all stream analyses in parallel"""
        
        stream_results = []
        
        with ThreadPoolExecutor(max_workers=3) as executor:
            # Submit all stream analyses
            future_to_stream = {
                executor.submit(self.hmm_stream.predict_event_timing, session_data, tracker_context, "cascade"): "hmm",
                executor.submit(self.energy_stream.analyze_energy_stream, session_data, tracker_context): "energy", 
                executor.submit(self.fvg_stream.analyze_fvg_stream, session_data, tracker_context): "fvg"
            }
            
            # Collect results
            for future in as_completed(future_to_stream):
                stream_name = future_to_stream[future]
                try:
                    result = future.result(timeout=30)  # 30 second timeout per stream
                    
                    # Convert HMM TimingPrediction to StreamResult format
                    if stream_name == "hmm" and result:
                        stream_result = StreamResult(
                            stream_name="market_state_hmm",
                            event_type=result.event_type,
                            predicted_time=result.time_window_start + 
                                         timedelta(minutes=(result.time_window_end - result.time_window_start).total_seconds()/120),  # Middle of window
                            confidence=result.confidence,
                            supporting_metrics=result.trigger_metrics,
                            state_evidence={
                                'state_sequence': [s.value for s in result.state_sequence],
                                'energy_threshold': result.energy_threshold
                            },
                            timestamp=datetime.now()
                        )
                        stream_results.append(stream_result)
                    elif stream_name in ["energy", "fvg"]:
                        stream_results.append(result)
                        
                except Exception as e:
                    print(f"⚠️ Stream {stream_name} failed: {e}")
                    
        return stream_results
    
    def converge_predictions(self, stream_results: List[StreamResult]) -> Optional[ConvergedPrediction]:
        """Converge multiple stream predictions into unified timing prediction"""
        
        if len(stream_results) < 2:
            print("⚠️ Need at least 2 streams for convergence")
            return None
            
        print(f"🔀 PARALLEL STREAMS CONVERGENCE")
        print(f"=" * 40)
        
        # Filter results by minimum confidence
        valid_results = [r for r in stream_results if r.confidence >= self.min_confidence_threshold]
        
        if len(valid_results) < 2:
            print(f"⚠️ Insufficient confident predictions (need ≥{self.min_confidence_threshold})")
            return None
        
        print(f"📊 Stream Results ({len(valid_results)} valid):")
        for result in valid_results:
            print(f"   {result.stream_name}: {result.event_type}")
            print(f"     Time: {result.predicted_time.strftime('%H:%M:%S')}")
            print(f"     Confidence: {result.confidence:.2f}")
        
        # Group predictions by event type family
        cascade_events = [r for r in valid_results if "cascade" in r.event_type.lower()]
        expansion_events = [r for r in valid_results if "expansion" in r.event_type.lower()]
        consolidation_events = [r for r in valid_results if any(term in r.event_type.lower() for term in ["consolidation", "exhaustion"])]
        
        # Find the largest group (most streams agree on event type)
        event_groups = [
            ("cascade", cascade_events),
            ("expansion", expansion_events), 
            ("consolidation", consolidation_events)
        ]
        
        dominant_event_type, dominant_group = max(event_groups, key=lambda x: len(x[1]))
        
        if len(dominant_group) < 2:
            print("⚠️ No consensus on event type")
            return None
        
        print(f"\n🎯 Consensus Event Type: {dominant_event_type} ({len(dominant_group)} streams)")
        
        # Calculate time window consensus
        predicted_times = [r.predicted_time for r in dominant_group]
        
        # Check if all predictions are within tolerance window
        time_spread = max(predicted_times) - min(predicted_times)
        if time_spread.total_seconds() / 60 > self.time_window_tolerance:
            print(f"⚠️ Time predictions too spread out ({time_spread.total_seconds()/60:.1f} min > {self.time_window_tolerance} min)")
            return None
        
        # Calculate consensus time window
        avg_time = min(predicted_times) + (time_spread / 2)
        window_start = avg_time - timedelta(minutes=3)
        window_end = avg_time + timedelta(minutes=3)
        
        # Calculate overall confidence and agreement
        confidences = [r.confidence for r in dominant_group]
        overall_confidence = np.mean(confidences)
        
        # Stream agreement score (higher when more streams agree and confidences are high)
        stream_agreement = (len(dominant_group) / len(valid_results)) * (overall_confidence)
        
        # Determine alert level
        if stream_agreement >= 0.8 and overall_confidence >= 0.8:
            alert_level = "critical"
        elif stream_agreement >= 0.7 and overall_confidence >= 0.7:
            alert_level = "high"
        elif stream_agreement >= 0.6 and overall_confidence >= 0.6:
            alert_level = "medium"
        else:
            alert_level = "low"
        
        # Generate countdown timer
        time_until_event = (avg_time - datetime.now()).total_seconds() / 60
        if time_until_event > 0:
            countdown_timer = f"T-{time_until_event:.0f} minutes to {dominant_event_type}"
        else:
            countdown_timer = f"{dominant_event_type} window active"
        
        # Collect convergence evidence
        convergence_evidence = {
            'participating_streams': [r.stream_name for r in dominant_group],
            'individual_confidences': {r.stream_name: r.confidence for r in dominant_group},
            'time_spread_minutes': time_spread.total_seconds() / 60,
            'consensus_strength': len(dominant_group) / len(stream_results),
            'supporting_metrics_summary': self._summarize_supporting_metrics(dominant_group)
        }
        
        convergence = ConvergedPrediction(
            unified_event_type=f"{dominant_event_type}_consensus",
            consensus_time_window=(window_start, window_end),
            overall_confidence=overall_confidence,
            stream_agreement=stream_agreement,
            contributing_streams=[r.stream_name for r in dominant_group],
            convergence_evidence=convergence_evidence,
            countdown_timer=countdown_timer,
            alert_level=alert_level
        )
        
        print(f"\n🎯 CONVERGENCE RESULT:")
        print(f"   Event: {convergence.unified_event_type}")
        print(f"   Time Window: {window_start.strftime('%H:%M')} - {window_end.strftime('%H:%M')}")
        print(f"   Confidence: {overall_confidence:.2f}")
        print(f"   Agreement: {stream_agreement:.2f}")
        print(f"   Alert Level: {alert_level.upper()}")
        print(f"   Countdown: {countdown_timer}")
        
        # Store in history
        self.convergence_history.append(convergence)
        if len(self.convergence_history) > 50:
            self.convergence_history = self.convergence_history[-50:]
            
        self.last_convergence_time = datetime.now()
        
        return convergence
    
    def _summarize_supporting_metrics(self, stream_results: List[StreamResult]) -> Dict[str, float]:
        """Summarize supporting metrics across streams"""
        
        summary = {}
        
        for result in stream_results:
            for metric_name, value in result.supporting_metrics.items():
                if isinstance(value, (int, float)):
                    if metric_name not in summary:
                        summary[metric_name] = []
                    summary[metric_name].append(value)
        
        # Calculate averages
        averaged_summary = {}
        for metric_name, values in summary.items():
            averaged_summary[metric_name] = np.mean(values)
            
        return averaged_summary
    
    def run_continuous_analysis(self, session_data: dict, tracker_context: tuple = None, 
                              update_interval_seconds: int = 60) -> None:
        """Run continuous parallel stream analysis (for real-time systems)"""
        
        print(f"🔄 Starting continuous parallel stream analysis (every {update_interval_seconds}s)")
        
        while True:
            try:
                # Run stream analysis
                stream_results = self.analyze_all_streams(session_data, tracker_context)
                
                # Attempt convergence
                convergence = self.converge_predictions(stream_results)
                
                if convergence and convergence.alert_level in ["high", "critical"]:
                    print(f"\n🚨 {convergence.alert_level.upper()} ALERT: {convergence.countdown_timer}")
                    
                    # In a real system, this would trigger alerts/notifications
                    
                time.sleep(update_interval_seconds)
                
            except KeyboardInterrupt:
                print("\n⏹️ Continuous analysis stopped by user")
                break
            except Exception as e:
                print(f"⚠️ Error in continuous analysis: {e}")
                time.sleep(update_interval_seconds)
    
    def get_stream_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of individual streams"""
        
        if not self.convergence_history:
            return {"message": "No convergence history available"}
        
        # Analyze which streams contribute most to successful convergences
        stream_participation = {}
        alert_level_distribution = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        
        for convergence in self.convergence_history:
            # Count stream participation
            for stream_name in convergence.contributing_streams:
                if stream_name not in stream_participation:
                    stream_participation[stream_name] = 0
                stream_participation[stream_name] += 1
            
            # Count alert levels
            alert_level_distribution[convergence.alert_level] += 1
        
        return {
            'total_convergences': len(self.convergence_history),
            'stream_participation_counts': stream_participation,
            'alert_level_distribution': alert_level_distribution,
            'average_confidence': np.mean([c.overall_confidence for c in self.convergence_history]),
            'average_agreement': np.mean([c.stream_agreement for c in self.convergence_history]),
            'last_convergence_time': self.last_convergence_time.isoformat()
        }

def main():
    """Test parallel event streams convergence"""
    
    print("🧪 PARALLEL EVENT STREAMS TEST")
    print("=" * 40)
    
    # Load test data
    try:
        with open('ny_pm_grokEnhanced_2025_07_23.json', 'r') as f:
            session_data = json.load(f)
        with open('HTF_Context_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            htf_tracker = json.load(f)
        with open('FVG_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            fvg_tracker = json.load(f)
        with open('Liquidity_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            liquidity_tracker = json.load(f)
            
        tracker_context = (htf_tracker, fvg_tracker, liquidity_tracker)
        
    except FileNotFoundError as e:
        print(f"❌ Test data not found: {e}")
        return
    
    # Initialize parallel streams
    streams = ParallelEventStreams()
    
    # Run stream analysis
    stream_results = streams.analyze_all_streams(session_data, tracker_context)
    
    print(f"\n📊 Individual Stream Results: {len(stream_results)}")
    for result in stream_results:
        print(f"   {result.stream_name}: {result.event_type} (confidence: {result.confidence:.2f})")
    
    # Attempt convergence
    convergence = streams.converge_predictions(stream_results)
    
    if convergence:
        print(f"\n✅ Streams converged successfully!")
        print(f"   Final prediction: {convergence.countdown_timer}")
        
        # Save convergence results
        convergence_data = {
            'convergence_metadata': {
                'test_type': 'parallel_streams_convergence',
                'date': '2025_07_23',
                'timestamp': datetime.now().isoformat()
            },
            'unified_prediction': {
                'event_type': convergence.unified_event_type,
                'time_window': f"{convergence.consensus_time_window[0].strftime('%H:%M')}-{convergence.consensus_time_window[1].strftime('%H:%M')}",
                'confidence': convergence.overall_confidence,
                'agreement': convergence.stream_agreement,
                'alert_level': convergence.alert_level,
                'countdown': convergence.countdown_timer
            },
            'stream_contributions': convergence.contributing_streams,
            'convergence_evidence': convergence.convergence_evidence
        }
        
        output_file = f"parallel_streams_test_{datetime.now().strftime('%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(convergence_data, f, indent=2, default=str)
        
        print(f"\n💾 Convergence results saved to: {output_file}")
        
    # Display performance summary
    performance = streams.get_stream_performance_summary()
    print(f"\n📈 Stream Performance Summary:")
    for key, value in performance.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    main()