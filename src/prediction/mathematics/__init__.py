#!/usr/bin/env python3
"""
Pure Mathematical Components for Financial Prediction
Contains mathematics-only modules with no API dependencies
"""

from .dynamic_volume import DynamicSyntheticVolumeCalculator, SyntheticVolumeComponents
from .hawkes_process import HawkesCascadePredictor, HawkesParameters, CascadePrediction

__all__ = [
    'DynamicSyntheticVolumeCalculator',
    'SyntheticVolumeComponents', 
    'HawkesCascadePredictor',
    'HawkesParameters',
    'CascadePrediction'
]

# Version tracking for mathematical components
__version__ = "1.0.0"
__math_components__ = {
    "dynamic_volume": "1.0.0",
    "hawkes_process": "1.0.0"
}