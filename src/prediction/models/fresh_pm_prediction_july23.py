#!/usr/bin/env python3
"""
Fresh July 23rd PM Prediction with Enhanced Analysis
Complete fresh prediction cycle: Lunch→PM with all enhancements active
"""

import json
import os
from datetime import datetime

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
try:
    from src.experimental.cross_session_predictor import CrossSessionPredictionSystem
    from enhanced_validation_with_hooks import EnhancedValidationSystem
    from fvg_enhanced_event_engine import FVGEnhancedEventEngine
    from automatic_pattern_analysis import AutomaticPatternAnalyzer
except ImportError as e:
    print(f"⚠️ Import error: {e}")
    print("Running in simulation mode with core functionality")

class FreshPMPredictionSystem:
    """Complete fresh PM prediction system with all enhancements"""
    
    def __init__(self):
        try:
            self.cross_session_system = CrossSessionPredictionSystem(error_threshold=25.0)
            self.enhanced_validator = EnhancedValidationSystem()
            self.fvg_engine = FVGEnhancedEventEngine()  
            self.pattern_analyzer = AutomaticPatternAnalyzer()
        except:
            print("⚠️ Using simulation mode - some features may be limited")
            self.cross_session_system = None
            self.enhanced_validator = None
            self.fvg_engine = FVGEnhancedEventEngine()
            self.pattern_analyzer = None
    
    def run_fresh_prediction_cycle(self) -> dict:
        """Run complete fresh prediction cycle from lunch to PM"""
        
        print("🚀 FRESH JULY 23RD PM PREDICTION CYCLE")
        print("=" * 45)
        print("Enhanced with: FVG proximity, automatic analysis, honest validation")
        print()
        
        # Step 1: Load lunch session data as source
        print("1️⃣ LOADING LUNCH SESSION AS PREDICTION SOURCE:")
        try:
            with open('lunch_grokEnhanced_2025_07_23.json', 'r') as f:
                lunch_data = json.load(f)
            with open('HTF_Context_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
                htf_tracker = json.load(f)
            with open('FVG_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
                fvg_tracker = json.load(f)
            with open('Liquidity_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
                liquidity_tracker = json.load(f)
            
            lunch_close = lunch_data.get('original_session_data', {}).get('price_data', {}).get('close', 23228.50)
            print(f"   ✅ Lunch session loaded: Close = {lunch_close:.2f}")
            print(f"   ✅ Tracker context loaded: T_memory = {fvg_tracker.get('t_memory', 'unknown')}")
            
        except FileNotFoundError as e:
            print(f"   ❌ Failed to load lunch data: {e}")
            return {'error': 'lunch_data_not_found'}
        
        # Step 2: Enhanced FVG analysis of lunch session
        print(f"\n2️⃣ FVG-ENHANCED EVENT ANALYSIS:")
        lunch_session_data = lunch_data.get('original_session_data', {})
        fvg_clusters = self.fvg_engine.extract_fvg_clusters_from_session(lunch_session_data)
        
        if fvg_clusters:
            print(f"   ✅ FVG Analysis: {len(fvg_clusters)} clusters identified")
            for i, cluster in enumerate(fvg_clusters[:3], 1):
                print(f"      {i}. {cluster.fvg_count} FVGs at {cluster.center_price:.1f} ({cluster.proximity_to_current:.1f}pts)")
        
        # Calculate enhanced probability branches
        enhanced_probabilities = self.fvg_engine.calculate_cascade_probabilities(fvg_clusters, horizon_minutes=180)
        print(f"   📊 Generated {len(enhanced_probabilities)} enhanced probability branches")
        
        # Step 3: Run enhanced cross-session prediction
        print(f"\n3️⃣ ENHANCED CROSS-SESSION PREDICTION:")
        
        if self.cross_session_system:
            try:
                # Run lunch→PM prediction with enhanced context
                prediction_results = self.cross_session_system.predictor.predict_london_from_asia(
                    lunch_data,
                    (htf_tracker, fvg_tracker, liquidity_tracker)
                )
                
                print(f"   🎯 Fresh PM Prediction: {prediction_results.predicted_close:.2f}")
                print(f"   📊 Prediction Range: {prediction_results.predicted_range[0]:.2f} - {prediction_results.predicted_range[1]:.2f}")
                print(f"   🎲 Confidence: {prediction_results.confidence_level:.2f}")
                print(f"   📈 Session Character: {prediction_results.session_character_prediction}")
                
                # Check for event intelligence
                metadata = prediction_results.prediction_metadata
                if 'asia_event_analysis' in metadata:
                    events = metadata['asia_event_analysis']['total_events']
                    print(f"   🔗 Event Intelligence: {events} events analyzed")
                
            except Exception as e:
                print(f"   ❌ Cross-session prediction failed: {e}")
                return self._generate_fallback_prediction(lunch_close, fvg_clusters, enhanced_probabilities)
        else:
            print(f"   🔧 Using enhanced fallback prediction")
            prediction_results = self._generate_fallback_prediction(lunch_close, fvg_clusters, enhanced_probabilities)
        
        # Step 4: Enhanced validation against actual PM results
        print(f"\n4️⃣ ENHANCED VALIDATION AGAINST ACTUAL PM:")
        
        try:
            with open('ny_pm_grokEnhanced_2025_07_23.json', 'r') as f:
                actual_pm_data = json.load(f)
            
            actual_price_data = actual_pm_data['original_session_data']['price_data']
            actual_close = actual_price_data['close']
            actual_range = actual_price_data['range']
            
            print(f"   📊 Actual PM Results: Close = {actual_close:.2f}, Range = {actual_range:.1f}")
            
            # Calculate prediction error
            if hasattr(prediction_results, 'predicted_close'):
                predicted_close = prediction_results.predicted_close
            else:
                predicted_close = prediction_results['predicted_close']
                
            prediction_error = abs(predicted_close - actual_close)
            range_error_pct = (prediction_error / actual_range) * 100
            
            print(f"   📈 Prediction Error: {prediction_error:.1f} points ({range_error_pct:.1f}% of range)")
            
            # Step 5: Post-prediction hook check
            print(f"\n5️⃣ POST-PREDICTION HOOK CHECK:")
            
            hook_triggered = False
            pattern_analysis = None
            
            if range_error_pct > 20.0:  # 20% threshold
                print(f"   🚨 ERROR > 20% THRESHOLD - TRIGGERING AUTOMATIC ANALYSIS")
                hook_triggered = True
                
                if self.pattern_analyzer:
                    pattern_analysis = self.pattern_analyzer.analyze_pattern_miss({
                        "predicted": predicted_close,
                        "actual": actual_close,
                        "error": prediction_error,
                        "range": actual_range,
                        "session_data": actual_pm_data['original_session_data'],
                        "focus": "fpfvg_cascade_patterns",
                        "question": "What mathematical relationship predicts this fresh prediction miss?"
                    })
                else:
                    print(f"   🔧 Pattern analyzer unavailable - logging analysis trigger")
                    pattern_analysis = {"triggered": True, "analyzer_unavailable": True}
            else:
                print(f"   ✅ Error within 20% threshold - No automatic analysis needed")
            
            # Compile complete results
            complete_results = {
                'fresh_prediction_metadata': {
                    'prediction_type': 'fresh_lunch_to_pm_enhanced',
                    'date': '2025_07_23',
                    'timestamp': datetime.now().isoformat(),
                    'enhancements_active': [
                        'FVG proximity clustering',
                        'Enhanced event intelligence', 
                        'Automatic pattern analysis',
                        'Post-prediction hooks',
                        'Honest accuracy validation'
                    ]
                },
                'lunch_source_data': {
                    'lunch_close': lunch_close,
                    'fvg_clusters_found': len(fvg_clusters),
                    'enhanced_probabilities': len(enhanced_probabilities)
                },
                'fresh_prediction': {
                    'predicted_close': predicted_close,
                    'predicted_range': getattr(prediction_results, 'predicted_range', [0, 0]),
                    'confidence': getattr(prediction_results, 'confidence_level', 0.5),
                    'session_character': getattr(prediction_results, 'session_character_prediction', 'unknown')
                },
                'actual_pm_results': {
                    'actual_close': actual_close,
                    'actual_range': actual_range,
                    'session_character': actual_price_data.get('session_character', 'unknown')
                },
                'accuracy_assessment': {
                    'prediction_error': prediction_error,
                    'range_error_pct': range_error_pct,
                    'quality': 'excellent' if range_error_pct < 10 else 'good' if range_error_pct < 20 else 'moderate' if range_error_pct < 50 else 'poor',
                    'honest_assessment': True
                },
                'post_prediction_hook': {
                    'triggered': hook_triggered,
                    'trigger_threshold': 20.0,
                    'pattern_analysis_results': pattern_analysis
                },
                'fvg_enhanced_context': {
                    'fvg_clusters': [
                        {
                            'center_price': c.center_price,
                            'fvg_count': c.fvg_count,
                            'cluster_density': c.cluster_density,
                            'proximity': c.proximity_to_current
                        } for c in fvg_clusters
                    ],
                    'enhanced_probabilities': enhanced_probabilities
                }
            }
            
            return complete_results
            
        except FileNotFoundError:
            print(f"   ❌ Actual PM data not found")
            return {'error': 'actual_pm_data_not_found'}
        except Exception as e:
            print(f"   ❌ Validation error: {e}")
            return {'error': str(e)}
    
    def _generate_fallback_prediction(self, lunch_close: float, 
                                    fvg_clusters: list, 
                                    enhanced_probabilities: list) -> dict:
        """Generate enhanced fallback prediction when cross-session system unavailable"""
        
        # Base prediction from lunch close
        base_movement = 50.0  # Expected PM movement
        
        # FVG influence
        fvg_influence = 0.0
        if fvg_clusters:
            nearest_cluster = min(fvg_clusters, key=lambda c: c.proximity_to_current)
            fvg_influence = nearest_cluster.strength * 20.0  # FVG strength influence
            if nearest_cluster.center_price > lunch_close:
                fvg_influence = abs(fvg_influence)  # Upward pull
            else:
                fvg_influence = -abs(fvg_influence)  # Downward pull
        
        # Enhanced probability influence
        prob_influence = 0.0
        if enhanced_probabilities:
            avg_probability = sum(p['probability'] for p in enhanced_probabilities) / len(enhanced_probabilities)
            prob_influence = (avg_probability - 0.5) * 40.0  # Probability deviation influence
        
        # Calculate enhanced prediction
        predicted_close = lunch_close + base_movement + fvg_influence + prob_influence
        predicted_range = [predicted_close - 30, predicted_close + 30]
        
        return {
            'predicted_close': predicted_close,
            'predicted_range': predicted_range,
            'confidence_level': 0.65,
            'session_character_prediction': 'fvg_enhanced_expansion',
            'fallback_mode': True,
            'fvg_influence': fvg_influence,
            'probability_influence': prob_influence
        }

def main():
    """Main execution"""
    print("🎯 FRESH JULY 23RD PM PREDICTION WITH ENHANCED ANALYSIS")
    print("=" * 65)
    
    # Initialize system
    system = FreshPMPredictionSystem()
    
    # Run complete fresh prediction cycle
    results = system.run_fresh_prediction_cycle()
    
    if 'error' in results:
        print(f"\n❌ Fresh prediction failed: {results['error']}")
        return
    
    # Save complete results
    output_file = f"fresh_pm_prediction_july23_{datetime.now().strftime('%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Display summary
    print(f"\n🎯 FRESH PREDICTION SUMMARY")
    print("=" * 30)
    
    fresh_pred = results['fresh_prediction']
    actual = results['actual_pm_results']
    accuracy = results['accuracy_assessment']
    hook = results['post_prediction_hook']
    
    print(f"Fresh Prediction: {fresh_pred['predicted_close']:.2f}")
    print(f"Actual PM Close: {actual['actual_close']:.2f}")
    print(f"Prediction Error: {accuracy['prediction_error']:.1f} points")
    print(f"Range-Relative Error: {accuracy['range_error_pct']:.1f}%")
    print(f"Quality Assessment: {accuracy['quality'].title()}")
    print(f"Post-Hook Triggered: {'🚨 YES' if hook['triggered'] else '✅ NO'}")
    
    fvg_context = results['fvg_enhanced_context']
    print(f"FVG Clusters: {len(fvg_context['fvg_clusters'])}")
    print(f"Enhanced Probabilities: {len(fvg_context['enhanced_probabilities'])}")
    
    print(f"\n💾 Complete results saved to: {output_file}")
    print(f"\n🚀 Fresh prediction cycle completed with full enhancement stack!")

if __name__ == "__main__":
    main()