#!/usr/bin/env python3
"""
Asia Weekend Predictor - Multi-Method Post-Weekend Session Prediction System
Combines weekend gap analysis, NWOG-enhanced systems, and multi-method validation
for comprehensive Asia session cascade timing prediction with 0.0-0.39min target accuracy.
"""

import json
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging

try:
    from src.weekend_gap_analyzer import WeekendGapAnalyzer, WeekendGapAnalysis
    from weekend_adapted_monte_carlo import WeekendAdaptedMonteCarlo, WeekendMonteCarloResults
    from hawkes_weekend_adaptation import NWOGEnhancedHawkesProcess, WeekendHawkesResults
    from src.utils import load_json_data, save_json_data
except ImportError:
    from weekend_gap_analyzer import WeekendGapAnalyzer, WeekendGapAnalysis
    from weekend_adapted_monte_carlo import WeekendAdaptedMonteCarlo, WeekendMonteCarloResults
    from hawkes_weekend_adaptation import NWOGEnhancedHawkesProcess, WeekendHawkesResults
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
    from utils import load_json_data, save_json_data

@dataclass
class AsiaWeekendPrediction:
    """Comprehensive Asia weekend prediction results."""
    consensus_cascade_time: float           # Weighted consensus prediction (minutes)
    consensus_confidence: float            # Overall prediction confidence (0.0-1.0)
    method_predictions: Dict[str, float]   # Individual method predictions
    method_confidences: Dict[str, float]   # Individual method confidences
    method_weights: Dict[str, float]       # Applied method weights
    gap_analysis: WeekendGapAnalysis       # Weekend gap analysis results
    weekend_context: Dict[str, Any]       # Enhanced weekend context
    prediction_range: Tuple[float, float] # (lower, upper) prediction bounds
    deployment_recommendation: str         # Deployment confidence assessment
    validation_metrics: Dict[str, Any]    # Comprehensive validation metrics

class AsiaWeekendPredictor:
    """
    Multi-method prediction system for post-weekend Asia sessions.
    Integrates gap analysis, Monte Carlo, Hawkes process, and validation systems.
    """
    
    def __init__(self):
        self.logger = logging.getLogger('AsiaWeekendPredictor')
        
        # Initialize component systems
        self.gap_analyzer = WeekendGapAnalyzer()
        self.monte_carlo = WeekendAdaptedMonteCarlo()
        self.hawkes_system = NWOGEnhancedHawkesProcess()
        
        # Method weighting configuration (can be adjusted based on performance)
        self.base_method_weights = {
            'weekend_monte_carlo': 0.35,    # Strong proven base system
            'nwog_hawkes_process': 0.40,    # Excellent for specific scenarios
            'gap_analysis_heuristic': 0.15, # Supplementary contextual method
            'ensemble_validation': 0.10     # Cross-validation weighting
        }
        
        # Performance-based weight adjustments
        self.gap_severity_weight_adjustments = {
            'minimal': {'weekend_monte_carlo': 1.2, 'nwog_hawkes_process': 0.8},
            'minor': {'weekend_monte_carlo': 1.1, 'nwog_hawkes_process': 0.9},
            'moderate': {'weekend_monte_carlo': 0.9, 'nwog_hawkes_process': 1.1},
            'major': {'weekend_monte_carlo': 0.7, 'nwog_hawkes_process': 1.3}
        }
    
    def predict_asia_weekend_session(self, friday_close: float, sunday_open: float,
                                   weekend_news: Optional[Dict[str, Any]] = None,
                                   session_volatility: float = 1.0,
                                   confidence_threshold: float = 0.7) -> AsiaWeekendPrediction:
        """
        Generate comprehensive Asia weekend session prediction using multiple methods.
        
        Args:
            friday_close: Previous week's closing price
            sunday_open: New Week Opening Gap (NWOG) price
            weekend_news: Optional weekend news accumulation data
            session_volatility: Expected session volatility factor
            confidence_threshold: Minimum confidence for high-confidence predictions
            
        Returns:
            Comprehensive Asia weekend prediction with multi-method validation
        """
        self.logger.info(f"🌏 Starting Asia Weekend Prediction: Gap {sunday_open - friday_close:.1f}pts")
        
        # Step 1: Analyze weekend gap
        gap_analysis = self.gap_analyzer.analyze_weekend_gap(friday_close, sunday_open, weekend_news)
        
        # Step 2: Run individual prediction methods
        method_results = self._run_all_prediction_methods(
            friday_close, sunday_open, weekend_news, session_volatility, gap_analysis
        )
        
        # Step 3: Calculate dynamic method weights
        dynamic_weights = self._calculate_dynamic_weights(gap_analysis, method_results)
        
        # Step 4: Generate consensus prediction
        consensus_timing, consensus_confidence = self._calculate_weighted_consensus(
            method_results, dynamic_weights
        )
        
        # Step 5: Calculate prediction bounds
        prediction_bounds = self._calculate_prediction_bounds(method_results, consensus_timing)
        
        # Step 6: Generate comprehensive context
        weekend_context = self._generate_comprehensive_context(gap_analysis, method_results)
        
        # Step 7: Assess deployment readiness
        deployment_recommendation = self._assess_deployment_readiness(
            consensus_confidence, gap_analysis, confidence_threshold
        )
        
        # Step 8: Generate validation metrics
        validation_metrics = self._generate_comprehensive_validation_metrics(
            gap_analysis, method_results, consensus_timing, consensus_confidence
        )
        
        self.logger.info(f"🎯 Consensus Prediction: {consensus_timing:.2f}min (confidence: {consensus_confidence:.1%})")
        
        return AsiaWeekendPrediction(
            consensus_cascade_time=consensus_timing,
            consensus_confidence=consensus_confidence,
            method_predictions={method: result['timing'] for method, result in method_results.items()},
            method_confidences={method: result['confidence'] for method, result in method_results.items()},
            method_weights=dynamic_weights,
            gap_analysis=gap_analysis,
            weekend_context=weekend_context,
            prediction_range=prediction_bounds,
            deployment_recommendation=deployment_recommendation,
            validation_metrics=validation_metrics
        )
    
    def _run_all_prediction_methods(self, friday_close: float, sunday_open: float,
                                  weekend_news: Optional[Dict[str, Any]], session_volatility: float,
                                  gap_analysis: WeekendGapAnalysis) -> Dict[str, Dict[str, Any]]:
        """Run all prediction methods and collect results."""
        results = {}
        
        # Method 1: Weekend-Adapted Monte Carlo
        try:
            monte_carlo_result = self.monte_carlo.predict_asia_cascade_timing(
                friday_close, sunday_open, weekend_news, session_volatility
            )
            results['weekend_monte_carlo'] = {
                'timing': monte_carlo_result.predicted_cascade_time,
                'confidence': monte_carlo_result.validation_metrics.get('prediction_confidence', 0.8),
                'method_data': monte_carlo_result
            }
        except Exception as e:
            self.logger.warning(f"Monte Carlo method failed: {e}")
            results['weekend_monte_carlo'] = {
                'timing': 0.5,  # Fallback to base timing
                'confidence': 0.3,
                'method_data': None
            }
        
        # Method 2: NWOG-Enhanced Hawkes Process
        try:
            hawkes_result = self.hawkes_system.predict_weekend_cascade_timing(
                friday_close, sunday_open, weekend_news
            )
            results['nwog_hawkes_process'] = {
                'timing': hawkes_result.predicted_cascade_time,
                'confidence': hawkes_result.cascade_confidence,
                'method_data': hawkes_result
            }
        except Exception as e:
            self.logger.warning(f"Hawkes method failed: {e}")
            results['nwog_hawkes_process'] = {
                'timing': 2.0,  # Conservative fallback
                'confidence': 0.4,
                'method_data': None
            }
        
        # Method 3: Gap Analysis Heuristic
        gap_heuristic_result = self._calculate_gap_heuristic_prediction(gap_analysis)
        results['gap_analysis_heuristic'] = gap_heuristic_result
        
        # Method 4: Ensemble Cross-Validation
        ensemble_result = self._calculate_ensemble_validation(results, gap_analysis)
        results['ensemble_validation'] = ensemble_result
        
        return results
    
    def _calculate_gap_heuristic_prediction(self, gap_analysis: WeekendGapAnalysis) -> Dict[str, Any]:
        """Calculate heuristic prediction based on gap characteristics."""
        # Simple heuristic based on gap severity and news
        base_timings = {
            'major': 0.2,      # Major gaps cascade quickly
            'moderate': 0.8,   # Moderate gaps have medium timing
            'minor': 2.0,      # Minor gaps take longer
            'minimal': 5.0     # Minimal gaps may not cascade early
        }
        
        base_timing = base_timings.get(gap_analysis.gap_severity, 2.0)
        
        # News adjustment
        if gap_analysis.weekend_news_impact:
            news_multiplier = gap_analysis.weekend_news_impact.get('news_multiplier', 1.0)
            adjusted_timing = base_timing * news_multiplier
        else:
            adjusted_timing = base_timing
        
        # Gap fill urgency
        if gap_analysis.liquidity_gap_zones:
            fill_probability = gap_analysis.liquidity_gap_zones[0].get('fill_probability', 0.5)
            adjusted_timing *= (1 - fill_probability * 0.3)  # Up to 30% reduction for urgent fills
        
        confidence = 0.6 if gap_analysis.gap_severity in ['minor', 'minimal'] else 0.8
        
        return {
            'timing': max(0.1, adjusted_timing),
            'confidence': confidence,
            'method_data': {
                'base_timing': base_timing,
                'gap_severity_factor': base_timing,
                'news_adjustment': gap_analysis.weekend_news_impact.get('news_multiplier', 1.0) if gap_analysis.weekend_news_impact else 1.0,
                'fill_urgency_factor': 1 - (gap_analysis.liquidity_gap_zones[0].get('fill_probability', 0) * 0.3) if gap_analysis.liquidity_gap_zones else 1.0
            }
        }
    
    def _calculate_ensemble_validation(self, method_results: Dict[str, Dict[str, Any]],
                                     gap_analysis: WeekendGapAnalysis) -> Dict[str, Any]:
        """Calculate ensemble validation based on method agreement."""
        timings = [result['timing'] for method, result in method_results.items() 
                  if method != 'ensemble_validation']
        
        if not timings:
            return {'timing': 1.0, 'confidence': 0.2, 'method_data': None}
        
        # Calculate ensemble metrics
        mean_timing = np.mean(timings)
        timing_std = np.std(timings)
        timing_range = max(timings) - min(timings)
        
        # Confidence based on method agreement
        if timing_range < 0.5:  # High agreement
            ensemble_confidence = 0.9
        elif timing_range < 1.0:  # Moderate agreement
            ensemble_confidence = 0.7
        else:  # Low agreement
            ensemble_confidence = 0.5
        
        # Adjust for gap characteristics
        if gap_analysis.gap_severity == 'minimal':
            ensemble_confidence *= 1.1  # More confident for minimal gaps
        elif gap_analysis.gap_severity == 'major':
            ensemble_confidence *= 0.8  # Less confident for major gaps
        
        return {
            'timing': mean_timing,
            'confidence': min(0.95, ensemble_confidence),
            'method_data': {
                'method_agreement': 1.0 - (timing_range / max(timings)),
                'timing_std': timing_std,
                'timing_range': timing_range,
                'method_count': len(timings)
            }
        }
    
    def _calculate_dynamic_weights(self, gap_analysis: WeekendGapAnalysis,
                                 method_results: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """Calculate dynamic weights based on gap characteristics and method performance."""
        weights = self.base_method_weights.copy()
        
        # Adjust weights based on gap severity
        severity_adjustments = self.gap_severity_weight_adjustments.get(gap_analysis.gap_severity, {})
        for method, adjustment in severity_adjustments.items():
            if method in weights:
                weights[method] *= adjustment
        
        # Adjust weights based on method confidence
        for method, result in method_results.items():
            if method in weights:
                confidence_factor = result['confidence']
                weights[method] *= (0.5 + confidence_factor * 0.5)  # 0.5-1.0 multiplier
        
        # Normalize weights to sum to 1.0
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {method: weight / total_weight for method, weight in weights.items()}
        
        return weights
    
    def _calculate_weighted_consensus(self, method_results: Dict[str, Dict[str, Any]],
                                    weights: Dict[str, float]) -> Tuple[float, float]:
        """Calculate weighted consensus prediction and confidence."""
        weighted_timing = 0.0
        weighted_confidence = 0.0
        
        for method, weight in weights.items():
            if method in method_results:
                result = method_results[method]
                weighted_timing += result['timing'] * weight
                weighted_confidence += result['confidence'] * weight
        
        return weighted_timing, weighted_confidence
    
    def _calculate_prediction_bounds(self, method_results: Dict[str, Dict[str, Any]],
                                   consensus_timing: float) -> Tuple[float, float]:
        """Calculate prediction bounds based on method variation."""
        timings = [result['timing'] for result in method_results.values()]
        
        if len(timings) < 2:
            # Single method fallback
            uncertainty = consensus_timing * 0.5
            return (max(0.0, consensus_timing - uncertainty), consensus_timing + uncertainty)
        
        timing_std = np.std(timings)
        confidence_multiplier = 2.0  # 95% confidence interval
        
        lower_bound = max(0.0, consensus_timing - timing_std * confidence_multiplier)
        upper_bound = consensus_timing + timing_std * confidence_multiplier
        
        return (lower_bound, upper_bound)
    
    def _generate_comprehensive_context(self, gap_analysis: WeekendGapAnalysis,
                                      method_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive weekend context for prediction."""
        return {
            'gap_analysis_context': self.gap_analyzer.generate_asia_session_context(gap_analysis),
            'method_specific_insights': {
                method: result.get('method_data') for method, result in method_results.items()
            },
            'prediction_scenario': {
                'gap_severity': gap_analysis.gap_severity,
                'gap_direction': gap_analysis.gap_direction,
                'news_impact_level': gap_analysis.weekend_news_impact.get('news_severity_score', 0.0) if gap_analysis.weekend_news_impact else 0.0,
                'liquidity_gap_zones_count': len(gap_analysis.liquidity_gap_zones),
                'expected_session_bias': gap_analysis.weekend_news_impact.get('dominant_sentiment', 'neutral') if gap_analysis.weekend_news_impact else 'neutral'
            },
            'historical_context': {
                'similar_gap_scenarios': f"{gap_analysis.gap_severity}_gap_{gap_analysis.gap_direction}",
                'expected_accuracy_range': f"0.39-{2.0 if gap_analysis.gap_severity == 'major' else 1.0}min"
            }
        }
    
    def _assess_deployment_readiness(self, consensus_confidence: float,
                                   gap_analysis: WeekendGapAnalysis,
                                   confidence_threshold: float) -> str:
        """Assess deployment readiness based on prediction confidence and gap characteristics."""
        if consensus_confidence >= confidence_threshold and gap_analysis.gap_severity in ['minimal', 'minor']:
            return "high_confidence_deployment"
        elif consensus_confidence >= 0.6 and gap_analysis.gap_severity in ['minor', 'moderate']:
            return "moderate_confidence_deployment"
        elif gap_analysis.gap_severity == 'major':
            return "enhanced_monitoring_required_major_gap"
        else:
            return "low_confidence_monitoring_mode"
    
    def _generate_comprehensive_validation_metrics(self, gap_analysis: WeekendGapAnalysis,
                                                 method_results: Dict[str, Dict[str, Any]],
                                                 consensus_timing: float,
                                                 consensus_confidence: float) -> Dict[str, Any]:
        """Generate comprehensive validation metrics."""
        return {
            'prediction_quality': {
                'consensus_confidence': consensus_confidence,
                'method_agreement': self._calculate_method_agreement(method_results),
                'prediction_stability': self._calculate_prediction_stability(method_results),
                'gap_analysis_quality': 0.95  # High confidence in gap analysis
            },
            'expected_performance': {
                'target_accuracy_minutes': 0.39 if gap_analysis.gap_severity == 'minimal' else 1.0,
                'confidence_interval_coverage': 0.95,
                'method_reliability_scores': {
                    method: result['confidence'] for method, result in method_results.items()
                }
            },
            'deployment_metrics': {
                'deployment_readiness_score': consensus_confidence,
                'risk_assessment': gap_analysis.gap_severity,
                'monitoring_requirements': 'standard' if gap_analysis.gap_severity in ['minimal', 'minor'] else 'enhanced'
            },
            'comparative_analysis': {
                'vs_base_monte_carlo': f"{((consensus_timing / 0.5) - 1) * 100:.1f}% timing difference",
                'vs_base_hawkes': f"Gap-enhanced with {len(getattr(method_results.get('nwog_hawkes_process', {}).get('method_data'), 'triggering_gap_events', []))} synthetic events",
                'improvement_factors': {
                    'nwog_integration': True,
                    'weekend_news_consideration': bool(gap_analysis.weekend_news_impact),
                    'multi_method_validation': len(method_results) >= 3
                }
            }
        }
    
    def _calculate_method_agreement(self, method_results: Dict[str, Dict[str, Any]]) -> float:
        """Calculate agreement between prediction methods."""
        timings = [result['timing'] for result in method_results.values()]
        if len(timings) < 2:
            return 1.0
        
        timing_range = max(timings) - min(timings)
        mean_timing = np.mean(timings)
        agreement = 1.0 - (timing_range / max(mean_timing, 1.0))
        return max(0.0, agreement)
    
    def _calculate_prediction_stability(self, method_results: Dict[str, Dict[str, Any]]) -> float:
        """Calculate stability of predictions across methods."""
        confidences = [result['confidence'] for result in method_results.values()]
        if not confidences:
            return 0.5
        
        avg_confidence = np.mean(confidences)
        confidence_std = np.std(confidences)
        stability = avg_confidence * (1 - confidence_std)
        return max(0.0, min(1.0, stability))

def test_asia_weekend_predictor():
    """Test Asia weekend predictor with comprehensive scenarios."""
    predictor = AsiaWeekendPredictor()
    
    # Test scenarios representing different weekend conditions
    scenarios = [
        {
            'name': 'Major Weekend Gap + High Impact News',
            'friday_close': 23250.0,
            'sunday_open': 23330.0,  # 80-point gap up
            'weekend_news': {
                'high_impact_events': [
                    {'event': 'Fed Chair Emergency Weekend Statement - Dovish', 'impact': 'high'},
                    {'event': 'China Major Economic Stimulus Announced', 'impact': 'high'}
                ],
                'medium_impact_events': [
                    {'event': 'EU GDP Beats Expectations', 'impact': 'medium'}
                ],
                'geopolitical_events': [],
                'sentiment_analysis': {'bullish': 0.85, 'bearish': 0.10, 'neutral': 0.05}
            },
            'session_volatility': 1.4
        },
        {
            'name': 'Moderate Gap - Standard Weekend',
            'friday_close': 23300.0,
            'sunday_open': 23275.0,  # 25-point gap down
            'weekend_news': {
                'high_impact_events': [],
                'medium_impact_events': [
                    {'event': 'Oil Prices Rise on Supply Concerns', 'impact': 'medium'}
                ],
                'geopolitical_events': [],
                'sentiment_analysis': {'bullish': 0.3, 'bearish': 0.5, 'neutral': 0.2}
            },
            'session_volatility': 1.0
        },
        {
            'name': 'Minimal Gap - Quiet Weekend',
            'friday_close': 23275.0,
            'sunday_open': 23280.0,  # 5-point gap up
            'weekend_news': None,
            'session_volatility': 0.8
        }
    ]
    
    print("🌏 ASIA WEEKEND PREDICTOR COMPREHENSIVE TEST")
    print("=" * 70)
    
    results_summary = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📊 Scenario {i}: {scenario['name']}")
        print("-" * 50)
        
        prediction = predictor.predict_asia_weekend_session(
            friday_close=scenario['friday_close'],
            sunday_open=scenario['sunday_open'],
            weekend_news=scenario['weekend_news'],
            session_volatility=scenario['session_volatility']
        )
        
        print(f"🎯 Consensus Prediction: {prediction.consensus_cascade_time:.2f} minutes")
        print(f"📊 Consensus Confidence: {prediction.consensus_confidence:.1%}")
        print(f"📈 Prediction Range: {prediction.prediction_range[0]:.2f} - {prediction.prediction_range[1]:.2f} minutes")
        print(f"🚀 Deployment Readiness: {prediction.deployment_recommendation}")
        
        print(f"\n🔬 Method Breakdown:")
        for method, timing in prediction.method_predictions.items():
            confidence = prediction.method_confidences[method]
            weight = prediction.method_weights[method]
            print(f"   {method}: {timing:.2f}min (confidence: {confidence:.1%}, weight: {weight:.1%})")
        
        print(f"\n🌐 Gap Analysis:")
        print(f"   Gap: {prediction.gap_analysis.gap_magnitude:.1f}pts ({prediction.gap_analysis.gap_percentage:.2f}%) {prediction.gap_analysis.gap_direction}")
        print(f"   Severity: {prediction.gap_analysis.gap_severity}")
        print(f"   Liquidity Zones: {len(prediction.gap_analysis.liquidity_gap_zones)}")
        
        results_summary.append({
            'scenario': scenario['name'],
            'prediction': prediction.consensus_cascade_time,
            'confidence': prediction.consensus_confidence,
            'deployment': prediction.deployment_recommendation
        })
    
    print(f"\n📋 RESULTS SUMMARY")
    print("=" * 50)
    for result in results_summary:
        print(f"• {result['scenario']}: {result['prediction']:.2f}min ({result['confidence']:.1%}) - {result['deployment']}")
    
    return scenarios, results_summary

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_asia_weekend_predictor()