#!/usr/bin/env python3
"""
Session-Relative PM Predictions (Option 3)
Generates PM cascade predictions adjusted for 13:30 session start from both NYAM and Lunch data.
"""

from src.utils import load_json_data, save_json_data
from datetime import datetime, timedelta
import json

def calculate_session_relative_prediction(base_prediction_minutes, session_start_time="13:30:00"):
    """
    Convert prediction minutes to actual clock time for user's session start.
    
    Args:
        base_prediction_minutes: Minutes into PM session (from <PERSON><PERSON>)
        session_start_time: User's actual PM session start time
    
    Returns:
        Dict with session-relative prediction details
    """
    
    # Parse session start time
    start_time = datetime.strptime(session_start_time, "%H:%M:%S")
    
    # Add prediction minutes to session start
    cascade_time = start_time + timedelta(minutes=base_prediction_minutes)
    
    return {
        "user_pm_session_start": session_start_time,
        "predicted_cascade_minutes_into_session": base_prediction_minutes,
        "predicted_cascade_clock_time": cascade_time.strftime("%H:%M:%S"),
        "session_relative_method": "option_3_time_translation"
    }

def generate_session_relative_predictions():
    """Generate PM predictions from both data sources with 13:30 session start."""
    
    print("🎯 SESSION-RELATIVE PM PREDICTIONS (Option 3)")
    print("=" * 60)
    print("User PM Session Start: 13:30 ET")
    print("Method: Time translation (ignores 13:00-13:30 gap)")
    print("")
    
    # Load existing predictions
    lunch_prediction = load_json_data('PM_Prediction_from_Lunch_Temporal_Isolation.json')
    nyam_prediction = load_json_data('PM_Prediction_from_NYAM_Experiment.json')
    
    # Extract base predictions
    lunch_cascade_minutes = lunch_prediction['temporal_isolation_prediction']['predicted_pm_cascade_minutes']
    nyam_cascade_minutes = nyam_prediction['nyam_to_pm_prediction']['predicted_pm_cascade_minutes']
    
    print("📊 BASE PREDICTIONS:")
    print(f"   From Lunch: {lunch_cascade_minutes} minutes into PM session")
    print(f"   From NYAM:  {nyam_cascade_minutes} minutes into PM session")
    print("")
    
    # Calculate session-relative predictions
    lunch_session_relative = calculate_session_relative_prediction(lunch_cascade_minutes)
    nyam_session_relative = calculate_session_relative_prediction(nyam_cascade_minutes)
    
    print("🕐 SESSION-RELATIVE PREDICTIONS (13:30 Start):")
    print(f"   From Lunch: {lunch_session_relative['predicted_cascade_clock_time']} ET")
    print(f"   From NYAM:  {nyam_session_relative['predicted_cascade_clock_time']} ET")
    print("")
    
    # Create comprehensive comparison results
    session_relative_results = {
        "session_relative_pm_predictions": {
            "user_session_configuration": {
                "pm_session_start_time": "13:30:00 ET",
                "method_used": "option_3_session_relative",
                "gap_handling": "ignored_13:00_to_13:30_gap",
                "prediction_basis": "time_translation_only"
            },
            "lunch_to_pm_prediction": {
                "data_source": "Lunch_Lvl-1_2025_07_25.json",
                "base_prediction_minutes": lunch_cascade_minutes,
                "session_relative_clock_time": lunch_session_relative['predicted_cascade_clock_time'],
                "prediction_confidence": lunch_prediction['temporal_isolation_prediction']['prediction_confidence'],
                "hawkes_parameters": lunch_prediction['temporal_isolation_prediction']['hawkes_parameters'],
                "source_session_character": "strong_upward_bias_with_multiple_liquidity_sweeps",
                "source_session_range": 60.0
            },
            "nyam_to_pm_prediction": {
                "data_source": "NYAM_Lvl-1_2025_07_25.json", 
                "base_prediction_minutes": nyam_cascade_minutes,
                "session_relative_clock_time": nyam_session_relative['predicted_cascade_clock_time'],
                "prediction_confidence": nyam_prediction['nyam_to_pm_prediction']['prediction_confidence'],
                "hawkes_parameters": nyam_prediction['nyam_to_pm_prediction']['hawkes_parameters'],
                "source_session_character": "strong_bidirectional_expansion_with_extended_consolidation",
                "source_session_range": 98.5
            }
        },
        "prediction_comparison": {
            "time_difference_minutes": abs(lunch_cascade_minutes - nyam_cascade_minutes),
            "lunch_predicts_earlier": lunch_cascade_minutes < nyam_cascade_minutes,
            "clock_time_difference": f"{abs(lunch_cascade_minutes - nyam_cascade_minutes)} minutes",
            "consistency_assessment": "significant_difference" if abs(lunch_cascade_minutes - nyam_cascade_minutes) > 3 else "reasonable_agreement"
        },
        "trading_implications": {
            "lunch_based_timing": {
                "cascade_time": lunch_session_relative['predicted_cascade_clock_time'],
                "entry_window": "13:30-13:32 ET",
                "based_on": "trending_lunch_momentum"
            },
            "nyam_based_timing": {
                "cascade_time": nyam_session_relative['predicted_cascade_clock_time'],
                "entry_window": "13:30-13:38 ET", 
                "based_on": "complex_AM_consolidation_buildup"
            },
            "recommendation": "monitor_both_windows" if abs(lunch_cascade_minutes - nyam_cascade_minutes) > 3 else "consistent_timing_expected"
        },
        "option_3_limitations": {
            "ignores_13:00_to_13:30_market_activity": True,
            "assumes_intensity_carries_forward": True,
            "no_hawkes_parameter_updates": True,
            "potential_accuracy_impact": "medium_to_high"
        },
        "generation_timestamp": datetime.now().isoformat()
    }
    
    # Save results
    save_json_data(session_relative_results, 'Session_Relative_PM_Predictions_1330_Start.json')
    
    print("💡 TRADING IMPLICATIONS:")
    if abs(lunch_cascade_minutes - nyam_cascade_minutes) > 3:
        print("   ⚠️  SIGNIFICANT DIFFERENCE detected")
        print(f"   📈 Lunch suggests: {lunch_session_relative['predicted_cascade_clock_time']} (trending momentum)")
        print(f"   📊 NYAM suggests: {nyam_session_relative['predicted_cascade_clock_time']} (complex buildup)")
        print("   🎯 Recommendation: Monitor both windows")
    else:
        print("   ✅ REASONABLE AGREEMENT between sources")
        print("   🎯 Consistent timing expected")
    
    print("")
    print("⚠️  OPTION 3 LIMITATIONS:")
    print("   • Ignores 13:00-13:30 market activity")
    print("   • Assumes Hawkes intensity carries forward unchanged")
    print("   • No parameter updates for 30-minute gap")
    print("")
    print("✅ Results saved: Session_Relative_PM_Predictions_1330_Start.json")
    
    return session_relative_results

if __name__ == "__main__":
    generate_session_relative_predictions()