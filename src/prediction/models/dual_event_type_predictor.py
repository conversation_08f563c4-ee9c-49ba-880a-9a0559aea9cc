#!/usr/bin/env python3
"""
Dual Event Type Predictor - First Touch vs Major Expansion
=========================================================

Based on Grok 4 Analysis: Market events fall into distinct categories:
1. "First Touch" - Immediate session open spikes (0-2 minutes)
2. "Major Expansion" - Sustained momentum phases (10-30 minutes)

The enhanced formula failed because it predicted major expansions but 
actual validations measured first touch events.

Key Implementation:
- Separate prediction models for each event type
- Automatic event type classification based on session characteristics
- Distinct parameter weightings for each model
"""

import json
import math
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from enum import Enum

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
class EventType(Enum):
    FIRST_TOUCH = "first_touch"
    MAJOR_EXPANSION = "major_expansion"
    
class SessionPhase(Enum):
    OPEN_SPIKE = "open_spike"
    CONSOLIDATION = "consolidation" 
    BREAKOUT = "breakout"
    MOMENTUM = "momentum"
    RETRACEMENT = "retracement"

class DualEventTypePredictor:
    """
    Separate prediction models for first touch vs major expansion events.
    """
    
    def __init__(self):
        # First Touch Model (session opens)
        self.first_touch_config = {
            "base_delay_minutes": 0.5,
            "max_delay_minutes": 3.0,
            "volatility_sensitivity": 0.3,
            "momentum_factor": 0.1,
            "distance_impact": 0.1
        }
        
        # Major Expansion Model (sustained moves)
        self.major_expansion_config = {
            "base_delay_minutes": 15.0,
            "max_delay_minutes": 45.0,
            "volatility_sensitivity": 0.8,
            "momentum_factor": 0.5,
            "distance_impact": 0.3
        }
    
    def classify_event_type(self, session_character: str, volatility_index: float, 
                          gamma_enhanced: float, session_distance: float) -> EventType:
        """
        Automatically classify whether to predict first touch or major expansion.
        
        Based on Grok 4's analysis of when each event type occurs.
        """
        
        # Session character indicators
        consolidation_indicators = ["consolidation", "accumulative", "range_bound"]
        expansion_indicators = ["expansion", "explosive", "breakout", "momentum"]
        
        # Check session character
        is_consolidation_heavy = any(indicator in session_character.lower() 
                                   for indicator in consolidation_indicators)
        is_expansion_heavy = any(indicator in session_character.lower() 
                               for indicator in expansion_indicators)
        
        # Classification logic based on Grok 4 insights
        if session_distance <= 1.0:
            # Contiguous sessions - usually first touch events
            if volatility_index < 0.1 and gamma_enhanced < 2.2:
                return EventType.FIRST_TOUCH
            elif is_consolidation_heavy:
                return EventType.FIRST_TOUCH
            else:
                # High momentum contiguous sessions can have major expansions
                return EventType.MAJOR_EXPANSION
                
        elif session_distance >= 2.5:
            # Inter-regional sessions
            if is_consolidation_heavy:
                # Consolidation sessions usually just spike at open
                return EventType.FIRST_TOUCH
            else:
                # Inter-regional expansions tend to be sustained
                return EventType.MAJOR_EXPANSION
                
        else:
            # Semi-contiguous sessions (1.0 < d < 2.5)
            if volatility_index > 0.15 or gamma_enhanced > 2.5:
                return EventType.MAJOR_EXPANSION
            else:
                return EventType.FIRST_TOUCH
    
    def predict_first_touch_timing(self, gamma_enhanced: float, volatility_index: float,
                                 t_memory: float, fvg_proximity: float, 
                                 session_distance: float) -> Dict:
        """
        Predict timing for first touch events (session open spikes).
        
        Based on Grok 4 finding that these occur at 0-1 minutes.
        """
        config = self.first_touch_config
        
        # Base timing (near session open)
        base_time = config["base_delay_minutes"]
        
        # Minimal adjustments for first touch (mostly immediate)
        volatility_adjustment = config["volatility_sensitivity"] * (volatility_index / t_memory)
        momentum_adjustment = config["momentum_factor"] * max(0, gamma_enhanced - 2.0)
        distance_adjustment = config["distance_impact"] * math.log(session_distance + 1)
        
        # Calculate prediction
        predicted_time = base_time + volatility_adjustment + momentum_adjustment + distance_adjustment
        predicted_time = min(predicted_time, config["max_delay_minutes"])
        
        # Uncertainty calculation (wider for distant sessions)
        uncertainty = 0.5 + 0.2 * session_distance + 0.1 * volatility_index
        
        return {
            "predicted_minutes": predicted_time,
            "event_type": EventType.FIRST_TOUCH.value,
            "confidence_interval": (max(0, predicted_time - uncertainty), 
                                  predicted_time + uncertainty),
            "components": {
                "base": base_time,
                "volatility_adj": volatility_adjustment,
                "momentum_adj": momentum_adjustment,
                "distance_adj": distance_adjustment
            }
        }
    
    def predict_major_expansion_timing(self, gamma_enhanced: float, volatility_index: float,
                                     t_memory: float, fvg_proximity: float,
                                     session_distance: float) -> Dict:
        """
        Predict timing for major expansion events (sustained momentum).
        
        These occur after initial session activity stabilizes.
        """
        config = self.major_expansion_config
        
        # Base timing (mid-session)
        base_time = config["base_delay_minutes"]
        
        # Stronger parameter influence for major expansions
        volatility_adjustment = config["volatility_sensitivity"] * (volatility_index / t_memory) * 10
        momentum_adjustment = config["momentum_factor"] * (gamma_enhanced - 1.5) * 5
        fvg_adjustment = -3.0 * fvg_proximity  # Closer FVGs = faster expansions
        distance_adjustment = config["distance_impact"] * session_distance * 2
        
        # Calculate prediction
        predicted_time = (base_time + volatility_adjustment + momentum_adjustment + 
                         fvg_adjustment + distance_adjustment)
        predicted_time = max(5.0, min(predicted_time, config["max_delay_minutes"]))
        
        # Higher uncertainty for major expansions
        uncertainty = 3.0 + 0.5 * session_distance + 0.3 * volatility_index
        
        return {
            "predicted_minutes": predicted_time,
            "event_type": EventType.MAJOR_EXPANSION.value,
            "confidence_interval": (max(0, predicted_time - uncertainty),
                                  predicted_time + uncertainty),
            "components": {
                "base": base_time,
                "volatility_adj": volatility_adjustment,
                "momentum_adj": momentum_adjustment,
                "fvg_adj": fvg_adjustment,
                "distance_adj": distance_adjustment
            }
        }
    
    def predict_with_event_classification(self, gamma_enhanced: float, volatility_index: float,
                                        t_memory: float, fvg_proximity: float,
                                        session_distance: float, session_character: str) -> Dict:
        """
        Make prediction with automatic event type classification.
        """
        
        # Classify event type
        event_type = self.classify_event_type(session_character, volatility_index, 
                                            gamma_enhanced, session_distance)
        
        # Make appropriate prediction
        if event_type == EventType.FIRST_TOUCH:
            prediction = self.predict_first_touch_timing(
                gamma_enhanced, volatility_index, t_memory, fvg_proximity, session_distance
            )
        else:
            prediction = self.predict_major_expansion_timing(
                gamma_enhanced, volatility_index, t_memory, fvg_proximity, session_distance
            )
        
        # Add classification info
        prediction["classified_event_type"] = event_type.value
        prediction["session_character"] = session_character
        
        return prediction
    
    def validate_dual_model(self, validation_data: List[Dict]) -> Dict:
        """
        Validate dual model approach against actual events.
        
        Assumes validation data represents first touch events (session opens).
        """
        results = []
        total_error_first_touch = 0
        total_error_major_expansion = 0
        
        for case in validation_data:
            params = case['source_parameters']
            actual_minutes = case['actual_expansion_minutes']
            
            # Make predictions with both models
            first_touch_pred = self.predict_first_touch_timing(
                params['gamma_enhanced'], params['volatility_index'],
                params['t_memory'], params['fvg_proximity'], case['session_distance']
            )
            
            major_expansion_pred = self.predict_major_expansion_timing(
                params['gamma_enhanced'], params['volatility_index'],
                params['t_memory'], params['fvg_proximity'], case['session_distance']
            )
            
            # Automatic classification
            auto_pred = self.predict_with_event_classification(
                params['gamma_enhanced'], params['volatility_index'],
                params['t_memory'], params['fvg_proximity'],
                case['session_distance'], params['session_character']
            )
            
            # Calculate errors
            ft_error = abs(first_touch_pred['predicted_minutes'] - actual_minutes)
            me_error = abs(major_expansion_pred['predicted_minutes'] - actual_minutes)
            auto_error = abs(auto_pred['predicted_minutes'] - actual_minutes)
            
            total_error_first_touch += ft_error
            total_error_major_expansion += me_error
            
            results.append({
                "pair_name": case['pair_name'],
                "actual_minutes": actual_minutes,
                "first_touch_prediction": first_touch_pred['predicted_minutes'],
                "major_expansion_prediction": major_expansion_pred['predicted_minutes'],
                "auto_classification": auto_pred['classified_event_type'],
                "auto_prediction": auto_pred['predicted_minutes'],
                "errors": {
                    "first_touch": ft_error,
                    "major_expansion": me_error,
                    "auto_classified": auto_error
                }
            })
        
        avg_ft_error = total_error_first_touch / len(validation_data)
        avg_me_error = total_error_major_expansion / len(validation_data)
        
        return {
            "validation_results": results,
            "summary": {
                "first_touch_avg_error": avg_ft_error,
                "major_expansion_avg_error": avg_me_error,
                "total_cases": len(results),
                "recommendation": ("First Touch Model" if avg_ft_error < avg_me_error 
                                 else "Major Expansion Model")
            }
        }

def main():
    """Test dual event type predictor."""
    
    # Load validation data
    try:
        with open('/Users/<USER>/grok-claude-automation/enhanced_formula_raw_validation_results.json', 'r') as f:
            validation_data = json.load(f)
    except FileNotFoundError:
        print("Validation data not found")
        return
    
    # Initialize dual predictor
    predictor = DualEventTypePredictor()
    
    # Run validation
    results = predictor.validate_dual_model(validation_data['validation_results'])
    
    print(f"\n🎯 Dual Event Type Predictor Results")
    print(f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print(f"First Touch Model Error: {results['summary']['first_touch_avg_error']:.2f} minutes")
    print(f"Major Expansion Model Error: {results['summary']['major_expansion_avg_error']:.2f} minutes")
    print(f"Recommended Model: {results['summary']['recommendation']}")
    
    # Save results
    output_file = "/Users/<USER>/grok-claude-automation/dual_event_type_results.json"
    with open(output_file, 'w') as f:
        json.dump({
            "dual_model_metadata": {
                "implementation_date": datetime.now().isoformat(),
                "based_on": "grok_4_event_type_classification",
                "models": ["first_touch", "major_expansion"],
                "validation_assumption": "actual_events_are_first_touch_session_opens"
            },
            "results": results
        }, f, indent=2)
    
    print(f"📁 Results saved to: {output_file}")

if __name__ == "__main__":
    main()