#!/usr/bin/env python3
"""
Cross-Session Event Timing Predictor
TRANSFORMATION: Predicts WHEN events will occur across sessions, not WHERE price will go.
Uses Asia session patterns to predict London event timing windows.
"""

import json
import os
import sys
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging

try:
sys.path.append('.')
from src.utils import load_json_data, save_json_data
    from market_state_hmm import MarketStateHMM, MarketState
    from parallel_event_streams import ParallelEventStreams
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

# Configure logging for event timing analysis
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EventType(Enum):
    """Market event types for timing prediction"""
    EXPANSION_START = "expansion_start"
    CASCADE_FORMATION = "cascade_formation"
    CONSOLIDATION_BEGIN = "consolidation_begin"
    MOMENTUM_ACCELERATION = "momentum_acceleration"
    REVERSAL_SETUP = "reversal_setup"
    LIQUIDITY_SWEEP = "liquidity_sweep"
    FVG_INTERACTION = "fvg_interaction"

@dataclass
class SessionEventProfile:
    """Event profile extracted from a session"""
    session_type: str
    primary_events: List[EventType]
    event_timing_pattern: Dict[str, float]  # event_type -> minutes_from_session_start
    energy_accumulation_rate: float
    momentum_persistence: float
    character_strength: float
    timing_signatures: Dict[str, Any]

@dataclass
class EventTimingPrediction:
    """Prediction of when an event will occur in the next session"""
    event_type: EventType
    predicted_time_window: Tuple[datetime, datetime]
    confidence: float
    trigger_conditions: Dict[str, float]
    carryover_factors: Dict[str, float]
    session_context: str
    timing_reasoning: str

@dataclass
class CrossSessionEventTransfer:
    """Event characteristics that transfer between sessions"""
    energy_momentum: float
    timing_pattern_strength: float
    event_sequence_tendency: List[EventType]
    cascade_priming_level: float
    consolidation_pressure: float
    t_memory_carryover: float
    half_hour_magnetism: float  # Attraction to :00/:30 marks

class CrossSessionEventState:
    """Encapsulates event timing transfer logic between sessions"""
    
    def __init__(self, from_session_data: Dict):
        """Initialize event state from session data"""
        
        # Extract session metadata
        session_metadata = from_session_data.get("session_metadata", {})
        if "original_session_data" in from_session_data:
            session_metadata = from_session_data["original_session_data"]["session_metadata"]
            
        self.session_type = session_metadata.get("session_type", "unknown")
        self.session_duration = session_metadata.get("duration_minutes", 300)
        
        # Extract price action for event pattern analysis
        price_data = from_session_data.get("price_data", {})
        if "original_session_data" in from_session_data:
            price_data = from_session_data["original_session_data"]["price_data"]
            
        self.session_character = price_data.get("session_character", "unknown")
        self.session_range = price_data.get("range", 100)
        
        # Extract phase transitions for event timing analysis
        phase_transitions = []
        if "original_session_data" in from_session_data:
            phase_transitions = from_session_data["original_session_data"].get("phase_transitions", [])
        
        self.phase_transitions = phase_transitions
        
        # Extract energy data from Grok calculations
        grok_data = from_session_data.get("grok_enhanced_calculations", {})
        unit_b = grok_data.get("unit_b_energy_structure", {})
        energy_accumulation = unit_b.get("energy_accumulation", {})
        
        self.energy_rate = energy_accumulation.get("energy_rate", 0.5)
        self.total_accumulated = energy_accumulation.get("total_accumulated", 150.0)
        
        # Calculate event-specific metrics
        self.event_density = len(phase_transitions) / (self.session_duration / 60) if self.session_duration > 0 else 0
        self.expansion_tendency = self._calculate_expansion_tendency()
        self.cascade_formation_strength = self._calculate_cascade_strength()
        
        # Timing pattern extraction
        self.timing_signatures = self._extract_timing_signatures()
        
    def _calculate_expansion_tendency(self) -> float:
        """Calculate tendency for expansion events based on session character"""
        
        expansion_weights = {
            "expansion_consolidation_final_expansion": 0.9,
            "trending_bullish": 0.8,
            "trending_bearish": 0.8,
            "expansion_then_consolidation": 0.7,
            "consolidation": 0.2,
            "ranging_with_liquidity_sweep": 0.5,
            "unknown": 0.4
        }
        
        return expansion_weights.get(self.session_character, 0.4)
    
    def _calculate_cascade_strength(self) -> float:
        """Calculate likelihood of cascade events based on energy and phase transitions"""
        
        # High energy + multiple phase transitions = high cascade potential
        energy_factor = min(1.0, self.energy_rate / 2.0)  # Normalize energy
        
        # Count expansion phases
        expansion_phases = sum(1 for phase in self.phase_transitions 
                             if phase.get("phase_type") == "expansion")
        phase_factor = min(1.0, expansion_phases / 3.0)  # Normalize phase count
        
        return (energy_factor + phase_factor) / 2.0
    
    def _extract_timing_signatures(self) -> Dict[str, Any]:
        """Extract timing patterns from phase transitions"""
        
        signatures = {
            'event_clustering_times': [],
            'expansion_start_times': [],
            'consolidation_durations': [],
            'half_hour_events': 0
        }
        
        for phase in self.phase_transitions:
            start_time = phase.get("start_time", "")
            phase_type = phase.get("phase_type", "")
            
            if start_time:
                # Extract minutes from time string (e.g., "13:45:00" -> 45)
                try:
                    time_parts = start_time.split(":")
                    minutes = int(time_parts[1])
                    
                    signatures['event_clustering_times'].append(minutes)
                    
                    if phase_type == "expansion":
                        signatures['expansion_start_times'].append(minutes)
                        
                    # Check for half-hour proximity (within 5 minutes of :00 or :30)
                    if (minutes <= 5 or minutes >= 25 and minutes <= 35 or minutes >= 55):
                        signatures['half_hour_events'] += 1
                        
                except (ValueError, IndexError):
                    continue
        
        # Calculate consolidation durations
        consolidation_phases = [p for p in self.phase_transitions if p.get("phase_type") == "consolidation"]
        for phase in consolidation_phases:
            start_time = phase.get("start_time", "")
            end_time = phase.get("end_time", "")
            
            if start_time and end_time:
                try:
                    # Simple duration calculation (assumes same hour)
                    start_min = int(start_time.split(":")[1])
                    end_min = int(end_time.split(":")[1])
                    duration = end_min - start_min if end_min > start_min else 60 - start_min + end_min
                    signatures['consolidation_durations'].append(duration)
                except (ValueError, IndexError):
                    continue
        
        return signatures
    
    def transfer_event_characteristics(self, time_gap_hours: float = 0.5, 
                                     target_session_type: str = "London") -> CrossSessionEventTransfer:
        """
        Transfer event timing characteristics to next session
        
        Args:
            time_gap_hours: Hours between sessions
            target_session_type: Type of target session
            
        Returns:
            Event transfer object with decayed characteristics
        """
        
        import math
        
        # Energy momentum decay (preserves timing potential)
        energy_decay_rate = math.exp(-0.08 * time_gap_hours)  # 8% hourly decay for events
        carried_energy_momentum = self.energy_rate * energy_decay_rate
        
        # Timing pattern strength (how well patterns persist)
        pattern_persistence_rate = 0.9 ** time_gap_hours  # 10% hourly decay
        timing_pattern_strength = self.expansion_tendency * pattern_persistence_rate
        
        # Event sequence tendency (what types of events are likely)
        event_sequence = self._predict_event_sequence()
        
        # Cascade priming (readiness for cascade events)
        cascade_priming = self.cascade_formation_strength * energy_decay_rate
        
        # Consolidation pressure (tendency toward consolidation)
        consolidation_pressure = (1.0 - self.expansion_tendency) * pattern_persistence_rate
        
        # T_memory carryover (affects timing precision)
        t_memory_carryover = 5.0 * (0.95 ** time_gap_hours)  # Gradual decay
        
        # Half-hour magnetism (attraction to :00/:30 timing)
        half_hour_events_ratio = self.timing_signatures['half_hour_events'] / max(1, len(self.phase_transitions))
        half_hour_magnetism = half_hour_events_ratio * 0.8  # Strong but not overwhelming
        
        return CrossSessionEventTransfer(
            energy_momentum=carried_energy_momentum,
            timing_pattern_strength=timing_pattern_strength,
            event_sequence_tendency=event_sequence,
            cascade_priming_level=cascade_priming,
            consolidation_pressure=consolidation_pressure,
            t_memory_carryover=t_memory_carryover,
            half_hour_magnetism=half_hour_magnetism
        )
    
    def _predict_event_sequence(self) -> List[EventType]:
        """Predict likely event sequence for next session"""
        
        sequence = []
        
        if self.expansion_tendency > 0.7:
            # High expansion tendency -> expect expansion events
            sequence.extend([EventType.EXPANSION_START, EventType.CASCADE_FORMATION])
            
        elif self.expansion_tendency < 0.3:
            # Low expansion tendency -> expect consolidation
            sequence.extend([EventType.CONSOLIDATION_BEGIN, EventType.FVG_INTERACTION])
            
        else:
            # Mixed tendency -> balanced sequence
            sequence.extend([EventType.MOMENTUM_ACCELERATION, EventType.REVERSAL_SETUP])
        
        # Add cascade if cascade strength is high
        if self.cascade_formation_strength > 0.6:
            sequence.append(EventType.CASCADE_FORMATION)
            
        # Add liquidity sweep if ranging character
        if "ranging" in self.session_character.lower() or "liquidity" in self.session_character.lower():
            sequence.append(EventType.LIQUIDITY_SWEEP)
        
        return sequence

class CrossSessionEventPredictor:
    """Main predictor class for cross-session event timing"""
    
    def __init__(self):
        self.prediction_history = []
        self.timing_accuracy_stats = {}
        
        # Event timing defaults (in minutes from session start)
        self.default_event_timings = {
            EventType.EXPANSION_START: 15,      # Usually early in session
            EventType.CASCADE_FORMATION: 25,   # After initial setup
            EventType.CONSOLIDATION_BEGIN: 45, # Mid-session
            EventType.MOMENTUM_ACCELERATION: 20,
            EventType.REVERSAL_SETUP: 35,
            EventType.LIQUIDITY_SWEEP: 10,     # Often early for liquidity grab
            EventType.FVG_INTERACTION: 30
        }
        
        # Confidence modifiers based on session combinations
        self.session_confidence_modifiers = {
            ('Asia', 'London'): 0.85,    # Strong correlation
            ('London', 'NY_AM'): 0.8,    # Good correlation  
            ('NY_AM', 'NY_PM'): 0.9,     # Very strong correlation
            ('Lunch', 'NY_PM'): 0.95,   # Highest correlation
        }
    
    def predict_london_event_timing(self, asia_session_data: Dict, 
                                   target_london_start: datetime,
                                   tracker_context: tuple = None) -> List[EventTimingPrediction]:
        """
        Predict London session event timing based on Asia session patterns
        
        TRANSFORMATION: Returns WHEN events will occur, not WHERE price will go
        """
        
        print("🔮 CROSS-SESSION EVENT TIMING PREDICTION")
        print("=" * 50)
        print(f"Source: Asia → Target: London")
        print(f"London start time: {target_london_start.strftime('%Y-%m-%d %H:%M')}")
        
        # Extract Asia session event state
        asia_state = CrossSessionEventState(asia_session_data)
        print(f"Asia session character: {asia_state.session_character}")
        print(f"Asia expansion tendency: {asia_state.expansion_tendency:.2f}")
        print(f"Asia cascade strength: {asia_state.cascade_formation_strength:.2f}")
        
        # Calculate event transfer characteristics
        time_gap_hours = 0.5  # 30 minutes between Asia close and London open
        event_transfer = asia_state.transfer_event_characteristics(time_gap_hours, "London")
        
        print(f"\n📊 Event Transfer Characteristics:")
        print(f"   Energy momentum: {event_transfer.energy_momentum:.2f}")
        print(f"   Timing pattern strength: {event_transfer.timing_pattern_strength:.2f}")
        print(f"   Cascade priming level: {event_transfer.cascade_priming_level:.2f}")
        print(f"   Half-hour magnetism: {event_transfer.half_hour_magnetism:.2f}")
        
        # Generate event timing predictions
        predictions = []
        
        for event_type in event_transfer.event_sequence_tendency:
            prediction = self._predict_single_event_timing(
                event_type, event_transfer, target_london_start, asia_state
            )
            if prediction:
                predictions.append(prediction)
        
        # Sort predictions by time
        predictions.sort(key=lambda p: p.predicted_time_window[0])
        
        print(f"\n🎯 Event Timing Predictions ({len(predictions)}):")
        for i, pred in enumerate(predictions, 1):
            start_time = pred.predicted_time_window[0].strftime('%H:%M')
            end_time = pred.predicted_time_window[1].strftime('%H:%M')
            print(f"   {i}. {pred.event_type.value}")
            print(f"      Time Window: {start_time} - {end_time}")
            print(f"      Confidence: {pred.confidence:.2f}")
            print(f"      Reasoning: {pred.timing_reasoning}")
        
        return predictions
    
    def _predict_single_event_timing(self, event_type: EventType, 
                                   event_transfer: CrossSessionEventTransfer,
                                   session_start: datetime,
                                   asia_state: CrossSessionEventState) -> Optional[EventTimingPrediction]:
        """Predict timing for a single event type"""
        
        # Base timing from defaults
        base_minutes = self.default_event_timings[event_type]
        
        # Apply event transfer modifiers
        timing_adjustments = 0
        
        # Energy momentum adjustment
        if event_transfer.energy_momentum > 0.8:
            timing_adjustments -= 5  # High energy = events happen sooner
        elif event_transfer.energy_momentum < 0.3:
            timing_adjustments += 10  # Low energy = events delayed
        
        # Half-hour magnetism (pull events toward :00 or :30 marks)
        if event_transfer.half_hour_magnetism > 0.5:
            # Find nearest half-hour mark to base timing
            target_minutes = base_minutes + timing_adjustments
            nearest_half_hour = round(target_minutes / 30) * 30
            
            # Apply magnetic pull (stronger magnetism = stronger pull)
            magnetism_strength = event_transfer.half_hour_magnetism
            adjusted_minutes = target_minutes + (nearest_half_hour - target_minutes) * magnetism_strength
            timing_adjustments = adjusted_minutes - base_minutes
        
        # Cascade priming for cascade events
        if event_type == EventType.CASCADE_FORMATION and event_transfer.cascade_priming_level > 0.7:
            timing_adjustments -= 8  # 8-minute lead time (12:22 pattern)
        
        # Final timing calculation
        predicted_minutes = base_minutes + timing_adjustments
        predicted_minutes = max(5, min(180, predicted_minutes))  # Bound to reasonable session range
        
        # Calculate time window
        predicted_time = session_start + timedelta(minutes=predicted_minutes)
        window_width = 5  # ±5 minute window
        
        time_window_start = predicted_time - timedelta(minutes=window_width)
        time_window_end = predicted_time + timedelta(minutes=window_width)
        
        # Calculate confidence
        base_confidence = 0.6
        
        # Higher confidence for strong patterns
        if event_transfer.timing_pattern_strength > 0.7:
            base_confidence += 0.2
        
        # Higher confidence for cascade events with high priming
        if (event_type == EventType.CASCADE_FORMATION and 
            event_transfer.cascade_priming_level > 0.6):
            base_confidence += 0.15
        
        # Session combination modifier
        session_modifier = self.session_confidence_modifiers.get(('Asia', 'London'), 0.85)
        final_confidence = min(0.95, base_confidence * session_modifier)
        
        # Build trigger conditions
        trigger_conditions = {
            'energy_threshold': 0.7 if event_type in [EventType.CASCADE_FORMATION, EventType.EXPANSION_START] else 0.4,
            't_memory_min': event_transfer.t_memory_carryover,
            'momentum_required': event_transfer.energy_momentum,
            'timing_precision': event_transfer.timing_pattern_strength
        }
        
        # Carryover factors for validation
        carryover_factors = {
            'asia_expansion_tendency': asia_state.expansion_tendency,
            'asia_cascade_strength': asia_state.cascade_formation_strength,
            'energy_momentum_decay': event_transfer.energy_momentum,
            'pattern_persistence': event_transfer.timing_pattern_strength
        }
        
        # Generate reasoning
        reasoning_parts = []
        if event_transfer.energy_momentum > 0.7:
            reasoning_parts.append("high energy carryover from Asia")
        if event_transfer.half_hour_magnetism > 0.5:
            reasoning_parts.append("half-hour timing magnetism")
        if event_type == EventType.CASCADE_FORMATION and event_transfer.cascade_priming_level > 0.6:
            reasoning_parts.append("cascade priming from Asia patterns")
        
        timing_reasoning = "Predicted based on: " + ", ".join(reasoning_parts) if reasoning_parts else "Standard timing pattern"
        
        return EventTimingPrediction(
            event_type=event_type,
            predicted_time_window=(time_window_start, time_window_end),
            confidence=final_confidence,
            trigger_conditions=trigger_conditions,
            carryover_factors=carryover_factors,
            session_context=f"Asia→London transfer",
            timing_reasoning=timing_reasoning
        )
    
    def validate_event_timing_accuracy(self, predictions: List[EventTimingPrediction],
                                     actual_london_data: Dict) -> Dict[str, Any]:
        """
        Validate event timing predictions against actual London session
        
        TRANSFORMATION: Measures timing accuracy, not price accuracy
        """
        
        print("\n📏 EVENT TIMING VALIDATION")
        print("=" * 35)
        
        # Extract actual London events from phase transitions
        london_phase_transitions = []
        if "original_session_data" in actual_london_data:
            london_phase_transitions = actual_london_data["original_session_data"].get("phase_transitions", [])
        
        validation_results = {
            'total_predictions': len(predictions),
            'successful_timing_matches': 0,
            'timing_accuracy_details': [],
            'average_timing_error_minutes': 0,
            'best_prediction': None,
            'worst_prediction': None
        }
        
        timing_errors = []
        
        for prediction in predictions:
            # Find closest actual event
            closest_actual_event = self._find_closest_actual_event(
                prediction, london_phase_transitions
            )
            
            if closest_actual_event:
                # Calculate timing accuracy
                timing_accuracy = self._calculate_timing_accuracy(
                    prediction, closest_actual_event
                )
                
                validation_results['timing_accuracy_details'].append(timing_accuracy)
                timing_errors.append(timing_accuracy['timing_error_minutes'])
                
                # Count as successful if within ±10 minutes
                if abs(timing_accuracy['timing_error_minutes']) <= 10:
                    validation_results['successful_timing_matches'] += 1
                    
                print(f"   ✅ {prediction.event_type.value}: {timing_accuracy['timing_error_minutes']:+.1f} min error")
            else:
                print(f"   ❓ {prediction.event_type.value}: No matching actual event found")
        
        # Calculate overall statistics
        if timing_errors:
            validation_results['average_timing_error_minutes'] = np.mean(np.abs(timing_errors))
            
            # Find best/worst predictions
            accuracy_details = validation_results['timing_accuracy_details']
            if accuracy_details:
                validation_results['best_prediction'] = min(accuracy_details, 
                                                          key=lambda x: abs(x['timing_error_minutes']))
                validation_results['worst_prediction'] = max(accuracy_details,
                                                           key=lambda x: abs(x['timing_error_minutes']))
        
        # Calculate success rate
        success_rate = validation_results['successful_timing_matches'] / max(1, len(predictions))
        validation_results['timing_success_rate'] = success_rate
        
        print(f"\n📊 Validation Summary:")
        print(f"   Timing Success Rate: {success_rate:.1%}")
        print(f"   Average Error: {validation_results['average_timing_error_minutes']:.1f} minutes")
        print(f"   Successful Matches: {validation_results['successful_timing_matches']}/{len(predictions)}")
        
        return validation_results
    
    def _find_closest_actual_event(self, prediction: EventTimingPrediction, 
                                 actual_transitions: List[Dict]) -> Optional[Dict]:
        """Find the closest actual event to the prediction"""
        
        # Map prediction event types to actual phase types
        event_mapping = {
            EventType.EXPANSION_START: "expansion",
            EventType.CASCADE_FORMATION: "expansion",  # Cascades show as expansions
            EventType.CONSOLIDATION_BEGIN: "consolidation",
            EventType.MOMENTUM_ACCELERATION: "expansion",
            EventType.REVERSAL_SETUP: "reversal",
            EventType.LIQUIDITY_SWEEP: "expansion",  # Sweeps often trigger expansions
            EventType.FVG_INTERACTION: "consolidation"
        }
        
        target_phase_type = event_mapping.get(prediction.event_type)
        if not target_phase_type:
            return None
        
        # Find matching phase transitions
        matching_transitions = [
            t for t in actual_transitions 
            if t.get("phase_type") == target_phase_type
        ]
        
        if not matching_transitions:
            return None
        
        # Find closest in time to prediction
        pred_time = prediction.predicted_time_window[0]
        
        closest_transition = None
        min_time_diff = float('inf')
        
        for transition in matching_transitions:
            start_time_str = transition.get("start_time", "")
            if start_time_str:
                try:
                    # Simple time parsing (assumes same day)
                    time_parts = start_time_str.split(":")
                    hours = int(time_parts[0])
                    minutes = int(time_parts[1])
                    
                    actual_time = pred_time.replace(hour=hours, minute=minutes, second=0)
                    time_diff = abs((actual_time - pred_time).total_seconds() / 60)
                    
                    if time_diff < min_time_diff:
                        min_time_diff = time_diff
                        closest_transition = transition
                        
                except (ValueError, IndexError):
                    continue
        
        return closest_transition
    
    def _calculate_timing_accuracy(self, prediction: EventTimingPrediction,
                                 actual_event: Dict) -> Dict[str, Any]:
        """Calculate timing accuracy metrics"""
        
        # Extract actual event timing
        start_time_str = actual_event.get("start_time", "")
        pred_time = prediction.predicted_time_window[0]
        
        try:
            time_parts = start_time_str.split(":")
            hours = int(time_parts[0])
            minutes = int(time_parts[1])
            
            actual_time = pred_time.replace(hour=hours, minute=minutes, second=0)
            timing_error_seconds = (actual_time - pred_time).total_seconds()
            timing_error_minutes = timing_error_seconds / 60
            
            # Determine if within prediction window
            within_window = (prediction.predicted_time_window[0] <= actual_time <= 
                           prediction.predicted_time_window[1])
            
            return {
                'event_type': prediction.event_type.value,
                'predicted_time': pred_time.strftime('%H:%M'),
                'actual_time': actual_time.strftime('%H:%M'),
                'timing_error_minutes': timing_error_minutes,
                'within_prediction_window': within_window,
                'prediction_confidence': prediction.confidence,
                'timing_quality': 'excellent' if abs(timing_error_minutes) <= 5 else
                                'good' if abs(timing_error_minutes) <= 10 else
                                'moderate' if abs(timing_error_minutes) <= 20 else 'poor'
            }
            
        except (ValueError, IndexError):
            return {
                'event_type': prediction.event_type.value,
                'timing_error_minutes': float('inf'),
                'within_prediction_window': False,
                'timing_quality': 'failed_to_parse'
            }

def main():
    """Test cross-session event timing prediction"""
    
    print("🧪 CROSS-SESSION EVENT TIMING TEST")
    print("=" * 45)
    
    # Load test data
    try:
        with open('asia_grokEnhanced_2025_07_23.json', 'r') as f:
            asia_data = json.load(f)
        with open('london_grokEnhanced_2025_07_23.json', 'r') as f:
            london_data = json.load(f)
            
    except FileNotFoundError as e:
        print(f"❌ Test data not found: {e}")
        return
    
    # Initialize predictor
    predictor = CrossSessionEventPredictor()
    
    # Set London session start time
    london_start = datetime(2025, 7, 23, 8, 0)  # 8:00 AM London time
    
    # Generate event timing predictions
    predictions = predictor.predict_london_event_timing(
        asia_data, london_start
    )
    
    if predictions:
        print(f"\n✅ Generated {len(predictions)} event timing predictions")
        
        # Validate against actual London data
        validation = predictor.validate_event_timing_accuracy(predictions, london_data)
        
        # Save results
        results = {
            'test_metadata': {
                'test_type': 'cross_session_event_timing',
                'source_session': 'asia_2025_07_23',
                'target_session': 'london_2025_07_23',
                'timestamp': datetime.now().isoformat()
            },
            'event_predictions': [
                {
                    'event_type': p.event_type.value,
                    'time_window': f"{p.predicted_time_window[0].strftime('%H:%M')}-{p.predicted_time_window[1].strftime('%H:%M')}",
                    'confidence': p.confidence,
                    'reasoning': p.timing_reasoning
                }
                for p in predictions
            ],
            'validation_results': validation
        }
        
        output_file = f"cross_session_event_timing_test_{datetime.now().strftime('%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {output_file}")
        
        # Summary
        success_rate = validation.get('timing_success_rate', 0)
        avg_error = validation.get('average_timing_error_minutes', 0)
        
        print(f"\n🏆 TRANSFORMATION SUCCESS:")
        print(f"   Timing Success Rate: {success_rate:.1%}")
        print(f"   Average Timing Error: {avg_error:.1f} minutes")
        print(f"   Paradigm: WHEN events occur, not WHERE price goes ✅")
    
    else:
        print("❌ No event timing predictions generated")

if __name__ == "__main__":
    main()