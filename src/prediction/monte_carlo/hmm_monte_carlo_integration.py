#!/usr/bin/env python3
"""
HMM ↔ Monte Carlo Bidirectional Integration System
Implements Opus 4's "jazz duo" vision where HMM states enhance Monte Carlo precision
and Monte Carlo outcomes refine HMM transition probabilities.
"""

import numpy as np
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
try:
    from market_state_hmm import MarketStateHMM, MarketState, StateMetrics
    from grok_monte_carlo_package import universal_timing_formula, VALIDATED_SESSIONS
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

@dataclass
class IntegratedPrediction:
    """Combined HMM + Monte Carlo prediction result"""
    monte_carlo_prediction: float
    hmm_state: MarketState
    hmm_confidence: float
    state_adjusted_prediction: float
    confidence_interval_lower: float
    confidence_interval_upper: float
    integration_confidence: float
    state_multiplier: float
    feedback_data: Dict[str, Any]

class HMMMonteCarloIntegrator:
    """Bidirectional integration between HMM states and Monte Carlo predictions"""
    
    def __init__(self):
        self.hmm = MarketStateHMM()
        
        # State-based Monte Carlo adjustments (Opus 4's GPS recalculation concept)
        self.state_multipliers = {
            MarketState.CONSOLIDATING: {
                'timing_multiplier': 1.2,      # Slower development in consolidation
                'confidence_interval_factor': 0.8,  # Tighter ranges
                'uncertainty_boost': 0.1       # Low uncertainty
            },
            MarketState.PRE_CASCADE: {
                'timing_multiplier': 0.6,      # Faster approach to events
                'confidence_interval_factor': 0.5,  # Very tight ranges
                'uncertainty_boost': 0.2       # Moderate uncertainty
            },
            MarketState.EXPANDING: {
                'timing_multiplier': 0.3,      # Immediate events
                'confidence_interval_factor': 0.4,  # Tight ranges
                'uncertainty_boost': 0.05      # Very low uncertainty
            },
            MarketState.EXHAUSTED: {
                'timing_multiplier': 1.5,      # Slower transition back
                'confidence_interval_factor': 1.2,  # Wider ranges
                'uncertainty_boost': 0.3       # Higher uncertainty
            }
        }
        
        # Feedback learning system (Monte Carlo → HMM)
        self.transition_feedback = {
            MarketState.CONSOLIDATING: {"successes": 0, "attempts": 0},
            MarketState.PRE_CASCADE: {"successes": 0, "attempts": 0},
            MarketState.EXPANDING: {"successes": 0, "attempts": 0},
            MarketState.EXHAUSTED: {"successes": 0, "attempts": 0}
        }
        
    def integrated_predict(self, session_data: dict, tracker_context: tuple = None,
                          news_impacted: bool = False, news_data: Dict = None) -> IntegratedPrediction:
        """Generate integrated HMM + Monte Carlo prediction"""
        
        print("🎯 HMM ↔ MONTE CARLO INTEGRATION")
        print("=" * 45)
        
        # Step 1: Get HMM state classification
        metrics = self.hmm.calculate_state_metrics(session_data, tracker_context)
        detected_state, state_confidence = self.hmm.classify_state(metrics)
        
        print(f"1️⃣ HMM State: {detected_state.value} (confidence: {state_confidence:.2f})")
        
        # Step 2: Generate baseline Monte Carlo prediction
        baseline_prediction = universal_timing_formula(
            volatility=metrics.volatility,
            distance=1.0,  # Default distance
            news_impacted=news_impacted,
            news_data=news_data
        )
        
        monte_carlo_minutes = baseline_prediction['predicted_minutes']
        print(f"2️⃣ Monte Carlo Baseline: {monte_carlo_minutes:.3f} minutes")
        
        # Step 3: Apply HMM state adjustments (Opus 4's GPS recalculation)
        state_adjustments = self.state_multipliers[detected_state]
        state_multiplier = state_adjustments['timing_multiplier']
        
        # Adjust prediction based on state
        state_adjusted_prediction = monte_carlo_minutes * state_multiplier
        
        # Apply learned feedback adjustments
        feedback_multiplier = self._get_feedback_multiplier(detected_state)
        final_prediction = state_adjusted_prediction * feedback_multiplier
        
        print(f"3️⃣ State Adjustment: {state_multiplier:.1f}x → {state_adjusted_prediction:.3f} minutes")
        print(f"4️⃣ Feedback Adjustment: {feedback_multiplier:.2f}x → {final_prediction:.3f} minutes")
        
        # Step 4: Calculate state-aware confidence intervals
        base_interval_width = baseline_prediction['confidence_upper'] - baseline_prediction['confidence_lower']
        state_interval_factor = state_adjustments['confidence_interval_factor']
        adjusted_interval_width = base_interval_width * state_interval_factor
        
        confidence_lower = max(0, final_prediction - adjusted_interval_width/2)
        confidence_upper = final_prediction + adjusted_interval_width/2
        
        # Step 5: Calculate integration confidence
        # Higher confidence when HMM state is clear and Monte Carlo is certain
        integration_confidence = min(0.95, 
            state_confidence * 0.7 +  # HMM confidence weight
            (1.0 - state_adjustments['uncertainty_boost']) * 0.3  # State uncertainty factor
        )
        
        print(f"5️⃣ Integration Confidence: {integration_confidence:.2f}")
        print(f"6️⃣ Confidence Interval: [{confidence_lower:.3f}, {confidence_upper:.3f}]")
        
        # Step 6: Prepare feedback data for learning
        feedback_data = {
            'hmm_state': detected_state.value,
            'state_confidence': state_confidence,
            'monte_carlo_baseline': monte_carlo_minutes,
            'state_multiplier_applied': state_multiplier,
            'feedback_multiplier_applied': feedback_multiplier,
            'energy_level': metrics.energy_level,
            't_memory': metrics.t_memory,
            'volatility': metrics.volatility,
            'timestamp': datetime.now().isoformat()
        }
        
        return IntegratedPrediction(
            monte_carlo_prediction=monte_carlo_minutes,
            hmm_state=detected_state,
            hmm_confidence=state_confidence,
            state_adjusted_prediction=final_prediction,
            confidence_interval_lower=confidence_lower,
            confidence_interval_upper=confidence_upper,
            integration_confidence=integration_confidence,
            state_multiplier=state_multiplier,
            feedback_data=feedback_data
        )
    
    def _get_feedback_multiplier(self, state: MarketState) -> float:
        """Get learned feedback multiplier for state"""
        
        feedback = self.transition_feedback[state]
        if feedback['attempts'] == 0:
            return 1.0  # No learning data yet
        
        success_rate = feedback['successes'] / feedback['attempts']
        
        # Adjust multiplier based on historical success
        # Higher success = use current multiplier, lower success = adjust away from 1.0
        if success_rate > 0.8:
            return 1.0  # Keep current approach
        elif success_rate > 0.6:
            return 0.95  # Slight conservative adjustment
        elif success_rate > 0.4:
            return 0.90  # Moderate adjustment
        else:
            return 0.85  # Significant adjustment needed
    
    def record_prediction_outcome(self, prediction: IntegratedPrediction, 
                                 actual_minutes: float, tolerance: float = 1.0):
        """Record prediction outcome for feedback learning"""
        
        error = abs(prediction.state_adjusted_prediction - actual_minutes)
        success = error <= tolerance
        
        # Update feedback for the state that was detected
        state_feedback = self.transition_feedback[prediction.hmm_state]
        state_feedback['attempts'] += 1
        if success:
            state_feedback['successes'] += 1
        
        print(f"📊 Feedback Recorded: {prediction.hmm_state.value}")
        print(f"   Prediction: {prediction.state_adjusted_prediction:.3f} min")
        print(f"   Actual: {actual_minutes:.3f} min")
        print(f"   Success: {'✅' if success else '❌'} (error: {error:.3f})")
        print(f"   State Success Rate: {state_feedback['successes']}/{state_feedback['attempts']} ({state_feedback['successes']/state_feedback['attempts']:.1%})")
        
        return success
    
    def enhanced_monte_carlo_with_hmm_constraints(self, session_data: dict, 
                                                 tracker_context: tuple = None,
                                                 n_simulations: int = 1000) -> Dict:
        """Run Monte Carlo with HMM state constraints"""
        
        # Get current HMM state
        metrics = self.hmm.calculate_state_metrics(session_data, tracker_context)
        detected_state, state_confidence = self.hmm.classify_state(metrics)
        
        # Get state constraints
        constraints = self.state_multipliers[detected_state]
        
        results = []
        for _ in range(n_simulations):
            # Generate base prediction
            base_pred = universal_timing_formula(
                volatility=metrics.volatility + np.random.normal(0, 0.01),
                distance=1.0,
                news_impacted=False
            )
            
            # Apply HMM state constraints
            constrained_prediction = base_pred['predicted_minutes'] * constraints['timing_multiplier']
            
            # Apply state-based noise reduction
            noise_factor = 1.0 - (state_confidence * 0.3)  # Higher confidence = less noise
            constrained_prediction += np.random.normal(0, 0.1 * noise_factor)
            
            results.append(max(0, constrained_prediction))
        
        return {
            'hmm_constrained_mean': np.mean(results),
            'hmm_constrained_std': np.std(results),
            'hmm_constrained_percentiles': {
                '10th': np.percentile(results, 10),
                '25th': np.percentile(results, 25),
                '50th': np.percentile(results, 50),
                '75th': np.percentile(results, 75),
                '90th': np.percentile(results, 90)
            },
            'constraining_state': detected_state.value,
            'state_confidence': state_confidence,
            'simulation_count': n_simulations
        }

def test_integration_system():
    """Test the integrated HMM + Monte Carlo system"""
    
    print("🧪 TESTING HMM ↔ MONTE CARLO INTEGRATION")
    print("=" * 50)
    
    # Load test data
    try:
        with open('lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            session_data = json.load(f)
        with open('FVG_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            fvg_tracker = json.load(f)
        
        tracker_context = ({}, fvg_tracker, {})
        
    except FileNotFoundError:
        print("❌ Test data not found - creating simulated data")
        session_data = {
            'price_data': {'range': 120},
            'grok_enhanced_calculations': {
                'unit_b_energy_structure': {
                    'energy_accumulation': {'energy_rate': 0.6}
                },
                'unit_c_advanced_dynamics': {
                    'temporal_momentum': {'momentum_strength': 0.8}
                }
            }
        }
        tracker_context = ({}, {'t_memory': 8.0}, {})
    
    # Initialize integrated system
    integrator = HMMMonteCarloIntegrator()
    
    # Test integrated prediction
    print("\n🎯 GENERATING INTEGRATED PREDICTION:")
    prediction = integrator.integrated_predict(session_data, tracker_context)
    
    # Display results
    print(f"\n📊 INTEGRATION RESULTS:")
    print(f"   Monte Carlo Baseline: {prediction.monte_carlo_prediction:.3f} minutes")
    print(f"   HMM State: {prediction.hmm_state.value} (confidence: {prediction.hmm_confidence:.2f})")
    print(f"   State Multiplier: {prediction.state_multiplier:.1f}x")
    print(f"   Final Prediction: {prediction.state_adjusted_prediction:.3f} minutes")
    print(f"   Integration Confidence: {prediction.integration_confidence:.2f}")
    print(f"   Confidence Range: [{prediction.confidence_interval_lower:.3f}, {prediction.confidence_interval_upper:.3f}]")
    
    # Test constrained Monte Carlo
    print(f"\n🎲 TESTING HMM-CONSTRAINED MONTE CARLO:")
    constrained_mc = integrator.enhanced_monte_carlo_with_hmm_constraints(session_data, tracker_context, 100)
    
    print(f"   Mean Prediction: {constrained_mc['hmm_constrained_mean']:.3f} minutes")
    print(f"   Standard Deviation: {constrained_mc['hmm_constrained_std']:.3f}")
    print(f"   50th Percentile: {constrained_mc['hmm_constrained_percentiles']['50th']:.3f}")
    print(f"   Constraining State: {constrained_mc['constraining_state']}")
    
    # Simulate feedback learning
    print(f"\n📚 SIMULATING FEEDBACK LEARNING:")
    actual_outcome = 0.8  # Simulated actual timing
    success = integrator.record_prediction_outcome(prediction, actual_outcome)
    
    return prediction, constrained_mc

if __name__ == "__main__":
    test_integration_system()