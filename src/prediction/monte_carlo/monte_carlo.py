import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging

class EventType(Enum):
    LIQUIDITY_ABSORPTION = "liquidity_absorption"
    FVG_REDELIVERY = "fvg_redelivery"
    RETRACEMENT_50 = "retracement_50%"
    LEVEL_REJECTION = "level_rejection"
    LEVEL_CONTINUATION = "level_continuation"

@dataclass
class Event:
    timestamp: float  # minutes from session start
    event_type: EventType
    price: float
    probability: float
    context: str

@dataclass
class PathResult:
    path_id: int
    events: List[Event]
    final_price: float
    total_range: float  # Now = session_high - session_low
    session_high: float
    session_low: float
    net_movement: float  # abs(final_price - start_price)
    probability_score: float

class PathGenerator:
    def __init__(self, tracker_state: Dict, session_params: Dict, session_character: str = None):
        """
        Initialize with current market state and session parameters
        
        Args:
            tracker_state: Current state from your framework
            session_params: Session-specific parameters
            session_character: Session character for scaling corrections (discovered pattern)
        """
        # Core parameters from framework
        self.t_memory = tracker_state.get('t_memory', 5.0)
        self.liquidity_levels = tracker_state.get('untaken_liquidity', [])
        self.liquidity_gradient = tracker_state.get('liquidity_gradient', 0.012)
        self.htf_structures = tracker_state.get('htf_structures', [])
        self.e_threshold_adj = tracker_state.get('e_threshold_adj', 1000)
        
        # Mathematical parameters
        self.gamma_enhanced = session_params.get('gamma_enhanced', 1.5)  # Assuming from your framework
        self.current_price = session_params.get('current_price', 5000.0)
        self.session_start_price = session_params.get('session_start_price', 5000.0)
        
        # Session character for pattern-based corrections (discovered relationship)
        self.session_character = session_character or ""
        
        # Simulation parameters
        self.time_window = 20  # minutes precision
        self.event_variance = 2  # ±2 events
        
        # Derived parameters
        self.memory_decay_factor = 1 + 0.2 * np.exp(-0.06 * self.t_memory)
        self.consolidation_probability = 1 - np.exp(-0.1 * self.t_memory)
        
        # Pattern-based corrections (manually discovered mathematical relationships)
        self.session_character_multiplier = self._calculate_session_character_multiplier()
        self.volatility_adjustment = self._calculate_volatility_adjustment()
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _calculate_session_character_multiplier(self) -> float:
        """
        Calculate session character scaling multiplier based on discovered patterns
        
        Discovered relationship: expansion_then_consolidation sessions require 
        magnitude scaling * 0.3 vs volatile_with_major_redeliveries sessions
        
        Returns:
            Scaling multiplier for magnitude generation
        """
        if "consolidation" in self.session_character.lower():
            return 0.3  # Strong dampening for consolidation patterns
        elif "expansion" in self.session_character.lower():
            return 0.4  # Moderate dampening for expansion patterns
        elif "volatile" in self.session_character.lower():
            return 1.0  # No dampening for volatile patterns
        else:
            return 0.7  # Default moderate dampening for unknown patterns
    
    def _calculate_volatility_adjustment(self) -> float:
        """
        Calculate volatility adjustment factor based on session character
        
        Discovered pattern: volatile sessions show 3.3x higher error rates
        
        Returns:
            Volatility adjustment factor
        """
        if "volatile" in self.session_character.lower() and "major_redeliveries" in self.session_character.lower():
            return 2.5  # Higher volatility factor for complex patterns
        elif "expansion_then_consolidation" in self.session_character:
            return 0.0  # Lower volatility for predictable consolidation
        else:
            return 1.0  # Default volatility
    
    def calculate_liquidity_magnetic_pull(self) -> Tuple[float, str]:
        """
        Calculate magnetic pull strength and direction from untaken liquidity
        
        Returns:
            Tuple of (pull_strength, direction)
        """
        if not self.liquidity_levels:
            return 0.0, "neutral"
        
        # Calculate weighted pull based on proximity and gradient
        upward_pull = sum(1/abs(level - self.current_price) 
                         for level in self.liquidity_levels 
                         if level > self.current_price)
        
        downward_pull = sum(1/abs(level - self.current_price) 
                           for level in self.liquidity_levels 
                           if level < self.current_price)
        
        # Apply liquidity gradient bias
        net_pull = (upward_pull - downward_pull) * (1 + self.liquidity_gradient)
        
        direction = "upward" if net_pull > 0 else "downward" if net_pull < 0 else "neutral"
        pull_strength = abs(net_pull)
        
        return pull_strength, direction
    
    def generate_time_to_move(self) -> float:
        """Generate time until next significant move using your formula"""
        mu = 22.5 / self.gamma_enhanced
        sigma = 5.0
        return max(1.0, np.random.normal(mu, sigma))
    
    def generate_move_magnitude(self) -> float:
        """
        Generate realistic move magnitude with pattern-based corrections
        
        Applies discovered mathematical relationships:
        - Session character multiplier (consolidation vs volatile patterns)
        - Volatility adjustment based on session type
        """
        # Base magnitude generation
        shape = 2.0  # Shape parameter
        scale = 8.0  # Scale parameter - gives moves typically 5-30 points
        base_magnitude = np.random.gamma(shape, scale)
        
        # Apply session character multiplier (discovered pattern correction)
        adjusted_magnitude = base_magnitude * self.session_character_multiplier
        
        # Apply volatility adjustment
        if self.volatility_adjustment > 1.0:
            # Increase variance for volatile sessions
            volatility_noise = np.random.normal(0, self.volatility_adjustment * 2.0)
            adjusted_magnitude += abs(volatility_noise)
        
        # Dynamic magnitude cap based on session character
        if "consolidation" in self.session_character.lower():
            max_magnitude = 25.0  # Lower cap for consolidation sessions
        elif "volatile" in self.session_character.lower():
            max_magnitude = 75.0  # Higher cap for volatile sessions
        else:
            max_magnitude = 50.0  # Default cap
        
        return min(adjusted_magnitude, max_magnitude)
    
    def determine_move_direction(self, pull_strength: float, pull_direction: str) -> int:
        """
        Determine move direction based on liquidity pull and random factors
        
        Returns:
            1 for up, -1 for down
        """
        base_probability = 0.5
        
        # Adjust probability based on liquidity pull
        if pull_direction == "upward":
            up_probability = base_probability + (pull_strength * 0.2)
        elif pull_direction == "downward":
            up_probability = base_probability - (pull_strength * 0.2)
        else:
            up_probability = base_probability
        
        # Clamp probability
        up_probability = np.clip(up_probability, 0.1, 0.9)
        
        return 1 if np.random.random() < up_probability else -1
    
    def generate_event_sequence(self, current_time: float, current_price: float) -> List[Event]:
        """Generate probable event sequence within time window"""
        events = []
        
        # Base number of events in 20-minute window
        base_events = np.random.poisson(lam=2.0)  # Average 2 events per 20 min
        n_events = max(1, base_events + np.random.randint(-self.event_variance, self.event_variance + 1))
        
        for i in range(n_events):
            # Distribute events across time window
            event_time = current_time + np.random.uniform(0, self.time_window)
            
            # Determine event type based on market state
            event_type = self._select_event_type(current_price, current_time)
            
            # Calculate event probability
            probability = self._calculate_event_probability(event_type, current_price)
            
            events.append(Event(
                timestamp=event_time,
                event_type=event_type,
                price=current_price,
                probability=probability,
                context=f"Generated at t={current_time:.1f}"
            ))
        
        return sorted(events, key=lambda x: x.timestamp)
    
    def _select_event_type(self, price: float, time: float) -> EventType:
        """Select event type based on current conditions"""
        # Probabilities based on market state
        probabilities = {
            EventType.LIQUIDITY_ABSORPTION: 0.3 if self.consolidation_probability > 0.7 else 0.1,
            EventType.FVG_REDELIVERY: 0.2 if self.t_memory > 3.0 else 0.05,
            EventType.RETRACEMENT_50: 0.15,
            EventType.LEVEL_REJECTION: 0.2,
            EventType.LEVEL_CONTINUATION: 0.15
        }
        
        # Normalize probabilities
        total = sum(probabilities.values())
        normalized_probs = [p/total for p in probabilities.values()]
        
        return np.random.choice(list(probabilities.keys()), p=normalized_probs)
    
    def _calculate_event_probability(self, event_type: EventType, price: float) -> float:
        """Calculate probability of event occurring"""
        base_prob = 0.5
        
        if event_type == EventType.LIQUIDITY_ABSORPTION:
            return base_prob * self.consolidation_probability
        elif event_type == EventType.FVG_REDELIVERY:
            return base_prob * (self.t_memory / 10.0)  # Higher T_memory = higher probability
        else:
            return base_prob
    
    def simulate_single_path(self, duration_minutes: int = 180) -> PathResult:
        """Simulate a single price path over specified duration"""
        events = []
        current_time = 0.0
        current_price = self.current_price
        path_probability = 1.0
        
        # Track session high/low for proper range calculation
        session_high = current_price
        session_low = current_price
        
        while current_time < duration_minutes:
            # Get liquidity state
            pull_strength, pull_direction = self.calculate_liquidity_magnetic_pull()
            
            # Generate next move timing
            time_to_move = self.generate_time_to_move()
            
            # Check if we're in consolidation phase
            if np.random.random() < self.consolidation_probability:
                # Consolidation: small moves, generate events
                current_time += self.time_window
                path_events = self.generate_event_sequence(current_time, current_price)
                events.extend(path_events)
                
                # Small price drift during consolidation
                price_drift = np.random.normal(0, 2.0)  # Small random walk
                current_price += price_drift
                
                # Update session high/low
                session_high = max(session_high, current_price)
                session_low = min(session_low, current_price)
                
            else:
                # Expansion phase: significant move
                current_time += time_to_move
                if current_time >= duration_minutes:
                    break
                
                # Generate move
                direction = self.determine_move_direction(pull_strength, pull_direction)
                magnitude = self.generate_move_magnitude()
                
                current_price += direction * magnitude
                
                # Update session high/low
                session_high = max(session_high, current_price)
                session_low = min(session_low, current_price)
                
                # Add expansion event
                expansion_event = Event(
                    timestamp=current_time,
                    event_type=EventType.LEVEL_CONTINUATION if direction * pull_strength > 0 else EventType.LEVEL_REJECTION,
                    price=current_price,
                    probability=0.8,  # High probability for actual moves
                    context=f"Expansion: {direction * magnitude:.1f} points"
                )
                events.append(expansion_event)
        
        # Calculate path metrics - FIXED to use proper range calculation
        total_range = session_high - session_low  # Proper range = high - low
        net_movement = abs(current_price - self.session_start_price)  # Net directional movement
        
        # Calculate overall path probability (simplified)
        event_probs = [e.probability for e in events]
        path_probability = np.mean(event_probs) if event_probs else 0.5
        
        return PathResult(
            path_id=0,  # Will be set by caller
            events=events,
            final_price=current_price,
            total_range=total_range,  # Now = session_high - session_low
            session_high=session_high,
            session_low=session_low,
            net_movement=net_movement,  # Separate metric for directional move
            probability_score=path_probability
        )
    
    def generate_paths(self, n_simulations: int = 1000, duration_minutes: int = 180) -> List[PathResult]:
        """
        Generate multiple path simulations
        
        Args:
            n_simulations: Number of Monte Carlo simulations
            duration_minutes: Duration of each simulation
            
        Returns:
            List of PathResult objects
        """
        self.logger.info(f"Generating {n_simulations} path simulations over {duration_minutes} minutes")
        
        paths = []
        for i in range(n_simulations):
            path = self.simulate_single_path(duration_minutes)
            path.path_id = i
            paths.append(path)
            
            if (i + 1) % 100 == 0:
                self.logger.info(f"Completed {i + 1}/{n_simulations} simulations")
        
        return paths
    
    def calculate_prediction_bands(self, paths: List[PathResult]) -> Dict:
        """
        Calculate prediction bands from simulation results
        
        Returns:
            Dictionary with percentile bands and statistics
        """
        final_prices = [path.final_price for path in paths]
        total_ranges = [path.total_range for path in paths]  # Now = high-low ranges
        net_movements = [path.net_movement for path in paths]  # Directional movements
        probability_scores = [path.probability_score for path in paths]
        
        prediction_bands = {
            'final_price_percentiles': {
                '10th': np.percentile(final_prices, 10),
                '25th': np.percentile(final_prices, 25),
                '50th': np.percentile(final_prices, 50),
                '75th': np.percentile(final_prices, 75),
                '90th': np.percentile(final_prices, 90)
            },
            'range_percentiles': {  # FIXED - now proper high-low ranges
                '10th': np.percentile(total_ranges, 10),
                '25th': np.percentile(total_ranges, 25),
                '50th': np.percentile(total_ranges, 50),
                '75th': np.percentile(total_ranges, 75),
                '90th': np.percentile(total_ranges, 90)
            },
            'net_movement_percentiles': {  # NEW - directional movement analysis
                '10th': np.percentile(net_movements, 10),
                '25th': np.percentile(net_movements, 25),
                '50th': np.percentile(net_movements, 50),
                '75th': np.percentile(net_movements, 75),
                '90th': np.percentile(net_movements, 90)
            },
            'statistics': {
                'mean_final_price': np.mean(final_prices),
                'std_final_price': np.std(final_prices),
                'mean_range': np.mean(total_ranges),  # Average high-low range
                'mean_net_movement': np.mean(net_movements),  # Average directional move
                'mean_probability': np.mean(probability_scores),
                'total_simulations': len(paths)
            }
        }
        
        return prediction_bands
    
    def analyze_event_frequencies(self, paths: List[PathResult]) -> Dict:
        """Analyze frequency of different event types across all paths"""
        event_counts = {event_type: 0 for event_type in EventType}
        total_events = 0
        
        for path in paths:
            for event in path.events:
                event_counts[event.event_type] += 1
                total_events += 1
        
        event_frequencies = {
            event_type.value: count / total_events if total_events > 0 else 0
            for event_type, count in event_counts.items()
        }
        
        return {
            'frequencies': event_frequencies,
            'total_events': total_events,
            'avg_events_per_path': total_events / len(paths) if paths else 0
        }

# Example usage and testing
if __name__ == "__main__":
    # Example tracker state (replace with real data)
    tracker_state = {
        't_memory': 5.0,
        'untaken_liquidity': [5010, 5025, 4985, 4970, 5040, 4960],
        'liquidity_gradient': 0.012,
        'htf_structures': [4950, 4980, 5020, 5050, 5080, 5100, 4920, 4900],
        'e_threshold_adj': 1000
    }
    
    session_params = {
        'gamma_enhanced': 1.5,
        'current_price': 5000.0,
        'session_start_price': 4998.0
    }
    
    # Create path generator
    generator = PathGenerator(tracker_state, session_params)
    
    # Generate paths
    paths = generator.generate_paths(n_simulations=100, duration_minutes=180)
    
    # Calculate prediction bands
    bands = generator.calculate_prediction_bands(paths)
    
    # Analyze events
    event_analysis = generator.analyze_event_frequencies(paths)
    
    print("Prediction Bands:")
    print(f"50th percentile final price: {bands['final_price_percentiles']['50th']:.2f}")
    print(f"90th percentile range: {bands['range_percentiles']['90th']:.2f}")
    print(f"\nEvent Frequencies:")
    for event_type, freq in event_analysis['frequencies'].items():
        print(f"{event_type}: {freq:.3f}")