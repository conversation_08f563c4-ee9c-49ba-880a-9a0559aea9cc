"""
Monte Carlo Data Adapter
Converts session data from your trading framework into Monte Carlo simulation parameters
"""

import json
from typing import Dict, List, Optional
from monte_carlo import PathGenerator

class MonteCarloAdapter:
    """Adapts session data format to Monte Carlo simulation parameters"""
    
    def __init__(self, session_data: Dict):
        """Initialize with session data from your framework"""
        self.session_data = session_data
        
    def extract_tracker_state(self) -> Dict:
        """Extract tracker state parameters from session data"""
        
        # Check if this is enhanced data format
        timing_enhancements = self.session_data.get("timing_enhancements", {})
        
        if timing_enhancements:
            # Enhanced format - extract from timing_enhancements
            time_dilation = timing_enhancements.get("time_dilation_enhanced", {})
            energy_threshold = timing_enhancements.get("energy_threshold_modeling", {})
            
            # Extract T_memory from temporal momentum
            temporal_momentum = time_dilation.get("temporal_momentum", 0.18)
            t_memory = abs(temporal_momentum) * 25.0  # Scale to meaningful range
            
            # Extract energy threshold
            current_energy = energy_threshold.get("current_energy", 1000)
            e_threshold_adj = current_energy
            
            # Extract liquidity gradient from enhanced data
            liquidity_gradient = 0.015  # Default for enhanced format
            
        else:
            # Standard format - extract from micro timing analysis
            micro_timing = self.session_data.get("micro_timing_analysis", {})
            
            # Calculate T_memory from consolidation duration
            consolidation = micro_timing.get("consolidation_analysis", {})
            consolidation_duration = consolidation.get("consolidation_duration", 60)
            t_memory = consolidation_duration / 12.0
            
            # Calculate liquidity gradient from gradient dynamics
            gradient_dynamics = micro_timing.get("gradient_dynamics", {})
            liquidity_gradient = gradient_dynamics.get("stability_factor", 0.68) * 0.02
            
            # Calculate E threshold from energy accumulation
            energy_data = micro_timing.get("energy_accumulation", {})
            e_threshold_adj = energy_data.get("total_energy", 1000)
        
        # Extract liquidity levels and HTF structures (same for both formats)
        untaken_liquidity = self._extract_liquidity_levels()
        htf_structures = self._extract_htf_structures()
        
        return {
            't_memory': t_memory,
            'untaken_liquidity': untaken_liquidity,
            'liquidity_gradient': liquidity_gradient,
            'htf_structures': htf_structures,
            'e_threshold_adj': e_threshold_adj
        }
    
    def extract_session_params(self) -> Dict:
        """Extract session parameters from session data"""
        
        # Check if this is enhanced data format
        timing_enhancements = self.session_data.get("timing_enhancements", {})
        
        if timing_enhancements:
            # Enhanced format - extract from timing_enhancements
            time_dilation = timing_enhancements.get("time_dilation_enhanced", {})
            gamma_enhanced = time_dilation.get("gamma_value", 1.42)
            
            # Extract prices from enhanced format
            price_data = self.session_data.get("price_data", {})
            if not price_data:
                # Fallback to session metadata or defaults
                session_meta = self.session_data.get("session_metadata", {})
                current_price = session_meta.get("close", 23329.0)
                session_start_price = session_meta.get("open", 23343.75)
            else:
                current_price = price_data.get("close", 23329.0)
                session_start_price = price_data.get("open", 23343.75)
        else:
            # Standard format
            price_data = self.session_data.get("price_data", {})
            micro_timing = self.session_data.get("micro_timing_analysis", {})
            
            # Extract prices
            current_price = price_data.get("close", 23329.0)
            session_start_price = price_data.get("open", 23343.75)
            
            # Calculate gamma_enhanced from temporal momentum
            temporal_momentum = micro_timing.get("temporal_momentum", {})
            gamma_enhanced = temporal_momentum.get("momentum_strength", 1.15)
        
        return {
            'gamma_enhanced': gamma_enhanced,
            'current_price': current_price,
            'session_start_price': session_start_price
        }
    
    def extract_session_character(self) -> str:
        """
        Extract session character from session data
        
        Returns:
            Session character string for pattern-based corrections
        """
        # Check for session character in various places
        session_character = ""
        
        # Check in price_data first (most likely location)
        price_data = self.session_data.get("price_data", {})
        if "session_character" in price_data:
            session_character = price_data["session_character"]
        
        # Check in session metadata
        elif "session_character" in self.session_data.get("session_metadata", {}):
            session_character = self.session_data["session_metadata"]["session_character"]
        
        # Check in original_session_data (for actual session files)
        elif "original_session_data" in self.session_data:
            orig_data = self.session_data["original_session_data"]
            if "price_data" in orig_data and "session_character" in orig_data["price_data"]:
                session_character = orig_data["price_data"]["session_character"]
        
        # Check timing enhancements for session character
        elif "timing_enhancements" in self.session_data:
            timing = self.session_data["timing_enhancements"]
            if "session_character" in timing:
                session_character = timing["session_character"]
        
        # Fallback: try to infer from price action
        if not session_character:
            session_character = self._infer_session_character()
        
        return session_character
    
    def _infer_session_character(self) -> str:
        """
        Infer session character from price data if not explicitly provided
        
        Returns:
            Inferred session character based on price action patterns
        """
        price_data = self.session_data.get("price_data", {})
        
        high = price_data.get("high", 0)
        low = price_data.get("low", 0)
        open_price = price_data.get("open", 0)
        close_price = price_data.get("close", 0)
        
        if not all([high, low, open_price, close_price]):
            return "unknown_pattern"
        
        range_size = high - low
        body_size = abs(close_price - open_price)
        
        # Simple pattern inference based on price action
        if range_size < 30:
            return "tight_consolidation"
        elif range_size > 100:
            if body_size / range_size < 0.3:
                return "volatile_with_major_redeliveries"
            else:
                return "strong_expansion"
        elif body_size / range_size < 0.5:
            return "expansion_then_consolidation"
        else:
            return "directional_movement"
    
    def _extract_liquidity_levels(self) -> List[float]:
        """Extract potential liquidity levels from price movements"""
        liquidity_levels = []
        
        # Get price data
        price_data = self.session_data.get("price_data", {})
        high = price_data.get("high", 23367.0)
        low = price_data.get("low", 23321.0)
        close = price_data.get("close", 23329.0)
        
        # Add key levels as potential liquidity
        liquidity_levels.extend([high, low])
        
        # Extract from price movements
        price_movements = self.session_data.get("price_movements", [])
        for movement in price_movements:
            price = movement.get("price", 0)
            context = movement.get("context", "")
            
            # Add levels that were touched but not fully broken
            if "touch" in movement.get("action", "") or "defend" in context:
                liquidity_levels.append(price)
        
        # Add some calculated levels around current price
        current_price = close
        for offset in [10, 20, 30, -10, -20, -30]:
            liquidity_levels.append(current_price + offset)
        
        return sorted(list(set(liquidity_levels)))  # Remove duplicates and sort
    
    def _extract_htf_structures(self) -> List[float]:
        """Extract HTF structure levels"""
        htf_structures = []
        
        price_data = self.session_data.get("price_data", {})
        current_price = price_data.get("close", 23329.0)
        
        # Generate HTF levels around current price
        # These would typically come from higher timeframe analysis
        base_levels = [
            current_price - 100, current_price - 75, current_price - 50,
            current_price - 25, current_price, current_price + 25,
            current_price + 50, current_price + 75, current_price + 100
        ]
        
        htf_structures.extend(base_levels)
        
        return htf_structures
    
    def create_path_generator(self) -> PathGenerator:
        """Create PathGenerator with extracted parameters and session character"""
        tracker_state = self.extract_tracker_state()
        session_params = self.extract_session_params()
        session_character = self.extract_session_character()
        
        return PathGenerator(tracker_state, session_params, session_character)
    
    def get_simulation_summary(self) -> Dict:
        """Get summary of extracted parameters for verification"""
        tracker_state = self.extract_tracker_state()
        session_params = self.extract_session_params()
        session_character = self.extract_session_character()
        
        return {
            "session_metadata": self.session_data.get("session_metadata", {}),
            "extracted_parameters": {
                "tracker_state": tracker_state,
                "session_params": session_params,
                "session_character": session_character
            },
            "parameter_sources": {
                "t_memory": "Derived from consolidation_duration / 12.0",
                "liquidity_gradient": "From gradient_dynamics.stability_factor * 0.02", 
                "gamma_enhanced": "From temporal_momentum.momentum_strength",
                "e_threshold_adj": "From energy_accumulation.total_energy",
                "session_character": "Extracted from price_data or inferred from price action"
            }
        }

def run_monte_carlo_from_session(session_file: str, n_simulations: int = 1000, 
                                duration_minutes: int = 180) -> Dict:
    """
    Run Monte Carlo simulation from session file
    
    Args:
        session_file: Path to session JSON file
        n_simulations: Number of simulations to run
        duration_minutes: Duration of each simulation
        
    Returns:
        Complete simulation results
    """
    # Load session data
    with open(session_file, 'r') as f:
        session_data = json.load(f)
    
    # Create adapter and generator
    adapter = MonteCarloAdapter(session_data)
    generator = adapter.create_path_generator()
    
    # Run simulations
    paths = generator.generate_paths(n_simulations, duration_minutes)
    
    # Calculate results
    prediction_bands = generator.calculate_prediction_bands(paths)
    event_analysis = generator.analyze_event_frequencies(paths)
    parameter_summary = adapter.get_simulation_summary()
    
    return {
        "monte_carlo_results": {
            "prediction_bands": prediction_bands,
            "event_analysis": event_analysis,
            "simulation_metadata": {
                "n_simulations": n_simulations,
                "duration_minutes": duration_minutes,
                "total_paths": len(paths)
            }
        },
        "parameter_extraction": parameter_summary,
        "original_session": session_data.get("session_metadata", {})
    }

# Example usage
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        session_file = sys.argv[1]
        print(f"Running Monte Carlo simulation from: {session_file}")
        
        results = run_monte_carlo_from_session(
            session_file, 
            n_simulations=500,  # Smaller for testing
            duration_minutes=180
        )
        
        # Print summary
        bands = results["monte_carlo_results"]["prediction_bands"]
        print(f"\nPrediction Results:")
        print(f"50th percentile final price: {bands['final_price_percentiles']['50th']:.2f}")
        print(f"75th percentile final price: {bands['final_price_percentiles']['75th']:.2f}")
        print(f"90th percentile range: {bands['range_percentiles']['90th']:.2f}")
        
        events = results["monte_carlo_results"]["event_analysis"]
        print(f"\nEvent Frequencies:")
        for event_type, freq in events['frequencies'].items():
            print(f"  {event_type}: {freq:.3f}")
            
        # Save results
        output_file = session_file.replace('.json', '_monte_carlo.json')
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\nResults saved to: {output_file}")
    else:
        print("Usage: python monte_carlo_adapter.py <session_file.json>")