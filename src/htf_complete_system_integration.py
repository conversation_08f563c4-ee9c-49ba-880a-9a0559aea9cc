#!/usr/bin/env python3
"""
HTF Complete System Integration - End-to-End Intelligence Pipeline

This module provides complete integration of the HTF Intelligence system:
1. HTF Intelligence Oversight Agent (System Coordination)
2. HTF Session Intelligence Parser (Level 1 → HTF Context)
3. HTF Master Controller Enhanced (Intelligence → Hawks Algorithm)

Complete data flow:
Level 1 Natural Language → Intelligence Parser → HTF Context Files → Enhanced Hawks → Predictions
"""

import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Import system components
import sys
sys.path.append(str(Path(__file__).parent))

try:
    from agents.htf_intelligence_oversight_agent import HTFIntelligenceOversightAgent
    OVERSIGHT_AVAILABLE = True
except ImportError:
    print("Warning: HTF Intelligence Oversight Agent not available")
    OVERSIGHT_AVAILABLE = False

try:
    from htf_master_controller_enhanced import HTFMasterControllerEnhanced
    ENHANCED_CONTROLLER_AVAILABLE = True
except ImportError:
    print("Warning: Enhanced HTF Master Controller not available")  
    ENHANCED_CONTROLLER_AVAILABLE = False

try:
    from htf_intelligence_integration import HTFIntelligenceIntegration
    INTELLIGENCE_INTEGRATION_AVAILABLE = True
except ImportError:
    print("Warning: HTF Intelligence Integration not available")
    INTELLIGENCE_INTEGRATION_AVAILABLE = False


class HTFCompleteSystemIntegration:
    """
    Complete HTF Intelligence System Integration
    
    Orchestrates the entire HTF intelligence pipeline from Level 1 natural
    language transcriptions through to HTF Hawks algorithm predictions.
    
    System Architecture:
    1. Oversight Agent: Monitors and coordinates all components
    2. Intelligence Parser: Level 1 → HTF context intelligence  
    3. Enhanced Controller: Intelligence → Hawks predictions
    4. Validation: End-to-end system health and accuracy
    """
    
    def __init__(self, base_dir: str = "/Users/<USER>/grok-claude-automation"):
        self.base_dir = Path(base_dir)
        
        # Initialize system components
        self.oversight_agent = None
        self.enhanced_controller = None
        self.intelligence_integration = None
        
        if OVERSIGHT_AVAILABLE:
            self.oversight_agent = HTFIntelligenceOversightAgent(base_dir)
            
        if ENHANCED_CONTROLLER_AVAILABLE:
            self.enhanced_controller = HTFMasterControllerEnhanced(base_dir)
            
        if INTELLIGENCE_INTEGRATION_AVAILABLE:
            self.intelligence_integration = HTFIntelligenceIntegration(base_dir)
        
        # System state
        self.system_initialized = False
        self.last_full_processing = None
        self.performance_metrics = {
            "sessions_processed": 0,
            "intelligence_events_created": 0,
            "hawks_predictions_generated": 0,
            "system_uptime": datetime.now(),
            "error_count": 0
        }
        
        self._setup_logging()
        self._validate_system_components()
        
    def _setup_logging(self):
        """Setup comprehensive system logging."""
        self.logger = logging.getLogger("HTFCompleteSystem")
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - HTF_System - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def _validate_system_components(self):
        """Validate all system components are available."""
        components_status = {
            "oversight_agent": OVERSIGHT_AVAILABLE and self.oversight_agent is not None,
            "enhanced_controller": ENHANCED_CONTROLLER_AVAILABLE and self.enhanced_controller is not None,
            "intelligence_integration": INTELLIGENCE_INTEGRATION_AVAILABLE and self.intelligence_integration is not None
        }
        
        available_components = sum(components_status.values())
        total_components = len(components_status)
        
        if available_components == total_components:
            self.system_initialized = True
            self.logger.info(f"✅ Complete HTF system initialized: {available_components}/{total_components} components available")
        else:
            self.logger.warning(f"⚠️ Partial HTF system: {available_components}/{total_components} components available")
            missing = [comp for comp, available in components_status.items() if not available]
            self.logger.warning(f"Missing components: {missing}")
        
        return components_status
    
    def process_complete_pipeline(self, session_file_path: str) -> Dict[str, Any]:
        """Process a session through the complete HTF intelligence pipeline."""
        session_name = Path(session_file_path).name
        self.logger.info(f"🔄 Processing complete HTF pipeline for {session_name}")
        
        pipeline_results = {
            "session": session_name,
            "timestamp": datetime.now().isoformat(),
            "steps": {
                "level_1_validation": {"status": "pending", "details": {}},
                "intelligence_processing": {"status": "pending", "details": {}},
                "oversight_coordination": {"status": "pending", "details": {}},
                "hawks_enhancement": {"status": "pending", "details": {}},
                "prediction_generation": {"status": "pending", "details": {}}
            },
            "overall_status": "processing",
            "processing_time": 0.0,
            "errors": []
        }
        
        start_time = time.time()
        
        try:
            # Step 1: Level 1 Data Validation
            self.logger.info(f"Step 1: Validating Level 1 data for {session_name}")
            
            if not Path(session_file_path).exists():
                pipeline_results["steps"]["level_1_validation"] = {
                    "status": "error",
                    "details": {"error": "Session file not found"}
                }
                pipeline_results["errors"].append("Session file not found")
            else:
                try:
                    with open(session_file_path, 'r') as f:
                        session_data = json.load(f)
                    
                    # Validate required structure
                    required_fields = ["session_metadata", "price_data", "price_movements"]
                    missing_fields = [field for field in required_fields if field not in session_data]
                    
                    if missing_fields:
                        pipeline_results["steps"]["level_1_validation"] = {
                            "status": "warning",
                            "details": {"missing_fields": missing_fields}
                        }
                    else:
                        pipeline_results["steps"]["level_1_validation"] = {
                            "status": "success",
                            "details": {"validation": "complete"}
                        }
                        
                except json.JSONDecodeError as e:
                    pipeline_results["steps"]["level_1_validation"] = {
                        "status": "error",
                        "details": {"error": f"JSON decode error: {str(e)}"}
                    }
                    pipeline_results["errors"].append(f"JSON decode error: {str(e)}")
            
            # Step 2: Intelligence Processing
            if pipeline_results["steps"]["level_1_validation"]["status"] in ["success", "warning"]:
                self.logger.info(f"Step 2: Processing HTF intelligence for {session_name}")
                
                if self.intelligence_integration:
                    try:
                        intelligence_result = self.intelligence_integration.process_session_with_integration(session_file_path)
                        
                        pipeline_results["steps"]["intelligence_processing"] = {
                            "status": intelligence_result["status"],
                            "details": {
                                "events_processed": intelligence_result.get("events_processed", 0),
                                "htf_context_updated": intelligence_result.get("htf_context_updated", False)
                            }
                        }
                        
                        if intelligence_result["status"] == "success":
                            self.performance_metrics["intelligence_events_created"] += intelligence_result.get("events_processed", 0)
                            
                    except Exception as e:
                        pipeline_results["steps"]["intelligence_processing"] = {
                            "status": "error",
                            "details": {"error": str(e)}
                        }
                        pipeline_results["errors"].append(f"Intelligence processing error: {str(e)}")
                else:
                    pipeline_results["steps"]["intelligence_processing"] = {
                        "status": "skipped",
                        "details": {"reason": "Intelligence integration component not available"}
                    }
            
            # Step 3: Oversight Coordination
            if pipeline_results["steps"]["intelligence_processing"]["status"] in ["success", "partial_success"]:
                self.logger.info(f"Step 3: Running oversight coordination for {session_name}")
                
                if self.oversight_agent:
                    try:
                        coordination_result = self.oversight_agent.coordinate_session_processing(session_file_path)
                        
                        success_steps = sum([
                            coordination_result.level_1_processed,
                            coordination_result.intelligence_processed,
                            coordination_result.abcd_pipeline_processed,
                            coordination_result.htf_context_updated,
                            coordination_result.hawks_data_ready
                        ])
                        
                        pipeline_results["steps"]["oversight_coordination"] = {
                            "status": "success" if success_steps >= 4 else "partial_success" if success_steps >= 2 else "error",
                            "details": {
                                "steps_completed": f"{success_steps}/5",
                                "processing_time": coordination_result.processing_time,
                                "errors": len(coordination_result.errors)
                            }
                        }
                        
                    except Exception as e:
                        pipeline_results["steps"]["oversight_coordination"] = {
                            "status": "error",
                            "details": {"error": str(e)}
                        }
                        pipeline_results["errors"].append(f"Oversight coordination error: {str(e)}")
                else:
                    pipeline_results["steps"]["oversight_coordination"] = {
                        "status": "skipped",
                        "details": {"reason": "Oversight agent component not available"}
                    }
            
            # Step 4: Hawks Enhancement
            if pipeline_results["steps"]["oversight_coordination"]["status"] in ["success", "partial_success"]:
                self.logger.info(f"Step 4: Enhanced HTF intensity calculation for {session_name}")
                
                if self.enhanced_controller:
                    try:
                        # Calculate HTF intensity with intelligence data
                        htf_intensity = self.enhanced_controller.calculate_htf_intensity()
                        intelligence_summary = self.enhanced_controller.get_intelligence_summary()
                        
                        pipeline_results["steps"]["hawks_enhancement"] = {
                            "status": "success",
                            "details": {
                                "htf_intensity": htf_intensity,
                                "intelligence_events": intelligence_summary["total_intelligence_events"],
                                "threshold_exceeded": intelligence_summary["threshold_exceeded"]
                            }
                        }
                        
                    except Exception as e:
                        pipeline_results["steps"]["hawks_enhancement"] = {
                            "status": "error",
                            "details": {"error": str(e)}
                        }
                        pipeline_results["errors"].append(f"Hawks enhancement error: {str(e)}")
                else:
                    pipeline_results["steps"]["hawks_enhancement"] = {
                        "status": "skipped",
                        "details": {"reason": "Enhanced controller component not available"}
                    }
            
            # Step 5: Prediction Generation
            if pipeline_results["steps"]["hawks_enhancement"]["status"] == "success":
                self.logger.info(f"Step 5: Generating intelligence-based predictions for {session_name}")
                
                if self.enhanced_controller:
                    try:
                        # Generate activation signal
                        activation_signal = self.enhanced_controller.generate_intelligence_activation_signal()
                        
                        if activation_signal:
                            pipeline_results["steps"]["prediction_generation"] = {
                                "status": "success",
                                "details": {
                                    "activation_generated": True,
                                    "target_sessions": activation_signal.target_sessions,
                                    "cascade_type": activation_signal.cascade_type,
                                    "confidence_boost": activation_signal.confidence_boost,
                                    "intelligence_events": len(activation_signal.intelligence_events)
                                }
                            }
                            self.performance_metrics["hawks_predictions_generated"] += 1
                        else:
                            pipeline_results["steps"]["prediction_generation"] = {
                                "status": "no_activation",
                                "details": {"reason": "HTF intensity below activation threshold"}
                            }
                            
                    except Exception as e:
                        pipeline_results["steps"]["prediction_generation"] = {
                            "status": "error",
                            "details": {"error": str(e)}
                        }
                        pipeline_results["errors"].append(f"Prediction generation error: {str(e)}")
                else:
                    pipeline_results["steps"]["prediction_generation"] = {
                        "status": "skipped",
                        "details": {"reason": "Enhanced controller component not available"}
                    }
            
        except Exception as e:
            pipeline_results["errors"].append(f"Pipeline error: {str(e)}")
            self.performance_metrics["error_count"] += 1
            self.logger.error(f"Pipeline error for {session_name}: {e}")
        
        # Calculate overall status
        pipeline_results["processing_time"] = time.time() - start_time
        
        successful_steps = sum(1 for step in pipeline_results["steps"].values() 
                             if step["status"] in ["success", "partial_success", "no_activation"])
        total_steps = len(pipeline_results["steps"])
        
        if successful_steps == total_steps:
            pipeline_results["overall_status"] = "success"
        elif successful_steps >= total_steps * 0.6:
            pipeline_results["overall_status"] = "partial_success"
        else:
            pipeline_results["overall_status"] = "failed"
        
        self.performance_metrics["sessions_processed"] += 1
        
        self.logger.info(f"🎯 Complete pipeline processing for {session_name}: {pipeline_results['overall_status']} ({successful_steps}/{total_steps} steps), {pipeline_results['processing_time']:.2f}s")
        
        return pipeline_results
    
    def batch_process_all_sessions(self) -> Dict[str, Any]:
        """Process all Level 1 sessions through complete pipeline."""
        self.logger.info("🚀 Starting batch processing of all sessions through complete HTF pipeline...")
        
        sessions_dir = self.base_dir / "data" / "sessions" / "level_1"
        session_files = list(sessions_dir.glob("*_Lvl-1_*.json"))
        
        batch_results = {
            "total_sessions": len(session_files),
            "processed_sessions": 0,
            "successful_sessions": 0,
            "partial_success_sessions": 0,
            "failed_sessions": 0,
            "total_processing_time": 0.0,
            "session_details": [],
            "system_performance": {},
            "timestamp": datetime.now().isoformat()
        }
        
        for session_file in session_files:
            try:
                pipeline_result = self.process_complete_pipeline(str(session_file))
                
                batch_results["processed_sessions"] += 1
                batch_results["total_processing_time"] += pipeline_result["processing_time"]
                
                if pipeline_result["overall_status"] == "success":
                    batch_results["successful_sessions"] += 1
                elif pipeline_result["overall_status"] == "partial_success":
                    batch_results["partial_success_sessions"] += 1
                else:
                    batch_results["failed_sessions"] += 1
                
                batch_results["session_details"].append({
                    "session": pipeline_result["session"],
                    "status": pipeline_result["overall_status"],
                    "errors": len(pipeline_result["errors"]),
                    "processing_time": pipeline_result["processing_time"]
                })
                
            except Exception as e:
                self.logger.error(f"Batch processing error for {session_file.name}: {e}")
                batch_results["failed_sessions"] += 1
        
        # System performance summary
        batch_results["system_performance"] = {
            "success_rate": f"{batch_results['successful_sessions']}/{batch_results['total_sessions']} ({batch_results['successful_sessions']/batch_results['total_sessions']*100:.1f}%)",
            "average_processing_time": batch_results["total_processing_time"] / batch_results["processed_sessions"] if batch_results["processed_sessions"] > 0 else 0,
            "intelligence_events_created": self.performance_metrics["intelligence_events_created"],
            "hawks_predictions_generated": self.performance_metrics["hawks_predictions_generated"],
            "error_count": self.performance_metrics["error_count"]
        }
        
        self.last_full_processing = datetime.now()
        
        self.logger.info(f"🎯 Batch processing complete: {batch_results['system_performance']['success_rate']} success rate, {batch_results['system_performance']['intelligence_events_created']} intelligence events, {batch_results['system_performance']['hawks_predictions_generated']} predictions")
        
        return batch_results
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        uptime = datetime.now() - self.performance_metrics["system_uptime"]
        
        # Component status
        components_status = self._validate_system_components()
        
        # System health from oversight agent
        system_health = None
        if self.oversight_agent:
            try:
                health_report = self.oversight_agent.check_system_health()
                system_health = {
                    "overall_status": health_report["overall_status"],
                    "critical_issues": len(health_report.get("critical_issues", [])),
                    "warnings": len(health_report.get("warnings", []))
                }
            except Exception as e:
                system_health = {"error": str(e)}
        
        # Intelligence summary from enhanced controller
        intelligence_summary = None
        if self.enhanced_controller:
            try:
                intelligence_summary = self.enhanced_controller.get_intelligence_summary()
            except Exception as e:
                intelligence_summary = {"error": str(e)}
        
        return {
            "system_initialized": self.system_initialized,
            "system_uptime": str(uptime),
            "components_status": components_status,
            "system_health": system_health,
            "intelligence_summary": intelligence_summary,
            "performance_metrics": self.performance_metrics,
            "last_full_processing": self.last_full_processing.isoformat() if self.last_full_processing else None
        }
    
    def generate_system_report(self) -> Dict[str, Any]:
        """Generate comprehensive system report."""
        system_status = self.get_system_status()
        
        report = {
            "report_timestamp": datetime.now().isoformat(),
            "system_overview": {
                "status": "operational" if system_status["system_initialized"] else "partial",
                "uptime": system_status["system_uptime"],
                "components_available": sum(system_status["components_status"].values()),
                "total_components": len(system_status["components_status"])
            },
            "performance_summary": system_status["performance_metrics"],
            "intelligence_integration": system_status.get("intelligence_summary", {}),
            "system_health": system_status.get("system_health", {}),
            "recommendations": []
        }
        
        # Generate recommendations
        if not system_status["system_initialized"]:
            report["recommendations"].append("Install missing system components for full functionality")
        
        if system_status.get("system_health", {}).get("critical_issues", 0) > 0:
            report["recommendations"].append("Address critical system health issues")
        
        if system_status.get("intelligence_summary", {}).get("total_intelligence_events", 0) < 10:
            report["recommendations"].append("Process more Level 1 sessions to increase intelligence coverage")
        
        if system_status["performance_metrics"]["error_count"] > 0:
            report["recommendations"].append("Review and resolve system errors")
        
        return report


def main():
    """Demo Complete HTF System Integration."""
    print("🌟 HTF Complete System Integration - Demo")
    print("=" * 60)
    
    system = HTFCompleteSystemIntegration()
    
    # System status
    print("\n📊 System Status:")
    status = system.get_system_status()
    
    print(f"System Initialized: {'✅ Yes' if status['system_initialized'] else '❌ No'}")
    print(f"System Uptime: {status['system_uptime']}")
    
    print(f"\n🔧 Components Status:")
    for component, available in status["components_status"].items():
        print(f"  • {component}: {'✅ Available' if available else '❌ Missing'}")
    
    if status.get("intelligence_summary"):
        intel = status["intelligence_summary"]
        print(f"\n🧠 Intelligence Summary:")
        print(f"  • Intelligence Events: {intel.get('total_intelligence_events', 0)}")
        print(f"  • HTF Intensity: {intel.get('current_htf_intensity', 0):.2f}")
        print(f"  • Threshold Exceeded: {'✅ Yes' if intel.get('threshold_exceeded', False) else '❌ No'}")
    
    # Test single session processing
    test_session = "/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYAM_Lvl-1_2025_07_28.json"
    if Path(test_session).exists():
        print(f"\n🔄 Testing complete pipeline with: {Path(test_session).name}")
        pipeline_result = system.process_complete_pipeline(test_session)
        
        print(f"Overall Status: {pipeline_result['overall_status']}")
        print(f"Processing Time: {pipeline_result['processing_time']:.2f}s")
        print(f"Errors: {len(pipeline_result['errors'])}")
        
        print(f"\n📋 Pipeline Steps:")
        for step_name, step_result in pipeline_result["steps"].items():
            status_emoji = "✅" if step_result["status"] == "success" else "⚠️" if step_result["status"] in ["partial_success", "warning", "no_activation"] else "❌"
            print(f"  {status_emoji} {step_name}: {step_result['status']}")
    
    # Generate system report
    print(f"\n📈 System Report:")
    report = system.generate_system_report()
    
    print(f"System Status: {report['system_overview']['status']}")
    print(f"Components: {report['system_overview']['components_available']}/{report['system_overview']['total_components']}")
    print(f"Sessions Processed: {report['performance_summary']['sessions_processed']}")
    print(f"Intelligence Events: {report['performance_summary']['intelligence_events_created']}")
    print(f"Hawks Predictions: {report['performance_summary']['hawks_predictions_generated']}")
    
    if report["recommendations"]:
        print(f"\n💡 Recommendations:")
        for rec in report["recommendations"]:
            print(f"  • {rec}")
    
    print(f"\n🎯 HTF Complete System Integration Demonstration Complete!")
    print(f"The system successfully demonstrates end-to-end intelligence processing:")
    print(f"  Level 1 Natural Language → Intelligence Processing → HTF Context → Hawks Algorithm")


if __name__ == "__main__":
    main()