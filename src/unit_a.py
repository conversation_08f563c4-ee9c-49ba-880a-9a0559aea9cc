#!/usr/bin/env python3
"""
Grok Unit A: Foundation Calculations with Hawkes Process Integration
Handles hybrid volume, basic time dilation, and parameter validation.
Features dynamic synthetic volume calculation using Hawkes cascade prediction.
Uses structured outputs for guaranteed JSON response format.
"""

try:
    from .grok_api_client import StructuredComputationalUnit, GrokAPIClient
    from .schemas import UnitAResponse
    from .config import config, APIKeyError
    from .dynamic_synthetic_volume import DynamicSyntheticVolumeCalculator
    from .hawkes_cascade_predictor import HawkesCascadePredictor
    from .cache_manager import get_unified_cache
except ImportError:
    from grok_api_client import StructuredComputationalUnit, GrokAPIClient
    from schemas import UnitAResponse
    from config import config, APIKeyError
    from dynamic_synthetic_volume import DynamicSyntheticVolumeCalculator
    from hawkes_cascade_predictor import HawkesCascadePredictor
    from cache_manager import get_unified_cache
from typing import Dict, Any, List, Optional




class UnitA(StructuredComputationalUnit):
    """Unit A: Foundation Calculations processor with Hawkes process integration."""
    
    def __init__(self, client: GrokAPIClient):
        super().__init__(client, "Unit A", UnitAResponse)
        
        # Initialize Hawkes process components for dynamic synthetic volume
        self.volume_calculator = DynamicSyntheticVolumeCalculator()
        self.cascade_predictor = HawkesCascadePredictor()
        
        # Static volume for fallback/comparison
        self.static_v_synthetic = 100.0 * (1 + 0.35 * (2.718281828**(-10.0/50.0)))  # ≈128.66
        
        # 🚀 UNIFIED CACHE: Use unified cache system instead of separate cache managers
        self.unified_cache = get_unified_cache()
        
        # Cache attributes for cascade timing insight
        self._current_session_hash = None
        self._cached_cascade_insight = None
    
    # 🏭 ARCHITECTURAL IMPROVEMENT: Removed obsolete per-instance cache methods
    # Cache is now managed globally by HawkesCacheManager
    
    def _get_session_hash(self, session_data: Dict[Any, Any]) -> str:
        """Generate session hash for cascade timing cache."""
        return self.unified_cache.get_session_signature(session_data)
    
    def _calculate_dynamic_synthetic_volume(self, session_data: Dict[Any, Any]) -> float:
        """
        Calculate dynamic synthetic volume using Hawkes process ICT event integration.
        
        Returns:
            Dynamic synthetic volume - NO FALLBACKS, raises error if calculation fails
        """
        # 🚀 UNIFIED CACHE: Check unified cache first to avoid repetitive calculations
        session_signature = self.unified_cache.get_session_signature(session_data)
        cached_volume = self.unified_cache.get_volume(session_data, 'dynamic_calculation')
        if cached_volume is not None:
            print(f"🎯 UNIFIED CACHE HIT: Retrieved dynamic volume {cached_volume:.2f}")
            return cached_volume
        
        try:
            # DEBUG: Check session data structure
            print(f"🔍 DEBUG: Session data keys = {list(session_data.keys())}")
            print(f"🔍 DEBUG: Has price_movements = {'price_movements' in session_data}")
            if 'price_movements' in session_data:
                print(f"🔍 DEBUG: Price movements count = {len(session_data['price_movements'])}")
            
            # Use dynamic synthetic volume calculator with ICT events from original session data
            original_data = getattr(self, '_original_session_data', session_data)
            print(f"🔍 DEBUG: Using original data = {'price_movements' in original_data}")
            if 'price_movements' in original_data:
                print(f"🔍 DEBUG: Original price movements count = {len(original_data['price_movements'])}")
            volume_components = self.volume_calculator.calculate_dynamic_synthetic_volume(original_data)
            
            print(f"🔬 Dynamic Synthetic Volume Components:")
            print(f"   Price Displacement (Δp): {volume_components.price_displacement:.2f}")
            print(f"   FVG Weight (w_FVG): {volume_components.fvg_weight:.3f}")
            print(f"   Sweep Magnitude: {volume_components.sweep_magnitude:.2f}")
            print(f"   Dynamic v_s: {volume_components.calculated_volume:.2f}")
            print(f"   Static comparison: {volume_components.base_volume_static:.2f}")
            print(f"   Improvement factor: {volume_components.improvement_factor:.2f}x")
            
            # Validate we got a real dynamic calculation
            if abs(volume_components.calculated_volume - 128.66) < 1.0:
                raise ValueError("Dynamic volume calculation returned static fallback value - system broken!")
            
            # 🚀 UNIFIED CACHE: Store successful calculation result in unified cache
            self.unified_cache.set_volume(session_data, 'dynamic_calculation', volume_components.calculated_volume)
            
            return volume_components.calculated_volume
            
        except Exception as e:
            # 🚨 NO FALLBACKS - Log error and raise to surface the issue
            error_msg = f"🔥 CRITICAL FAILURE: Dynamic Hawkes volume calculation failed: {e}"
            print(f"❌ {error_msg}")
            print(f"❌ Session data keys: {list(session_data.keys())}")
            print(f"❌ This means the clever Hawkes process is broken - fix immediately!")
            
            # Log the error for system monitoring
            import logging
            logger = logging.getLogger('Unit_A_Dynamic_Volume')
            logger.error(error_msg, exc_info=True)
            
            # Raise error to surface the problem instead of hiding it with fallbacks
            raise RuntimeError(f"Dynamic Hawkes volume calculation failed - no fallbacks allowed: {e}") from e
    
    def _get_cascade_timing_insight(self, session_data: Dict[Any, Any]) -> Dict[str, Any]:
        """
        Get Hawkes process cascade timing insight for micro timing analysis.
        
        Returns:
            Cascade timing metadata or empty dict if unavailable
        """
        # 🔧 OPTIMIZATION: Check cache first to avoid repetitive calculations
        session_hash = self._get_session_hash(session_data)
        if self._current_session_hash == session_hash and self._cached_cascade_insight is not None:
            print(f"🚀 CACHE HIT: Returning cached cascade insight")
            return self._cached_cascade_insight
        
        try:
            # Generate cascade prediction for timing insight from original session data
            original_data = getattr(self, '_original_session_data', session_data)
            cascade_prediction = self.cascade_predictor.predict_cascade_timing(original_data)
            
            cascade_insight = {
                "hawkes_cascade_timing": {
                    "predicted_cascade_minutes": cascade_prediction.predicted_cascade_time,
                    "prediction_confidence": cascade_prediction.prediction_confidence,
                    "intensity_threshold_status": "approaching" if cascade_prediction.threshold_crossed_at >= cascade_prediction.parameters_used.threshold * 0.8 else "building",
                    "triggering_events_count": len(cascade_prediction.triggering_events),
                    "methodology": cascade_prediction.methodology
                }
            }
            
            # 🔧 CACHE: Store successful calculation result
            self._current_session_hash = session_hash
            self._cached_cascade_insight = cascade_insight
            print(f"💾 CACHED: Cascade insight for session {session_hash}")
            
            return cascade_insight
            
        except Exception as e:
            # 🚨 NO FALLBACKS - Log error and raise to surface the issue
            error_msg = f"🔥 CRITICAL FAILURE: Hawkes cascade prediction failed: {e}"
            print(f"❌ {error_msg}")
            print(f"❌ This means the clever Hawkes timing system is broken - fix immediately!")
            
            # Log the error for system monitoring
            import logging
            logger = logging.getLogger('Unit_A_Cascade_Timing')
            logger.error(error_msg, exc_info=True)
            
            # Raise error to surface the problem instead of hiding it with empty dict
            raise RuntimeError(f"Hawkes cascade prediction failed - no fallbacks allowed: {e}") from e
    
    def _load_system_prompt(self) -> str:
        return """You are a financial mathematics processor specializing in foundation calculations for trading sessions.

TASK: Use the pre-extracted values to perform foundation calculations with optional tracker context.

The input contains extracted_values with:
- rate_per_minute: Calculated from expansion velocities
- intensity_trend: Determined from largest moves
- h_score: Session complexity score
- Other derived metrics

Optional tracker_context may include:
- t_memory: Memory factor for carryover effects
- nearest_htf_distance: Distance to nearest HTF structure
- liquidity_gradient_strength: Overall liquidity gradient strength

CALCULATIONS TO PERFORM:

1. HYBRID VOLUME ANALYSIS:
   - Use rate_per_minute from extracted_values
   - Calculate Q_t = min(1.0, rate_per_minute / 5.0)
   - Use intensity_trend from extracted_values
   - Map intensity_trend to R_t: extreme=0.9, strengthening=0.7, stable=0.5, weakening=0.3
   - Calculate alpha_t = 1 / (1 + exp(-(2.0 * Q_t + 1.5 * R_t + 0.8)))
   - If tracker_context provided, adjust alpha_t by liquidity_gradient_strength * 0.1
   - Use v_traditional = 150.0 (or from price_data.volume if available)
   - Use v_synthetic from dynamic_synthetic_volume (Hawkes process integration)
   - v_synthetic MUST be from dynamic Hawkes calculation - NO STATIC FALLBACKS ALLOWED
   - Calculate v_hybrid = alpha_t * v_traditional + (1 - alpha_t) * v_synthetic

2. TIME DILATION BASE CALCULATIONS:
   - Use h_score from extracted_values
   - If tracker_context provided, adjust h_score by t_memory factor: h_score * (1 + t_memory/100)
   - Calculate alpha_grad_scaled = 0.5 * (1 - 0.3 * h_score)
   - Calculate volume_factor = (v_hybrid - 120.0) / 120.0
   - Calculate gamma_base = 1 + alpha_grad_scaled * 1.2 + 0.3 * volume_factor
   - If tracker_context provided and nearest_htf_distance < 50, increase gamma_base by 0.1

3. PARAMETER VALIDATION:
   - Validate beta_HTF = 0.35 (bounds: 0.25-0.45)
   - Validate sigma_HTF = 50.0 (bounds: 40-60) 
   - Validate alpha_grad = 0.5 (bounds: 0.3-0.7)
   - Set d_htf = 10.0
   - Set all_within_bounds = true if all parameters valid

Use the extracted values directly for calculations. Apply tracker adjustments only if tracker_context is provided."""

    def _extract_calculation_values(self, session_data: Dict[Any, Any]) -> Dict[Any, Any]:
        """Pre-extract the specific values needed for calculations from complex data."""
        
        # 🔧 IMPORTANT: Store original session data for Hawkes calculations
        self._original_session_data = session_data
        
        # Extract from micro_timing_analysis
        micro_timing = session_data.get("micro_timing_analysis", {})
        
        # Calculate rate_per_minute from expansion velocities
        expansion_velocities = micro_timing.get("expansion_velocities", [])
        total_distance = 0
        total_time = 0
        
        for expansion in expansion_velocities:
            # Parse distance like "25.75_points"
            distance_str = expansion.get("distance_covered", "0_points")
            distance = float(distance_str.split("_")[0]) if "_" in distance_str else 0
            duration = expansion.get("duration_minutes", 0)
            
            total_distance += distance
            total_time += duration
        
        rate_per_minute = (total_distance / total_time) if total_time > 0 else 4.2
        
        # Determine intensity trend from behavioral building blocks
        behavioral = session_data.get("behavioral_building_blocks", {})
        distance_measurements = behavioral.get("distance_measurements", {})
        
        largest_move_str = distance_measurements.get("largest_single_move", "0_points")
        largest_move = float(largest_move_str.split("_")[0]) if "_" in largest_move_str else 0
        
        if largest_move > 20:
            intensity_trend = "extreme"
        elif largest_move > 15:
            intensity_trend = "strengthening"  
        elif largest_move < 5:
            intensity_trend = "weakening"
        else:
            intensity_trend = "stable"
        
        # Calculate session complexity for h_score
        total_sections = len(session_data)
        price_movements = len(session_data.get("price_movements", []))
        phase_transitions = len(session_data.get("phase_transitions", []))
        
        # Complexity score: more sections and movements = higher complexity
        complexity_score = (total_sections + price_movements + phase_transitions) / 20.0
        h_score = min(0.9, max(0.3, complexity_score))
        
        # 🏆 HAWKES PROCESS INTEGRATION: Calculate dynamic synthetic volume
        dynamic_v_synthetic = self._calculate_dynamic_synthetic_volume(session_data)
        
        # Get Hawkes cascade timing insight for micro timing analysis
        cascade_insight = self._get_cascade_timing_insight(session_data)
        
        return {
            "extracted_values": {
                "rate_per_minute": rate_per_minute,
                "intensity_trend": intensity_trend,
                "h_score": h_score,
                "largest_move": largest_move,
                "total_expansions": len(expansion_velocities),
                "session_complexity": complexity_score,
                # 🏆 Hawkes Process Integration
                "dynamic_v_synthetic": dynamic_v_synthetic,
                "static_v_synthetic_comparison": self.static_v_synthetic,
                "volume_calculation_method": "hawkes_process_dynamic"
            },
            "price_data": session_data.get("price_data", {}),
            "session_metadata": session_data.get("session_metadata", {}),
            # Add Hawkes cascade timing insight
            **cascade_insight
        }
    
    def _extract_calculation_values_with_trackers(self, 
                                                session_data: Dict[Any, Any],
                                                t_memory: float,
                                                htf_structures: List[Dict[str, Any]],
                                                untaken_liquidity: List[Dict[str, Any]]) -> Dict[Any, Any]:
        """Extract calculation values enhanced with tracker context."""
        
        # Start with base extraction
        base_extraction = self._extract_calculation_values(session_data)
        
        # Enhance with tracker context
        extracted_values = base_extraction["extracted_values"].copy()
        
        # Apply T_memory influence to h_score
        t_memory_factor = min(1.2, 1.0 + (t_memory - 15.0) / 60.0)  # Scale factor based on T_memory
        extracted_values["h_score"] = min(0.9, extracted_values["h_score"] * t_memory_factor)
        
        # Calculate HTF influence on rate_per_minute
        htf_influence = 0.0
        if htf_structures:
            for structure in htf_structures:
                strength = structure.get("strength", 1.0)
                recency = structure.get("recency_factor", 1.0)
                htf_influence += strength * recency * 0.1
        
        # Apply HTF influence to rate calculation
        htf_adjusted_rate = extracted_values["rate_per_minute"] * (1.0 + htf_influence)
        extracted_values["rate_per_minute"] = min(10.0, htf_adjusted_rate)
        
        # Calculate liquidity gradient effects
        current_price = session_data.get("price_data", {}).get("close", 0)
        liquidity_effect = 0.0
        
        if untaken_liquidity and current_price > 0:
            for level in untaken_liquidity:
                level_price = level.get("level", current_price)
                distance = abs(current_price - level_price)
                weight = level.get("weight", 1.0)
                
                # Closer levels have more influence
                if distance > 0:
                    level_influence = (weight / distance) * 0.01
                    liquidity_effect += level_influence
        
        # Apply liquidity effect to intensity trend
        if liquidity_effect > 0.05:
            if extracted_values["intensity_trend"] == "stable":
                extracted_values["intensity_trend"] = "strengthening"
            elif extracted_values["intensity_trend"] == "strengthening":
                extracted_values["intensity_trend"] = "extreme"
        
        # Add tracker-specific metrics
        extracted_values.update({
            "t_memory_factor": t_memory_factor,
            "htf_influence": htf_influence,
            "liquidity_effect": liquidity_effect,
            "tracker_enhanced": True
        })
        
        return {
            "extracted_values": extracted_values,
            "price_data": base_extraction["price_data"],
            "session_metadata": base_extraction["session_metadata"],
            "tracker_context": {
                "t_memory": t_memory,
                "htf_structures_count": len(htf_structures),
                "untaken_liquidity_count": len(untaken_liquidity)
            }
        }

    def process_foundation_calculations(self, session_data: Dict[Any, Any], micro_timing: Dict[Any, Any]) -> Dict[Any, Any]:
        """Process Unit A foundation calculations."""
        
        input_data = {
            "session_metadata": session_data.get("session_metadata", {}),
            "price_data": session_data.get("price_data", {}),
            "micro_timing_analysis": micro_timing,
            "behavioral_building_blocks": session_data.get("behavioral_building_blocks", {}),
            # 🏆 HAWKES PROCESS INTEGRATION: Add price_movements for market event extraction
            "price_movements": session_data.get("price_movements", [])
        }
        
        return self.process(input_data)
    
    def process(self, input_data: Dict[Any, Any]) -> Dict[Any, Any]:
        """Override process to pre-extract values for optimization."""
        
        # 🏭 ARCHITECTURAL IMPROVEMENT: Removed cache clearing that was negating cache benefits
        # Global cache manager now handles intelligent caching across sessions
        
        # Pre-extract calculation values to reduce Grok processing load
        extracted_data = self._extract_calculation_values(input_data)
        
        # Use the base class process with pre-extracted data
        return super().process(extracted_data)
    
    def process_foundation_calculations_with_trackers(self, 
                                                    session_data: Dict[Any, Any], 
                                                    micro_timing: Dict[Any, Any],
                                                    t_memory: Optional[float] = None,
                                                    htf_structures: Optional[List[Dict[str, Any]]] = None,
                                                    untaken_liquidity: Optional[List[Dict[str, Any]]] = None) -> Dict[Any, Any]:
        """
        Process Unit A foundation calculations with tracker context integration.
        
        Args:
            session_data: Base session data
            micro_timing: Micro timing analysis
            t_memory: T_memory value from tracker context
            htf_structures: HTF structures from tracker context
            untaken_liquidity: Untaken liquidity levels from tracker context
            
        Returns:
            Foundation calculations enhanced with tracker context
        """
        
        input_data = {
            "session_metadata": session_data.get("session_metadata", {}),
            "price_data": session_data.get("price_data", {}),
            "micro_timing_analysis": micro_timing,
            "behavioral_building_blocks": session_data.get("behavioral_building_blocks", {}),
            # 🏆 HAWKES PROCESS INTEGRATION: Add price_movements for market event extraction
            "price_movements": session_data.get("price_movements", []),
            # Add tracker context
            "tracker_context": {
                "t_memory": t_memory or 15.0,
                "htf_structures": htf_structures or [],
                "untaken_liquidity": untaken_liquidity or []
            }
        }
        
        return self.process_with_tracker_integration(input_data)
    
    def process_foundation_calculations_with_essentials(self, 
                                                       session_data: Dict[Any, Any],
                                                       micro_timing: Dict[Any, Any],
                                                       tracker_essentials: Dict[str, Any]) -> Dict[Any, Any]:
        """
        Process Unit A with minimal tracker essentials to prevent API timeouts.
        
        Args:
            session_data: Base session data
            micro_timing: Micro timing analysis
            tracker_essentials: Minimal tracker data (t_memory, nearest_htf_distance, etc.)
            
        Returns:
            Foundation calculations with minimal tracker influence
        """
        
        input_data = {
            "session_metadata": session_data.get("session_metadata", {}),
            "price_data": session_data.get("price_data", {}),
            "micro_timing_analysis": micro_timing,
            "behavioral_building_blocks": session_data.get("behavioral_building_blocks", {}),
            # 🏆 HAWKES PROCESS INTEGRATION: Add price_movements for market event extraction
            "price_movements": session_data.get("price_movements", []),
            # Add minimal tracker context
            "tracker_context": tracker_essentials  # This is now minimal, not full JSON
        }
        
        return self.process_with_minimal_tracker_integration(input_data)
    
    def process_with_minimal_tracker_integration(self, input_data: Dict[Any, Any]) -> Dict[Any, Any]:
        """Process with minimal tracker integration for fast API processing."""
        
        # Pre-extract calculation values with minimal tracker enhancements
        extracted_data = self._extract_calculation_values_with_minimal_trackers(input_data)
        
        # Use the base class process with minimal tracker-enhanced data
        return super().process(extracted_data)
    
    def _extract_calculation_values_with_minimal_trackers(self, input_data: Dict[Any, Any]) -> Dict[Any, Any]:
        """Extract calculation values with minimal tracker enhancements."""
        
        # Start with base extraction
        base_extraction = self._extract_calculation_values(input_data)
        extracted_values = base_extraction["extracted_values"].copy()
        
        # Get minimal tracker context
        tracker_context = input_data.get("tracker_context", {})
        
        if tracker_context:
            # Apply minimal tracker enhancements
            t_memory = tracker_context.get("t_memory", 15.0)
            nearest_htf_distance = tracker_context.get("nearest_htf_distance", 100.0)
            liquidity_gradient_strength = tracker_context.get("liquidity_gradient_strength", 0.0)
            
            # Apply T_memory influence to h_score (minimal adjustment)
            t_memory_factor = 1.0 + (t_memory / 100.0)  # Subtle influence
            extracted_values["h_score"] = min(0.9, extracted_values["h_score"] * t_memory_factor)
            
            # Apply HTF distance influence to rate calculation (minimal adjustment)
            if nearest_htf_distance < 50.0:
                htf_proximity_boost = 1.0 + ((50.0 - nearest_htf_distance) / 200.0)  # Max 25% boost
                extracted_values["rate_per_minute"] = min(10.0, extracted_values["rate_per_minute"] * htf_proximity_boost)
            
            # Apply liquidity gradient to intensity (minimal adjustment)
            if liquidity_gradient_strength > 0.01:
                if extracted_values["intensity_trend"] == "stable":
                    extracted_values["intensity_trend"] = "strengthening"
            
            # Add minimal tracker metadata
            extracted_values.update({
                "tracker_enhanced": True,
                "t_memory_applied": t_memory,
                "htf_distance": nearest_htf_distance,
                "liquidity_strength": liquidity_gradient_strength
            })
        
        return {
            "extracted_values": extracted_values,
            "price_data": base_extraction["price_data"],
            "session_metadata": base_extraction["session_metadata"],
            "tracker_context": tracker_context  # Pass minimal tracker context to prompt
        }
    
    def process_with_tracker_integration(self, input_data: Dict[Any, Any]) -> Dict[Any, Any]:
        """Process with tracker integration for enhanced calculations."""
        
        # Extract tracker context
        tracker_context = input_data.get("tracker_context", {})
        t_memory = tracker_context.get("t_memory", 15.0)
        htf_structures = tracker_context.get("htf_structures", [])
        untaken_liquidity = tracker_context.get("untaken_liquidity", [])
        
        # Pre-extract calculation values with tracker enhancements
        extracted_data = self._extract_calculation_values_with_trackers(
            input_data, t_memory, htf_structures, untaken_liquidity
        )
        
        # Use the base class process with tracker-enhanced data
        return super().process(extracted_data)


def create_unit_a(api_key: str = None) -> UnitA:
    """Factory function to create Unit A processor with centralized API key management."""
    try:
        from .grok_api_client import GrokClientFactory
        from .schemas import UnitAResponse
    except ImportError:
        from grok_api_client import GrokClientFactory
        from schemas import UnitAResponse
    
    # 🏭 ARCHITECTURAL IMPROVEMENT: Use centralized factory instead of duplicated instantiation
    computational_unit = GrokClientFactory.create_unit_client("Unit A", UnitAResponse, api_key)
    return UnitA(computational_unit.client)