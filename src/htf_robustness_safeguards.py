#!/usr/bin/env python3
"""
HTF Robustness Safeguards and Fallback Mechanisms
Implements Grok's sophisticated protection mechanisms for HTF system reliability.

Key Features:
- Dynamic γ thresholds: Cap γ(t) <0.1 when liquidity_imbalance_ratio <0.5
- Session-only fallbacks: Revert to γ=0 if HTF intensity <0.1
- News-driven variability control: Bayesian classification with day-of-week priors
- False positive control: <5% false positive rate using Gaussian Naive Bayes
- Circuit breaker patterns for failing calculations
- Graceful degradation when HTF calculations stall

Mathematical Foundation:
- Threshold conditions for safe operation
- Statistical bounds for parameter stability
- Automatic fallback triggers based on system health
- Performance monitoring with automatic recovery
"""

import numpy as np
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from sklearn.naive_bayes import GaussianNB
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')


@dataclass
class SafeguardStatus:
    """Status of robustness safeguards."""
    name: str
    active: bool
    threshold_value: float
    current_value: float
    last_triggered: Optional[str]
    trigger_count: int


@dataclass
class SystemHealth:
    """Overall HTF system health assessment."""
    overall_status: str  # 'healthy', 'degraded', 'critical'
    active_safeguards: List[str]
    fallback_mode: bool
    performance_score: float
    reliability_score: float
    last_assessment: str


@dataclass
class FallbackResult:
    """Result from fallback mechanism activation."""
    fallback_type: str
    reason: str
    fallback_parameters: Dict[str, Any]
    performance_impact: str
    recovery_recommendations: List[str]


class HTFRobustnessSafeguards:
    """
    HTF Robustness Safeguards System
    
    Provides comprehensive protection mechanisms for HTF calculations
    with automatic fallbacks and system health monitoring.
    """
    
    def __init__(self):
        """Initialize robustness safeguards system."""
        
        # Threshold configurations
        self.thresholds = {
            'gamma_liquidity_threshold': 0.5,     # liquidity_imbalance_ratio threshold
            'gamma_cap_low_liquidity': 0.1,       # γ cap when liquidity low
            'htf_intensity_threshold': 0.1,       # HTF intensity minimum
            'parameter_stability_threshold': 0.3,  # Parameter change limit
            'processing_time_threshold': 5000,     # Processing time limit (ms)
            'false_positive_rate_threshold': 0.05  # 5% false positive limit
        }
        
        # Safeguard states
        self.safeguards = {
            'gamma_liquidity_cap': SafeguardStatus('gamma_liquidity_cap', True, 0.5, 0.0, None, 0),
            'htf_intensity_fallback': SafeguardStatus('htf_intensity_fallback', True, 0.1, 0.0, None, 0),
            'parameter_stability_check': SafeguardStatus('parameter_stability_check', True, 0.3, 0.0, None, 0),
            'processing_timeout_guard': SafeguardStatus('processing_timeout_guard', True, 5000, 0.0, None, 0),
            'false_positive_control': SafeguardStatus('false_positive_control', True, 0.05, 0.0, None, 0)
        }
        
        # Fallback configurations
        self.fallback_modes = {
            'session_only': {'gamma': 0.0, 'htf_coupling': False},
            'reduced_coupling': {'gamma': 0.1, 'htf_coupling': True},
            'emergency_mode': {'gamma': 0.0, 'htf_coupling': False, 'simplified_intensity': True}
        }
        
        # Bayesian classifier for false positive control
        self.false_positive_classifier = GaussianNB()
        self.classifier_scaler = StandardScaler()
        self.classifier_trained = False
        
        # Day-of-week priors for news-driven variability
        self.day_priors = {
            'monday': {'cascade_prob': 0.4, 'news_impact': 0.3},
            'tuesday': {'cascade_prob': 0.7, 'news_impact': 0.4},
            'wednesday': {'cascade_prob': 0.5, 'news_impact': 0.3},
            'thursday': {'cascade_prob': 0.5, 'news_impact': 0.3},
            'friday': {'cascade_prob': 0.6, 'news_impact': 0.5}
        }
        
        # System health tracking
        self.health_history = []
        self.performance_metrics = {
            'successful_calculations': 0,
            'fallback_activations': 0,
            'error_count': 0,
            'average_processing_time': 0.0
        }
        
        print("🛡️ HTF ROBUSTNESS SAFEGUARDS: Protection system initialized")
        print(f"   Thresholds: γ_liquidity={self.thresholds['gamma_liquidity_threshold']}, \"\n               \"HTF_intensity={self.thresholds['htf_intensity_threshold']}")
        print(f"   Safeguards: {len(self.safeguards)} active protection mechanisms")
    
    def evaluate_system_safety(self, session_data: Dict[Any, Any],
                             htf_parameters: Dict[str, float],
                             gamma_timeline: List[float],
                             htf_intensity_timeline: List[float],
                             processing_time_ms: float) -> Tuple[SystemHealth, Optional[FallbackResult]]:
        """
        Evaluate system safety and determine if safeguards should activate.
        
        Args:
            session_data: Session data for context
            htf_parameters: Current HTF parameters
            gamma_timeline: Computed γ(t) values
            htf_intensity_timeline: HTF intensity values
            processing_time_ms: Processing time for performance monitoring
            
        Returns:
            SystemHealth assessment and optional FallbackResult if triggered
        """
        start_time = time.time()
        
        print("🛡️ SAFEGUARD EVALUATION: Assessing system safety")
        
        # Update performance metrics
        self.performance_metrics['average_processing_time'] = \
            (self.performance_metrics['average_processing_time'] + processing_time_ms) / 2
        
        # Evaluate individual safeguards
        safeguard_results = {}
        fallback_triggered = None
        
        # 1. Gamma liquidity cap safeguard
        liquidity_result = self._evaluate_gamma_liquidity_cap(session_data, gamma_timeline)
        safeguard_results['gamma_liquidity_cap'] = liquidity_result
        
        # 2. HTF intensity fallback safeguard
        intensity_result = self._evaluate_htf_intensity_fallback(htf_intensity_timeline)
        safeguard_results['htf_intensity_fallback'] = intensity_result
        
        # 3. Parameter stability safeguard
        stability_result = self._evaluate_parameter_stability(htf_parameters)
        safeguard_results['parameter_stability_check'] = stability_result
        
        # 4. Processing timeout safeguard
        timeout_result = self._evaluate_processing_timeout(processing_time_ms)
        safeguard_results['processing_timeout_guard'] = timeout_result
        
        # 5. False positive control safeguard
        fp_result = self._evaluate_false_positive_control(session_data, htf_parameters)
        safeguard_results['false_positive_control'] = fp_result
        
        # Determine if fallback is needed
        critical_safeguards = [
            safeguard_results['htf_intensity_fallback'],
            safeguard_results['processing_timeout_guard']
        ]
        
        if any(result['triggered'] for result in critical_safeguards):
            fallback_triggered = self._activate_fallback_mechanism(safeguard_results)
            self.performance_metrics['fallback_activations'] += 1
        else:
            self.performance_metrics['successful_calculations'] += 1
        
        # Calculate system health
        system_health = self._calculate_system_health(safeguard_results, fallback_triggered)
        
        # Store health assessment
        self.health_history.append(system_health)
        if len(self.health_history) > 100:  # Keep last 100 assessments
            self.health_history.pop(0)
        
        evaluation_time = (time.time() - start_time) * 1000
        
        print(f"🛡️ SAFEGUARD EVALUATION completed in {evaluation_time:.1f}ms")
        print(f"   System Status: {system_health.overall_status}")
        print(f"   Active Safeguards: {len(system_health.active_safeguards)}")
        print(f"   Fallback Mode: {system_health.fallback_mode}")
        
        return system_health, fallback_triggered
    
    def _evaluate_gamma_liquidity_cap(self, session_data: Dict[Any, Any], 
                                    gamma_timeline: List[float]) -> Dict[str, Any]:
        """
        Evaluate gamma liquidity cap safeguard.
        Cap γ(t) <0.1 when liquidity_imbalance_ratio <0.5.
        """
        # Calculate liquidity imbalance ratio
        liquidity_analysis = session_data.get('liquidity_analysis', {})
        untaken_liquidity = liquidity_analysis.get('untaken_liquidity', [])
        
        if not untaken_liquidity:
            imbalance_ratio = 1.0  # Neutral
        else:
            buy_side = sum(1 for level in untaken_liquidity if level.get('side', '') == 'buy')
            sell_side = sum(1 for level in untaken_liquidity if level.get('side', '') == 'sell')
            
            if sell_side == 0 and buy_side > 0:
                imbalance_ratio = 2.0
            elif buy_side == 0 and sell_side > 0:
                imbalance_ratio = 2.0
            elif sell_side > 0:
                imbalance_ratio = max(buy_side / sell_side, sell_side / buy_side)
            else:
                imbalance_ratio = 1.0
        
        # Check if cap should be applied
        threshold = self.thresholds['gamma_liquidity_threshold']
        cap_value = self.thresholds['gamma_cap_low_liquidity']
        
        should_cap = imbalance_ratio < threshold
        max_gamma = max(gamma_timeline) if gamma_timeline else 0.0
        
        # Update safeguard status
        safeguard = self.safeguards['gamma_liquidity_cap']
        safeguard.current_value = imbalance_ratio
        
        if should_cap and max_gamma > cap_value:
            safeguard.trigger_count += 1
            safeguard.last_triggered = datetime.now().isoformat()
            
            return {
                'triggered': True,
                'reason': f'Low liquidity imbalance ({imbalance_ratio:.3f} < {threshold})',
                'action': f'Cap γ(t) to {cap_value}',
                'severity': 'medium'
            }
        
        return {'triggered': False, 'reason': 'Liquidity sufficient', 'severity': 'low'}
    
    def _evaluate_htf_intensity_fallback(self, htf_intensity_timeline: List[float]) -> Dict[str, Any]:
        """
        Evaluate HTF intensity fallback safeguard.
        Revert to γ=0 if HTF intensity <0.1.
        """
        if not htf_intensity_timeline:
            return {'triggered': True, 'reason': 'No HTF intensity data', 'severity': 'high'}
        
        min_intensity = min(htf_intensity_timeline)
        avg_intensity = np.mean(htf_intensity_timeline)
        threshold = self.thresholds['htf_intensity_threshold']
        
        # Update safeguard status
        safeguard = self.safeguards['htf_intensity_fallback']
        safeguard.current_value = avg_intensity
        
        if avg_intensity < threshold:
            safeguard.trigger_count += 1
            safeguard.last_triggered = datetime.now().isoformat()
            
            return {
                'triggered': True,
                'reason': f'HTF intensity too low (avg: {avg_intensity:.3f} < {threshold})',
                'action': 'Fallback to session-only mode (γ=0)',
                'severity': 'high'
            }
        
        return {'triggered': False, 'reason': 'HTF intensity sufficient', 'severity': 'low'}
    
    def _evaluate_parameter_stability(self, htf_parameters: Dict[str, float]) -> Dict[str, Any]:
        """Evaluate parameter stability safeguard."""
        
        # Check for reasonable parameter values
        alpha_h = htf_parameters.get('alpha_h', 0.4)
        beta_h = htf_parameters.get('beta_h', 0.001)
        mu_h = htf_parameters.get('mu_h', 0.05)
        
        stability_issues = []
        
        # Check alpha_h bounds
        if alpha_h < 0.001 or alpha_h > 2.0:
            stability_issues.append(f'alpha_h out of bounds: {alpha_h:.6f}')
        
        # Check beta_h bounds
        if beta_h < 0.0001 or beta_h > 0.01:
            stability_issues.append(f'beta_h out of bounds: {beta_h:.6f}')
        
        # Check mu_h bounds
        if mu_h < 0.001 or mu_h > 0.5:
            stability_issues.append(f'mu_h out of bounds: {mu_h:.6f}')
        
        # Update safeguard status
        safeguard = self.safeguards['parameter_stability_check']
        safeguard.current_value = len(stability_issues)
        
        if stability_issues:
            safeguard.trigger_count += 1
            safeguard.last_triggered = datetime.now().isoformat()
            
            return {
                'triggered': True,
                'reason': f'Parameter instability: {", ".join(stability_issues)}',
                'action': 'Reset to default parameters',
                'severity': 'medium'
            }
        
        return {'triggered': False, 'reason': 'Parameters stable', 'severity': 'low'}
    
    def _evaluate_processing_timeout(self, processing_time_ms: float) -> Dict[str, Any]:
        """Evaluate processing timeout safeguard."""
        
        threshold = self.thresholds['processing_time_threshold']
        
        # Update safeguard status
        safeguard = self.safeguards['processing_timeout_guard']
        safeguard.current_value = processing_time_ms
        
        if processing_time_ms > threshold:
            safeguard.trigger_count += 1
            safeguard.last_triggered = datetime.now().isoformat()
            
            return {
                'triggered': True,
                'reason': f'Processing timeout ({processing_time_ms:.1f}ms > {threshold}ms)',
                'action': 'Switch to emergency mode',
                'severity': 'critical'
            }
        
        return {'triggered': False, 'reason': 'Processing time acceptable', 'severity': 'low'}
    
    def _evaluate_false_positive_control(self, session_data: Dict[Any, Any],
                                       htf_parameters: Dict[str, float]) -> Dict[str, Any]:
        """
        Evaluate false positive control using Bayesian classification.
        Target: <5% false positive rate.
        """
        
        # Extract day-of-week context
        session_date = session_data.get('session_metadata', {}).get('date', '')
        weekday_name = self._get_weekday_name(session_date)
        
        # Get day-specific priors
        day_prior = self.day_priors.get(weekday_name, self.day_priors['wednesday'])
        
        # Calculate session features for classification
        session_features = self._extract_session_features_for_classification(session_data)
        
        # Estimate false positive probability using Bayesian approach
        if self.classifier_trained:
            # Use trained classifier
            features_scaled = self.classifier_scaler.transform([session_features])
            predicted_probs = self.false_positive_classifier.predict_proba(features_scaled)[0]
            false_positive_prob = predicted_probs[0] if len(predicted_probs) > 1 else 0.05
        else:
            # Use day-of-week prior as estimate
            base_prob = 0.05  # 5% baseline
            day_adjustment = day_prior['cascade_prob'] - 0.5  # Adjust from neutral
            false_positive_prob = max(0.01, min(0.15, base_prob + day_adjustment * 0.02))
        
        threshold = self.thresholds['false_positive_rate_threshold']
        
        # Update safeguard status
        safeguard = self.safeguards['false_positive_control']
        safeguard.current_value = false_positive_prob
        
        if false_positive_prob > threshold:
            safeguard.trigger_count += 1
            safeguard.last_triggered = datetime.now().isoformat()
            
            return {
                'triggered': True,
                'reason': f'High false positive risk ({false_positive_prob:.2%} > {threshold:.1%})',
                'action': 'Apply stricter detection thresholds',
                'severity': 'medium'
            }
        
        return {'triggered': False, 'reason': 'False positive rate acceptable', 'severity': 'low'}
    
    def _activate_fallback_mechanism(self, safeguard_results: Dict[str, Dict]) -> FallbackResult:
        """Activate appropriate fallback mechanism based on safeguard results."""
        
        # Determine fallback level based on severity
        critical_count = sum(1 for result in safeguard_results.values() 
                           if result.get('severity') == 'critical')
        high_count = sum(1 for result in safeguard_results.values() 
                        if result.get('severity') == 'high')
        
        if critical_count > 0:
            # Emergency mode fallback
            fallback_type = 'emergency_mode'
            fallback_params = self.fallback_modes['emergency_mode'].copy()
            performance_impact = 'severe'
        elif high_count > 0:
            # Session-only fallback
            fallback_type = 'session_only'
            fallback_params = self.fallback_modes['session_only'].copy()
            performance_impact = 'moderate'
        else:
            # Reduced coupling fallback
            fallback_type = 'reduced_coupling'
            fallback_params = self.fallback_modes['reduced_coupling'].copy()
            performance_impact = 'minimal'
        
        # Generate recovery recommendations
        recovery_recommendations = []
        for name, result in safeguard_results.items():
            if result.get('triggered', False):
                recovery_recommendations.append(f"Address {name}: {result.get('action', 'Review configuration')}")
        
        # Compile reason
        triggered_safeguards = [name for name, result in safeguard_results.items() 
                              if result.get('triggered', False)]
        reason = f"Safeguards triggered: {', '.join(triggered_safeguards)}"
        
        print(f"🚨 FALLBACK ACTIVATED: {fallback_type} ({performance_impact} impact)")
        
        return FallbackResult(
            fallback_type=fallback_type,
            reason=reason,
            fallback_parameters=fallback_params,
            performance_impact=performance_impact,
            recovery_recommendations=recovery_recommendations
        )
    
    def _calculate_system_health(self, safeguard_results: Dict[str, Dict],
                               fallback_result: Optional[FallbackResult]) -> SystemHealth:
        """Calculate overall system health assessment."""
        
        # Count active safeguards
        active_safeguards = [name for name, result in safeguard_results.items() 
                           if result.get('triggered', False)]
        
        # Determine overall status
        if fallback_result and fallback_result.performance_impact == 'severe':
            overall_status = 'critical'
        elif len(active_safeguards) > 2:
            overall_status = 'degraded'
        elif len(active_safeguards) > 0:
            overall_status = 'degraded'
        else:
            overall_status = 'healthy'
        
        # Calculate performance score
        total_calculations = (self.performance_metrics['successful_calculations'] + 
                            self.performance_metrics['fallback_activations'])
        if total_calculations > 0:
            performance_score = self.performance_metrics['successful_calculations'] / total_calculations
        else:
            performance_score = 1.0
        
        # Calculate reliability score
        error_rate = self.performance_metrics['error_count'] / max(total_calculations, 1)
        reliability_score = max(0.0, 1.0 - error_rate)
        
        return SystemHealth(
            overall_status=overall_status,
            active_safeguards=active_safeguards,
            fallback_mode=fallback_result is not None,
            performance_score=performance_score,
            reliability_score=reliability_score,
            last_assessment=datetime.now().isoformat()
        )
    
    def _extract_session_features_for_classification(self, session_data: Dict[Any, Any]) -> List[float]:
        """Extract features for false positive classification."""
        
        price_data = session_data.get('price_data', {})
        micro_timing = session_data.get('micro_timing_analysis', {})
        
        # Extract numerical features
        features = [
            price_data.get('range', 0) / 100.0,  # Normalized range
            len(micro_timing.get('cascade_events', [])),  # Event count
            len(session_data.get('liquidity_analysis', {}).get('untaken_liquidity', [])),  # Liquidity levels
            price_data.get('close', 23300) / 23300.0,  # Normalized price
            len(session_data.get('structures_identified', {}).get('fair_value_gaps', []))  # FVG count
        ]
        
        return features
    
    def _get_weekday_name(self, date_str: str) -> str:
        """Get weekday name from date string."""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            weekday = date_obj.weekday()
            weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            return weekdays[weekday]
        except:
            return 'wednesday'  # Default to mid-week
    
    def train_false_positive_classifier(self, historical_sessions: List[Dict[Any, Any]],
                                      true_labels: List[bool]) -> None:
        """Train false positive classifier on historical data."""
        
        if len(historical_sessions) != len(true_labels):
            raise ValueError("Session count must match label count")
        
        print(f"🎓 TRAINING FALSE POSITIVE CLASSIFIER: {len(historical_sessions)} sessions")
        
        # Extract features
        features = []
        for session in historical_sessions:
            session_features = self._extract_session_features_for_classification(session)
            features.append(session_features)
        
        # Convert to numpy arrays
        X = np.array(features)
        y = np.array(true_labels, dtype=int)
        
        # Scale features and train classifier
        X_scaled = self.classifier_scaler.fit_transform(X)
        self.false_positive_classifier.fit(X_scaled, y)
        
        self.classifier_trained = True
        
        # Calculate training accuracy
        accuracy = self.false_positive_classifier.score(X_scaled, y)
        
        print(f"✅ CLASSIFIER TRAINED: Accuracy = {accuracy:.2%}")
    
    def get_safeguard_status(self) -> Dict[str, Any]:
        """Get comprehensive safeguard status."""
        
        safeguard_status = {}
        for name, safeguard in self.safeguards.items():
            safeguard_status[name] = {
                'active': safeguard.active,
                'threshold': safeguard.threshold_value,
                'current_value': safeguard.current_value,
                'trigger_count': safeguard.trigger_count,
                'last_triggered': safeguard.last_triggered
            }
        
        return {
            'safeguards': safeguard_status,
            'thresholds': self.thresholds.copy(),
            'fallback_modes': list(self.fallback_modes.keys()),
            'performance_metrics': self.performance_metrics.copy(),
            'classifier_trained': self.classifier_trained,
            'health_assessments': len(self.health_history)
        }
    
    def reset_safeguards(self) -> None:
        """Reset all safeguard trigger counts and states."""
        for safeguard in self.safeguards.values():
            safeguard.trigger_count = 0
            safeguard.last_triggered = None
            safeguard.current_value = 0.0
        
        self.performance_metrics = {
            'successful_calculations': 0,
            'fallback_activations': 0,
            'error_count': 0,
            'average_processing_time': 0.0
        }
        
        print("🔄 SAFEGUARDS RESET: All counters and states cleared")


def create_htf_robustness_safeguards() -> HTFRobustnessSafeguards:
    """Factory function to create HTF robustness safeguards."""
    return HTFRobustnessSafeguards()


if __name__ == "__main__":
    # Test HTF robustness safeguards
    print("🧪 TESTING HTF ROBUSTNESS SAFEGUARDS")
    print("=" * 60)
    
    safeguards = create_htf_robustness_safeguards()
    
    # Mock session data with low liquidity (should trigger gamma cap)
    mock_session = {
        'session_metadata': {
            'date': '2025-07-25',
            'session_type': 'NY_PM'
        },
        'price_data': {
            'range': 20.0,  # Low volatility
            'close': 23400
        },
        'liquidity_analysis': {
            'untaken_liquidity': [
                {'side': 'buy', 'level': 23400}  # Only one side - low imbalance
            ]
        },
        'micro_timing_analysis': {
            'cascade_events': [
                {'timestamp': '15:38:00', 'magnitude': 15.0}
            ]
        }
    }
    
    # Mock HTF data
    mock_htf_parameters = {
        'alpha_h': 0.4,
        'beta_h': 0.001,
        'mu_h': 0.05
    }
    
    mock_gamma_timeline = [0.35, 0.40, 0.38, 0.42]  # High gamma values
    mock_htf_intensity = [0.05, 0.04, 0.03, 0.02]   # Low HTF intensity
    mock_processing_time = 150.0  # Normal processing time
    
    # Test safeguard evaluation
    system_health, fallback_result = safeguards.evaluate_system_safety(
        mock_session,
        mock_htf_parameters,
        mock_gamma_timeline,
        mock_htf_intensity,
        mock_processing_time
    )
    
    print(f"🛡️ Safeguard Evaluation Results:")
    print(f"  System Status: {system_health.overall_status}")
    print(f"  Active Safeguards: {system_health.active_safeguards}")
    print(f"  Fallback Mode: {system_health.fallback_mode}")
    print(f"  Performance Score: {system_health.performance_score:.2%}")
    print(f"  Reliability Score: {system_health.reliability_score:.2%}")
    
    if fallback_result:
        print(f"\n🚨 Fallback Activated:")
        print(f"  Type: {fallback_result.fallback_type}")
        print(f"  Reason: {fallback_result.reason}")
        print(f"  Performance Impact: {fallback_result.performance_impact}")
        print(f"  Parameters: {fallback_result.fallback_parameters}")
        
        print(f"  Recovery Recommendations:")
        for rec in fallback_result.recovery_recommendations[:3]:  # Show first 3
            print(f"    - {rec}")
    
    # Show safeguard status
    status = safeguards.get_safeguard_status()
    print(f"\n🔧 Safeguard Status:")
    print(f"  Total Safeguards: {len(status['safeguards'])}")
    print(f"  Classifier Trained: {status['classifier_trained']}")
    print(f"  Successful Calculations: {status['performance_metrics']['successful_calculations']}")
    print(f"  Fallback Activations: {status['performance_metrics']['fallback_activations']}")
    
    # Show individual safeguard details
    print(f"\n📊 Individual Safeguards:")
    for name, safeguard_data in status['safeguards'].items():
        print(f"  {name}:")
        print(f"    Triggers: {safeguard_data['trigger_count']}")
        print(f"    Current: {safeguard_data['current_value']:.3f}")
        print(f"    Threshold: {safeguard_data['threshold']:.3f}")
    
    print(f"\n✅ HTF Robustness Safeguards testing completed!")