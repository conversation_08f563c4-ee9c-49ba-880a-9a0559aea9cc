#!/usr/bin/env python3
"""
HTF Session Intelligence Parser - The Missing Intelligence Layer

This parser bridges the gap between Level 1 natural language transcriptions
and HTF context files by providing intelligent event interpretation and
cross-session reference resolution.

Key Features:
- Natural language parsing for session references
- Cross-session level resolution and validation
- Automatic HTF context updates with date-specific identifiers
- Structure promotion logic (session → daily → weekly)
- End-of-session batch processing for all affected files
- 3-day rolling session database with HTF memory beyond

Architecture:
Level 1 Data → Intelligence Parser → HTF Context Updates → HTF Algorithm
"""

import json
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
import logging


@dataclass
class SessionReference:
    """Represents a parsed session reference from Level 1 data."""
    session_type: str
    date: str
    level: float
    structure_type: str  # "high", "low", "fpfvg_premium", etc.
    original_text: str
    confidence: float


@dataclass
class HTFEvent:
    """Represents an HTF event with date-specific identifiers."""
    event_type: str
    level: float
    reference_date: str
    violated_on: str
    origin_session: str
    taken_by_session: str
    htf_significance: str
    session_details: Dict[str, Any]
    timestamp: datetime


class HTFSessionIntelligenceParser:
    """
    The HTF Session Intelligence Parser that connects Level 1 transcriptions
    to HTF context files through intelligent event interpretation.
    """
    
    def __init__(self, base_dir: str = "/Users/<USER>/grok-claude-automation"):
        self.base_dir = Path(base_dir)
        self.sessions_dir = self.base_dir / "data" / "sessions" / "level_1"
        self.htf_dir = self.base_dir / "data" / "trackers" / "htf"
        self.liquidity_dir = self.base_dir / "data" / "trackers" / "liquidity"
        self.fvg_dir = self.base_dir / "data" / "trackers" / "fvg"
        
        # Session database for cross-reference (3-day rolling + HTF memory)
        self.session_database = {}
        self.daily_structures = {}  # For day-end structure promotion
        self.htf_structures = {}    # For HTF memory beyond 3 days
        
        # Natural language patterns for session references
        self.session_patterns = self._initialize_session_patterns()
        
        # Setup logging
        self._setup_logging()
        
        # Load existing session data
        self._load_session_database()
        
    def _setup_logging(self):
        """Setup comprehensive logging."""
        self.logger = logging.getLogger("HTFIntelligenceParser")
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - HTF_Intelligence - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def _initialize_session_patterns(self) -> Dict[str, List[str]]:
        """Initialize natural language patterns for session reference parsing."""
        return {
            "session_references": [
                # Fixed: Handle both spaces and underscores, make "session" optional
                r"(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]?am|nyam|ny[_\s]?pm|nypm|pm)(?:[_\s]+session)?[_\s]+(?P<structure>high|low)",
                r"(?P<structure>high|low)[_\s]+(?:of[_\s]+(?:the[_\s]+)?)?(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]?am|nyam|ny[_\s]?pm|nypm|pm)(?:[_\s]+session)?",
                r"previous[_\s]+day'?s?[_\s]*(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]?am|nyam|ny[_\s]?pm|nypm|pm)?[_\s]*(?P<structure>high|low)",
                r"yesterday'?s?[_\s]*(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]?am|nyam|ny[_\s]?pm|nypm|pm)?[_\s]*(?P<structure>high|low)",
                # With level capture
                r"(?P<session>asia|london|lunch|ny[_\s]?am|ny[_\s]?pm)(?:[_\s]+session)?[_\s]+(?P<structure>low|high)[_\s]+(?:at[_\s]+)?(?P<level>[0-9,]+\.?[0-9]*)",
            ],
            "takeout_actions": [
                # Fixed: Handle underscores in takeout descriptions
                r"(?P<level>[\d,.]+)[_\s]+(?:gets?|was|is)?[_\s]*taken[_\s]+out",
                r"took[_\s]+(?:out[_\s]+)?(?P<reference>.*?)[_\s]+(?:at[_\s]+)?(?P<level>[\d,.]+)?",
                r"sweep[_\s]+(?:of[_\s]+)?(?P<reference>.*?)(?:[_\s]+at[_\s]+(?P<level>[\d,.]+))?",
                r"(?P<reference>.*?)[_\s]+taken[_\s]+(?:out[_\s]+)?(?:at[_\s]+(?P<level>[\d,.]+))?",
                # New: Direct underscore patterns for HTF event descriptions
                r"(?P<session>asia|london|lunch|ny_am|ny_pm|premarket|midnight)[_]+session[_]+(?P<structure>high|low)[_]+(?P<action>taken|violated|swept|broken)",
                r"(?P<session>asia|london|lunch|ny_am|ny_pm|premarket|midnight)[_]+(?P<structure>high|low)[_]+(?P<action>taken|violated|swept|broken)",
            ],
            "daily_context": [
                r"daily\s+(?P<structure>high|low)",
                r"(?P<structure>high|low)\s+(?:of\s+)?(?:the\s+)?day",
                r"new\s+daily\s+(?P<structure>high|low)",
            ]
        }
    
    def _load_session_database(self):
        """Load existing session data to build cross-reference database."""
        self.logger.info("Loading session database for cross-reference...")
        
        # Load all Level 1 session files
        for session_file in self.sessions_dir.glob("*_Lvl-1_*.json"):
            try:
                with open(session_file, 'r') as f:
                    session_data = json.load(f)
                
                # Extract session info
                metadata = session_data.get("session_metadata", {})
                price_data = session_data.get("price_data", {})
                
                session_key = f"{metadata.get('session_type', 'unknown')}_{metadata.get('date', 'unknown')}"
                
                self.session_database[session_key] = {
                    "file_path": str(session_file),
                    "session_type": metadata.get("session_type"),
                    "date": metadata.get("date"),
                    "high": price_data.get("high"),
                    "low": price_data.get("low"),
                    "open": price_data.get("open"),
                    "close": price_data.get("close"),
                    "range": price_data.get("range"),
                    "movements": session_data.get("price_movements", [])
                }
                
            except Exception as e:
                self.logger.warning(f"Could not load session file {session_file}: {e}")
        
        self.logger.info(f"Loaded {len(self.session_database)} sessions into database")
        
        # Build daily structure database for promotion logic
        self._build_daily_structures()
    
    def _build_daily_structures(self):
        """Build daily structure database for session → daily promotion logic."""
        daily_groups = {}
        
        # Group sessions by date
        for session_key, session_data in self.session_database.items():
            date = session_data["date"]
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(session_data)
        
        # Find daily highs/lows for each date
        for date, sessions in daily_groups.items():
            if not sessions:
                continue
            
            # Filter sessions with valid high/low data
            valid_highs = [s["high"] for s in sessions if s["high"] is not None]
            valid_lows = [s["low"] for s in sessions if s["low"] is not None]
            
            if not valid_highs or not valid_lows:
                continue
                
            # Find highest high and lowest low of the day
            daily_high = max(valid_highs)
            daily_low = min(valid_lows)
            
            # Find which session created the daily high/low
            daily_high_session = None
            daily_low_session = None
            
            for session in sessions:
                if session["high"] == daily_high:
                    daily_high_session = session["session_type"]
                if session["low"] == daily_low:
                    daily_low_session = session["session_type"]
            
            self.daily_structures[date] = {
                "daily_high": daily_high,
                "daily_low": daily_low,
                "daily_high_session": daily_high_session,
                "daily_low_session": daily_low_session,
                "sessions": sessions
            }
        
        self.logger.info(f"Built daily structures for {len(self.daily_structures)} days")
    
    def parse_session_reference(self, text: str, current_date: str) -> Optional[SessionReference]:
        """Parse natural language session references from Level 1 data."""
        text_lower = text.lower()
        
        for pattern in self.session_patterns["session_references"]:
            match = re.search(pattern, text_lower, re.IGNORECASE)
            if match:
                session_raw = match.group("session")
                structure = match.group("structure")
                
                # Normalize session name
                session_type = self._normalize_session_name(session_raw)
                
                # Determine reference date
                if "previous" in text_lower or "yesterday" in text_lower:
                    ref_date = self._get_previous_day(current_date)
                else:
                    ref_date = current_date
                
                # Look up level in session database
                session_key = f"{session_type}_{ref_date}"
                if session_key in self.session_database:
                    session_data = self.session_database[session_key]
                    level = session_data.get(structure)
                    
                    if level:
                        return SessionReference(
                            session_type=session_type,
                            date=ref_date,
                            level=level,
                            structure_type=structure,
                            original_text=text,
                            confidence=0.9
                        )
        
        return None
    
    def _normalize_session_name(self, session_raw: str) -> str:
        """Normalize session names to standard format."""
        session_map = {
            "premarket": "Premarket",
            "pre-market": "Premarket", 
            "midnight": "Midnight",
            "asia": "Asia",
            "london": "London",
            "lunch": "Lunch",
            "ny am": "NY_AM",
            "nyam": "NY_AM",
            "ny pm": "NY_PM",
            "nypm": "NY_PM",
            "pm": "NY_PM"
        }
        return session_map.get(session_raw.lower(), session_raw)
    
    def _get_previous_day(self, current_date: str) -> str:
        """Get previous business day date."""
        current = datetime.strptime(current_date, "%Y-%m-%d")
        previous = current - timedelta(days=1)
        
        # Skip weekends (simple implementation)
        while previous.weekday() >= 5:  # Saturday = 5, Sunday = 6
            previous -= timedelta(days=1)
            
        return previous.strftime("%Y-%m-%d")
    
    def parse_level_1_session(self, session_file_path: str) -> List[HTFEvent]:
        """Parse a Level 1 session file and extract HTF events."""
        try:
            with open(session_file_path, 'r') as f:
                session_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Could not load session file {session_file_path}: {e}")
            return []
        
        metadata = session_data.get("session_metadata", {})
        current_date = metadata.get("date")
        current_session = metadata.get("session_type")
        movements = session_data.get("price_movements", [])
        
        htf_events = []
        
        for movement in movements:
            context = movement.get("context", "")
            timestamp_str = movement.get("timestamp", "")
            price = movement.get("price")
            action = movement.get("action", "")
            
            # Look for takeout references in context
            if any(keyword in context.lower() for keyword in ["taken", "took", "sweep"]):
                # Parse the session reference
                session_ref = self.parse_session_reference(context, current_date)
                
                if session_ref:
                    # Determine HTF significance
                    htf_significance = self._determine_htf_significance(session_ref, current_date)
                    
                    # Create HTF event with date-specific identifier
                    htf_event = HTFEvent(
                        event_type="level_takeout",
                        level=session_ref.level,
                        reference_date=session_ref.date,
                        violated_on=current_date,
                        origin_session=f"{session_ref.session_type}_{session_ref.date}",
                        taken_by_session=f"{current_session}_{current_date}",
                        htf_significance=htf_significance,
                        session_details={
                            "context": context,
                            "timestamp": timestamp_str,
                            "price": price,
                            "action": action
                        },
                        timestamp=datetime.now()
                    )
                    
                    htf_events.append(htf_event)
                    
                    self.logger.info(f"Parsed HTF event: {htf_significance} at {session_ref.level}")
        
        return htf_events
    
    def _determine_htf_significance(self, session_ref: SessionReference, current_date: str) -> str:
        """Determine HTF significance with date-specific identifiers."""
        ref_date = session_ref.date
        session_type = session_ref.session_type
        structure = session_ref.structure_type
        level = session_ref.level
        
        # Check if this level was the daily high/low
        if ref_date in self.daily_structures:
            daily_data = self.daily_structures[ref_date]
            
            if structure == "high" and level == daily_data["daily_high"]:
                return f"daily_high_{ref_date}_violated"
            elif structure == "low" and level == daily_data["daily_low"]:
                return f"daily_low_{ref_date}_violated"
        
        # Default to session-level significance with date specificity
        return f"{session_type.lower()}_{structure}_{ref_date}_violated"
    
    def update_htf_context_files(self, htf_events: List[HTFEvent], target_session: str, target_date: str):
        """Update HTF context files with intelligent event processing."""
        if not htf_events:
            return
            
        # Load existing HTF context file
        htf_file_pattern = f"HTF_Context_{target_session}_*{target_date}*.json"
        htf_files = list(self.htf_dir.glob(htf_file_pattern))
        
        if not htf_files:
            self.logger.warning(f"No HTF context file found for {target_session}_{target_date}")
            return
            
        htf_file = htf_files[0]  # Use first match
        
        try:
            with open(htf_file, 'r') as f:
                htf_context = json.load(f)
        except Exception as e:
            self.logger.error(f"Could not load HTF context file {htf_file}: {e}")
            return
        
        # Add HTF events section if not exists
        if "htf_events" not in htf_context:
            htf_context["htf_events"] = []
        
        # Add new events with date-specific identifiers
        for event in htf_events:
            event_record = {
                "event_type": event.event_type,
                "level": event.level,
                "htf_significance": event.htf_significance,
                "reference_date": event.reference_date,
                "violated_on": event.violated_on,
                "origin_session": event.origin_session,
                "taken_by_session": event.taken_by_session,
                "session_details": event.session_details,
                "timestamp": event.timestamp.isoformat(),
                "processed_by": "HTF_Session_Intelligence_Parser"
            }
            
            htf_context["htf_events"].append(event_record)
        
        # Update last_update timestamp
        htf_context["last_update"] = datetime.now().isoformat()
        htf_context["intelligence_processed"] = True
        
        # Save updated HTF context
        try:
            with open(htf_file, 'w') as f:
                json.dump(htf_context, f, indent=2)
                
            self.logger.info(f"Updated HTF context file {htf_file} with {len(htf_events)} events")
            
        except Exception as e:
            self.logger.error(f"Could not save HTF context file {htf_file}: {e}")
    
    def process_session_intelligence(self, session_file_path: str) -> Dict[str, Any]:
        """Main method to process a Level 1 session through intelligence layer."""
        self.logger.info(f"Processing session intelligence for {session_file_path}")
        
        # Parse HTF events from Level 1 data
        htf_events = self.parse_level_1_session(session_file_path)
        
        if not htf_events:
            self.logger.info("No HTF events found in session")
            return {"status": "no_htf_events", "events_processed": 0}
        
        # Extract session info for file updates
        try:
            with open(session_file_path, 'r') as f:
                session_data = json.load(f)
                
            metadata = session_data.get("session_metadata", {})
            target_session = metadata.get("session_type")
            target_date = metadata.get("date")
            
        except Exception as e:
            self.logger.error(f"Could not extract session metadata: {e}")
            return {"status": "error", "error": str(e)}
        
        # Update HTF context files
        self.update_htf_context_files(htf_events, target_session, target_date)
        
        # Process results
        results = {
            "status": "success",
            "events_processed": len(htf_events),
            "session_type": target_session,
            "session_date": target_date,
            "htf_events": [
                {
                    "htf_significance": event.htf_significance,
                    "level": event.level,
                    "reference_date": event.reference_date,
                    "origin_session": event.origin_session
                }
                for event in htf_events
            ]
        }
        
        self.logger.info(f"Successfully processed {len(htf_events)} HTF events")
        return results
    
    def batch_process_all_sessions(self) -> Dict[str, Any]:
        """Batch process all Level 1 sessions for HTF intelligence."""
        self.logger.info("Starting batch processing of all Level 1 sessions...")
        
        results = {
            "total_sessions": 0,
            "processed_sessions": 0,
            "total_htf_events": 0,
            "session_results": [],
            "errors": []
        }
        
        # Process all Level 1 session files
        for session_file in self.sessions_dir.glob("*_Lvl-1_*.json"):
            results["total_sessions"] += 1
            
            try:
                session_result = self.process_session_intelligence(str(session_file))
                
                if session_result["status"] == "success":
                    results["processed_sessions"] += 1
                    results["total_htf_events"] += session_result["events_processed"]
                
                results["session_results"].append({
                    "file": session_file.name,
                    "result": session_result
                })
                
            except Exception as e:
                error_msg = f"Error processing {session_file.name}: {str(e)}"
                self.logger.error(error_msg)
                results["errors"].append(error_msg)
        
        self.logger.info(f"Batch processing complete: {results['processed_sessions']}/{results['total_sessions']} sessions, {results['total_htf_events']} HTF events")
        
        return results


def main():
    """Demo the HTF Session Intelligence Parser."""
    parser = HTFSessionIntelligenceParser()
    
    print("🧠 HTF Session Intelligence Parser - Demo")
    print("=" * 50)
    
    # Process a specific session
    test_session = "/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYAM_Lvl-1_2025_07_28.json"
    
    if Path(test_session).exists():
        print(f"\n📋 Processing test session: {Path(test_session).name}")
        result = parser.process_session_intelligence(test_session)
        
        print(f"\n✅ Results:")
        print(f"Status: {result['status']}")
        print(f"Events Processed: {result.get('events_processed', 0)}")
        
        if result.get('htf_events'):
            print(f"\n🎯 HTF Events Found:")
            for event in result['htf_events']:
                print(f"  • {event['htf_significance']} at {event['level']}")
                print(f"    Origin: {event['origin_session']}")
    
    # Batch process all sessions
    print(f"\n🔄 Running batch processing on all sessions...")
    batch_results = parser.batch_process_all_sessions()
    
    print(f"\n📊 Batch Results:")
    print(f"Total Sessions: {batch_results['total_sessions']}")
    print(f"Processed: {batch_results['processed_sessions']}")
    print(f"Total HTF Events: {batch_results['total_htf_events']}")
    
    if batch_results['errors']:
        print(f"\n⚠️ Errors: {len(batch_results['errors'])}")
        for error in batch_results['errors'][:3]:  # Show first 3 errors
            print(f"  • {error}")


if __name__ == "__main__":
    main()