#!/usr/bin/env python3
"""
Ground Truth Storage System

Manages storage and retrieval of objective market facts for validation.
Provides persistent storage of ground truth data separate from mathematical models.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data, ensure_directory


@dataclass
class StoredGroundTruth:
    """Structure for stored ground truth records"""
    session_id: str
    timestamp: str
    file_source: str
    ground_truth_facts: Dict[str, Any]
    system_predictions: Optional[Dict[str, Any]] = None
    validation_results: Optional[Dict[str, Any]] = None


class GroundTruthStore:
    """
    Persistent storage for ground truth market facts.
    
    Maintains separation between observed reality and mathematical interpretations.
    """
    
    def __init__(self, storage_directory: str = "data/validation"):
        """
        Initialize ground truth storage system.
        
        Args:
            storage_directory: Directory for storing ground truth data
        """
        self.storage_dir = storage_directory
        self.facts_file = os.path.join(storage_directory, "ground_truth_facts.json")
        self.predictions_file = os.path.join(storage_directory, "system_predictions.json")
        self.validation_file = os.path.join(storage_directory, "validation_results.json")
        
        # Ensure storage directory exists
        ensure_directory(storage_directory)
        
        # Initialize storage files if they don't exist
        self._initialize_storage_files()
        
        # In-memory cache
        self._facts_cache: List[StoredGroundTruth] = []
        self._load_cache()
    
    def _initialize_storage_files(self):
        """Initialize storage files with empty structure"""
        
        if not os.path.exists(self.facts_file):
            initial_structure = {
                'metadata': {
                    'created': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'description': 'Ground truth market facts - objective observations'
                },
                'facts': []
            }
            save_json_data(initial_structure, self.facts_file)
        
        if not os.path.exists(self.predictions_file):
            initial_structure = {
                'metadata': {
                    'created': datetime.now().isoformat(),
                    'version': '1.0.0', 
                    'description': 'System predictions for validation comparison'
                },
                'predictions': []
            }
            save_json_data(initial_structure, self.predictions_file)
        
        if not os.path.exists(self.validation_file):
            initial_structure = {
                'metadata': {
                    'created': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'description': 'Validation results comparing predictions vs ground truth'
                },
                'validations': []
            }
            save_json_data(initial_structure, self.validation_file)
    
    def _load_cache(self):
        """Load existing ground truth data into memory cache"""
        
        try:
            facts_data = load_json_data(self.facts_file)
            predictions_data = load_json_data(self.predictions_file)
            validations_data = load_json_data(self.validation_file)
            
            # Build cache from stored data
            self._facts_cache = []
            
            for fact_record in facts_data.get('facts', []):
                # Find corresponding prediction and validation
                session_id = fact_record.get('session_id')
                
                prediction = None
                for pred in predictions_data.get('predictions', []):
                    if pred.get('session_id') == session_id:
                        prediction = pred
                        break
                
                validation = None
                for val in validations_data.get('validations', []):
                    if val.get('session_id') == session_id:
                        validation = val
                        break
                
                stored_record = StoredGroundTruth(
                    session_id=session_id,
                    timestamp=fact_record.get('timestamp', ''),
                    file_source=fact_record.get('file_source', ''),
                    ground_truth_facts=fact_record.get('ground_truth_facts', {}),
                    system_predictions=prediction.get('predictions', {}) if prediction else None,
                    validation_results=validation.get('results', {}) if validation else None
                )
                
                self._facts_cache.append(stored_record)
        
        except Exception as e:
            print(f"Warning: Could not load ground truth cache: {e}")
            self._facts_cache = []
    
    def store_ground_truth(self, 
                          session_id: str,
                          ground_truth_facts: Dict[str, Any],
                          file_source: str = "",
                          system_predictions: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store ground truth facts for a session.
        
        Args:
            session_id: Unique identifier for the session
            ground_truth_facts: Objective market facts
            file_source: Source file for the session data
            system_predictions: Optional system predictions for comparison
            
        Returns:
            True if stored successfully
        """
        
        timestamp = datetime.now().isoformat()
        
        # Create storage record
        stored_record = StoredGroundTruth(
            session_id=session_id,
            timestamp=timestamp,
            file_source=file_source,
            ground_truth_facts=ground_truth_facts,
            system_predictions=system_predictions
        )
        
        # Add to cache
        self._facts_cache.append(stored_record)
        
        # Persist to files
        return self._persist_to_storage()
    
    def store_system_predictions(self, 
                                session_id: str,
                                predictions: Dict[str, Any]) -> bool:
        """
        Store system predictions for later validation.
        
        Args:
            session_id: Session identifier
            predictions: System predictions to validate later
            
        Returns:
            True if stored successfully
        """
        
        # Find existing record in cache
        for record in self._facts_cache:
            if record.session_id == session_id:
                record.system_predictions = predictions
                return self._persist_to_storage()
        
        # Create new record if not found
        timestamp = datetime.now().isoformat()
        stored_record = StoredGroundTruth(
            session_id=session_id,
            timestamp=timestamp,
            file_source="",
            ground_truth_facts={},
            system_predictions=predictions
        )
        
        self._facts_cache.append(stored_record)
        return self._persist_to_storage()
    
    def store_validation_results(self,
                                session_id: str,
                                validation_results: Dict[str, Any]) -> bool:
        """
        Store validation results comparing predictions vs ground truth.
        
        Args:
            session_id: Session identifier
            validation_results: Results of validation comparison
            
        Returns:
            True if stored successfully
        """
        
        # Find existing record in cache
        for record in self._facts_cache:
            if record.session_id == session_id:
                record.validation_results = validation_results
                return self._persist_to_storage()
        
        return False
    
    def get_ground_truth(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve ground truth facts for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Ground truth facts or None if not found
        """
        
        for record in self._facts_cache:
            if record.session_id == session_id:
                return record.ground_truth_facts
        
        return None
    
    def get_session_record(self, session_id: str) -> Optional[StoredGroundTruth]:
        """
        Get complete record for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Complete stored record or None if not found
        """
        
        for record in self._facts_cache:
            if record.session_id == session_id:
                return record
        
        return None
    
    def get_all_sessions(self) -> List[StoredGroundTruth]:
        """Get all stored session records"""
        return self._facts_cache.copy()
    
    def get_sessions_with_predictions(self) -> List[StoredGroundTruth]:
        """Get sessions that have both ground truth and predictions"""
        return [
            record for record in self._facts_cache
            if record.ground_truth_facts and record.system_predictions
        ]
    
    def get_sessions_for_validation(self) -> List[StoredGroundTruth]:
        """Get sessions ready for validation (have ground truth and predictions)"""
        return [
            record for record in self._facts_cache
            if (record.ground_truth_facts and 
                record.system_predictions and 
                not record.validation_results)
        ]
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """
        Generate summary of validation results.
        
        Returns:
            Summary statistics of ground truth vs predictions
        """
        
        total_sessions = len(self._facts_cache)
        sessions_with_ground_truth = len([r for r in self._facts_cache if r.ground_truth_facts])
        sessions_with_predictions = len([r for r in self._facts_cache if r.system_predictions])
        sessions_validated = len([r for r in self._facts_cache if r.validation_results])
        
        # Calculate accuracy metrics where available
        consolidation_accuracy = self._calculate_consolidation_accuracy()
        expansion_accuracy = self._calculate_expansion_accuracy()
        
        return {
            'storage_summary': {
                'total_sessions': total_sessions,
                'sessions_with_ground_truth': sessions_with_ground_truth,
                'sessions_with_predictions': sessions_with_predictions,
                'sessions_validated': sessions_validated,
                'validation_coverage': (sessions_validated / total_sessions * 100) if total_sessions > 0 else 0
            },
            'accuracy_metrics': {
                'consolidation_accuracy': consolidation_accuracy,
                'expansion_accuracy': expansion_accuracy
            },
            'epistemic_closure_detected': self._detect_epistemic_closure()
        }
    
    def _calculate_consolidation_accuracy(self) -> Optional[float]:
        """Calculate accuracy of consolidation predictions"""
        
        correct = 0
        total = 0
        
        for record in self._facts_cache:
            if not (record.ground_truth_facts and record.validation_results):
                continue
            
            val_results = record.validation_results
            if 'consolidation_accuracy' in val_results:
                total += 1
                if val_results['consolidation_accuracy'] == 'accurate':
                    correct += 1
        
        return (correct / total * 100) if total > 0 else None
    
    def _calculate_expansion_accuracy(self) -> Optional[float]:
        """Calculate accuracy of expansion predictions"""
        
        correct = 0
        total = 0
        
        for record in self._facts_cache:
            if not (record.ground_truth_facts and record.validation_results):
                continue
            
            val_results = record.validation_results
            if 'expansion_accuracy' in val_results:
                total += 1
                if val_results['expansion_accuracy'] == 'accurate':
                    correct += 1
        
        return (correct / total * 100) if total > 0 else None
    
    def _detect_epistemic_closure(self) -> List[str]:
        """Detect sessions showing epistemic closure problems"""
        
        closure_cases = []
        
        for record in self._facts_cache:
            if not record.validation_results:
                continue
            
            val_results = record.validation_results
            if val_results.get('potential_epistemic_closure', False):
                closure_cases.append(record.session_id)
        
        return closure_cases
    
    def _persist_to_storage(self) -> bool:
        """Persist current cache to storage files"""
        
        try:
            # Prepare facts data
            facts_data = {
                'metadata': {
                    'updated': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'total_sessions': len(self._facts_cache)
                },
                'facts': []
            }
            
            predictions_data = {
                'metadata': {
                    'updated': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'total_predictions': 0
                },
                'predictions': []
            }
            
            validations_data = {
                'metadata': {
                    'updated': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'total_validations': 0
                },
                'validations': []
            }
            
            # Build data structures from cache
            for record in self._facts_cache:
                if record.ground_truth_facts:
                    facts_data['facts'].append({
                        'session_id': record.session_id,
                        'timestamp': record.timestamp,
                        'file_source': record.file_source,
                        'ground_truth_facts': record.ground_truth_facts
                    })
                
                if record.system_predictions:
                    predictions_data['predictions'].append({
                        'session_id': record.session_id,
                        'timestamp': record.timestamp,
                        'predictions': record.system_predictions
                    })
                    predictions_data['metadata']['total_predictions'] += 1
                
                if record.validation_results:
                    validations_data['validations'].append({
                        'session_id': record.session_id,
                        'timestamp': record.timestamp,
                        'results': record.validation_results
                    })
                    validations_data['metadata']['total_validations'] += 1
            
            # Save to files
            save_json_data(facts_data, self.facts_file)
            save_json_data(predictions_data, self.predictions_file)
            save_json_data(validations_data, self.validation_file)
            
            return True
            
        except Exception as e:
            print(f"Error persisting ground truth storage: {e}")
            return False
    
    def export_validation_report(self, output_file: str) -> bool:
        """
        Export comprehensive validation report.
        
        Args:
            output_file: File to save the report
            
        Returns:
            True if exported successfully
        """
        
        summary = self.get_validation_summary()
        
        # Build detailed report
        report = {
            'report_metadata': {
                'generated': datetime.now().isoformat(),
                'report_type': 'ground_truth_validation',
                'version': '1.0.0'
            },
            'executive_summary': summary,
            'detailed_sessions': []
        }
        
        # Add detailed session data
        for record in self._facts_cache:
            if record.ground_truth_facts:
                session_detail = {
                    'session_id': record.session_id,
                    'timestamp': record.timestamp,
                    'file_source': record.file_source,
                    'has_ground_truth': bool(record.ground_truth_facts),
                    'has_predictions': bool(record.system_predictions),
                    'has_validation': bool(record.validation_results),
                    'ground_truth_summary': self._summarize_ground_truth(record.ground_truth_facts)
                }
                
                if record.validation_results:
                    session_detail['validation_summary'] = record.validation_results
                
                report['detailed_sessions'].append(session_detail)
        
        try:
            save_json_data(report, output_file)
            return True
        except Exception as e:
            print(f"Error exporting validation report: {e}")
            return False
    
    def _summarize_ground_truth(self, facts: Dict[str, Any]) -> Dict[str, Any]:
        """Create summary of ground truth facts"""
        
        return {
            'consolidation_occurred': facts.get('consolidation_occurred', False),
            'expansion_occurred': facts.get('expansion_occurred', False),
            'session_range': facts.get('session_range', 0),
            'net_direction': facts.get('net_movement_direction', 'unknown'),
            'volatility_character': facts.get('volatility_character', 'unknown')
        }


def create_ground_truth_store(storage_dir: str = "data/validation") -> GroundTruthStore:
    """Create ground truth storage system"""
    return GroundTruthStore(storage_directory=storage_dir)


if __name__ == "__main__":
    # Test the ground truth storage system
    print("🗄️ GROUND TRUTH STORAGE SYSTEM TEST")
    print("=" * 45)
    
    # Create storage system
    store = create_ground_truth_store()
    
    # Test storing sample ground truth
    sample_facts = {
        'consolidation_occurred': True,
        'expansion_occurred': False,
        'session_range': 18.5,
        'net_movement_direction': 'sideways',
        'volatility_character': 'low'
    }
    
    sample_predictions = {
        'predicted_consolidation': True,
        'predicted_expansion': False,
        'confidence': 0.75
    }
    
    session_id = f"TEST_SESSION_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"📊 Storing test session: {session_id}")
    
    # Store ground truth
    success = store.store_ground_truth(
        session_id=session_id,
        ground_truth_facts=sample_facts,
        file_source="test_session.json"
    )
    
    if success:
        print("✅ Ground truth stored successfully")
    else:
        print("❌ Failed to store ground truth")
    
    # Store predictions
    success = store.store_system_predictions(session_id, sample_predictions)
    
    if success:
        print("✅ System predictions stored successfully")
    else:
        print("❌ Failed to store predictions")
    
    # Generate summary
    summary = store.get_validation_summary()
    print(f"\n📈 Storage Summary:")
    print(f"   Total sessions: {summary['storage_summary']['total_sessions']}")
    print(f"   Ground truth coverage: {summary['storage_summary']['sessions_with_ground_truth']}")
    print(f"   Prediction coverage: {summary['storage_summary']['sessions_with_predictions']}")
    
    # Export report
    report_file = f"ground_truth_storage_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    if store.export_validation_report(report_file):
        print(f"📄 Validation report exported: {report_file}")
    else:
        print("❌ Failed to export validation report")
    
    print("\n✅ Ground truth storage system test complete")