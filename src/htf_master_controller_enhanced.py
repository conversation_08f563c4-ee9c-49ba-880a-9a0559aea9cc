#!/usr/bin/env python3
"""
HTF Master Controller Enhanced - Intelligence Data Integration

Enhanced HTF Master Controller that integrates with the new HTF Intelligence
system to use dynamic intelligence data instead of static calibration files.

This controller now reads from HTF context files with intelligence events
processed from Level 1 natural language transcriptions, providing more
accurate and current HTF event data for the Hawks algorithm.
"""

import json
import math
import numpy as np
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging


@dataclass
class IntelligenceHTFEvent:
    """Represents an HTF event from intelligence processing."""
    time: datetime
    event_type: str
    level: float
    htf_significance: str
    reference_date: str
    violated_on: str
    origin_session: str
    taken_by_session: str
    magnitude: float
    confidence: float
    source_file: str


@dataclass
class ActivationSignal:
    """Represents an activation signal for subordinate processes."""
    target_sessions: List[str]
    activation_window: str
    cascade_type: str
    param_adjustments: Dict[str, float]
    confidence_boost: float
    htf_intensity: float
    activation_time: datetime
    intelligence_events: List[IntelligenceHTFEvent]


class HTFMasterControllerEnhanced:
    """
    Enhanced HTF Master Controller with Intelligence Data Integration
    
    This enhanced controller integrates with the HTF Intelligence system to:
    1. Read HTF events from intelligence-processed context files
    2. Use dynamic HTF data instead of static calibration
    3. Provide more accurate HTF intensity calculations
    4. Generate intelligence-informed activation signals
    """
    
    def __init__(self, base_dir: str = "/Users/<USER>/grok-claude-automation"):
        self.base_dir = Path(base_dir)
        self.htf_dir = self.base_dir / "data" / "trackers" / "htf"
        
        # Setup logging first
        self._setup_logging()
        
        # Load HTF parameters (fallback to static if needed)
        self.htf_params = self._load_htf_parameters()
        self.session_gammas = self._load_session_gammas()
        
        # HTF activation threshold
        self.threshold_h = 0.5
        
        # Intelligence event cache
        self.intelligence_events_cache = []
        self.last_intelligence_update = None
        
        # Session targeting matrix
        self.temporal_marker_matrix = self._initialize_temporal_matrix()
        
    def _setup_logging(self):
        """Setup logging for enhanced controller."""
        self.logger = logging.getLogger("HTFMasterControllerEnhanced")
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - HTF_Enhanced - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def _load_htf_parameters(self) -> Dict[str, float]:
        """Load HTF parameters with intelligence enhancement."""
        # Try to load enhanced parameters first
        enhanced_params_file = self.base_dir / "htf_intelligence_parameters.json"
        
        if enhanced_params_file.exists():
            try:
                with open(enhanced_params_file, 'r') as f:
                    data = json.load(f)
                self.logger.info("Loaded enhanced HTF parameters from intelligence system")
                return data.get('htf_parameters', {})
            except Exception as e:
                self.logger.warning(f"Could not load enhanced HTF parameters: {e}")
        
        # Fallback to static calibration
        try:
            calibration_file = self.base_dir / "htf_calibrated_gammas_july28.json"
            with open(calibration_file, 'r') as f:
                data = json.load(f)
            self.logger.info("Using static HTF parameters (fallback)")
            return data['htf_parameters']
        except Exception as e:
            self.logger.warning(f"Could not load static HTF parameters: {e}")
            # Final fallback parameters
            return {
                "mu_h": 0.02,
                "alpha_h": 35.51,
                "beta_h": 0.00442
            }
    
    def _load_session_gammas(self) -> Dict[str, float]:
        """Load session gamma values with intelligence enhancement."""
        # Try enhanced gammas first
        enhanced_params_file = self.base_dir / "htf_intelligence_parameters.json"
        
        if enhanced_params_file.exists():
            try:
                with open(enhanced_params_file, 'r') as f:
                    data = json.load(f)
                gammas = data.get('session_gammas', {})
                if gammas:
                    self.logger.info("Loaded enhanced session gammas from intelligence system")
                    return gammas
            except Exception as e:
                self.logger.warning(f"Could not load enhanced session gammas: {e}")
        
        # Fallback to static calibration
        try:
            calibration_file = self.base_dir / "htf_calibrated_gammas_july28.json"
            with open(calibration_file, 'r') as f:
                data = json.load(f)
            self.logger.info("Using static session gammas (fallback)")
            return data['calibrated_gammas_july28']
        except Exception as e:
            self.logger.warning(f"Could not load static session gammas: {e}")
            # Final fallback gammas
            return {
                "Asia": 0.0895,
                "Midnight": 0.2987,
                "London": 0.1934,
                "Premarket": 0.1523,
                "NY_AM": 0.0278,
                "Lunch": 0.2534,
                "NY_PM": 0.000163
            }
    
    def _initialize_temporal_matrix(self) -> Dict[str, Dict[str, Any]]:
        """Initialize enhanced temporal marker matrix."""
        return {
            # Intelligence-based event types
            "daily_high_violated": {
                "target_sessions": ["NY_PM", "Asia"],
                "cascade_type": "expansion_lower",
                "confidence_factor": 0.90
            },
            "daily_low_violated": {
                "target_sessions": ["NY_PM", "London"],
                "cascade_type": "expansion_higher",
                "confidence_factor": 0.90
            },
            "session_high_violated": {
                "target_sessions": ["NY_PM", "NY_AM"],
                "cascade_type": "continuation_reversal",
                "confidence_factor": 0.85
            },
            "session_low_violated": {
                "target_sessions": ["NY_PM", "NY_AM"],
                "cascade_type": "continuation_reversal",
                "confidence_factor": 0.85
            },
            "premarket_high_violated": {
                "target_sessions": ["NY_AM", "Lunch"],
                "cascade_type": "gap_continuation",
                "confidence_factor": 0.80
            },
            "premarket_low_violated": {
                "target_sessions": ["NY_AM", "Lunch"],
                "cascade_type": "gap_continuation",
                "confidence_factor": 0.80
            },
            # Legacy event types (compatibility)
            "session_high_htf": {
                "target_sessions": ["NY_PM", "Asia"],
                "cascade_type": "expansion_lower",
                "confidence_factor": 0.85
            },
            "friday_close_htf": {
                "target_sessions": ["Asia", "Midnight", "London"],
                "cascade_type": "weekend_carryover",
                "confidence_factor": 0.92
            }
        }
    
    def load_intelligence_htf_events(self, force_reload: bool = False) -> List[IntelligenceHTFEvent]:
        """Load HTF events from intelligence-processed context files."""
        current_time = datetime.now()
        
        # Check cache validity (refresh every 2 minutes for dynamic data)
        if (not force_reload and self.intelligence_events_cache and 
            self.last_intelligence_update and 
            (current_time - self.last_intelligence_update).total_seconds() < 120):
            return self.intelligence_events_cache
        
        self.logger.info("Loading HTF events from intelligence context files...")
        
        intelligence_events = []
        files_processed = 0
        events_found = 0
        
        # Process all HTF context files with intelligence
        for htf_file in self.htf_dir.glob("HTF_*.json"):
            try:
                with open(htf_file, 'r') as f:
                    htf_data = json.load(f)
                
                files_processed += 1
                
                # Check if file has intelligence processing
                if not htf_data.get("intelligence_processed", False):
                    continue
                
                # Extract intelligence events
                htf_events = htf_data.get("htf_events", [])
                
                for event_data in htf_events:
                    try:
                        # Parse event timestamp
                        event_time = datetime.fromisoformat(event_data['timestamp'])
                        
                        # Calculate magnitude from level and context
                        level = event_data.get('level', 0.0)
                        magnitude = self._calculate_event_magnitude(event_data, htf_data)
                        
                        # Create intelligence HTF event
                        intel_event = IntelligenceHTFEvent(
                            time=event_time,
                            event_type=event_data.get('event_type', 'level_takeout'),
                            level=level,
                            htf_significance=event_data.get('htf_significance', ''),
                            reference_date=event_data.get('reference_date', ''),
                            violated_on=event_data.get('violated_on', ''),
                            origin_session=event_data.get('origin_session', ''),
                            taken_by_session=event_data.get('taken_by_session', ''),
                            magnitude=magnitude,
                            confidence=event_data.get('confidence', 0.95),
                            source_file=str(htf_file.name)
                        )
                        
                        intelligence_events.append(intel_event)
                        events_found += 1
                        
                    except Exception as e:
                        self.logger.warning(f"Could not parse intelligence event in {htf_file.name}: {e}")
                        continue
                        
            except Exception as e:
                self.logger.warning(f"Could not process HTF context file {htf_file.name}: {e}")
                continue
        
        # Update cache
        self.intelligence_events_cache = intelligence_events
        self.last_intelligence_update = current_time
        
        self.logger.info(f"Loaded {events_found} intelligence HTF events from {files_processed} context files")
        
        return intelligence_events
    
    def _calculate_event_magnitude(self, event_data: Dict[str, Any], htf_context: Dict[str, Any]) -> float:
        """Calculate event magnitude from intelligence data."""
        # Base magnitude from confidence
        base_magnitude = event_data.get('confidence', 0.95)
        
        # HTF significance multipliers
        htf_significance = event_data.get('htf_significance', '')
        significance_multipliers = {
            # Weekly level events (highest significance)
            'weekly_high': 3.0,
            'weekly_low': 3.0,
            
            # Daily level events (high significance)
            'daily_high': 2.5,
            'daily_low': 2.5,
            
            # Session-specific events (medium-high significance)
            'asia_session_low': 2.2,      # 🔥 CRITICAL MISSING MULTIPLIER
            'london_session_high': 2.3,   # 🔥 CRITICAL MISSING MULTIPLIER
            'asia_session_high': 2.1,
            'london_session_low': 2.0,
            'ny_pm_session_high': 1.9,
            'ny_am_session_high': 1.8,
            'ny_pm_session_low': 1.8,
            'ny_am_session_low': 1.7,
            
            # Generic session events (medium significance)
            'session_high': 1.5,
            'session_low': 1.5,
            
            # Premarket events (lower significance)
            'premarket_high': 1.2,
            'premarket_low': 1.2
        }
        
        # Find multiplier based on significance
        multiplier = 1.0
        for sig_type, mult in significance_multipliers.items():
            if sig_type in htf_significance:
                multiplier = mult
                break
        
        # HTF influence factor enhancement
        htf_influence = htf_context.get('htf_influence_factor', 0.0)
        influence_boost = 1.0 + (htf_influence * 0.5)
        
        # Calculate final magnitude
        magnitude = base_magnitude * multiplier * influence_boost
        
        return min(magnitude, 5.0)  # Cap at 5.0 for stability
    
    def calculate_htf_intensity(self, target_time: Optional[datetime] = None) -> float:
        """Calculate HTF intensity using intelligence events."""
        if target_time is None:
            target_time = datetime.now()
            
        # Load intelligence events
        intelligence_events = self.load_intelligence_htf_events()
        
        if not intelligence_events:
            self.logger.warning("No intelligence events available, using baseline intensity")
            return self.htf_params["mu_h"]
        
        # HTF Hawkes process calculation: λ_HTF(t) = μ_h + Σ α_h · exp(-β_h (t - t_j)) · magnitude_j
        mu_h = self.htf_params["mu_h"]
        alpha_h = self.htf_params["alpha_h"]
        beta_h = self.htf_params["beta_h"]
        
        intensity = mu_h
        influence_sum = 0.0
        valid_events = 0
        
        for event in intelligence_events:
            # Only consider events before target time
            if event.time > target_time:
                continue
            
            # Calculate time difference in hours
            time_diff_hours = (target_time - event.time).total_seconds() / 3600
            
            # Skip very old events (beyond 7 days)
            if time_diff_hours > 168:  # 7 days
                continue
            
            # Calculate exponential decay
            decay = math.exp(-beta_h * time_diff_hours)
            
            # Add event influence
            event_influence = alpha_h * decay * event.magnitude
            influence_sum += event_influence
            valid_events += 1
        
        intensity += influence_sum
        
        self.logger.info(f"HTF intensity calculated: {intensity:.2f} (baseline: {mu_h:.2f}, influence: {influence_sum:.2f}, events: {valid_events})")
        
        return intensity
    
    def generate_intelligence_activation_signal(self, target_time: Optional[datetime] = None) -> Optional[ActivationSignal]:
        """Generate activation signal based on intelligence HTF intensity."""
        if target_time is None:
            target_time = datetime.now()
            
        # Calculate current HTF intensity
        htf_intensity = self.calculate_htf_intensity(target_time)
        
        # Check if intensity exceeds activation threshold
        if htf_intensity <= self.threshold_h:
            self.logger.info(f"HTF intensity {htf_intensity:.2f} below threshold {self.threshold_h}")
            return None
        
        # Load recent intelligence events for context
        intelligence_events = self.load_intelligence_htf_events()
        recent_events = [
            event for event in intelligence_events
            if (target_time - event.time).total_seconds() < 86400  # Last 24 hours
        ]
        
        if not recent_events:
            self.logger.warning("HTF intensity above threshold but no recent intelligence events found")
            return None
        
        # Determine dominant event type and target sessions
        event_types = {}
        for event in recent_events:
            sig_parts = event.htf_significance.split('_')
            if len(sig_parts) >= 2:
                event_type = f"{sig_parts[0]}_{sig_parts[1]}"  # e.g. "daily_high", "session_low"
                event_types[event_type] = event_types.get(event_type, 0) + event.magnitude
        
        # Find dominant event type
        if not event_types:
            dominant_event_type = "session_high_htf"  # fallback
        else:
            dominant_event_type = max(event_types, key=event_types.get)
        
        # Get targeting info from temporal matrix
        targeting_info = self.temporal_marker_matrix.get(dominant_event_type, {
            "target_sessions": ["NY_PM"],
            "cascade_type": "general_activation",
            "confidence_factor": 0.8
        })
        
        # Calculate parameter adjustments
        baseline_boost = htf_intensity / self.threshold_h
        confidence_boost = min(1.5, baseline_boost ** 0.5)
        
        param_adjustments = {
            "baseline_boost": baseline_boost,
            "decay_gamma": 0.143,  # Default gamma
            "confidence_boost": confidence_boost
        }
        
        # Create activation signal
        activation_signal = ActivationSignal(
            target_sessions=targeting_info["target_sessions"],
            activation_window=self._determine_activation_window(target_time),
            cascade_type=targeting_info["cascade_type"],
            param_adjustments=param_adjustments,
            confidence_boost=confidence_boost,
            htf_intensity=htf_intensity,
            activation_time=target_time,
            intelligence_events=recent_events
        )
        
        self.logger.info(f"Generated intelligence activation signal: {len(targeting_info['target_sessions'])} target sessions, {htf_intensity:.2f} intensity, {len(recent_events)} recent events")
        
        return activation_signal
    
    def _determine_activation_window(self, target_time: datetime) -> str:
        """Determine activation window based on current time and market session."""
        hour = target_time.hour
        
        # Market session windows (ET)
        if 19 <= hour <= 23:  # 7 PM - 11 PM
            return "asia_session"
        elif 0 <= hour <= 6:   # 12 AM - 6 AM
            return "midnight_london"
        elif 7 <= hour <= 11:  # 7 AM - 11 AM  
            return "premarket_nyam"
        elif 12 <= hour <= 17: # 12 PM - 5 PM
            return "lunch_nypm"
        else:
            return "general_market"
    
    def get_intelligence_summary(self) -> Dict[str, Any]:
        """Get summary of intelligence data integration."""
        intelligence_events = self.load_intelligence_htf_events()
        current_intensity = self.calculate_htf_intensity()
        
        # Event type analysis
        event_types = {}
        for event in intelligence_events:
            sig_parts = event.htf_significance.split('_')
            if len(sig_parts) >= 2:
                event_type = f"{sig_parts[0]}_{sig_parts[1]}"
                event_types[event_type] = event_types.get(event_type, 0) + 1
        
        return {
            "intelligence_integration_active": True,
            "total_intelligence_events": len(intelligence_events),
            "current_htf_intensity": current_intensity,
            "activation_threshold": self.threshold_h,
            "threshold_exceeded": current_intensity > self.threshold_h,
            "event_types_distribution": event_types,
            "last_intelligence_update": self.last_intelligence_update.isoformat() if self.last_intelligence_update else None,
            "parameters_source": "intelligence_enhanced" if (self.base_dir / "htf_intelligence_parameters.json").exists() else "static_fallback"
        }
    
    def validate_intelligence_integration(self) -> Dict[str, Any]:
        """Validate intelligence integration health."""
        validation_results = {
            "status": "healthy",
            "issues": [],
            "warnings": [],
            "recommendations": []
        }
        
        # Check HTF context files availability
        htf_files = list(self.htf_dir.glob("HTF_*.json"))
        if not htf_files:
            validation_results["status"] = "error"
            validation_results["issues"].append("No HTF context files found")
        
        # Check intelligence processing
        intelligence_files = 0
        for htf_file in htf_files:
            try:
                with open(htf_file, 'r') as f:
                    htf_data = json.load(f)
                if htf_data.get("intelligence_processed", False):
                    intelligence_files += 1
            except:
                continue
        
        if intelligence_files == 0:
            validation_results["status"] = "warning"
            validation_results["warnings"].append("No intelligence-processed HTF files found")
            validation_results["recommendations"].append("Run HTF Intelligence Integration to process Level 1 data")
        
        # Check parameter files
        enhanced_params_file = self.base_dir / "htf_intelligence_parameters.json"
        if not enhanced_params_file.exists():
            validation_results["warnings"].append("Enhanced parameters file not found, using static fallback")
            validation_results["recommendations"].append("Generate enhanced parameters from intelligence data")
        
        # Check event availability
        intelligence_events = self.load_intelligence_htf_events()
        if len(intelligence_events) < 5:
            validation_results["warnings"].append(f"Only {len(intelligence_events)} intelligence events available")
            validation_results["recommendations"].append("Process more Level 1 sessions to increase event coverage")
        
        validation_results["intelligence_files"] = intelligence_files
        validation_results["total_htf_files"] = len(htf_files)
        validation_results["intelligence_events"] = len(intelligence_events)
        
        return validation_results


def main():
    """Demo Enhanced HTF Master Controller with Intelligence Integration."""
    print("🧠 Enhanced HTF Master Controller - Intelligence Demo")
    print("=" * 60)
    
    controller = HTFMasterControllerEnhanced()
    
    # Intelligence summary
    print("\n📊 Intelligence Integration Summary:")
    summary = controller.get_intelligence_summary()
    
    print(f"Intelligence Integration: {'✅ Active' if summary['intelligence_integration_active'] else '❌ Inactive'}")
    print(f"Intelligence Events: {summary['total_intelligence_events']}")
    print(f"Current HTF Intensity: {summary['current_htf_intensity']:.2f}")
    print(f"Activation Threshold: {summary['activation_threshold']}")
    print(f"Threshold Exceeded: {'✅ Yes' if summary['threshold_exceeded'] else '❌ No'}")
    print(f"Parameters Source: {summary['parameters_source']}")
    
    if summary['event_types_distribution']:
        print(f"\n🎯 Event Types Distribution:")
        for event_type, count in summary['event_types_distribution'].items():
            print(f"  • {event_type}: {count}")
    
    # Validation
    print(f"\n🔍 Intelligence Integration Validation:")
    validation = controller.validate_intelligence_integration()
    
    print(f"Status: {validation['status']}")
    print(f"Intelligence Files: {validation['intelligence_files']}/{validation['total_htf_files']}")
    print(f"Intelligence Events: {validation['intelligence_events']}")
    
    if validation['issues']:
        print(f"❌ Issues: {validation['issues']}")
    
    if validation['warnings']:
        print(f"⚠️ Warnings: {validation['warnings']}")
    
    if validation['recommendations']:
        print(f"💡 Recommendations:")
        for rec in validation['recommendations']:
            print(f"  • {rec}")
    
    # Test activation signal generation
    print(f"\n🚀 Testing Activation Signal Generation:")
    activation_signal = controller.generate_intelligence_activation_signal()
    
    if activation_signal:
        print(f"✅ Activation Signal Generated:")
        print(f"  Target Sessions: {activation_signal.target_sessions}")
        print(f"  Cascade Type: {activation_signal.cascade_type}")
        print(f"  HTF Intensity: {activation_signal.htf_intensity:.2f}")
        print(f"  Confidence Boost: {activation_signal.confidence_boost:.2f}")
        print(f"  Intelligence Events: {len(activation_signal.intelligence_events)}")
    else:
        print(f"❌ No activation signal generated (intensity below threshold)")


if __name__ == "__main__":
    main()