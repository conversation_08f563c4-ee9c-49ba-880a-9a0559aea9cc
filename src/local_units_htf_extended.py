#!/usr/bin/env python3
"""
Extended LocalUnitA with Multi-Scale Hawkes Modeling
Implements Grok's sophisticated HTF intensity coupling with fractal modeling.

Key Features:
- HTF intensity function: λ_HTF(t) = μ_h + Σ α_h · exp(-β_h (t - t_j))
- Fractal coupling: λ_session(t) = μ_s + Σ α_s · exp(-β_s (t - t_i)) + γ · λ_HTF(t)
- Adaptive γ(t) based on proximity, liquidity, and news context
- Efficient NumPy/SciPy computation with β_h = 0.001 (16.7-hour horizon)

Mathematical Foundation:
- β_h = 0.001: ~1000-minute (16.7-hour) influence horizon for weekly cycles
- Initial parameters: μ_h = 0.05, α_h = 0.4 based on multi-scale literature
- Session parameters: μ_s = 0.15, α_s = 0.6, β_s = 0.02 (existing system)
"""

import numpy as np
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

# Import existing local units and HTF detector
from .local_units import LocalUnitA, LocalUnitResult
from .htf_event_detector import create_htf_event_detector, HTFEvent


@dataclass
class HTFIntensityResult:
    """Result structure for HTF intensity calculations."""
    htf_intensity_timeline: List[float]
    session_intensity_timeline: List[float]
    coupled_intensity_timeline: List[float]
    gamma_timeline: List[float]
    htf_events_used: List[HTFEvent]
    processing_time_ms: float
    intensity_metadata: Dict[str, Any]


class LocalUnitAExtended(LocalUnitA):
    """
    Extended LocalUnitA with Multi-Scale Hawkes Process Integration
    
    Extends the base LocalUnitA with HTF event detection and fractal coupling
    between session-level and weekly-level Hawkes processes.
    """
    
    def __init__(self, beta_s: float = 0.02, alpha_s: float = 0.6, mu_s: float = 0.15,
                 beta_h: float = 0.001, alpha_h: float = 0.4, mu_h: float = 0.05, 
                 gamma_base: float = 0.3):
        """
        Initialize extended Unit A with multi-scale Hawkes parameters.
        
        Args:
            beta_s: Session decay parameter (existing system)
            alpha_s: Session excitation parameter (existing system)
            mu_s: Session baseline intensity (existing system)
            beta_h: HTF decay parameter (16.7-hour horizon)
            alpha_h: HTF excitation parameter (literature-based)
            mu_h: HTF baseline intensity (literature-based)
            gamma_base: Base coupling strength between scales
        """
        super().__init__()
        
        # Session-level Hawkes parameters (existing)
        self.beta_s = beta_s
        self.alpha_s = alpha_s
        self.mu_s = mu_s
        
        # HTF Hawkes parameters (new)
        self.beta_h = beta_h  # 0.001 = ~1000 minutes = 16.7 hours
        self.alpha_h = alpha_h
        self.mu_h = mu_h
        
        # Coupling parameters
        self.gamma_base = gamma_base
        self.gamma_delta = 0.1  # Adaptive coupling adjustment range
        
        # HTF event detector
        self.htf_detector = create_htf_event_detector()
        
        # Performance tracking
        self.intensity_calculations_count = 0
        self.htf_events_cache = []
        
        print("🔗 HTF EXTENDED UNIT A: Multi-scale Hawkes process initialized")
        print(f"   HTF Parameters: μ_h={mu_h}, α_h={alpha_h}, β_h={beta_h}")
        print(f"   Coupling: γ_base={gamma_base} (16.7-hour HTF horizon)")
    
    def process(self, session_data: Dict[Any, Any], 
                unit_a_results: Optional[Dict] = None,
                historical_sessions: Optional[List[Dict[Any, Any]]] = None,
                enable_htf_coupling: bool = True) -> LocalUnitResult:
        """
        Process Unit A with HTF coupling extension.
        
        Args:
            session_data: Current session data
            unit_a_results: Optional previous results (compatibility)
            historical_sessions: Historical sessions for HTF context
            enable_htf_coupling: Whether to enable HTF coupling
            
        Returns:
            LocalUnitResult with HTF intensity integration
        """
        start_time = time.time()
        
        # Execute base Unit A processing
        base_result = super().process(session_data, unit_a_results)
        
        # HTF intensity calculation (if enabled)
        htf_result = None
        if enable_htf_coupling:
            htf_result = self._calculate_htf_intensity_coupling(
                session_data, historical_sessions, base_result.calculations
            )
        
        # Enhanced processing time
        processing_time = (time.time() - start_time) * 1000
        
        # Combine base and HTF results
        enhanced_calculations = base_result.calculations.copy()
        if htf_result:
            enhanced_calculations['htf_intensity_coupling'] = {
                'htf_intensity_timeline': htf_result.htf_intensity_timeline,
                'coupled_intensity_timeline': htf_result.coupled_intensity_timeline,
                'gamma_adaptive': htf_result.gamma_timeline,
                'htf_events_detected': len(htf_result.htf_events_used),
                'htf_processing_time_ms': htf_result.processing_time_ms,
                'intensity_metadata': htf_result.intensity_metadata
            }
        
        return LocalUnitResult(
            unit_name="Unit A Extended (HTF)",
            processing_time_ms=processing_time,
            calculations=enhanced_calculations,
            metadata={
                **base_result.metadata,
                'htf_coupling_enabled': enable_htf_coupling,
                'htf_intensity_calculated': htf_result is not None,
                'multi_scale_hawkes': True
            }
        )
    
    def _calculate_htf_intensity_coupling(self, session_data: Dict[Any, Any],
                                        historical_sessions: Optional[List[Dict[Any, Any]]],
                                        base_calculations: Dict[str, Any]) -> HTFIntensityResult:
        """
        Calculate HTF intensity coupling with session-level Hawkes process.
        
        Implements:
        - λ_HTF(t) = μ_h + Σ α_h · exp(-β_h (t - t_j))
        - λ_session(t) = μ_s + Σ α_s · exp(-β_s (t - t_i)) + γ(t) · λ_HTF(t)
        """
        start_time = time.time()
        
        print("🔗 HTF COUPLING: Computing multi-scale intensity")
        
        # Detect HTF events from current session and historical context
        htf_events = self.htf_detector.detect_htf_events(session_data, historical_sessions)
        
        # Get additional HTF events from detector history for intensity calculation
        historical_htf_events = self.htf_detector.get_htf_events_for_intensity(lookback_hours=24)
        all_htf_events = htf_events + historical_htf_events
        
        # Extract session-level events (cascade events)
        micro_timing = session_data.get('micro_timing_analysis', {})
        session_events = micro_timing.get('cascade_events', [])
        
        # Session duration for intensity timeline
        session_metadata = session_data.get('session_metadata', {})
        duration_minutes = session_metadata.get('duration_minutes', 300)
        
        # Calculate intensity timelines
        timeline_minutes = min(duration_minutes, 180)  # Limit to 180 minutes for performance
        time_points = np.linspace(0, timeline_minutes, timeline_minutes)
        
        htf_intensity_timeline = []
        session_intensity_timeline = []
        coupled_intensity_timeline = []
        gamma_timeline = []
        
        for t in time_points:
            # HTF intensity calculation
            htf_intensity = self._compute_htf_intensity(all_htf_events, t)
            
            # Session intensity calculation (existing logic)  
            session_intensity = self._compute_session_intensity(session_events, t)
            
            # Adaptive gamma calculation
            gamma_t = self._compute_adaptive_gamma(session_data, base_calculations, t)
            
            # Coupled intensity (fractal coupling)
            coupled_intensity = session_intensity + gamma_t * htf_intensity
            
            htf_intensity_timeline.append(htf_intensity)
            session_intensity_timeline.append(session_intensity)
            coupled_intensity_timeline.append(coupled_intensity)
            gamma_timeline.append(gamma_t)
        
        processing_time = (time.time() - start_time) * 1000
        self.intensity_calculations_count += len(time_points)
        
        print(f"🎯 HTF COUPLING completed: {len(time_points)} intensity points in {processing_time:.1f}ms")
        print(f"   HTF Events Used: {len(all_htf_events)}")
        print(f"   Average γ(t): {np.mean(gamma_timeline):.3f}")
        
        return HTFIntensityResult(
            htf_intensity_timeline=htf_intensity_timeline,
            session_intensity_timeline=session_intensity_timeline,
            coupled_intensity_timeline=coupled_intensity_timeline,
            gamma_timeline=gamma_timeline,
            htf_events_used=all_htf_events,
            processing_time_ms=processing_time,
            intensity_metadata={
                'timeline_points': len(time_points),
                'htf_events_count': len(all_htf_events),
                'session_events_count': len(session_events),
                'max_htf_intensity': max(htf_intensity_timeline) if htf_intensity_timeline else 0,
                'max_coupled_intensity': max(coupled_intensity_timeline) if coupled_intensity_timeline else 0,
                'gamma_range': [min(gamma_timeline), max(gamma_timeline)] if gamma_timeline else [0, 0]
            }
        )
    
    def _compute_htf_intensity(self, htf_events: List[HTFEvent], t: float) -> float:
        """
        Compute HTF intensity at time t.
        
        Formula: λ_HTF(t) = μ_h + Σ α_h · exp(-β_h (t - t_j))
        
        Args:
            htf_events: List of HTF events
            t: Current time in minutes
            
        Returns:
            HTF intensity value
        """
        if not htf_events:
            return self.mu_h
        
        intensity = self.mu_h
        
        for event in htf_events:
            # Convert event timestamp to minutes offset (simplified)
            event_time = self._parse_event_time_to_minutes(event.timestamp)
            
            if event_time is not None and event_time < t:
                time_diff = t - event_time
                
                # HTF decay with long horizon (β_h = 0.001)
                decay_factor = np.exp(-self.beta_h * time_diff)
                
                # Weight by event confidence
                weighted_alpha = self.alpha_h * event.confidence
                
                intensity += weighted_alpha * decay_factor
        
        return intensity
    
    def _compute_session_intensity(self, session_events: List[Dict], t: float) -> float:
        """
        Compute session-level intensity at time t.
        
        Formula: λ_session(t) = μ_s + Σ α_s · exp(-β_s (t - t_i))
        
        Args:
            session_events: List of session cascade events
            t: Current time in minutes
            
        Returns:
            Session intensity value
        """
        if not session_events:
            return self.mu_s
        
        intensity = self.mu_s
        
        for event in session_events:
            # Convert event timestamp to minutes offset
            event_time = self._parse_session_event_time_to_minutes(event.get('timestamp', ''))
            
            if event_time is not None and event_time < t:
                time_diff = t - event_time
                
                # Session decay (β_s = 0.02)
                decay_factor = np.exp(-self.beta_s * time_diff)
                
                # Scale by event magnitude
                magnitude = event.get('magnitude', 1.0)
                magnitude_weight = min(2.0, magnitude / 25.0)  # Normalize around 25 points
                
                weighted_alpha = self.alpha_s * magnitude_weight
                
                intensity += weighted_alpha * decay_factor
        
        return intensity
    
    def _compute_adaptive_gamma(self, session_data: Dict[Any, Any], 
                              base_calculations: Dict[str, Any], t: float) -> float:
        """
        Compute adaptive coupling parameter γ(t).
        
        Formula: γ(t) = γ_base + δ · f(proximity, liquidity, news)
        
        Args:
            session_data: Session data for context
            base_calculations: Base Unit A calculations
            t: Current time in minutes
            
        Returns:
            Adaptive gamma value
        """
        gamma_t = self.gamma_base
        
        # Proximity factor (weekly cycle phase)
        proximity_factor = self._calculate_proximity_factor(session_data, t)
        
        # Liquidity factor
        liquidity_factor = self._calculate_liquidity_factor(session_data)
        
        # News factor (simplified - would integrate with news_context in full implementation)
        news_factor = self._calculate_news_factor(session_data)
        
        # Combined adjustment
        f_adjustment = proximity_factor + liquidity_factor + news_factor
        gamma_adjustment = self.gamma_delta * f_adjustment
        
        # Apply bounds [0.1, 0.8]
        gamma_t = max(0.1, min(0.8, gamma_t + gamma_adjustment))
        
        return gamma_t
    
    def _calculate_proximity_factor(self, session_data: Dict[Any, Any], t: float) -> float:
        """Calculate proximity factor based on weekly cycle phase."""
        session_metadata = session_data.get('session_metadata', {})
        session_date = session_metadata.get('date', '')
        
        try:
            from datetime import datetime
            date_obj = datetime.strptime(session_date, '%Y-%m-%d')
            weekday = date_obj.weekday()  # Monday = 0
            
            # Higher coupling on Tuesday (weekday = 1)
            if weekday == 1:  # Tuesday
                return 0.3
            elif weekday == 0:  # Monday  
                return 0.2
            elif weekday == 4:  # Friday
                return 0.2
            else:
                return 0.0
        except:
            return 0.0
    
    def _calculate_liquidity_factor(self, session_data: Dict[Any, Any]) -> float:
        """Calculate liquidity factor from untaken liquidity analysis."""
        liquidity_analysis = session_data.get('liquidity_analysis', {})
        untaken_liquidity = liquidity_analysis.get('untaken_liquidity', [])
        
        if not untaken_liquidity:
            return 0.0
        
        # Calculate imbalance ratio
        buy_side = sum(1 for level in untaken_liquidity if level.get('side', '') == 'buy')
        sell_side = sum(1 for level in untaken_liquidity if level.get('side', '') == 'sell')
        
        if sell_side == 0 and buy_side > 0:
            imbalance_ratio = 2.0
        elif buy_side == 0 and sell_side > 0:
            imbalance_ratio = 2.0
        elif sell_side > 0:
            imbalance_ratio = max(buy_side / sell_side, sell_side / buy_side)
        else:
            imbalance_ratio = 1.0
        
        # Convert to factor
        if imbalance_ratio > 1.5:
            return 0.2
        elif imbalance_ratio > 1.2:
            return 0.1
        else:
            return 0.0
    
    def _calculate_news_factor(self, session_data: Dict[Any, Any]) -> float:
        """Calculate news factor (simplified implementation)."""
        # This would integrate with news_context.news_events in full implementation
        # For now, use session character as proxy
        
        session_character = session_data.get('price_data', {}).get('session_character', '')
        
        # High volatility sessions may have news impact
        if 'expansion' in session_character.lower():
            return 0.1
        elif 'major' in session_character.lower():
            return 0.2
        else:
            return 0.0
    
    def _parse_event_time_to_minutes(self, timestamp: str) -> Optional[float]:
        """Parse HTF event timestamp to minutes offset (simplified)."""
        try:
            # Simplified parsing - in full implementation would handle proper timestamps
            if ':' in timestamp:
                parts = timestamp.split(':')
                hours = int(parts[0]) if parts[0].isdigit() else 0
                minutes = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else 0
                return hours * 60 + minutes
            return 0.0
        except:
            return None
    
    def _parse_session_event_time_to_minutes(self, timestamp: str) -> Optional[float]:
        """Parse session event timestamp to minutes offset."""
        try:
            # Extract time portion (e.g., "14:23:00" -> 14*60 + 23 = 863 minutes)
            if ':' in timestamp:
                time_part = timestamp.split('T')[-1].split('.')[0]  # Handle ISO format
                parts = time_part.split(':')
                hours = int(parts[0])
                minutes = int(parts[1])
                
                # Convert to session-relative time (assuming session starts at market open)
                # This is simplified - full implementation would handle proper time conversion
                session_minutes = (hours - 9) * 60 + minutes  # Assuming 9 AM start
                return max(0, session_minutes)
            return 0.0
        except:
            return None
    
    def get_htf_parameters(self) -> Dict[str, float]:
        """Get current HTF Hawkes parameters."""
        return {
            'mu_h': self.mu_h,
            'alpha_h': self.alpha_h,
            'beta_h': self.beta_h,
            'gamma_base': self.gamma_base,
            'gamma_delta': self.gamma_delta,
            'htf_horizon_hours': 1.0 / (self.beta_h * 60.0)  # Convert to hours
        }
    
    def update_htf_parameters(self, mu_h: Optional[float] = None, 
                            alpha_h: Optional[float] = None,
                            beta_h: Optional[float] = None,
                            gamma_base: Optional[float] = None) -> None:
        """Update HTF parameters (for Bayesian inference integration)."""
        if mu_h is not None:
            self.mu_h = mu_h
        if alpha_h is not None:
            self.alpha_h = alpha_h
        if beta_h is not None:
            self.beta_h = beta_h
        if gamma_base is not None:
            self.gamma_base = gamma_base
        
        print(f"🔧 HTF Parameters Updated: μ_h={self.mu_h}, α_h={self.alpha_h}, β_h={self.beta_h}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get HTF processing statistics."""
        return {
            'intensity_calculations_performed': self.intensity_calculations_count,
            'htf_events_cached': len(self.htf_events_cache),
            'current_parameters': self.get_htf_parameters(),
            'detector_status': self.htf_detector.get_detector_status()
        }


def create_local_unit_a_extended(**kwargs) -> LocalUnitAExtended:
    """
    Factory function to create extended Unit A with HTF coupling.
    
    Args:
        **kwargs: HTF parameters (beta_h, alpha_h, mu_h, gamma_base)
    """
    return LocalUnitAExtended(**kwargs)


if __name__ == "__main__":
    # Test extended Unit A with HTF coupling
    print("🧪 TESTING HTF EXTENDED UNIT A")
    print("=" * 60)
    
    # Create extended unit
    unit_a_extended = create_local_unit_a_extended()
    
    # Mock session data with HTF context
    mock_session = {
        'session_metadata': {
            'date': '2025-07-25',  # Friday
            'session_type': 'NY_PM',
            'duration_minutes': 159
        },
        'price_data': {
            'high': 23459.75,
            'low': 23407.0,
            'range': 52.75,
            'session_character': 'expansion_consolidation_major_downward_movement'
        },
        'micro_timing_analysis': {
            'cascade_events': [
                {
                    'timestamp': '15:38:00',
                    'event_type': 'major_cascade',
                    'price_level': 23450.75,
                    'magnitude': 43.75,
                    'duration_minutes': 22.0
                }
            ]
        },
        'liquidity_analysis': {
            'untaken_liquidity': [
                {'side': 'buy', 'level': 23400},
                {'side': 'sell', 'level': 23460},
                {'side': 'sell', 'level': 23465}
            ]
        }
    }
    
    # Test HTF-extended processing
    result = unit_a_extended.process(mock_session, enable_htf_coupling=True)
    
    print(f"🎯 HTF Extended Unit A Results:")
    print(f"  Processing Time: {result.processing_time_ms:.2f}ms")
    print(f"  HTF Coupling Enabled: {result.metadata.get('htf_coupling_enabled', False)}")
    print(f"  Multi-Scale Hawkes: {result.metadata.get('multi_scale_hawkes', False)}")
    
    # Show HTF coupling results
    htf_coupling = result.calculations.get('htf_intensity_coupling', {})
    if htf_coupling:
        print(f"  HTF Events Detected: {htf_coupling.get('htf_events_detected', 0)}")
        print(f"  HTF Processing Time: {htf_coupling.get('htf_processing_time_ms', 0):.2f}ms")
        
        metadata = htf_coupling.get('intensity_metadata', {})
        print(f"  Max HTF Intensity: {metadata.get('max_htf_intensity', 0):.3f}")
        print(f"  Max Coupled Intensity: {metadata.get('max_coupled_intensity', 0):.3f}")
        
        gamma_range = metadata.get('gamma_range', [0, 0])
        print(f"  γ(t) Range: [{gamma_range[0]:.3f}, {gamma_range[1]:.3f}]")
    
    # Show HTF parameters
    params = unit_a_extended.get_htf_parameters()
    print(f"\n🔧 HTF Parameters:")
    print(f"  HTF Horizon: {params['htf_horizon_hours']:.1f} hours")
    print(f"  Base Coupling (γ): {params['gamma_base']:.3f}")
    print(f"  HTF Baseline (μ_h): {params['mu_h']:.3f}")
    print(f"  HTF Excitation (α_h): {params['alpha_h']:.3f}")
    
    # Processing statistics
    stats = unit_a_extended.get_processing_stats()
    print(f"\n📊 Processing Statistics:")
    print(f"  Intensity Calculations: {stats['intensity_calculations_performed']}")
    print(f"  HTF Events Cached: {stats['htf_events_cached']}")
    
    print(f"\n✅ HTF Extended Unit A testing completed!")