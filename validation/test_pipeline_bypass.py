#!/usr/bin/env python3
"""
Quick Pipeline Bypass Test
Tests the pipeline with bypassed units to verify Fix #1 and #2 are working
"""

import os
import time
from src.utils import load_json_data
from src.hawkes_cascade_predictor import HawkesCascadePredictor
from src.cache_manager import get_unified_cache

def test_hawkes_with_bypass():
    """Test just the Hawkes predictor to verify our optimizations work independently."""
    print("🧪 HAWKES OPTIMIZATION TEST (BYPASS MODE)")
    print("=" * 50)
    
    try:
        # Load session data
        session_data = load_json_data('ASIA_Lvl-1_2025_07_29.json.json')
        print(f"✅ Loaded session: {len(session_data)} entries")
        
        # Test Hawkes predictor directly (this should be fast now)
        predictor = HawkesCascadePredictor()
        cache = get_unified_cache()
        
        # Clear cache to test fresh
        cache.clear()
        initial_stats = cache.get_stats()
        
        print(f"\n🚀 Running Hawkes prediction...")
        start_time = time.time()
        
        # Run prediction (should use our optimizations)
        prediction = predictor.predict_cascade_timing(session_data)
        
        end_time = time.time()
        final_stats = cache.get_stats()
        
        print(f"\n📊 RESULTS:")
        print(f"  ⏱️ Execution time: {end_time - start_time:.3f}s")
        print(f"  🎯 Cascade time: {prediction.predicted_cascade_time:.1f} minutes")
        print(f"  🔥 Confidence: {prediction.prediction_confidence:.3f}")
        print(f"  🔢 Iterations: {len(prediction.intensity_buildup)}/180")
        print(f"  💾 Cache hits: {final_stats['hits'] - initial_stats['hits']}")
        print(f"  💾 Cache misses: {final_stats['misses'] - initial_stats['misses']}")
        
        # Test optimization effectiveness
        if end_time - start_time < 1.0:
            print(f"  ✅ FIX #1 WORKING: Fast execution (<1s)")
        else:
            print(f"  ❌ FIX #1 ISSUE: Slow execution (>1s)")
            
        if len(prediction.intensity_buildup) < 20:
            print(f"  ✅ EARLY TERMINATION: {180 - len(prediction.intensity_buildup)} iterations saved")
        else:
            print(f"  ❌ EARLY TERMINATION: Only {180 - len(prediction.intensity_buildup)} iterations saved")
            
        if final_stats['hits'] > initial_stats['hits']:
            print(f"  ✅ FIX #2 WORKING: Cache hits detected")
        else:
            print(f"  ⚠️ FIX #2 WARNING: No cache hits (expected for first run)")
            
        # Test cache with repeated calculation
        print(f"\n🔄 Testing cache efficiency with repeat calculation...")
        repeat_start = time.time()
        prediction2 = predictor.predict_cascade_timing(session_data)
        repeat_end = time.time()
        repeat_stats = cache.get_stats()
        
        print(f"  ⏱️ Repeat execution: {repeat_end - repeat_start:.3f}s")
        print(f"  💾 Additional cache hits: {repeat_stats['hits'] - final_stats['hits']}")
        
        if repeat_end - repeat_start < end_time - start_time:
            print(f"  ✅ CACHE ACCELERATION: {((end_time - start_time) - (repeat_end - repeat_start)):.3f}s faster")
        else:
            print(f"  ⚠️ CACHE PERFORMANCE: No acceleration detected")
            
        print(f"\n🏆 OPTIMIZATION STATUS:")
        if (end_time - start_time < 1.0 and len(prediction.intensity_buildup) < 20 and
            repeat_stats['hits'] > final_stats['hits']):
            print(f"  ✅ ALL OPTIMIZATIONS WORKING PERFECTLY!")
            print(f"  🚀 Ready for production use")
        else:
            print(f"  ⚠️ Some optimizations need attention")
            
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hawkes_with_bypass()
    print(f"\n{'='*50}")
    if success:
        print("🎯 BYPASS TEST COMPLETED - OPTIMIZATIONS VERIFIED")
    else:
        print("❌ BYPASS TEST FAILED - NEEDS DEBUGGING")