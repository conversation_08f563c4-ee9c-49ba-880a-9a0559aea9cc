#!/usr/bin/env python3
"""
Validation: Premarket Monte Carlo Predictions vs Actual NYAM Results
Evaluates the accuracy of our News-Integrated Monte Carlo system
"""

import json
import sys
from datetime import datetime
sys.path.append('.')
from src.utils import load_json_data, save_json_data

def validate_predictions():
    """Compare predictions against actual NYAM session results"""
    
    print("📊 PREDICTION VALIDATION: Premarket → NYAM (July 25th)")
    print("=" * 60)
    
    # Load prediction results
    predictions = load_json_data('premarket_to_nyam_predictions_20250728_160447.json')
    
    # Load actual NYAM session results
    actual_nyam = load_json_data('NYAM_Lvl-1_2025_07_25.json')
    
    # Extract prediction data
    pred_data = predictions['monte_carlo_predictions']
    price_pred = pred_data['price_predictions']
    timing_pred = pred_data['event_timing_predictions']
    starting_price = pred_data['simulation_parameters']['starting_price']
    
    # Extract actual data
    actual_data = actual_nyam['price_data']
    actual_open = actual_data['open']
    actual_close = actual_data['close']
    actual_high = actual_data['high']
    actual_low = actual_data['low']
    actual_range = actual_data['range']
    
    # Calculate prediction accuracy
    predicted_final = price_pred['final_price_percentiles']['50th']
    predicted_range = price_pred['range_percentiles']['50th']
    
    price_error = abs(actual_close - predicted_final)
    range_error = abs(actual_range - predicted_range)
    
    # Price accuracy as percentage
    price_accuracy = (1 - (price_error / actual_close)) * 100 if actual_close != 0 else 0
    range_accuracy = (1 - (range_error / actual_range)) * 100 if actual_range != 0 else 0
    
    print("🎯 PRICE PREDICTIONS:")
    print(f"   Starting Price Predicted: {starting_price:.2f}")
    print(f"   Starting Price Actual:    {actual_open:.2f}")
    print(f"   Gap Accuracy: {abs(starting_price - actual_open):.2f} points")
    print()
    print(f"   Final Price Predicted (50th): {predicted_final:.2f}")
    print(f"   Final Price Actual:           {actual_close:.2f}")
    print(f"   Price Error: {price_error:.2f} points ({price_accuracy:.1f}% accuracy)")
    print()
    print(f"   Range Predicted (50th): {predicted_range:.1f} points")
    print(f"   Range Actual:           {actual_range:.1f} points")
    print(f"   Range Error: {range_error:.1f} points ({range_accuracy:.1f}% accuracy)")
    print()
    
    # Check percentile brackets
    actual_final_bracket = determine_percentile_bracket(actual_close, price_pred['final_price_percentiles'])
    actual_range_bracket = determine_percentile_bracket(actual_range, price_pred['range_percentiles'])
    
    print("📈 PERCENTILE ANALYSIS:")
    print(f"   Actual final price fell in: {actual_final_bracket}")
    print(f"   Actual range fell in: {actual_range_bracket}")
    print()
    
    # Event timing validation (approximate from price movements)
    price_movements = actual_nyam.get('price_movements', [])
    actual_timing_events = extract_actual_timing_events(price_movements, actual_nyam)
    
    print("⏰ EVENT TIMING VALIDATION:")
    cascade_predicted = timing_pred['cascade_initiation_minutes']
    expansion_predicted = timing_pred['expansion_phase_minutes']
    
    if actual_timing_events:
        cascade_actual = actual_timing_events.get('first_major_move_minutes', None)
        expansion_actual = actual_timing_events.get('expansion_phase_minutes', None)
        
        if cascade_actual:
            cascade_error = abs(cascade_predicted - cascade_actual)
            print(f"   Cascade Initiation Predicted: {cascade_predicted:.1f} minutes")
            print(f"   Cascade Initiation Actual:    {cascade_actual:.1f} minutes")
            print(f"   Cascade Timing Error: {cascade_error:.1f} minutes")
        else:
            print(f"   Cascade Initiation Predicted: {cascade_predicted:.1f} minutes")
            print(f"   Cascade Timing: Unable to validate from data")
            
        if expansion_actual:
            expansion_error = abs(expansion_predicted - expansion_actual)
            print(f"   Expansion Phase Predicted: {expansion_predicted:.1f} minutes")
            print(f"   Expansion Phase Actual:    {expansion_actual:.1f} minutes")
            print(f"   Expansion Timing Error: {expansion_error:.1f} minutes")
    else:
        print(f"   Cascade Initiation Predicted: {cascade_predicted:.1f} minutes")
        print(f"   Expansion Phase Predicted: {expansion_predicted:.1f} minutes")
        print(f"   ⚠️  Actual timing data limited - manual analysis required")
    
    print()
    
    # Overall assessment
    print("🏆 OVERALL VALIDATION ASSESSMENT:")
    
    # Price prediction quality
    if price_error < 20:
        price_quality = "Excellent"
    elif price_error < 50:
        price_quality = "Good"
    elif price_error < 100:
        price_quality = "Moderate"
    else:
        price_quality = "Poor"
    
    # Range prediction quality
    if range_error < 20:
        range_quality = "Excellent"
    elif range_error < 50:
        range_quality = "Good"
    else:
        range_quality = "Poor"
    
    print(f"   Price Prediction Quality: {price_quality} ({price_error:.1f} point error)")
    print(f"   Range Prediction Quality: {range_quality} ({range_error:.1f} point error)")
    
    # Percentile bracket assessment
    if "25th-75th" in actual_final_bracket:
        bracket_quality = "Good - within central range"
    elif "10th-90th" in actual_final_bracket:
        bracket_quality = "Acceptable - within broad range"
    else:
        bracket_quality = "Poor - outside prediction range"
    
    print(f"   Percentile Bracket Quality: {bracket_quality}")
    
    # Generate validation report
    validation_report = {
        "validation_metadata": {
            "validation_timestamp": datetime.now().isoformat(),
            "prediction_method": "news_integrated_monte_carlo_with_tracker_context",
            "source_session": "PREMARKET_2025_07_25",
            "target_session": "NYAM_2025_07_25"
        },
        "prediction_accuracy": {
            "price_error_points": price_error,
            "range_error_points": range_error,
            "price_accuracy_percent": price_accuracy,
            "range_accuracy_percent": range_accuracy,
            "actual_final_price_bracket": actual_final_bracket,
            "actual_range_bracket": actual_range_bracket
        },
        "actual_vs_predicted": {
            "predicted_final_50th": predicted_final,
            "actual_final": actual_close,
            "predicted_range_50th": predicted_range,
            "actual_range": actual_range,
            "starting_gap_accuracy": abs(starting_price - actual_open)
        },
        "quality_assessment": {
            "price_prediction_quality": price_quality,
            "range_prediction_quality": range_quality,
            "percentile_bracket_quality": bracket_quality,
            "overall_performance": "Good" if price_error < 50 and range_error < 50 else "Needs Improvement"
        },
        "system_performance": {
            "news_integration_active": True,
            "tracker_context_used": True,
            "t_memory_applied": predictions['monte_carlo_predictions']['simulation_parameters']['t_memory'],
            "energy_rate_applied": predictions['monte_carlo_predictions']['simulation_parameters']['energy_rate']
        }
    }
    
    # Save validation report
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_filename = f"premarket_nyam_validation_report_{timestamp}.json"
    save_json_data(validation_report, report_filename)
    
    print(f"\n📄 Validation report saved: {report_filename}")
    print("=" * 60)
    
    return validation_report


def determine_percentile_bracket(actual_value: float, percentiles: dict) -> str:
    """Determine which percentile bracket the actual value fell into"""
    
    if actual_value <= percentiles["10th"]:
        return "Below 10th percentile"
    elif actual_value <= percentiles["25th"]:
        return "10th-25th percentile"
    elif actual_value <= percentiles["50th"]:
        return "25th-50th percentile"
    elif actual_value <= percentiles["75th"]:
        return "50th-75th percentile"
    elif actual_value <= percentiles["90th"]:
        return "75th-90th percentile"
    else:
        return "Above 90th percentile"


def extract_actual_timing_events(price_movements: list, session_data: dict) -> dict:
    """Extract actual timing events from price movement data"""
    
    if not price_movements:
        return {}
    
    session_start = "09:30:00"  # NYAM start time
    timing_events = {}
    
    # Find first major move (typically first significant price action)
    first_move = price_movements[0] if price_movements else None
    if first_move:
        # Calculate minutes from session start
        move_time = first_move.get('timestamp', '09:30:00')
        if move_time and move_time != session_start:
            try:
                # Simple time difference calculation
                move_hour, move_min, _ = map(int, move_time.split(':'))
                start_hour, start_min, _ = map(int, session_start.split(':'))
                
                move_minutes = (move_hour * 60 + move_min) - (start_hour * 60 + start_min)
                timing_events['first_major_move_minutes'] = move_minutes
            except:
                pass
    
    # Look for expansion phase indicators
    expansion_moves = [m for m in price_movements if 'expansion' in m.get('context', '').lower()]
    if expansion_moves:
        # Use the first major expansion move as expansion phase timing
        try:
            exp_time = expansion_moves[0].get('timestamp', '09:30:00')
            exp_hour, exp_min, _ = map(int, exp_time.split(':'))
            start_hour, start_min, _ = map(int, session_start.split(':'))
            
            exp_minutes = (exp_hour * 60 + exp_min) - (start_hour * 60 + start_min)
            timing_events['expansion_phase_minutes'] = exp_minutes
        except:
            pass
    
    return timing_events


if __name__ == "__main__":
    validate_predictions()