#!/usr/bin/env python3
"""
Modular JSON Safety Test - Validate new modular JSON utilities work identically
Tests that extracted JSON utilities maintain 100% compatibility
"""

import os
import tempfile
from pathlib import Path

print("🧪 TESTING MODULAR JSON UTILITIES SAFETY")
print("=" * 55)

try:
    # Test new modular JSON utilities
    from src.infrastructure.json_utils import load_json_data, save_json_data, safe_json_load, safe_json_save
    print("✅ Modular JSON utilities import successful")
except Exception as e:
    print(f"❌ Modular JSON utilities import failed: {e}")
    exit(1)

try:
    # Test original JSON utilities for comparison
    from src.utils import load_json_data as orig_load, save_json_data as orig_save
    print("✅ Original JSON utilities import successful")
except Exception as e:
    print(f"❌ Original JSON utilities import failed: {e}")
    exit(1)

# Test data (identical to safety test)
test_data = {
    "session_metadata": {
        "session_type": "TEST_MODULAR",
        "date": "2025_07_31",
        "test_purpose": "validate_modular_json_safety"
    },
    "mathematical_results": {
        "predicted_cascade_time": 15.5,
        "confidence": 0.95,
        "method": "hawkes_process"
    },
    "validation": {
        "modular_system": "working",
        "backward_compatible": True
    }
}

# Create temporary files for testing
with tempfile.NamedTemporaryFile(mode='w', suffix='_modular.json', delete=False) as temp_file:
    modular_path = temp_file.name

with tempfile.NamedTemporaryFile(mode='w', suffix='_original.json', delete=False) as temp_file:
    original_path = temp_file.name

try:
    print("\n🔬 TESTING MODULAR VS ORIGINAL COMPATIBILITY")
    print("-" * 50)
    
    # Test modular save
    print("Testing modular save_json_data...")
    save_json_data(test_data, modular_path)
    print("✅ Modular save_json_data: SUCCESS")
    
    # Test original save
    print("Testing original save_json_data...")
    orig_save(test_data, original_path)
    print("✅ Original save_json_data: SUCCESS")
    
    # Test modular load
    print("Testing modular load_json_data...")
    modular_loaded = load_json_data(modular_path)
    print("✅ Modular load_json_data: SUCCESS")
    
    # Test original load  
    print("Testing original load_json_data...")
    original_loaded = orig_load(original_path)
    print("✅ Original load_json_data: SUCCESS")
    
    # Compare results
    print("\n🔍 COMPATIBILITY VALIDATION")
    print("-" * 30)
    
    if modular_loaded == test_data:
        print("✅ Modular data integrity: PERFECT")
    else:
        raise Exception("Modular data integrity failed")
    
    if original_loaded == test_data:
        print("✅ Original data integrity: PERFECT")
    else:
        raise Exception("Original data integrity failed")
    
    if modular_loaded == original_loaded:
        print("✅ Modular vs Original: IDENTICAL")
    else:
        raise Exception("Modular and original results differ")
    
    # Test safe functions from modular
    safe_path = modular_path.replace('.json', '_safe.json')
    print("\nTesting modular safe functions...")
    
    safe_result = safe_json_save(test_data, safe_path)
    if safe_result:
        print("✅ Modular safe_json_save: SUCCESS")
    else:
        raise Exception("Modular safe save failed")
    
    safe_loaded = safe_json_load(safe_path)
    if safe_loaded and safe_loaded == test_data:
        print("✅ Modular safe_json_load: SUCCESS")
    else:
        raise Exception("Modular safe load failed")
    
    # Test cross-compatibility: save with original, load with modular
    cross_path = modular_path.replace('.json', '_cross.json')
    print("\nTesting cross-compatibility...")
    
    orig_save(test_data, cross_path)  # Save with original
    cross_loaded = load_json_data(cross_path)  # Load with modular
    
    if cross_loaded == test_data:
        print("✅ Cross-compatibility: PERFECT")
    else:
        raise Exception("Cross-compatibility failed")
    
    print("\n🏆 MODULAR JSON VALIDATION COMPLETE")
    print("=" * 55)
    print("✅ ALL MODULAR JSON OPERATIONS: WORKING PERFECTLY")
    print("✅ 100% backward compatibility: MAINTAINED")
    print("✅ Original vs Modular: IDENTICAL BEHAVIOR")
    print("✅ Cross-compatibility: PRESERVED")
    print("✅ Data integrity: MAINTAINED")
    
    print(f"\n💡 CONCLUSION: Modular JSON utilities are SAFE")
    print(f"   ✅ All existing functionality preserved")
    print(f"   ✅ Original imports still work")
    print(f"   ✅ New modular imports work identically")
    print(f"   🔄 Ready for gradual migration")

except Exception as e:
    print(f"\n❌ MODULAR JSON TEST FAILED: {e}")
    print("🛑 DO NOT proceed with modular migration until fixed")
    print("🔄 Original system remains intact and working")

finally:
    # Cleanup
    for temp_file_path in [modular_path, original_path, 
                          modular_path.replace('.json', '_safe.json'),
                          modular_path.replace('.json', '_cross.json')]:
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)