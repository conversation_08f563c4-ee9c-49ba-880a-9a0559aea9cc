#!/usr/bin/env python3
"""
Ground Truth Validation Test - Phase 1 Implementation

Opus 4's Quick Win: Single Binary Consolidation Test
"Did price stay within 20 points?" - This single binary fact will immediately 
reveal if your 'consolidation' classifications match reality.

This test addresses the epistemic closure problem by validating the system's
mathematical interpretation of "consolidation" against objective market reality.
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

sys.path.append('.')
from src.market_observer import MarketObserver, create_market_observer
from src.validation.ground_truth_store import GroundTruthStore, create_ground_truth_store
from src.utils import load_json_data


class GroundTruthValidator:
    """
    Ground Truth Validation System - Phase 1
    
    Implements Opus 4's consolidation test to detect epistemic closure issues
    where the system's mathematical interpretation doesn't match market reality.
    """
    
    def __init__(self, consolidation_threshold: float = 20.0):
        """
        Initialize the ground truth validator.
        
        Args:
            consolidation_threshold: Points defining consolidation range (default: 20.0)
        """
        self.consolidation_threshold = consolidation_threshold
        self.observer = create_market_observer(consolidation_threshold)
        self.ground_truth_store = create_ground_truth_store()
        
        # Test results
        self.test_results = []
        self.epistemic_closure_cases = []
        
    def run_consolidation_test_on_session(self, session_file: str) -> Dict[str, Any]:
        """
        Run Opus 4's binary consolidation test on a single session.
        
        Args:
            session_file: Path to session JSON file
            
        Returns:
            Test results with ground truth vs system comparison
        """
        
        print(f"🔍 Testing session: {os.path.basename(session_file)}")
        
        try:
            # Load session data
            session_data = load_json_data(session_file)
            
            # Run the binary consolidation test
            test_result = self.observer.get_consolidation_test_result(session_data)
            
            # Store ground truth facts
            session_id = test_result['ground_truth'].get('session_id', 
                                                       f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            ground_truth_facts = {
                'consolidation_occurred': test_result['ground_truth']['consolidation_occurred'],
                'session_range': test_result['ground_truth']['session_range'],
                'price_stayed_within_range': test_result['ground_truth']['price_stayed_within_range'],
                'threshold_used': test_result['ground_truth']['threshold_used']
            }
            
            system_predictions = {
                'system_says_consolidation': test_result['system_interpretation']['system_says_consolidation'],
                'system_character': test_result['system_interpretation']['system_character']
            }
            
            # Store in ground truth database
            self.ground_truth_store.store_ground_truth(
                session_id=session_id,
                ground_truth_facts=ground_truth_facts,
                file_source=session_file,
                system_predictions=system_predictions
            )
            
            # Store validation results
            validation_results = test_result['validation_result']
            validation_results['test_timestamp'] = datetime.now().isoformat()
            validation_results['test_file'] = session_file
            
            self.ground_truth_store.store_validation_results(session_id, validation_results)
            
            # Track results
            self.test_results.append(test_result)
            
            # Check for epistemic closure
            if test_result['validation_result']['potential_epistemic_closure']:
                self.epistemic_closure_cases.append({
                    'session_file': session_file,
                    'ground_truth_range': test_result['ground_truth']['session_range'],
                    'ground_truth_consolidation': test_result['ground_truth']['consolidation_occurred'],
                    'system_says_consolidation': test_result['system_interpretation']['system_says_consolidation'],
                    'mismatch_type': 'consolidation_classification'
                })
                print(f"⚠️  EPISTEMIC CLOSURE DETECTED - System vs Reality Mismatch!")
            else:
                print(f"✅ System interpretation matches ground truth")
            
            # Display results
            self._display_test_result(test_result, session_file)
            
            return test_result
            
        except Exception as e:
            error_result = {
                'session_file': session_file,
                'error': str(e),
                'test_status': 'failed'
            }
            print(f"❌ Test failed for {session_file}: {str(e)}")
            return error_result
    
    def _display_test_result(self, test_result: Dict[str, Any], session_file: str):
        """Display formatted test results."""
        
        ground_truth = test_result['ground_truth']
        validation = test_result['validation_result']
        
        print(f"   📊 Ground Truth: Range = {ground_truth['session_range']:.1f} points")
        print(f"   📊 Consolidation? {ground_truth['consolidation_occurred']} (threshold: {ground_truth['threshold_used']})")
        print(f"   🤖 System Match: {validation['ground_truth_matches_system']}")
        print(f"   🎯 Accuracy: {validation['range_based_accuracy']}")
        print()
    
    def run_batch_consolidation_test(self, session_files: List[str]) -> Dict[str, Any]:
        """
        Run consolidation test on multiple session files.
        
        Args:
            session_files: List of session file paths
            
        Returns:
            Comprehensive batch test results
        """
        
        print(f"🚀 GROUND TRUTH VALIDATION TEST - BATCH MODE")
        print(f"   Testing {len(session_files)} sessions")
        print(f"   Consolidation threshold: {self.consolidation_threshold} points")
        print("=" * 60)
        
        successful_tests = 0
        failed_tests = 0
        accurate_predictions = 0
        
        for session_file in session_files:
            try:
                test_result = self.run_consolidation_test_on_session(session_file)
                
                if 'error' not in test_result:
                    successful_tests += 1
                    if test_result['validation_result']['range_based_accuracy'] == 'accurate':
                        accurate_predictions += 1
                else:
                    failed_tests += 1
                    
            except Exception as e:
                print(f"❌ Batch test error for {session_file}: {str(e)}")
                failed_tests += 1
                continue
        
        # Generate summary
        accuracy_rate = (accurate_predictions / successful_tests * 100) if successful_tests > 0 else 0
        epistemic_closure_rate = (len(self.epistemic_closure_cases) / successful_tests * 100) if successful_tests > 0 else 0
        
        summary = {
            'batch_summary': {
                'total_files_tested': len(session_files),
                'successful_tests': successful_tests,
                'failed_tests': failed_tests,
                'accurate_predictions': accurate_predictions,
                'accuracy_rate_percent': accuracy_rate,
                'epistemic_closure_cases': len(self.epistemic_closure_cases),
                'epistemic_closure_rate_percent': epistemic_closure_rate
            },
            'test_results': self.test_results,
            'epistemic_closure_cases': self.epistemic_closure_cases,
            'ground_truth_store_summary': self.ground_truth_store.get_validation_summary()
        }
        
        self._display_batch_summary(summary)
        
        return summary
    
    def _display_batch_summary(self, summary: Dict[str, Any]):
        """Display formatted batch test summary."""
        
        batch = summary['batch_summary']
        
        print("=" * 60)
        print("📈 GROUND TRUTH VALIDATION RESULTS")
        print("=" * 60)
        print(f"✅ Successful Tests: {batch['successful_tests']}/{batch['total_files_tested']}")
        print(f"🎯 Prediction Accuracy: {batch['accurate_predictions']}/{batch['successful_tests']} ({batch['accuracy_rate_percent']:.1f}%)")
        print(f"⚠️  Epistemic Closure Cases: {batch['epistemic_closure_cases']} ({batch['epistemic_closure_rate_percent']:.1f}%)")
        
        if batch['epistemic_closure_cases'] > 0:
            print(f"\n🔍 EPISTEMIC CLOSURE ANALYSIS:")
            for case in summary['epistemic_closure_cases']:
                print(f"   📁 {os.path.basename(case['session_file'])}")
                print(f"      Range: {case['ground_truth_range']:.1f}pt | Ground Truth: {case['ground_truth_consolidation']} | System: {case['system_says_consolidation']}")
        
        print(f"\n💾 Ground truth data stored for future validation phases")
        print("=" * 60)
    
    def save_test_report(self, summary: Dict[str, Any], output_file: str = None) -> str:
        """Save comprehensive test report to file."""
        
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"ground_truth_validation_report_{timestamp}.json"
        
        # Prepare comprehensive report
        report = {
            'ground_truth_validation_report': {
                'test_metadata': {
                    'test_timestamp': datetime.now().isoformat(),
                    'test_type': 'opus_4_consolidation_binary_test',
                    'consolidation_threshold': self.consolidation_threshold,
                    'test_description': 'Single binary test: Did price stay within 20 points?',
                    'purpose': 'Detect epistemic closure - system vs reality mismatch'
                },
                'batch_summary': summary['batch_summary'],
                'epistemic_closure_analysis': {
                    'cases_detected': len(summary['epistemic_closure_cases']),
                    'closure_rate_percent': summary['batch_summary']['epistemic_closure_rate_percent'],
                    'cases_detail': summary['epistemic_closure_cases']
                },
                'ground_truth_store_status': summary['ground_truth_store_summary'],
                'next_phase_recommendations': self._generate_next_phase_recommendations(summary)
            }
        }
        
        # Save report
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📄 Comprehensive test report saved: {output_file}")
        return output_file
    
    def _generate_next_phase_recommendations(self, summary: Dict[str, Any]) -> List[str]:
        """Generate recommendations for next validation phases."""
        
        recommendations = []
        
        batch = summary['batch_summary']
        accuracy_rate = batch['accuracy_rate_percent']
        closure_rate = batch['epistemic_closure_rate_percent']
        
        if accuracy_rate < 80:
            recommendations.append("LOW ACCURACY: Proceed to Phase 2 shadow validation system - system needs recalibration")
        elif closure_rate > 20:
            recommendations.append("HIGH EPISTEMIC CLOSURE: Implement divergence analysis to identify specific mathematical model issues")
        else:
            recommendations.append("GOOD BASELINE: Ready for Phase 2 implementation with expanded binary tests")
        
        if len(summary['epistemic_closure_cases']) > 0:
            recommendations.append("EPISTEMIC CLOSURE DETECTED: Review session_character calculation methodology")
            recommendations.append("MATHEMATICAL AUDIT: Validate consolidation detection algorithms against ground truth")
        
        recommendations.append("Phase 2: Implement expansion test, liquidity sweep test, and FVG delivery test")
        recommendations.append("Phase 3: Build real-time shadow validation running parallel to live system")
        
        return recommendations


def find_session_files(pattern: str = "*grokEnhanced*.json") -> List[str]:
    """Find session files matching the pattern."""
    import glob
    return glob.glob(pattern)


def main():
    """Main execution for ground truth validation test."""
    
    print("🔍 GROUND TRUTH VALIDATION TEST - Phase 1 Implementation")
    print("   Opus 4's Quick Win: Single Binary Consolidation Test")
    print("   'Did price stay within 20 points?'")
    print()
    
    # Initialize validator
    validator = GroundTruthValidator(consolidation_threshold=20.0)
    
    # Find session files
    session_files = find_session_files()
    
    if not session_files:
        print("❌ No session files found matching pattern '*grokEnhanced*.json'")
        print("   Place session files in current directory or specify files manually")
        return
    
    print(f"📁 Found {len(session_files)} session files:")
    for f in session_files[:5]:  # Show first 5
        print(f"   - {os.path.basename(f)}")
    if len(session_files) > 5:
        print(f"   ... and {len(session_files) - 5} more")
    print()
    
    # Run batch test
    summary = validator.run_batch_consolidation_test(session_files)
    
    # Save comprehensive report
    report_file = validator.save_test_report(summary)
    
    # Export ground truth store data
    ground_truth_export_file = f"ground_truth_data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    if validator.ground_truth_store.export_validation_report(ground_truth_export_file):
        print(f"📊 Ground truth database exported: {ground_truth_export_file}")
    
    print("\n🎯 PHASE 1 GROUND TRUTH VALIDATION COMPLETE")
    print("   Single binary consolidation test implemented and executed")
    print("   Ground truth database populated for future validation phases")
    
    if summary['batch_summary']['epistemic_closure_cases'] > 0:
        print("   ⚠️  EPISTEMIC CLOSURE DETECTED - System requires mathematical audit")
    else:
        print("   ✅ No epistemic closure detected - System interpretations match reality")


if __name__ == "__main__":
    main()