#!/usr/bin/env python3

import json

def validate_enhanced_session(filepath, session_name):
    """Validate that enhanced session data contains real calculations"""
    print(f"\n🔍 Validating {session_name} Session...")
    print("=" * 50)
    
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
            
        # Extract enhanced calculations
        grok_calc = data.get('grok_enhanced_calculations', {})
        unit_b = grok_calc.get('unit_b_energy_structure', {})
        unit_c = grok_calc.get('unit_c_advanced_dynamics', {})
        
        # Energy calculations
        energy_accumulation = unit_b.get('energy_accumulation', {})
        energy_rate = energy_accumulation.get('energy_rate', 1.0)
        total_accumulated = energy_accumulation.get('total_accumulated', 0.0)
        efficiency_factor = energy_accumulation.get('efficiency_factor', 0.0)
        
        # Momentum calculations  
        temporal_momentum = unit_c.get('temporal_momentum', {})
        momentum_strength = temporal_momentum.get('momentum_strength', 0.0)
        persistence_factor = temporal_momentum.get('persistence_factor', 0.0)
        
        # Validation checks
        energy_valid = energy_rate != 1.0
        momentum_valid = momentum_strength > 0.0
        accumulation_valid = total_accumulated > 0.0
        efficiency_valid = efficiency_factor > 0.0
        persistence_valid = persistence_factor > 0.0
        
        print(f"📊 Energy Rate: {energy_rate:.6f} (≠ 1.0: {'✅' if energy_valid else '❌'})")
        print(f"💪 Momentum Strength: {momentum_strength:.6f} (> 0: {'✅' if momentum_valid else '❌'})")
        print(f"⚡ Total Accumulated: {total_accumulated:.6f} (> 0: {'✅' if accumulation_valid else '❌'})")
        print(f"⚙️ Efficiency Factor: {efficiency_factor:.6f} (> 0: {'✅' if efficiency_valid else '❌'})")
        print(f"🔄 Persistence Factor: {persistence_factor:.6f} (> 0: {'✅' if persistence_valid else '❌'})")
        
        # Overall validation
        all_valid = all([energy_valid, momentum_valid, accumulation_valid, efficiency_valid, persistence_valid])
        
        print(f"\n🎯 Overall Validation: {'✅ PASSED' if all_valid else '❌ FAILED'}")
        print(f"📈 Real Calculations Detected: {'YES' if all_valid else 'NO'}")
        
        return all_valid
        
    except Exception as e:
        print(f"❌ Error validating {session_name}: {str(e)}")
        return False

def validate_tracker_files(session_name):
    """Validate that all 3 tracker files were generated"""
    print(f"\n📁 Validating {session_name} Tracker Files...")
    print("=" * 40)
    
    tracker_types = ['HTF_Context', 'FVG_State', 'Liquidity_State']
    all_exist = True
    
    for tracker_type in tracker_types:
        filepath = f"{tracker_type}_{session_name}_grokEnhanced_2025-07-25.json"
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            print(f"✅ {tracker_type}: Found and valid")
        except FileNotFoundError:
            print(f"❌ {tracker_type}: Missing")
            all_exist = False
        except json.JSONDecodeError:
            print(f"❌ {tracker_type}: Invalid JSON")
            all_exist = False
    
    print(f"\n📋 Tracker Files Status: {'✅ ALL GENERATED' if all_exist else '❌ INCOMPLETE'}")
    return all_exist

def main():
    print("🚀 FRIDAY JULY 25TH SESSION VALIDATION")
    print("=" * 60)
    
    # Validate enhanced session data
    lunch_valid = validate_enhanced_session('Lunch_session_enhanced_2025_07_25.json', 'Lunch')
    nypm_valid = validate_enhanced_session('NYPM_session_enhanced_2025_07_25.json', 'NY PM')
    
    # Validate tracker files
    lunch_trackers = validate_tracker_files('Lunch')
    nypm_trackers = validate_tracker_files('NY_PM')
    
    # Final summary
    print(f"\n🏆 FINAL VALIDATION SUMMARY")
    print("=" * 40)
    print(f"🍽️ Lunch Session Enhanced Data: {'✅ VALID' if lunch_valid else '❌ INVALID'}")
    print(f"🍽️ Lunch Session Tracker Files: {'✅ COMPLETE' if lunch_trackers else '❌ INCOMPLETE'}")
    print(f"🌇 NY PM Session Enhanced Data: {'✅ VALID' if nypm_valid else '❌ INVALID'}")
    print(f"🌇 NY PM Session Tracker Files: {'✅ COMPLETE' if nypm_trackers else '❌ INCOMPLETE'}")
    
    all_passed = all([lunch_valid, nypm_valid, lunch_trackers, nypm_trackers])
    print(f"\n🎯 OVERALL STATUS: {'✅ ALL VALIDATIONS PASSED' if all_passed else '❌ SOME VALIDATIONS FAILED'}")
    
    if all_passed:
        print("\n🎉 Both sessions successfully processed through A→B→C→D pipeline!")
        print("📊 Enhanced data contains real calculations (not fallback values)")
        print("📁 All tracker files generated for production use")

if __name__ == "__main__":
    main()