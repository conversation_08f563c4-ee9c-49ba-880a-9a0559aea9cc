#!/usr/bin/env python3
"""
Test runner for Grok-Claude Automation financial prediction system
Provides comprehensive test execution with coverage reporting
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description} failed")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"ERROR: Command not found. Make sure pytest is installed.")
        return False


def install_dependencies():
    """Install test dependencies"""
    dependencies = [
        "pytest>=7.0.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "pytest-xdist>=3.0.0",  # For parallel test execution
        "pytest-html>=3.1.0",   # For HTML reports
    ]
    
    for dep in dependencies:
        cmd = [sys.executable, "-m", "pip", "install", dep]
        if not run_command(cmd, f"Installing {dep}"):
            return False
    return True


def run_unit_tests(coverage=False, verbose=False):
    """Run unit tests"""
    cmd = [sys.executable, "-m", "pytest", "tests/unit/", "-v" if verbose else "-q"]
    
    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-fail-under=70"
        ])
    
    return run_command(cmd, "Unit Tests")


def run_integration_tests(verbose=False):
    """Run integration tests"""
    cmd = [sys.executable, "-m", "pytest", "tests/integration/", "-v" if verbose else "-q", "-m", "integration"]
    return run_command(cmd, "Integration Tests")


def run_e2e_tests(verbose=False):
    """Run end-to-end tests"""
    cmd = [sys.executable, "-m", "pytest", "tests/e2e/", "-v" if verbose else "-q", "-m", "e2e"]
    return run_command(cmd, "End-to-End Tests")


def run_all_tests(coverage=False, verbose=False, parallel=False):
    """Run all tests"""
    cmd = [sys.executable, "-m", "pytest", "tests/"]
    
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    if parallel:
        cmd.extend(["-n", "auto"])  # Use all available CPUs
    
    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=xml:coverage.xml",
            "--cov-fail-under=70"
        ])
    
    return run_command(cmd, "All Tests")


def run_specific_test(test_path, verbose=False):
    """Run a specific test file or test function"""
    cmd = [sys.executable, "-m", "pytest", test_path, "-v" if verbose else "-q"]
    return run_command(cmd, f"Specific Test: {test_path}")


def run_tests_by_marker(marker, verbose=False):
    """Run tests by marker (unit, integration, e2e, slow, api)"""
    cmd = [sys.executable, "-m", "pytest", "-m", marker, "-v" if verbose else "-q"]
    return run_command(cmd, f"Tests with marker: {marker}")


def generate_test_report():
    """Generate comprehensive test report"""
    cmd = [
        sys.executable, "-m", "pytest", "tests/",
        "--html=test_report.html",
        "--self-contained-html",
        "--cov=src",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "-v"
    ]
    return run_command(cmd, "Test Report Generation")


def check_test_structure():
    """Check test structure and coverage"""
    print("\n" + "="*60)
    print("TEST STRUCTURE ANALYSIS")
    print("="*60)
    
    test_dir = Path("tests")
    if not test_dir.exists():
        print("ERROR: tests/ directory not found")
        return False
    
    # Count test files
    unit_tests = list((test_dir / "unit").glob("test_*.py")) if (test_dir / "unit").exists() else []
    integration_tests = list((test_dir / "integration").glob("test_*.py")) if (test_dir / "integration").exists() else []
    e2e_tests = list((test_dir / "e2e").glob("test_*.py")) if (test_dir / "e2e").exists() else []
    
    print(f"Unit tests: {len(unit_tests)} files")
    for test in unit_tests:
        print(f"  - {test.name}")
    
    print(f"Integration tests: {len(integration_tests)} files")
    for test in integration_tests:
        print(f"  - {test.name}")
    
    print(f"End-to-end tests: {len(e2e_tests)} files")
    for test in e2e_tests:
        print(f"  - {test.name}")
    
    # Check conftest.py
    conftest = test_dir / "conftest.py"
    if conftest.exists():
        print(f"✓ conftest.py found ({conftest.stat().st_size} bytes)")
    else:
        print("✗ conftest.py not found")
    
    return True


def main():
    parser = argparse.ArgumentParser(description="Test runner for Grok-Claude Automation")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--e2e", action="store_true", help="Run end-to-end tests only")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--parallel", "-p", action="store_true", help="Run tests in parallel")
    parser.add_argument("--marker", "-m", help="Run tests with specific marker")
    parser.add_argument("--test", "-t", help="Run specific test file or function")
    parser.add_argument("--report", action="store_true", help="Generate HTML test report")
    parser.add_argument("--check-structure", action="store_true", help="Check test structure")
    
    args = parser.parse_args()
    
    # Default to checking structure if no other action specified
    if not any([args.install_deps, args.unit, args.integration, args.e2e, args.all, 
                args.marker, args.test, args.report]):
        args.check_structure = True
    
    success = True
    
    if args.install_deps:
        success &= install_dependencies()
    
    if args.check_structure:
        success &= check_test_structure()
    
    if args.unit:
        success &= run_unit_tests(coverage=args.coverage, verbose=args.verbose)
    
    if args.integration:
        success &= run_integration_tests(verbose=args.verbose)
    
    if args.e2e:
        success &= run_e2e_tests(verbose=args.verbose)
    
    if args.all:
        success &= run_all_tests(coverage=args.coverage, verbose=args.verbose, parallel=args.parallel)
    
    if args.marker:
        success &= run_tests_by_marker(args.marker, verbose=args.verbose)
    
    if args.test:
        success &= run_specific_test(args.test, verbose=args.verbose)
    
    if args.report:
        success &= generate_test_report()
    
    if success:
        print("\n" + "="*60)
        print("✓ ALL OPERATIONS COMPLETED SUCCESSFULLY")
        print("="*60)
        
        if args.coverage or args.report:
            print("\nGenerated files:")
            if Path("htmlcov").exists():
                print("  - htmlcov/index.html (Coverage report)")
            if Path("test_report.html").exists():
                print("  - test_report.html (Test report)")
        
        sys.exit(0)
    else:
        print("\n" + "="*60)
        print("✗ SOME OPERATIONS FAILED")
        print("="*60)
        sys.exit(1)


if __name__ == "__main__":
    main()
