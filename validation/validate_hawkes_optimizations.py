#!/usr/bin/env python3
"""
Hawkes Optimization Validation Script
Validates the performance improvements from Fix #1 (Early Termination) and Fix #2 (Unified Cache)
"""

import time
import sys
from typing import Dict, Any, List
from src.hawkes_cascade_predictor import HawkesCascadePredictor
from src.dynamic_synthetic_volume import DynamicSyntheticVolumeCalculator
from src.cache_manager import get_unified_cache
from src.utils import load_json_data


def test_hawkes_early_termination(session_files: List[str]) -> Dict[str, Any]:
    """Test early termination effectiveness in Hawkes predictor."""
    print("🧪 TESTING HAWKES EARLY TERMINATION")
    print("=" * 50)
    
    predictor = HawkesCascadePredictor()
    results = {
        'sessions_tested': 0,
        'total_time': 0,
        'average_time': 0,
        'iterations_saved': [],
        'early_terminations': 0,
        'sessions': {}
    }
    
    for session_file in session_files:
        try:
            print(f"\n📁 Testing: {session_file}")
            session_data = load_json_data(session_file)
            
            start_time = time.time()
            prediction = predictor.predict_cascade_timing(session_data)
            end_time = time.time()
            
            duration = end_time - start_time
            results['total_time'] += duration
            results['sessions_tested'] += 1
            
            # Count iterations used vs maximum possible (180)
            iterations_used = len(prediction.intensity_buildup)
            iterations_saved = 180 - iterations_used
            results['iterations_saved'].append(iterations_saved)
            
            if iterations_used < 180:
                results['early_terminations'] += 1
            
            results['sessions'][session_file] = {
                'duration': duration,
                'iterations_used': iterations_used,
                'iterations_saved': iterations_saved,
                'cascade_time': prediction.predicted_cascade_time,
                'confidence': prediction.prediction_confidence
            }
            
            print(f"  ⏱️  Duration: {duration:.3f}s")
            print(f"  🔢 Iterations: {iterations_used}/180 (saved {iterations_saved})")
            print(f"  🎯 Cascade: {prediction.predicted_cascade_time:.1f} min")
            
        except Exception as e:
            print(f"  ❌ Failed: {e}")
    
    if results['sessions_tested'] > 0:
        results['average_time'] = results['total_time'] / results['sessions_tested']
        results['average_iterations_saved'] = sum(results['iterations_saved']) / len(results['iterations_saved'])
        results['early_termination_rate'] = results['early_terminations'] / results['sessions_tested']
    
    return results


def test_unified_cache_performance(session_files: List[str]) -> Dict[str, Any]:
    """Test unified cache system performance."""
    print("\n🧪 TESTING UNIFIED CACHE PERFORMANCE")
    print("=" * 50)
    
    cache = get_unified_cache()
    volume_calc = DynamicSyntheticVolumeCalculator()
    
    # Clear cache to start fresh
    cache.clear()
    
    results = {
        'cache_tests': 0,
        'cache_hits': 0,
        'cache_misses': 0,
        'hit_rate': 0.0,
        'sessions': {}
    }
    
    for session_file in session_files:
        try:
            print(f"\n📁 Testing cache with: {session_file}")
            session_data = load_json_data(session_file)
            
            # Test multiple volume calculations on same session
            test_timestamps = ['09:30:00', '10:00:00', '10:30:00', '09:30:00', '10:00:00']
            
            session_results = {'calculations': [], 'cache_behavior': []}
            
            for timestamp in test_timestamps:
                start_stats = cache.get_stats()
                
                try:
                    volume = volume_calc.get_volume_for_timestamp(session_data, timestamp)
                    session_results['calculations'].append({
                        'timestamp': timestamp,
                        'volume': volume
                    })
                except Exception as e:
                    print(f"    ⚠️ Volume calc failed for {timestamp}: {e}")
                    continue
                
                end_stats = cache.get_stats()
                
                hit_increase = end_stats['hits'] - start_stats['hits']
                miss_increase = end_stats['misses'] - start_stats['misses']
                
                session_results['cache_behavior'].append({
                    'timestamp': timestamp,
                    'was_cache_hit': hit_increase > 0,
                    'was_cache_miss': miss_increase > 0
                })
                
                if hit_increase > 0:
                    print(f"    🎯 CACHE HIT: {timestamp}")
                elif miss_increase > 0:
                    print(f"    💾 CACHE MISS: {timestamp}")
            
            results['sessions'][session_file] = session_results
            
        except Exception as e:
            print(f"  ❌ Cache test failed: {e}")
    
    # Final cache stats
    final_stats = cache.get_stats()
    results.update(final_stats)
    
    return results


def run_validation():
    """Run complete validation of Hawkes optimizations."""
    print("🚀 HAWKES OPTIMIZATION VALIDATION")
    print("=" * 60)
    print("Validating Fix #1 (Early Termination) and Fix #2 (Unified Cache)")
    print("=" * 60)
    
    # Find available session files
    session_files = []
    test_files = [
        'LONDON_Lvl-1_2025_07_28.json',
        'ASIA_Lvl-1_2025_07_29.json.json',
        'NYAM_Lvl-1_2025_07_29.json',
        'PREMARKET_Lvl-1_2025_07_29.json'
    ]
    
    for file in test_files:
        try:
            load_json_data(file)
            session_files.append(file)
            print(f"✅ Found session: {file}")
        except:
            print(f"⚠️ Missing session: {file}")
    
    if not session_files:
        print("❌ No session files found for testing!")
        return
    
    print(f"\n📊 Testing with {len(session_files)} sessions")
    
    # Test Fix #1: Early Termination
    hawkes_results = test_hawkes_early_termination(session_files)
    
    # Test Fix #2: Unified Cache
    cache_results = test_unified_cache_performance(session_files[:2])  # Test with first 2 sessions
    
    # Final Report
    print("\n" + "=" * 60)
    print("🎯 VALIDATION RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"\n📈 HAWKES EARLY TERMINATION (Fix #1):")
    print(f"  • Sessions tested: {hawkes_results['sessions_tested']}")
    print(f"  • Average execution time: {hawkes_results.get('average_time', 0):.3f}s")
    print(f"  • Early termination rate: {hawkes_results.get('early_termination_rate', 0):.1%}")
    print(f"  • Average iterations saved: {hawkes_results.get('average_iterations_saved', 0):.1f}/180")
    
    if hawkes_results.get('average_iterations_saved', 0) > 150:
        print("  ✅ EXCELLENT: >150 iterations saved per session (>83% reduction)")
    elif hawkes_results.get('average_iterations_saved', 0) > 100:
        print("  ✅ GOOD: >100 iterations saved per session (>55% reduction)")
    else:
        print("  ⚠️ NEEDS IMPROVEMENT: <100 iterations saved")
    
    print(f"\n🗄️ UNIFIED CACHE SYSTEM (Fix #2):")
    print(f"  • Cache hit rate: {cache_results.get('hit_rate', 0):.1%}")
    print(f"  • Total hits: {cache_results.get('hits', 0)}")
    print(f"  • Total misses: {cache_results.get('misses', 0)}")
    print(f"  • Cache size: {cache_results.get('cache_size', 0)}")
    
    if cache_results.get('hit_rate', 0) > 0.3:
        print("  ✅ EXCELLENT: >30% cache hit rate")
    elif cache_results.get('hit_rate', 0) > 0.1:
        print("  ✅ GOOD: >10% cache hit rate")
    else:
        print("  ⚠️ NEEDS IMPROVEMENT: Low cache hit rate")
    
    print(f"\n🏆 OVERALL ASSESSMENT:")
    
    fast_execution = hawkes_results.get('average_time', 10) < 1.0
    good_termination = hawkes_results.get('average_iterations_saved', 0) > 100
    cache_working = cache_results.get('hit_rate', 0) > 0.1
    
    if fast_execution and good_termination and cache_working:
        print("  ✅ SUCCESS: Both optimizations working excellently!")
        print("  🚀 System ready for production use")
    elif fast_execution and good_termination:
        print("  ✅ GOOD: Early termination working, cache needs improvement")
        print("  📈 Major performance gains achieved")
    else:
        print("  ⚠️ PARTIAL: Some optimizations need more work")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    run_validation()