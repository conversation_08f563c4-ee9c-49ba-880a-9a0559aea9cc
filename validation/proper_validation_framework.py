#!/usr/bin/env python3
"""
Proper Validation Framework
Template for conducting honest same-day validation with range-based accuracy metrics
"""

import json
from datetime import datetime
from typing import Dict, Optional, Tuple

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
class TradingAccuracyValidator:
    """Proper validation framework for trading predictions"""
    
    def __init__(self):
        self.accuracy_standards = {
            'excellent': {'threshold': 10, 'description': 'Highly actionable'},
            'good': {'threshold': 20, 'description': 'Actionable'},
            'moderate': {'threshold': 50, 'description': 'Limited value'}, 
            'poor': {'threshold': float('inf'), 'description': 'Not actionable'}
        }
    
    def calculate_range_based_accuracy(self, 
                                     predicted_close: float,
                                     actual_close: float,
                                     actual_range: float,
                                     predicted_range: float,
                                     predicted_character: str,
                                     actual_character: str) -> Dict:
        """Calculate proper range-based accuracy metrics"""
        
        # Core accuracy calculations
        close_error = abs(predicted_close - actual_close)
        range_relative_error = (close_error / actual_range) * 100 if actual_range > 0 else 100
        
        # Range prediction accuracy
        range_error = abs(predicted_range - actual_range)
        range_error_pct = (range_error / actual_range) * 100 if actual_range > 0 else 100
        
        # Character prediction accuracy
        character_match = predicted_character == actual_character
        
        # Determine quality assessment
        quality = self._assess_quality(range_relative_error)
        
        # Session movement capture
        movement_captured = max(0, 100 - range_relative_error)
        
        return {
            'core_metrics': {
                'close_error_points': close_error,
                'range_relative_error_pct': range_relative_error,
                'range_error_pct': range_error_pct,
                'movement_captured_pct': movement_captured
            },
            'quality_assessment': {
                'overall_quality': quality['grade'],
                'trading_value': quality['description'],
                'actionable': quality['grade'] in ['excellent', 'good']
            },
            'prediction_details': {
                'predicted_close': predicted_close,
                'actual_close': actual_close,
                'predicted_range': predicted_range,
                'actual_range': actual_range,
                'character_match': character_match
            },
            'comparative_analysis': {
                'misleading_price_relative': f"{(close_error / actual_close) * 100:.2f}%",
                'correct_range_relative': f"{range_relative_error:.1f}%",
                'why_range_matters': "Trading success depends on capturing session movement, not absolute price precision"
            }
        }
    
    def _assess_quality(self, range_relative_error: float) -> Dict:
        """Assess prediction quality based on range-relative error"""
        for grade, criteria in self.accuracy_standards.items():
            if range_relative_error < criteria['threshold']:
                return {'grade': grade, 'description': criteria['description']}
        return {'grade': 'poor', 'description': 'Not actionable'}
    
    def validate_same_day_prediction(self,
                                   prediction_file: str,
                                   actual_session_file: str,
                                   prediction_date: str) -> Dict:
        """
        Perform proper same-day validation
        
        Args:
            prediction_file: Path to prediction results
            actual_session_file: Path to actual PM session data  
            prediction_date: Date in YYYY_MM_DD format
            
        Returns:
            Comprehensive validation results with honest accuracy metrics
        """
        
        try:
            # Load prediction data
            with open(prediction_file, 'r') as f:
                prediction_data = json.load(f)
            
            # Load actual session data
            with open(actual_session_file, 'r') as f:
                actual_data = json.load(f)
            
            # Extract prediction values
            cross_pred = prediction_data['cross_session_prediction']
            predicted_close = cross_pred['predicted_close']
            predicted_high = cross_pred['predicted_high']
            predicted_low = cross_pred['predicted_low']
            predicted_range = predicted_high - predicted_low
            predicted_character = cross_pred['session_character_prediction']
            
            # Extract actual values
            actual_price_data = actual_data['original_session_data']['price_data']
            actual_close = actual_price_data['close']
            actual_high = actual_price_data['high']
            actual_low = actual_price_data['low']
            actual_range = actual_price_data['range']
            actual_character = actual_price_data['session_character']
            
            # Calculate honest accuracy
            accuracy_results = self.calculate_range_based_accuracy(
                predicted_close, actual_close, actual_range, predicted_range,
                predicted_character, actual_character
            )
            
            # Generate validation report
            validation_report = self._generate_validation_report(
                accuracy_results, prediction_date, actual_data, prediction_data
            )
            
            return {
                'validation_metadata': {
                    'validation_type': 'same_day_range_based',
                    'prediction_date': prediction_date,
                    'validation_timestamp': datetime.now().isoformat(),
                    'proper_methodology': True
                },
                'accuracy_analysis': accuracy_results,
                'validation_report': validation_report,
                'files_analyzed': {
                    'prediction_file': prediction_file,
                    'actual_session_file': actual_session_file
                }
            }
            
        except FileNotFoundError as e:
            return self._handle_missing_data_error(str(e), prediction_date)
        except Exception as e:
            return self._handle_validation_error(str(e), prediction_date)
    
    def _generate_validation_report(self, 
                                  accuracy: Dict, 
                                  date: str,
                                  actual_data: Dict,
                                  prediction_data: Dict) -> str:
        """Generate comprehensive validation report"""
        
        core = accuracy['core_metrics']
        quality = accuracy['quality_assessment']
        details = accuracy['prediction_details']
        comparison = accuracy['comparative_analysis']
        
        # Session metadata
        session_meta = actual_data['original_session_data']['session_metadata']
        session_duration = session_meta.get('duration_minutes', 'Unknown')
        
        report = f"""
🎯 PROPER SAME-DAY VALIDATION REPORT
===================================
Date: {date}
Session: PM ({session_duration} minutes)
Validation Method: Range-Based Accuracy (Correct)

📊 ACTUAL PM SESSION RESULTS:
   Close: {details['actual_close']:.2f}
   Range: {details['actual_range']:.2f} points
   Character: {actual_data['original_session_data']['price_data']['session_character']}

🎯 PREDICTION ACCURACY (HONEST METRICS):
   Predicted Close: {details['predicted_close']:.2f}
   Close Error: {core['close_error_points']:.1f} points
   
   Range-Relative Error: {core['range_relative_error_pct']:.1f}% 
   Session Movement Captured: {core['movement_captured_pct']:.1f}%
   
   Quality Assessment: {quality['overall_quality'].title()}
   Trading Value: {quality['trading_value']}
   Actionable: {'✅ YES' if quality['actionable'] else '❌ NO'}

📈 RANGE PREDICTION ACCURACY:
   Predicted Range: {details['predicted_range']:.1f} points
   Actual Range: {details['actual_range']:.1f} points
   Range Error: {core['range_error_pct']:.1f}%

🔤 CHARACTER PREDICTION:
   Predicted: {details['predicted_close']}
   Actual: {actual_data['original_session_data']['price_data']['session_character']}
   Match: {'✅ CORRECT' if accuracy['prediction_details']['character_match'] else '❌ MISMATCH'}

⚖️ MEASUREMENT METHODOLOGY COMPARISON:
   MISLEADING (Price-Relative): {comparison['misleading_price_relative']} ("Excellent")
   CORRECT (Range-Relative): {comparison['correct_range_relative']} ("{quality['overall_quality'].title()}")
   
   Why Range-Based Matters: {comparison['why_range_matters']}

🎯 TRADING ACCURACY STANDARDS:
   • <10% of range = Excellent (Highly actionable)
   • 10-20% of range = Good (Actionable)  
   • 20-50% of range = Moderate (Limited value)
   • >50% of range = Poor (Not actionable)

📋 VALIDATION QUALITY CHECKS:
   ✅ Same-Day Data: Prediction and actual from {date}
   ✅ Range-Based Metrics: Proper trading accuracy measurement
   ✅ Character Analysis: Session behavior prediction assessed
   ✅ Honest Assessment: No misleading percentage calculations

🚀 FINAL ASSESSMENT: {quality['overall_quality'].upper()} PREDICTION
Trading Recommendation: {quality['trading_value']}
Session Movement Captured: {core['movement_captured_pct']:.0f}%
"""
        return report
    
    def _handle_missing_data_error(self, error: str, date: str) -> Dict:
        """Handle case where actual PM data doesn't exist"""
        return {
            'validation_status': 'impossible',
            'error_type': 'missing_actual_data',
            'error_message': error,
            'date': date,
            'explanation': f"July {date.split('_')[2]}rd PM session data not found - cannot perform same-day validation",
            'recommendation': "Process PM session through Grok 4 pipeline to generate actual results for validation",
            'current_validation_flaw': "Using different day actuals creates meaningless comparison"
        }
    
    def _handle_validation_error(self, error: str, date: str) -> Dict:
        """Handle general validation errors"""
        return {
            'validation_status': 'failed',
            'error_type': 'validation_error', 
            'error_message': error,
            'date': date,
            'recommendation': "Check file formats and data structure consistency"
        }

def demonstrate_proper_framework():
    """Demonstrate how proper validation should work"""
    
    validator = TradingAccuracyValidator()
    
    print("🎯 PROPER VALIDATION FRAMEWORK DEMONSTRATION")
    print("=" * 55)
    
    # Attempt validation with July 23rd data (will show missing data handling)
    print("\n1️⃣ ATTEMPTING PROPER SAME-DAY VALIDATION:")
    print("   Prediction: July 23rd AM+Lunch→PM simulation")
    print("   Actual: July 23rd PM session (if available)")
    
    result = validator.validate_same_day_prediction(
        'enhanced_am_lunch_pm_simulation_2025_07_23.json',
        'ny_pm_grokEnhanced_2025_07_23.json',  # This file doesn't exist
        '2025_07_23'
    )
    
    if result.get('validation_status') == 'impossible':
        print(f"\n   ❌ {result['explanation']}")
        print(f"   🔧 {result['recommendation']}")
        print(f"   ⚠️ Current Issue: {result['current_validation_flaw']}")
    
    # Show what proper validation would look like with hypothetical data
    print("\n2️⃣ HYPOTHETICAL PROPER VALIDATION EXAMPLE:")
    
    # Simulate proper validation with realistic trading data
    hypothetical_accuracy = validator.calculate_range_based_accuracy(
        predicted_close=23285.73,  # Our prediction
        actual_close=23250.00,     # Hypothetical actual
        actual_range=75.0,         # Hypothetical actual range
        predicted_range=56.0,      # Our predicted range
        predicted_character="neutral_continuation",
        actual_character="expansion_consolidation_distribution"
    )
    
    print("\n   📊 HYPOTHETICAL RESULTS:")
    core = hypothetical_accuracy['core_metrics']
    quality = hypothetical_accuracy['quality_assessment'] 
    
    print(f"   Close Error: {core['close_error_points']:.1f} points")
    print(f"   Range-Relative Error: {core['range_relative_error_pct']:.1f}%")
    print(f"   Quality: {quality['overall_quality'].title()}")
    print(f"   Trading Value: {quality['trading_value']}")
    print(f"   Movement Captured: {core['movement_captured_pct']:.1f}%")
    
    # Save framework for future use
    framework_data = {
        'framework_metadata': {
            'framework_type': 'proper_trading_validation',
            'created': datetime.now().isoformat(),
            'purpose': 'Replace misleading price-relative with honest range-relative accuracy'
        },
        'accuracy_standards': validator.accuracy_standards,
        'methodology': {
            'correct_formula': 'range_relative_error = (prediction_error / session_range) * 100',
            'validation_requirements': [
                'Same-day prediction and actual data',
                'Range-based accuracy calculation', 
                'Character prediction assessment',
                'Honest quality grading'
            ]
        },
        'demonstration_results': {
            'missing_data_handling': result,
            'hypothetical_proper_validation': hypothetical_accuracy
        }
    }
    
    with open('proper_validation_framework.json', 'w') as f:
        json.dump(framework_data, f, indent=2, default=str)
    
    print(f"\n💾 Proper validation framework saved to: proper_validation_framework.json")
    
    return validator

def main():
    """Main execution"""
    validator = demonstrate_proper_framework()
    
    print(f"\n🎯 FRAMEWORK SUMMARY")
    print("=" * 25)
    print("✅ Range-based accuracy calculation implemented")
    print("✅ Same-day validation framework created")
    print("✅ Honest quality assessment standards defined")
    print("✅ Missing data handling demonstrated")
    print("\n🔧 NEXT STEPS:")
    print("1. Process July 23rd PM session through Grok 4 pipeline")
    print("2. Run proper same-day validation with framework")
    print("3. Replace misleading 'excellent' claims with honest assessments")

if __name__ == "__main__":
    main()