#!/usr/bin/env python3
"""
Validate HMM Monte Carlo Prediction against Ground Truth
Compares the HMM Monte Carlo predictions against actual NYAM session data
to assess improvement in cascade vs expansion timing challenge.
"""

import json
from datetime import datetime
from typing import Dict, Any
import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data

def load_ground_truth_data() -> Dict[str, Any]:
    """Load actual NYAM session data for validation"""
    
    # Load the NYAM Lvl-1 data (ground truth)
    nyam_data = load_json_data('NYAM_Lvl-1_2025_07_25.json')
    
    return nyam_data

def extract_actual_event_timing(nyam_data: Dict[str, Any]) -> Dict[str, float]:
    """Extract actual cascade and expansion timing from NYAM session"""
    
    # From previous validation, we know:
    # - Cascade occurred at 120.0 minutes from session start
    # - Expansion occurred at 26.0 minutes from session start
    
    # These are the ground truth values established in the timing validation
    actual_timings = {
        'cascade_actual_minutes': 120.0,
        'expansion_actual_minutes': 26.0,
        'dominant_session_phase': 'cascade_dominant',
        'consolidation_break_occurred': True,
        'early_action_phase': True
    }
    
    return actual_timings

def calculate_prediction_accuracy(predicted: Dict[str, Any], actual: Dict[str, float]) -> Dict[str, Any]:
    """Calculate accuracy metrics for HMM Monte Carlo prediction"""
    
    # Extract predictions
    cascade_predicted = predicted['ny_am_predictions']['cascade_timing_minutes']
    expansion_predicted = predicted['ny_am_predictions']['expansion_timing_minutes']
    
    # Extract actuals
    cascade_actual = actual['cascade_actual_minutes']
    expansion_actual = actual['expansion_actual_minutes']
    
    # Calculate errors
    cascade_error = abs(cascade_predicted - cascade_actual)
    expansion_error = abs(expansion_predicted - expansion_actual)
    
    # Calculate accuracy grades
    def grade_accuracy(error_minutes: float) -> str:
        if error_minutes <= 1.0:
            return "excellent"
        elif error_minutes <= 5.0:
            return "good"
        elif error_minutes <= 10.0:
            return "moderate"
        else:
            return "poor"
    
    cascade_grade = grade_accuracy(cascade_error)
    expansion_grade = grade_accuracy(expansion_error)
    
    # Calculate overall improvement vs previous system
    previous_cascade_error = 111.9  # Previous error from ground truth validation
    previous_expansion_error = 5.6  # Previous error from ground truth validation
    
    cascade_improvement = ((previous_cascade_error - cascade_error) / previous_cascade_error) * 100
    expansion_change = ((expansion_error - previous_expansion_error) / previous_expansion_error) * 100
    
    return {
        'cascade_timing_analysis': {
            'predicted_minutes': cascade_predicted,
            'actual_minutes': cascade_actual,
            'error_minutes': cascade_error,
            'accuracy_grade': cascade_grade,
            'previous_error': previous_cascade_error,
            'improvement_percentage': cascade_improvement
        },
        'expansion_timing_analysis': {
            'predicted_minutes': expansion_predicted,
            'actual_minutes': expansion_actual,
            'error_minutes': expansion_error,
            'accuracy_grade': expansion_grade,
            'previous_error': previous_expansion_error,
            'change_percentage': expansion_change
        },
        'overall_assessment': {
            'cascade_vs_expansion_challenge': {
                'cascade_error_before': f"{previous_cascade_error} minutes",
                'cascade_error_after': f"{cascade_error:.1f} minutes",
                'expansion_error_before': f"{previous_expansion_error} minutes", 
                'expansion_error_after': f"{expansion_error:.1f} minutes",
                'cascade_improvement_achieved': cascade_improvement > 0,
                'expansion_performance_maintained': expansion_error <= 15.0  # Reasonable threshold
            },
            'hmm_method_effectiveness': {
                'cascade_timing_grade': cascade_grade,
                'expansion_timing_grade': expansion_grade,
                'overall_success': cascade_improvement > 50 and expansion_error <= 15.0
            }
        }
    }

def main():
    """Validate HMM Monte Carlo prediction against ground truth"""
    
    print("🔍 HMM MONTE CARLO PREDICTION VALIDATION")
    print("=" * 50)
    print("🎯 CASCADE vs EXPANSION TIMING CHALLENGE ASSESSMENT")
    print("=" * 50)
    
    # Load prediction results
    prediction_data = load_json_data('hmm_monte_carlo_premarket_to_nyam_prediction_20250728_173045.json')
    
    print(f"1️⃣ HMM Monte Carlo Predictions:")
    print(f"   Cascade Timing: {prediction_data['ny_am_predictions']['cascade_timing_minutes']:.1f} minutes")
    print(f"   Expansion Timing: {prediction_data['ny_am_predictions']['expansion_timing_minutes']:.1f} minutes")
    print(f"   Session Character: {prediction_data['ny_am_predictions']['session_character_prediction']}")
    
    # Load ground truth
    nyam_ground_truth = load_ground_truth_data()
    actual_timings = extract_actual_event_timing(nyam_ground_truth)
    
    print(f"\n2️⃣ Ground Truth Actual Events:")
    print(f"   Cascade Actual: {actual_timings['cascade_actual_minutes']:.1f} minutes")
    print(f"   Expansion Actual: {actual_timings['expansion_actual_minutes']:.1f} minutes")
    print(f"   Dominant Phase: {actual_timings['dominant_session_phase']}")
    
    # Calculate accuracy
    accuracy_results = calculate_prediction_accuracy(prediction_data, actual_timings)
    
    print(f"\n3️⃣ Prediction Accuracy Analysis:")
    
    cascade_analysis = accuracy_results['cascade_timing_analysis']
    print(f"   🎯 CASCADE TIMING:")
    print(f"      Predicted: {cascade_analysis['predicted_minutes']:.1f} min")
    print(f"      Actual: {cascade_analysis['actual_minutes']:.1f} min")
    print(f"      Error: {cascade_analysis['error_minutes']:.1f} min ({cascade_analysis['accuracy_grade']})")
    print(f"      Improvement: {cascade_analysis['improvement_percentage']:.1f}% vs previous system")
    
    expansion_analysis = accuracy_results['expansion_timing_analysis']
    print(f"   🎯 EXPANSION TIMING:")
    print(f"      Predicted: {expansion_analysis['predicted_minutes']:.1f} min")
    print(f"      Actual: {expansion_analysis['actual_minutes']:.1f} min")
    print(f"      Error: {expansion_analysis['error_minutes']:.1f} min ({expansion_analysis['accuracy_grade']})")
    print(f"      Change: {expansion_analysis['change_percentage']:.1f}% vs previous system")
    
    # Overall assessment
    overall = accuracy_results['overall_assessment']
    challenge_status = overall['cascade_vs_expansion_challenge']
    method_effectiveness = overall['hmm_method_effectiveness']
    
    print(f"\n4️⃣ CASCADE vs EXPANSION CHALLENGE RESULTS:")
    print(f"   Before: Cascade {challenge_status['cascade_error_before']}, Expansion {challenge_status['expansion_error_before']}")
    print(f"   After:  Cascade {challenge_status['cascade_error_after']}, Expansion {challenge_status['expansion_error_after']}")
    print(f"   ✅ Cascade Improvement: {'YES' if challenge_status['cascade_improvement_achieved'] else 'NO'}")
    print(f"   ✅ Expansion Maintained: {'YES' if challenge_status['expansion_performance_maintained'] else 'NO'}")
    
    print(f"\n5️⃣ HMM Method Effectiveness:")
    print(f"   Cascade Grade: {method_effectiveness['cascade_timing_grade'].upper()}")
    print(f"   Expansion Grade: {method_effectiveness['expansion_timing_grade'].upper()}")
    print(f"   Overall Success: {'✅ YES' if method_effectiveness['overall_success'] else '❌ NO'}")
    
    # Save validation results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    validation_output = f"hmm_monte_carlo_validation_results_{timestamp}.json"
    
    validation_results = {
        'validation_metadata': {
            'validation_type': 'hmm_monte_carlo_prediction_accuracy',
            'timestamp': datetime.now().isoformat(),
            'challenge_addressed': 'cascade_vs_expansion_timing_calibration',
            'methodology': 'ground_truth_comparison'
        },
        'prediction_source': 'hmm_monte_carlo_premarket_to_nyam_prediction_20250728_173045.json',
        'ground_truth_source': 'NYAM_Lvl-1_2025_07_25.json',
        'accuracy_analysis': accuracy_results,
        'validation_conclusion': {
            'cascade_timing_improvement': challenge_status['cascade_improvement_achieved'],
            'expansion_timing_maintained': challenge_status['expansion_performance_maintained'], 
            'hmm_method_effective': method_effectiveness['overall_success'],
            'recommended_action': 'further_calibration' if not method_effectiveness['overall_success'] else 'deploy_hmm_method'
        }
    }
    
    save_json_data(validation_results, validation_output)
    
    print(f"\n📁 Validation Results Saved: {validation_output}")
    
    # Final recommendation
    if method_effectiveness['overall_success']:
        print(f"\n🎉 RECOMMENDATION: HMM Monte Carlo method shows significant improvement!")
        print(f"   Ready for deployment to address cascade vs expansion challenge.")
    else:
        print(f"\n⚠️ RECOMMENDATION: Further calibration needed.")
        print(f"   Consider additional timing multiplier adjustments.")
    
    return accuracy_results

if __name__ == "__main__":
    main()