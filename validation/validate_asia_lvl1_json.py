#!/usr/bin/env python3
"""
JSON Validation Script for Asia Lvl-1 Output
Validates that <PERSON>'s output matches the required format exactly.
"""

import json
import sys
from typing import Dict, Any, List

def validate_asia_lvl1_json(json_data: Dict[str, Any]) -> List[str]:
    """
    Validate Asia Lvl-1 JSON structure and data types.
    Returns list of validation errors (empty if valid).
    """
    errors = []
    
    # Required top-level keys
    required_keys = [
        "session_metadata", "price_data", "price_movements", 
        "micro_timing_analysis", "behavioral_building_blocks", "validation_context"
    ]
    
    for key in required_keys:
        if key not in json_data:
            errors.append(f"Missing required top-level key: {key}")
    
    # Validate session_metadata
    if "session_metadata" in json_data:
        metadata = json_data["session_metadata"]
        required_metadata = [
            "session_id", "session_type", "date", "start_time", "end_time", 
            "duration_minutes", "sequence_position", "timezone", "session_character",
            "trading_hours", "weekend_context"
        ]
        
        for key in required_metadata:
            if key not in metadata:
                errors.append(f"Missing session_metadata key: {key}")
        
        # Validate data types
        if "duration_minutes" in metadata and not isinstance(metadata["duration_minutes"], (int, float)):
            errors.append("duration_minutes must be a number")
        
        if "sequence_position" in metadata and not isinstance(metadata["sequence_position"], int):
            errors.append("sequence_position must be an integer")
    
    # Validate price_data
    if "price_data" in json_data:
        price_data = json_data["price_data"]
        required_price_fields = ["open", "high", "low", "close", "range", "session_character", "gap_behavior"]
        
        for key in required_price_fields:
            if key not in price_data:
                errors.append(f"Missing price_data key: {key}")
        
        # Validate price numbers
        price_fields = ["open", "high", "low", "close", "range"]
        for field in price_fields:
            if field in price_data and not isinstance(price_data[field], (int, float)):
                errors.append(f"{field} must be a number")
        
        # Validate gap_behavior
        if "gap_behavior" in price_data:
            gap_behavior = price_data["gap_behavior"]
            if "gap_filled" in gap_behavior and not isinstance(gap_behavior["gap_filled"], bool):
                errors.append("gap_filled must be a boolean (true/false)")
    
    # Validate price_movements array
    if "price_movements" in json_data:
        if not isinstance(json_data["price_movements"], list):
            errors.append("price_movements must be an array")
        else:
            for i, movement in enumerate(json_data["price_movements"]):
                required_movement_fields = ["timestamp", "price", "action", "context"]
                for field in required_movement_fields:
                    if field not in movement:
                        errors.append(f"price_movements[{i}] missing field: {field}")
                
                if "price" in movement and not isinstance(movement["price"], (int, float)):
                    errors.append(f"price_movements[{i}].price must be a number")
    
    # Validate micro_timing_analysis
    if "micro_timing_analysis" in json_data:
        micro_timing = json_data["micro_timing_analysis"]
        required_micro_fields = ["cascade_validation", "gap_fill_analysis", "liquidity_events"]
        
        for field in required_micro_fields:
            if field not in micro_timing:
                errors.append(f"Missing micro_timing_analysis key: {field}")
        
        # Validate cascade_validation booleans
        if "cascade_validation" in micro_timing:
            cascade = micro_timing["cascade_validation"]
            if "cascade_occurred" in cascade and not isinstance(cascade["cascade_occurred"], bool):
                errors.append("cascade_occurred must be a boolean")
    
    # Validate behavioral_building_blocks
    if "behavioral_building_blocks" in json_data:
        behavioral = json_data["behavioral_building_blocks"]
        required_behavioral = ["weekend_gap_response", "asia_session_characteristics", "distance_measurements"]
        
        for field in required_behavioral:
            if field not in behavioral:
                errors.append(f"Missing behavioral_building_blocks key: {field}")
    
    # Validate validation_context
    if "validation_context" in json_data:
        validation = json_data["validation_context"]
        required_validation = ["nwog_prediction_validation", "method_performance", "learning_insights"]
        
        for field in required_validation:
            if field not in validation:
                errors.append(f"Missing validation_context key: {field}")
    
    return errors

def validate_json_file(filepath: str) -> bool:
    """Validate JSON file and print results."""
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        print(f"✅ JSON file '{filepath}' loaded successfully")
        
        # Validate structure
        errors = validate_asia_lvl1_json(data)
        
        if not errors:
            print("✅ JSON structure validation PASSED")
            print("✅ All required fields present")
            print("✅ Data types correct")
            print("🎯 JSON is ready for processing!")
            return True
        else:
            print("❌ JSON validation FAILED:")
            for error in errors:
                print(f"   • {error}")
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON syntax error: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ File not found: {filepath}")
        return False
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def validate_json_string(json_string: str) -> bool:
    """Validate JSON string directly."""
    try:
        data = json.loads(json_string)
        errors = validate_asia_lvl1_json(data)
        
        if not errors:
            print("✅ JSON validation PASSED")
            return True
        else:
            print("❌ JSON validation FAILED:")
            for error in errors:
                print(f"   • {error}")
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON syntax error: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 validate_asia_lvl1_json.py <json_file>")
        print("Example: python3 validate_asia_lvl1_json.py asia_session_output.json")
        sys.exit(1)
    
    filepath = sys.argv[1]
    success = validate_json_file(filepath)
    sys.exit(0 if success else 1)