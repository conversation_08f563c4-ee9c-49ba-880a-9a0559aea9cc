#!/usr/bin/env python3
"""
Mathematical Consistency Check
Verify mathematical operations match documentation formulas
"""

import sys
sys.path.append('/Users/<USER>/grok-claude-automation/src')

import re
import ast
import math
from pathlib import Path
from typing import Dict, List, Any, <PERSON><PERSON>

def find_htf_formula_implementations():
    """Find HTF formula: λ_HTF(t) = 0.02 + 35.51·Σ exp(-0.00442·Δt)·magnitude"""
    print("⚡ HTF FORMULA VERIFICATION")
    print("=" * 60)
    print("Target: λ_HTF(t) = μ_h + α_h·Σ exp(-β_h·Δt)·magnitude")
    print("Expected: λ_HTF(t) = 0.02 + 35.51·Σ exp(-0.00442·Δt)·magnitude")
    
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    
    # Search patterns for HTF formula components
    htf_patterns = {
        'mu_h_constant': [r'0\.02', r'mu_h.*=.*0\.02'],
        'alpha_h_constant': [r'35\.51', r'alpha_h.*=.*35\.51'],
        'beta_h_constant': [r'0\.00442', r'beta_h.*=.*0\.00442'],
        'exponential_formula': [r'exp\s*\(\s*-.*\*.*\)', r'math\.exp\s*\(\s*-.*\*.*\)'],
        'htf_intensity_calc': [r'htf_intensity.*=.*\+.*exp', r'lambda.*htf.*=.*\+.*exp'],
        'summation_loop': [r'for.*event.*in.*events', r'sum\s*\(.*exp.*\)']
    }
    
    implementations = {}
    
    # Search Python files
    python_files = list(base_dir.glob("**/*.py"))
    
    for py_file in python_files:
        try:
            with open(py_file, 'r') as f:
                content = f.read()
            
            file_matches = {}
            for pattern_name, patterns in htf_patterns.items():
                matches = []
                for pattern in patterns:
                    found = re.findall(pattern, content, re.IGNORECASE)
                    matches.extend(found)
                
                if matches:
                    file_matches[pattern_name] = matches
            
            if file_matches:
                implementations[py_file.name] = file_matches
                
        except Exception:
            continue
    
    print(f"\n📋 HTF FORMULA IMPLEMENTATIONS FOUND:")
    print(f"   Files with HTF formulas: {len(implementations)}")
    
    for filename, patterns in implementations.items():
        print(f"\n   📄 {filename}:")
        for pattern_name, matches in patterns.items():
            print(f"      • {pattern_name}: {len(matches)} matches")
            if matches and len(str(matches[0])) < 50:  # Show short matches
                print(f"        Example: {matches[0]}")
    
    # Find specific implementations with all components
    complete_implementations = []
    
    for filename, patterns in implementations.items():
        has_mu = 'mu_h_constant' in patterns
        has_alpha = 'alpha_h_constant' in patterns  
        has_beta = 'beta_h_constant' in patterns
        has_exp = 'exponential_formula' in patterns
        
        score = sum([has_mu, has_alpha, has_beta, has_exp])
        if score >= 3:  # Has most components
            complete_implementations.append((filename, score, patterns))
    
    print(f"\n🎯 COMPLETE HTF IMPLEMENTATIONS:")
    for filename, score, patterns in sorted(complete_implementations, key=lambda x: x[1], reverse=True):
        print(f"   ✅ {filename} (completeness: {score}/4)")
    
    return implementations, complete_implementations

def extract_actual_htf_formula(filename: str):
    """Extract actual HTF formula implementation from file."""
    print(f"\n🔍 EXTRACTING HTF FORMULA FROM: {filename}")
    
    file_path = Path(f"/Users/<USER>/grok-claude-automation/{filename}")
    
    # Handle different possible paths
    possible_paths = [
        Path(f"/Users/<USER>/grok-claude-automation/{filename}"),
        Path(f"/Users/<USER>/grok-claude-automation/src/{filename}"),
        Path(f"/Users/<USER>/grok-claude-automation/scripts/{filename}"),
    ]
    
    content = None
    for path in possible_paths:
        if path.exists():
            try:
                with open(path, 'r') as f:
                    content = f.read()
                break
            except Exception:
                continue
    
    if not content:
        print(f"   ❌ Could not read {filename}")
        return None
    
    # Look for HTF intensity calculation methods
    htf_methods = re.findall(r'def\s+(\w*htf\w*intensity\w*|\w*calculate\w*htf\w*)[^:]*:.*?(?=def|\Z)', 
                            content, re.IGNORECASE | re.DOTALL)
    
    if htf_methods:
        print(f"   📊 HTF Methods Found: {len(htf_methods)}")
        for i, method in enumerate(htf_methods[:2]):  # Show first 2
            method_lines = method.split('\n')[:10]  # First 10 lines
            print(f"      Method {i+1}:")
            for line in method_lines:
                if line.strip():
                    print(f"        {line.strip()[:80]}")
    
    # Look for parameter definitions
    param_patterns = {
        'mu_h': r'mu_h\s*=\s*([\d.]+)',
        'alpha_h': r'alpha_h\s*=\s*([\d.]+)',
        'beta_h': r'beta_h\s*=\s*([\d.]+)'
    }
    
    found_params = {}
    for param, pattern in param_patterns.items():
        match = re.search(pattern, content)
        if match:
            found_params[param] = float(match.group(1))
    
    print(f"   📊 Parameters Found: {found_params}")
    
    # Look for the actual formula implementation
    formula_patterns = [
        r'(\w+)\s*=\s*(\w+)\s*\+\s*.*exp.*\*.*magnitude',
        r'(\w+)\s*\+=\s*.*exp.*\*.*',
        r'intensity\s*=\s*.*\+.*exp.*'
    ]
    
    formula_matches = []
    for pattern in formula_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        formula_matches.extend(matches)
    
    if formula_matches:
        print(f"   🧮 Formula Patterns: {len(formula_matches)}")
        for match in formula_matches[:3]:
            print(f"      • {match}")
    
    return {
        'parameters': found_params,
        'methods': len(htf_methods),
        'formulas': formula_matches
    }

def find_session_formula_implementations():
    """Find Session formula: λ_session(t) = 0.15 + 0.6·Σ exp(-0.02·Δt)"""
    print("\n🎯 SESSION FORMULA VERIFICATION")  
    print("=" * 60)
    print("Target: λ_session(t) = μ_s + α_s·Σ exp(-β_s·Δt)")
    print("Expected: λ_session(t) = 0.15 + 0.6·Σ exp(-0.02·Δt)")
    
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    
    # Search patterns for Session formula components
    session_patterns = {
        'mu_s_constant': [r'0\.15', r'0\.163', r'mu_s.*=.*0\.1'],  # Include variations
        'alpha_s_constant': [r'0\.6', r'0\.72', r'alpha_s.*=.*0\.[67]'],  # Include variations
        'beta_s_constant': [r'0\.02', r'beta_s.*=.*0\.02'],
        'session_exponential': [r'exp\s*\(\s*-.*session.*\)', r'exp\s*\(\s*-.*0\.02'],
        'session_intensity': [r'session.*intensity.*=', r'lambda.*session.*='],
        'session_events_loop': [r'for.*event.*in.*session', r'session_events']
    }
    
    implementations = {}
    python_files = list(base_dir.glob("**/*.py"))
    
    for py_file in python_files:
        try:
            with open(py_file, 'r') as f:
                content = f.read()
            
            file_matches = {}
            for pattern_name, patterns in session_patterns.items():
                matches = []
                for pattern in patterns:
                    found = re.findall(pattern, content, re.IGNORECASE)
                    matches.extend(found)
                
                if matches:
                    file_matches[pattern_name] = matches
            
            if file_matches:
                implementations[py_file.name] = file_matches
                
        except Exception:
            continue
    
    print(f"\n📋 SESSION FORMULA IMPLEMENTATIONS:")
    print(f"   Files with Session formulas: {len(implementations)}")
    
    # Find most complete implementations
    complete_session = []
    for filename, patterns in implementations.items():
        has_mu = 'mu_s_constant' in patterns
        has_alpha = 'alpha_s_constant' in patterns
        has_beta = 'beta_s_constant' in patterns  
        has_exp = 'session_exponential' in patterns
        
        score = sum([has_mu, has_alpha, has_beta, has_exp])
        if score >= 2:
            complete_session.append((filename, score, patterns))
    
    print(f"\n🎯 COMPLETE SESSION IMPLEMENTATIONS:")
    for filename, score, patterns in sorted(complete_session, key=lambda x: x[1], reverse=True)[:5]:
        print(f"   ✅ {filename} (completeness: {score}/4)")
        for pattern_name, matches in patterns.items():
            if matches:
                example = matches[0] if len(str(matches[0])) < 30 else f"{matches[0][:30]}..."
                print(f"      • {pattern_name}: {example}")
    
    return implementations, complete_session

def find_coupling_formula_implementations():
    """Find Coupling formula: λ_total = λ_session + γ·λ_HTF"""
    print("\n⚡ COUPLING FORMULA VERIFICATION")
    print("=" * 60)
    print("Target: λ_total(t) = λ_session(t) + γ(t)·λ_HTF(t)")
    
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    
    # Search patterns for Coupling formula components
    coupling_patterns = {
        'lambda_total': [r'lambda_total\s*=', r'λ_total\s*=', r'total.*intensity.*='],
        'lambda_session': [r'lambda_session', r'λ_session', r'session.*intensity'],
        'lambda_htf': [r'lambda_htf', r'λ_htf', r'htf.*intensity'],
        'gamma_coupling': [r'gamma.*\*.*htf', r'γ.*\*.*htf', r'coupling.*htf'],
        'addition_formula': [r'session.*\+.*htf', r'htf.*\+.*session', r'\+.*gamma.*\*'],
        'coupling_method': [r'def.*coupling', r'def.*coupled', r'calculate.*coupled']
    }
    
    implementations = {}
    python_files = list(base_dir.glob("**/*.py"))
    
    for py_file in python_files:
        try:
            with open(py_file, 'r') as f:
                content = f.read()
            
            file_matches = {}
            for pattern_name, patterns in coupling_patterns.items():
                matches = []
                for pattern in patterns:
                    found = re.findall(pattern, content, re.IGNORECASE)
                    matches.extend(found)
                
                if matches:
                    file_matches[pattern_name] = matches
            
            if file_matches:
                implementations[py_file.name] = file_matches
                
        except Exception:
            continue
    
    print(f"\n📋 COUPLING FORMULA IMPLEMENTATIONS:")
    print(f"   Files with Coupling formulas: {len(implementations)}")
    
    # Find most complete implementations
    complete_coupling = []
    for filename, patterns in implementations.items():
        has_total = 'lambda_total' in patterns
        has_session = 'lambda_session' in patterns
        has_htf = 'lambda_htf' in patterns
        has_gamma = 'gamma_coupling' in patterns
        has_add = 'addition_formula' in patterns
        
        score = sum([has_total, has_session, has_htf, has_gamma, has_add])
        if score >= 2:
            complete_coupling.append((filename, score, patterns))
    
    print(f"\n🎯 COMPLETE COUPLING IMPLEMENTATIONS:")
    for filename, score, patterns in sorted(complete_coupling, key=lambda x: x[1], reverse=True)[:5]:
        print(f"   ✅ {filename} (completeness: {score}/5)")
        for pattern_name, matches in patterns.items():
            if matches:
                example = matches[0] if len(str(matches[0])) < 40 else f"{matches[0][:40]}..."
                print(f"      • {pattern_name}: {example}")
    
    return implementations, complete_coupling

def verify_mathematical_consistency():
    """Verify mathematical consistency against documentation."""
    print("📐 MATHEMATICAL CONSISTENCY VERIFICATION")
    print("=" * 80)
    print("Comparing actual implementations vs documented formulas\n")
    
    # Phase 1: HTF Formula
    htf_impls, complete_htf = find_htf_formula_implementations()
    
    # Phase 2: Extract detailed HTF formula
    if complete_htf:
        best_htf_file = complete_htf[0][0]  # Best implementation
        htf_details = extract_actual_htf_formula(best_htf_file)
    
    # Phase 3: Session Formula  
    session_impls, complete_session = find_session_formula_implementations()
    
    # Phase 4: Coupling Formula
    coupling_impls, complete_coupling = find_coupling_formula_implementations()
    
    # Summary and Verification
    print(f"\n🏆 MATHEMATICAL CONSISTENCY SUMMARY")
    print("=" * 60)
    
    # HTF Formula Check
    expected_htf_params = {'mu_h': 0.02, 'alpha_h': 35.51, 'beta_h': 0.00442}
    actual_htf_params = htf_details.get('parameters', {}) if 'htf_details' in locals() else {}
    
    print(f"📊 HTF FORMULA VERIFICATION:")
    print(f"   Expected: λ_HTF(t) = 0.02 + 35.51·Σ exp(-0.00442·Δt)·magnitude")
    print(f"   Implementations found: {len(complete_htf)}")
    
    if actual_htf_params:
        print(f"   Parameters found:")
        for param, expected in expected_htf_params.items():
            actual = actual_htf_params.get(param, 'NOT FOUND')
            match = "✅" if actual == expected else "❌"
            print(f"      {param}: expected={expected}, actual={actual} {match}")
    else:
        print(f"   ❌ Could not extract parameters from implementations")
    
    # Session Formula Check
    print(f"\n📊 SESSION FORMULA VERIFICATION:")
    print(f"   Expected: λ_session(t) = 0.15-0.163 + 0.6-0.72·Σ exp(-0.02·Δt)")
    print(f"   Implementations found: {len(complete_session)}")
    
    # Coupling Formula Check
    print(f"\n📊 COUPLING FORMULA VERIFICATION:")
    print(f"   Expected: λ_total(t) = λ_session(t) + γ(t)·λ_HTF(t)")
    print(f"   Implementations found: {len(complete_coupling)}")
    
    # Overall consistency score
    consistency_score = 0
    total_checks = 3
    
    if len(complete_htf) > 0:
        consistency_score += 1
    if len(complete_session) > 0: 
        consistency_score += 1
    if len(complete_coupling) > 0:
        consistency_score += 1
    
    consistency_percentage = (consistency_score / total_checks) * 100
    
    print(f"\n🎯 OVERALL CONSISTENCY:")
    print(f"   Mathematical formulas implemented: {consistency_score}/{total_checks}")
    print(f"   Consistency score: {consistency_percentage:.1f}%")
    print(f"   Status: {'✅ CONSISTENT' if consistency_percentage >= 80 else '❌ INCONSISTENT'}")
    
    return {
        'htf': {'implementations': len(complete_htf), 'params': actual_htf_params if 'htf_details' in locals() else {}},
        'session': {'implementations': len(complete_session)},
        'coupling': {'implementations': len(complete_coupling)},
        'consistency_score': consistency_percentage
    }

if __name__ == "__main__":
    consistency_results = verify_mathematical_consistency()