#!/usr/bin/env python3
"""
HTF Integration Demo
Demonstrates how to integrate HTF detector and context calculator with existing pipeline
Shows preprocessing weight injection before Unit A processing
"""

import json
from pathlib import Path
from datetime import datetime
from htf_event_extractor import HTF<PERSON>ventDetector
from htf_context_calculator import HTFContextCalculator

class HTFIntegrationDemo:
    """
    Demonstrates HTF integration with existing pipeline
    Shows how to inject htf_context_weight before Unit A processing
    """
    
    def __init__(self):
        self.detector = HTFEventDetector()
        self.calculator = HTFContextCalculator()
        self.htf_events = []
        
    def run_full_demo(self):
        """Run complete HTF integration demonstration"""
        print("🔄 HTF Integration Demo - Complete Workflow")
        print("=" * 60)
        
        # Step 1: Extract HTF events from historical data
        print("\n📊 Step 1: Extracting HTF events from historical data...")
        self.htf_events = self._extract_htf_events()
        
        # Step 2: Demonstrate HTF context calculation for recent sessions
        print(f"\n🧮 Step 2: Calculating HTF context weights...")
        recent_sessions = self._get_recent_sessions()
        
        enhanced_sessions = []
        for session_file in recent_sessions[:5]:  # Test with 5 recent sessions
            enhanced_session = self._enhance_session_with_htf(session_file)
            enhanced_sessions.append(enhanced_session)
        
        # Step 3: Show integration with Unit A preprocessing
        print(f"\n🔧 Step 3: Demonstrating Unit A preprocessing integration...")
        self._demonstrate_unit_a_integration(enhanced_sessions)
        
        # Step 4: Validate HTF enhancement impact
        print(f"\n📈 Step 4: Validating HTF enhancement impact...")
        self._validate_enhancement_impact(enhanced_sessions)
        
        print(f"\n✅ HTF Integration Demo completed successfully!")
        return enhanced_sessions
    
    def _extract_htf_events(self):
        """Extract HTF events using the HTF detector"""
        results = self.detector.detect_events()
        htf_events = results['htf_events']
        
        print(f"   Found {len(htf_events)} HTF events across {results['analysis_metadata']['sessions_analyzed']} sessions")
        
        # Show summary of events
        event_types = {}
        for event in htf_events:
            event_types[event['type']] = event_types.get(event['type'], 0) + 1
        
        for event_type, count in event_types.items():
            print(f"   - {event_type}: {count} events")
        
        return htf_events
    
    def _get_recent_sessions(self):
        """Get list of recent session files for demonstration"""
        session_patterns = [
            "data/enhanced/grok_enhanced/*_grokEnhanced_2025_07_*.json",
            "*_Lvl-1_2025_07_*.json"
        ]
        
        import glob
        session_files = []
        for pattern in session_patterns:
            session_files.extend(glob.glob(pattern))
        
        # Sort by filename (contains dates)
        session_files.sort()
        
        print(f"   Found {len(session_files)} recent session files")
        return session_files
    
    def _enhance_session_with_htf(self, session_file):
        """Enhance a session with HTF context"""
        try:
            # Generate HTF context for this session
            htf_context = self.calculator.generate_htf_context_for_session(
                session_file, 
                self.htf_events
            )
            
            # Load original session data
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            
            # Create enhanced session with HTF context injection
            enhanced_session = {
                'original_session_file': session_file,
                'original_session_data': session_data,
                'htf_context': htf_context,
                'enhancement_metadata': {
                    'enhancement_time': datetime.now().isoformat(),
                    'htf_events_used': len(self.htf_events),
                    'enhancement_type': 'htf_preprocessing_weight'
                }
            }
            
            filename = Path(session_file).name
            print(f"   ✅ Enhanced {filename}: weight={htf_context['htf_context_weight']:.3f}, phase={htf_context['weekly_phase']}")
            
            return enhanced_session
            
        except Exception as e:
            print(f"   ❌ Error enhancing {session_file}: {e}")
            return None
    
    def _demonstrate_unit_a_integration(self, enhanced_sessions):
        """Demonstrate how HTF weights integrate with Unit A calculations"""
        print(f"   Demonstrating v_synthetic enhancement with HTF weights:")
        print(f"   ✅ CORRECTED APPROACH: HTF enhances existing v_synthetic calculations")
        
        for enhanced_session in enhanced_sessions:
            if not enhanced_session:
                continue
                
            htf_context = enhanced_session['htf_context']
            session_file = enhanced_session['original_session_file']
            filename = Path(session_file).name
            
            # ✅ CORRECT IMPLEMENTATION: HTF enhances existing calculation
            # Step 1: Calculate session-level v_synthetic (preserving existing intelligence)
            v_synthetic = self._calculate_session_volume(enhanced_session)
            
            # Step 2: Apply HTF enhancement as volume amplifier
            htf_weight = htf_context['htf_context_weight']
            v_synthetic_enhanced = v_synthetic * htf_weight  # ✅ Multiply by HTF
            
            # Calculate enhancement percentage
            enhancement_percent = ((v_synthetic_enhanced / v_synthetic) - 1) * 100
            
            print(f"   - {filename[:20]}...")
            print(f"     Session v_synthetic: {v_synthetic:.2f} (keeps existing calculation)")
            print(f"     HTF weight: {htf_weight:.3f} (weekly influence multiplier)")
            print(f"     Enhanced v_synthetic: {v_synthetic_enhanced:.2f} ({enhancement_percent:+.1f}%)")
            print(f"     Weekly phase: {htf_context['weekly_phase']} → HTF acts as {self._get_amplifier_description(htf_weight)}")
            print()
    
    def _calculate_session_volume(self, enhanced_session):
        """
        Calculate session-level v_synthetic preserving existing intelligence
        This simulates the actual Unit A calculation that HTF enhances
        """
        session_data = enhanced_session['original_session_data']
        
        # Simulate realistic session-based v_synthetic calculation
        # In reality, this would use the actual Unit A mathematical framework
        base_volume = 128.66  # Starting point from CLAUDE.md
        
        # Apply session-specific intelligence (price action, volatility, structures)
        session_modifier = 1.0
        
        # Factor in price range for volatility
        if 'price_data' in session_data:
            price_data = session_data['price_data']
            if 'high' in price_data and 'low' in price_data:
                price_range = abs(price_data['high'] - price_data['low'])
                volatility_factor = min(price_range / 100, 2.0)  # Cap at 2x
                session_modifier *= (1 + volatility_factor * 0.2)
        
        # Factor in structure count
        active_structures = self._extract_active_structures_from_session(session_data)
        structure_factor = len(active_structures) * 0.05  # 5% per structure
        session_modifier *= (1 + structure_factor)
        
        # Add session time influence (London more volatile than Asian)
        filename = enhanced_session['original_session_file']
        if 'LONDON' in filename.upper():
            session_modifier *= 1.2
        elif 'ASIA' in filename.upper():
            session_modifier *= 0.9
        
        return base_volume * session_modifier
    
    def _extract_active_structures_from_session(self, session_data):
        """Extract active structures from session data"""
        if 'active_structures' in session_data:
            return session_data['active_structures']
        return []
    
    def _get_amplifier_description(self, htf_weight):
        """Get human-readable description of HTF amplification"""
        if htf_weight > 1.3:
            return "strong amplifier"
        elif htf_weight > 1.1:
            return "moderate amplifier"
        elif htf_weight < 0.8:
            return "dampener"
        else:
            return "neutral"
    
    def _validate_enhancement_impact(self, enhanced_sessions):
        """Validate the impact of HTF enhancement"""
        print(f"   HTF Enhancement Impact Analysis:")
        
        weights = []
        phases = []
        
        for enhanced_session in enhanced_sessions:
            if not enhanced_session:
                continue
                
            htf_context = enhanced_session['htf_context']
            weights.append(htf_context['htf_context_weight'])
            phases.append(htf_context['weekly_phase'])
        
        if weights:
            import numpy as np
            
            avg_weight = np.mean(weights)
            min_weight = min(weights)
            max_weight = max(weights)
            weight_std = np.std(weights)
            
            print(f"   - Average HTF weight: {avg_weight:.3f}")
            print(f"   - Weight range: {min_weight:.3f} - {max_weight:.3f}")
            print(f"   - Weight std deviation: {weight_std:.3f}")
            
            # Phase distribution
            phase_counts = {}
            for phase in phases:
                phase_counts[phase] = phase_counts.get(phase, 0) + 1
            
            print(f"   - Weekly phase distribution:")
            for phase, count in phase_counts.items():
                percentage = (count / len(phases)) * 100
                print(f"     * {phase}: {count} sessions ({percentage:.1f}%)")
        
        # Expected impact on cascade timing error
        if weights:
            baseline_error = 111.9  # minutes (from your specifications)
            avg_enhancement = (avg_weight - 1.0) * 100  # Convert to percentage
            
            # Estimate potential error reduction (conservative estimate)
            potential_reduction = min(avg_enhancement * 0.5, 30)  # Cap at 30% reduction
            estimated_new_error = baseline_error * (1 - potential_reduction / 100)
            
            print(f"   - Baseline cascade timing error: {baseline_error:.1f} minutes")
            print(f"   - Average HTF enhancement: {avg_enhancement:+.1f}%")
            print(f"   - Potential error reduction: {potential_reduction:.1f}%")
            print(f"   - Estimated enhanced error: {estimated_new_error:.1f} minutes")
            
            if estimated_new_error <= 78:  # Target from Opus specs
                print(f"   ✅ HTF enhancement meets 30% reduction target!")
            else:
                print(f"   ⚠️  HTF enhancement may need calibration for target achievement")

def main():
    """Run the HTF integration demonstration"""
    demo = HTFIntegrationDemo()
    enhanced_sessions = demo.run_full_demo()
    
    # Save demo results
    demo_results = {
        'demo_metadata': {
            'demo_time': datetime.now().isoformat(),
            'sessions_enhanced': len([s for s in enhanced_sessions if s is not None]),
            'htf_events_detected': len(demo.htf_events)
        },
        'enhanced_sessions_summary': []
    }
    
    for session in enhanced_sessions:
        if session:
            summary = {
                'session_file': session['original_session_file'],
                'htf_weight': session['htf_context']['htf_context_weight'],
                'weekly_phase': session['htf_context']['weekly_phase'],
                'liquidity_imbalance': session['htf_context']['liquidity_imbalance']
            }
            demo_results['enhanced_sessions_summary'].append(summary)
    
    output_file = f"htf_integration_demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(demo_results, f, indent=2, default=str)
    
    print(f"\n💾 Demo results saved to: {output_file}")

if __name__ == "__main__":
    main()