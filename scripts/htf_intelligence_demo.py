#!/usr/bin/env python3
"""
HTF Intelligence Demo - Complete System Demonstration

This demo shows the complete HTF Session Intelligence system in action,
demonstrating how Level 1 natural language transcriptions are automatically
converted to HTF context updates with date-specific identifiers.
"""

import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from htf_intelligence_integration import HTFIntelligenceIntegration


def show_level_1_examples():
    """Show Level 1 natural language examples."""
    print("📝 Level 1 Natural Language Examples:")
    print("=" * 45)
    
    examples = [
        {
            "context": "Took pre-market low immediately at open, then expanded higher",
            "parsing": "premarket_low_2025-07-28_violated at 23466.25"
        },
        {
            "context": "Previous day's New York AM session high taken",
            "parsing": "ny_am_high_2025-07-28_violated at 23529.0"
        },
        {
            "context": "Lunch session high from yesterday broken at 15:25",
            "parsing": "lunch_high_2025-07-28_violated at 23509.25"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. Natural Language:")
        print(f"   \"{example['context']}\"")
        print(f"   Intelligence Parsing:")
        print(f"   → {example['parsing']}")


def show_htf_context_before_after():
    """Show HTF context files before and after intelligence processing."""
    integration = HTFIntelligenceIntegration()
    
    # Find an HTF context file with intelligence
    htf_dir = integration.base_dir / "data" / "trackers" / "htf"
    
    for htf_file in htf_dir.glob("HTF_Context_*_grokEnhanced_*.json"):
        try:
            with open(htf_file, 'r') as f:
                htf_data = json.load(f)
                
            if htf_data.get("intelligence_processed") and htf_data.get("htf_events"):
                print(f"\n📊 HTF Context File: {htf_file.name}")
                print("=" * 50)
                
                print(f"Session Type: {htf_data.get('session_type', 'Unknown')}")
                print(f"Date: {htf_data.get('date', 'Unknown')}")
                print(f"Intelligence Processed: {htf_data.get('intelligence_processed', False)}")
                print(f"Events Added: {htf_data.get('intelligence_events_added', 0)}")
                
                print(f"\n🎯 HTF Intelligence Events:")
                for event in htf_data.get("htf_events", []):
                    print(f"  • {event.get('htf_significance', 'Unknown')}")
                    print(f"    Level: {event.get('level', 'Unknown')}")
                    print(f"    Origin: {event.get('origin_session', 'Unknown')}")
                    print(f"    Context: \"{event.get('session_details', {}).get('context', 'Unknown')}\"")
                    print()
                
                break
                
        except Exception as e:
            continue


def demonstrate_date_specific_identifiers():
    """Demonstrate the critical date-specific identifier system."""
    print("\n🔒 Date-Specific Identifier Prevention System:")
    print("=" * 50)
    
    print("❌ WRONG (Generic - causes confusion):")
    print('   "htf_significance": "previous_day_high_violated"')
    print('   "htf_significance": "previous_day_high_violated"  # Same phrase!')
    print("   → System can't distinguish which day's high")
    
    print("\n✅ CORRECT (Date-specific - prevents confusion):")
    print('   "htf_significance": "daily_high_2025_07_30_violated"')
    print('   "htf_significance": "daily_high_2025_07_31_violated"')
    print("   → System knows exactly which day's high for each event")
    
    print("\n🧠 Intelligence Benefits:")
    print("   • No temporal confusion in multi-day processing")
    print("   • Precise HTF algorithm inputs")
    print("   • Accurate cross-session reference resolution")
    print("   • Future-proof event identification")


def main():
    """Run complete HTF Intelligence demonstration."""
    print("🧠 HTF Session Intelligence System - Complete Demo")
    print("=" * 60)
    
    # Show Level 1 examples
    show_level_1_examples()
    
    # Show date-specific identifier system
    demonstrate_date_specific_identifiers()
    
    # Show actual HTF context results
    show_htf_context_before_after()
    
    # Show system statistics
    integration = HTFIntelligenceIntegration()
    validation = integration.validate_integration()
    
    print(f"\n📈 System Statistics:")
    print("=" * 25)
    print(f"HTF Files with Intelligence: {validation['htf_files_with_intelligence']}")
    print(f"Total Intelligence Events: {validation['total_intelligence_events']}")
    print(f"Files Processed: {validation['files_checked']}")
    
    print(f"\n🎯 Intelligence Processing Success:")
    print("✅ Level 1 natural language → Parsed session references")
    print("✅ Cross-session level resolution → Found actual levels")
    print("✅ Date-specific HTF significance → Prevented confusion")
    print("✅ HTF context file updates → Integration complete")
    print("✅ Ready for HTF algorithm processing → End-to-end flow")
    
    print(f"\n🚀 Manual Transcription Eliminated:")
    print("• No more manual HTF context updates")
    print("• No more session reference confusion")
    print("• No more temporal identifier conflicts")
    print("• Automatic intelligence processing for all sessions")


if __name__ == "__main__":
    main()