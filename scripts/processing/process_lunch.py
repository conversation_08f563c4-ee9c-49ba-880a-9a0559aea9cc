#!/usr/bin/env python3

import json
from src.pipeline import GrokPipeline

def main():
    # Load lunch session data
    with open('Lunch_Lvl-1_2025_07_25.json', 'r') as f:
        lunch_session_data = json.load(f)

    # Create pipeline and process
    pipeline = GrokPipeline()
    print('Starting Lunch session processing through A→B→C→D pipeline...')

    # Process the session with enhanced pipeline
    result = pipeline.process_session(lunch_session_data)

    # Save enhanced lunch session data
    with open('Lunch_session_enhanced_2025_07_25.json', 'w') as f:
        json.dump(result, f, indent=2)

    print('✅ Lunch session A→B→C→D pipeline processing completed successfully')
    print('Enhanced data saved to: Lunch_session_enhanced_2025_07_25.json')

    # Quick validation check
    if 'enhanced_predictions' in result:
        energy_rate = result['enhanced_predictions'].get('energy_rate', 1.0)
        momentum_strength = result['enhanced_predictions'].get('momentum_strength', 0.0)
        print('Energy Rate:', energy_rate)
        print('Momentum Strength:', momentum_strength)
        print('Real calculations detected:', energy_rate != 1.0 and momentum_strength > 0)

if __name__ == "__main__":
    main()