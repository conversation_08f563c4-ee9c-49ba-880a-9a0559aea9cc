#!/usr/bin/env python3
"""
Cascade Data Extractor for HTF Reverse Engineering
Extracts cascade timing patterns from existing 121 sessions to enable 
HTF event reconstruction via forensic analysis.

Based on Opus 4's approach: τ_cascade = f(λ_session, λ_HTF, λ_noise)
"""

import json
import os
import glob
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import numpy as np
from datetime import datetime, timedelta

@dataclass
class CascadeEvent:
    """Individual cascade event extracted from session data."""
    session_id: str
    timestamp: str
    cascade_time_minutes: float
    price: float
    action: str
    intensity_estimate: float
    session_type: str
    session_date: str
    context: str

@dataclass  
class SessionBaseline:
    """Baseline cascade behavior for a session type."""
    session_type: str
    mean_cascade_time: float
    std_cascade_time: float
    typical_intensity: float
    quiet_period_parameters: Dict[str, float]
    sample_size: int

class CascadeDataExtractor:
    """
    Extract cascade timing patterns from session files for HTF forensic analysis.
    
    This provides the foundation data for reverse-engineering HTF events
    from cascade timing deviations.
    """
    
    def __init__(self):
        self.cascade_database = []
        self.session_baselines = {}
        
        # Intensity estimation mapping
        self.action_intensity_map = {
            'break': 0.8,
            'major_break': 1.0,
            'touch': 0.3,
            'retest': 0.4,
            'sweep': 0.9,
            'rejection': 0.7,
            'delivery': 0.6
        }
        
        print("🔍 CASCADE DATA EXTRACTOR: Forensic analysis preparation")
        print("   Extracting cascade patterns for HTF reverse engineering")
    
    def extract_cascade_database(self, directory_path: str = "/Users/<USER>/grok-claude-automation") -> List[CascadeEvent]:
        """Extract all cascade events from available session files."""
        
        print(f"🔍 EXTRACTION: Processing session files from {directory_path}")
        
        # Find all session files
        session_files = self._find_session_files(directory_path)
        print(f"   Found {len(session_files)} session files")
        
        # Extract cascade events from each session
        total_cascades = 0
        for i, file_path in enumerate(session_files):
            session_cascades = self._extract_session_cascades(file_path)
            self.cascade_database.extend(session_cascades)
            total_cascades += len(session_cascades)
            
            if (i + 1) % 20 == 0:
                print(f"   Processed {i + 1}/{len(session_files)} sessions")
        
        print(f"   Extracted {total_cascades} cascade events total")
        
        # Calculate session baselines
        self._calculate_session_baselines()
        
        return self.cascade_database
    
    def _find_session_files(self, directory_path: str) -> List[str]:
        """Find all relevant session files."""
        
        patterns = ["*Lvl*.json"]
        session_files = []
        
        for pattern in patterns:
            files = glob.glob(os.path.join(directory_path, pattern))
            session_files.extend(files)
        
        # Filter out non-session files
        filtered_files = []
        exclude_terms = ['fvg_state', 'htf_context', 'liquidity_state', 'tracker', 
                        'prediction', 'validation', 'analysis']
        
        for file_path in session_files:
            filename = os.path.basename(file_path).lower()
            if not any(term in filename for term in exclude_terms):
                filtered_files.append(file_path)
        
        return filtered_files
    
    def _extract_session_cascades(self, file_path: str) -> List[CascadeEvent]:
        """Extract cascade events from individual session file."""
        
        try:
            with open(file_path, 'r') as f:
                session_data = json.load(f)
        except Exception as e:
            print(f"⚠️ Could not load {file_path}: {e}")
            return []
        
        cascades = []
        filename = os.path.basename(file_path)
        
        # Extract session metadata
        metadata = session_data.get('session_metadata', {})
        session_id = metadata.get('session_id', filename.replace('.json', ''))
        session_type = metadata.get('session_type', self._extract_session_type(filename))
        session_date = metadata.get('date', 'unknown')
        
        # Extract cascade events from price movements
        price_movements = session_data.get('price_movements', [])
        
        for movement in price_movements:
            if self._is_cascade_event(movement):
                cascade_time = self._parse_timestamp_to_minutes(movement.get('timestamp', ''))
                
                cascade = CascadeEvent(
                    session_id=session_id,
                    timestamp=movement.get('timestamp', ''),
                    cascade_time_minutes=cascade_time,
                    price=float(movement.get('price', 0)),
                    action=movement.get('action', ''),
                    intensity_estimate=self._estimate_intensity(movement),
                    session_type=session_type,
                    session_date=session_date,
                    context=movement.get('context', '')
                )
                
                cascades.append(cascade)
        
        # Also extract from phase transitions (expansion phases often indicate cascades)
        phase_transitions = session_data.get('phase_transitions', [])
        for phase in phase_transitions:
            if phase.get('phase_type') == 'expansion':
                cascade_time = self._parse_timestamp_to_minutes(phase.get('start_time', ''))
                
                # Calculate intensity from phase characteristics
                phase_range = abs(float(phase.get('high', 0)) - float(phase.get('low', 0)))
                phase_intensity = min(phase_range / 50.0, 1.0)  # Normalize to 0-1
                
                cascade = CascadeEvent(
                    session_id=session_id,
                    timestamp=phase.get('start_time', ''),
                    cascade_time_minutes=cascade_time,
                    price=float(phase.get('high', 0)),
                    action='expansion',
                    intensity_estimate=phase_intensity,
                    session_type=session_type,
                    session_date=session_date,
                    context=phase.get('description', '')
                )
                
                cascades.append(cascade)
        
        return cascades
    
    def _is_cascade_event(self, movement: Dict[str, Any]) -> bool:
        """Determine if price movement represents a cascade event."""
        action = movement.get('action', '').lower()
        context = movement.get('context', '').lower()
        
        # Cascade indicators
        cascade_actions = ['break', 'sweep', 'delivery', 'major']
        cascade_contexts = ['expansion', 'cascade', 'delivery', 'sweep']
        
        return (any(ca in action for ca in cascade_actions) or 
                any(cc in context for cc in cascade_contexts))
    
    def _estimate_intensity(self, movement: Dict[str, Any]) -> float:
        """Estimate cascade intensity from movement characteristics."""
        action = movement.get('action', '').lower()
        context = movement.get('context', '').lower()
        
        # Base intensity from action
        base_intensity = 0.5
        for action_key, intensity in self.action_intensity_map.items():
            if action_key in action:
                base_intensity = intensity
                break
        
        # Adjust based on context
        if 'major' in context:
            base_intensity *= 1.3
        elif 'minor' in context:
            base_intensity *= 0.7
        
        # FVG delivery suggests stronger cascade
        if 'fvg' in context:
            base_intensity *= 1.2
            
        return min(base_intensity, 1.0)
    
    def _parse_timestamp_to_minutes(self, timestamp: str) -> float:
        """Parse timestamp to minutes from session start."""
        if not timestamp or timestamp == 'unknown':
            return 0.0
            
        try:
            if ':' in timestamp:
                parts = timestamp.split(':')
                hours = int(parts[0])
                minutes = int(parts[1]) if len(parts) > 1 else 0
                
                # Convert to minutes from common session start (e.g., 13:30 for NY_PM)
                total_minutes = hours * 60 + minutes
                
                # Normalize to session-relative time (approximate)
                if total_minutes > 800:  # After 13:20
                    return total_minutes - 810  # Relative to 13:30 start
                else:
                    return total_minutes
            
            return 0.0
        except:
            return 0.0
    
    def _extract_session_type(self, filename: str) -> str:
        """Extract session type from filename."""
        filename_upper = filename.upper()
        
        session_types = {
            'ASIA': 'ASIA',
            'LONDON': 'LONDON',
            'LUNCH': 'LUNCH',
            'NYAM': 'NY_AM', 
            'NYPM': 'NY_PM',
            'PREMARKET': 'PREMARKET',
            'MIDNIGHT': 'MIDNIGHT'
        }
        
        for key, session_type in session_types.items():
            if key in filename_upper:
                return session_type
        
        return 'UNKNOWN'
    
    def _calculate_session_baselines(self):
        """Calculate baseline cascade behavior for each session type."""
        
        print("🧮 BASELINES: Calculating session-specific cascade baselines")
        
        # Group cascades by session type
        session_groups = defaultdict(list)
        for cascade in self.cascade_database:
            session_groups[cascade.session_type].append(cascade)
        
        # Calculate baseline for each session type
        for session_type, cascades in session_groups.items():
            if len(cascades) < 3:  # Need minimum samples
                continue
                
            # Identify "quiet" cascades (likely minimal HTF influence)
            quiet_cascades = [c for c in cascades if c.intensity_estimate < 0.4]
            
            if len(quiet_cascades) < 2:
                quiet_cascades = cascades  # Use all if no quiet periods
            
            # Calculate statistics
            cascade_times = [c.cascade_time_minutes for c in quiet_cascades]
            intensities = [c.intensity_estimate for c in quiet_cascades]
            
            baseline = SessionBaseline(
                session_type=session_type,
                mean_cascade_time=np.mean(cascade_times),
                std_cascade_time=np.std(cascade_times),
                typical_intensity=np.mean(intensities),
                quiet_period_parameters={
                    'median_time': np.median(cascade_times),
                    'q75_time': np.percentile(cascade_times, 75),
                    'q25_time': np.percentile(cascade_times, 25)
                },
                sample_size=len(quiet_cascades)
            )
            
            self.session_baselines[session_type] = baseline
            
            print(f"   {session_type}: {len(cascades)} cascades, baseline = {baseline.mean_cascade_time:.1f}±{baseline.std_cascade_time:.1f} min")
    
    def identify_htf_influenced_cascades(self) -> List[Dict[str, Any]]:
        """Identify cascades likely influenced by HTF events."""
        
        htf_influenced = []
        
        print("🔍 HTF INFLUENCE: Identifying HTF-influenced cascades")
        
        for cascade in self.cascade_database:
            baseline = self.session_baselines.get(cascade.session_type)
            if not baseline:
                continue
            
            # Calculate deviation from baseline
            deviation = baseline.mean_cascade_time - cascade.cascade_time_minutes
            
            # HTF influence criteria:
            # 1. Significant acceleration (>15 minutes early)
            # 2. Higher intensity than typical
            # 3. Not in statistical noise range
            
            if (deviation > 15 and  # 15+ minutes early
                cascade.intensity_estimate > baseline.typical_intensity * 1.2 and  # 20% higher intensity
                abs(deviation) > baseline.std_cascade_time * 2):  # Outside 2-sigma
                
                htf_contribution = {
                    'cascade_event': cascade,
                    'baseline_deviation_minutes': deviation,
                    'implied_htf_acceleration': deviation,
                    'confidence_score': self._calculate_htf_confidence(cascade, baseline, deviation),
                    'implied_htf_intensity': deviation / (0.3 * cascade.cascade_time_minutes) if cascade.cascade_time_minutes > 0 else 0
                }
                
                htf_influenced.append(htf_contribution)
        
        print(f"   Identified {len(htf_influenced)} HTF-influenced cascades")
        
        return htf_influenced
    
    def _calculate_htf_confidence(self, cascade: CascadeEvent, baseline: SessionBaseline, deviation: float) -> float:
        """Calculate confidence that cascade was HTF-influenced."""
        
        # Base confidence from deviation magnitude
        deviation_score = min(abs(deviation) / 30.0, 1.0)  # Max at 30min deviation
        
        # Intensity score
        intensity_ratio = cascade.intensity_estimate / max(baseline.typical_intensity, 0.1)
        intensity_score = min(intensity_ratio / 2.0, 1.0)  # Max at 2x typical intensity
        
        # Context score
        context_score = 0.8 if any(term in cascade.context.lower() 
                                 for term in ['major', 'delivery', 'sweep']) else 0.5
        
        # Combined confidence
        confidence = (0.5 * deviation_score + 0.3 * intensity_score + 0.2 * context_score)
        
        return round(confidence, 3)
    
    def save_cascade_database(self, output_path: str = "/Users/<USER>/grok-claude-automation/cascade_database.json"):
        """Save extracted cascade database for HTF forensics."""
        
        # Convert to serializable format
        database_export = {
            "extraction_metadata": {
                "extraction_timestamp": datetime.now().isoformat(),
                "total_cascades": len(self.cascade_database),
                "session_types_covered": list(self.session_baselines.keys()),
                "extraction_method": "forensic_htf_reverse_engineering"
            },
            "session_baselines": {
                session_type: {
                    "mean_cascade_time": baseline.mean_cascade_time,
                    "std_cascade_time": baseline.std_cascade_time,
                    "typical_intensity": baseline.typical_intensity,
                    "sample_size": baseline.sample_size,
                    "quiet_period_parameters": baseline.quiet_period_parameters
                }
                for session_type, baseline in self.session_baselines.items()
            },
            "cascade_events": [
                {
                    "session_id": cascade.session_id,
                    "timestamp": cascade.timestamp,
                    "cascade_time_minutes": cascade.cascade_time_minutes,
                    "price": cascade.price,
                    "action": cascade.action,
                    "intensity_estimate": cascade.intensity_estimate,
                    "session_type": cascade.session_type,
                    "session_date": cascade.session_date,
                    "context": cascade.context
                }
                for cascade in self.cascade_database
            ]
        }
        
        try:
            with open(output_path, 'w') as f:
                json.dump(database_export, f, indent=2)
            print(f"💾 Cascade database saved to: {output_path}")
            return True
        except Exception as e:
            print(f"❌ Failed to save database: {e}")
            return False
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate summary of cascade extraction results."""
        
        # Session type distribution
        session_distribution = defaultdict(int)
        for cascade in self.cascade_database:
            session_distribution[cascade.session_type] += 1
        
        # Action type distribution  
        action_distribution = defaultdict(int)
        for cascade in self.cascade_database:
            action_distribution[cascade.action] += 1
        
        # Intensity distribution
        intensities = [c.intensity_estimate for c in self.cascade_database]
        
        return {
            "summary_statistics": {
                "total_cascades_extracted": len(self.cascade_database),
                "session_types_covered": len(self.session_baselines),
                "average_intensity": np.mean(intensities) if intensities else 0,
                "intensity_std": np.std(intensities) if intensities else 0
            },
            "session_distribution": dict(session_distribution),
            "action_distribution": dict(action_distribution),
            "session_baselines_calculated": len(self.session_baselines),
            "ready_for_htf_forensics": len(self.cascade_database) > 50
        }


def main():
    """Main execution for cascade data extraction."""
    print("🔍 CASCADE DATA EXTRACTOR FOR HTF FORENSICS")
    print("=" * 60)
    
    # Initialize extractor
    extractor = CascadeDataExtractor()
    
    # Extract cascade database
    cascade_database = extractor.extract_cascade_database()
    
    # Identify HTF-influenced cascades
    htf_influenced = extractor.identify_htf_influenced_cascades()
    
    # Save database
    extractor.save_cascade_database()
    
    # Generate and display summary
    summary = extractor.generate_summary_report()
    
    print(f"\n📊 EXTRACTION SUMMARY:")
    print(f"   Total cascades: {summary['summary_statistics']['total_cascades_extracted']}")
    print(f"   Session types: {summary['summary_statistics']['session_types_covered']}")
    print(f"   Average intensity: {summary['summary_statistics']['average_intensity']:.3f}")
    print(f"   HTF-influenced: {len(htf_influenced)}")
    
    print(f"\n🎯 SESSION DISTRIBUTION:")
    for session_type, count in summary['session_distribution'].items():
        print(f"   {session_type}: {count} cascades")
    
    print(f"\n✅ CASCADE DATABASE READY FOR HTF FORENSIC ANALYSIS")
    print(f"   Ready for forensics: {summary['ready_for_htf_forensics']}")
    
    return cascade_database, htf_influenced


if __name__ == "__main__":
    main()