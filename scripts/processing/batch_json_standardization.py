#!/usr/bin/env python3
"""
Batch JSON Standardization Script
Replaces direct JSON operations with standardized load_json_data/save_json_data functions
"""

import os
import re
import sys
from pathlib import Path

def get_files_needing_updates():
    """Get list of Python files that need JSON standardization"""
    
    # Files to skip (already updated or system files)
    skip_files = {
        'src/utils.py',           # Contains the functions
        'batch_json_standardization.py',  # This script
        'test_integrated_hmm_monte_carlo.py',  # Already updated
        'market_state_hmm.py',    # Already updated
        'src/pipeline.py',        # Already updated
        'corrected_grok_predictor.py',  # Already updated
        'ensemble_validator.py',  # Already updated
        'test_tracker_system_without_api.py'  # Already updated
    }
    
    files_to_update = []
    
    # Get all Python files with JSON operations
    result = os.popen("grep -r 'with open.*\\.json' *.py | cut -d: -f1 | sort | uniq").read()
    
    for file_path in result.strip().split('\n'):
        if file_path and file_path not in skip_files:
            if os.path.exists(file_path):
                files_to_update.append(file_path)
    
    return files_to_update

def add_imports_to_file(file_path):
    """Add standardized imports to a Python file"""
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check if imports already exist
        if 'from src.utils import load_json_data' in content:
            return True  # Already has imports
        
        # Find import section
        lines = content.split('\n')
        
        # Find the best place to insert imports (after existing imports)
        insert_index = 0
        for i, line in enumerate(lines):
            if line.startswith('import ') or line.startswith('from '):
                insert_index = i + 1
            elif line.strip() == '' and insert_index > 0:
                insert_index = i + 1
                break
        
        # Add the imports
        new_imports = [
            'import sys',
            'sys.path.append(\'.\')',
            'from src.utils import load_json_data, save_json_data'
        ]
        
        # Insert imports
        for i, new_import in enumerate(new_imports):
            if new_import not in content:
                lines.insert(insert_index + i, new_import)
        
        # Write back to file
        with open(file_path, 'w') as f:
            f.write('\n'.join(lines))
        
        print(f"   ✅ Added imports to {file_path}")
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to add imports to {file_path}: {e}")
        return False

def replace_json_operations(file_path):
    """Replace JSON operations in a file"""
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        replacements_made = 0
        
        # Pattern 1: with open(file, 'r') as f: json.load(f)
        pattern1 = r"with open\(([^,]+),\s*['\"]r['\"].*?\)\s*as\s+\w+:\s*(\w+)\s*=\s*json\.load\(\w+\)"
        matches1 = re.findall(pattern1, content, re.DOTALL)
        
        for match in matches1:
            file_var = match[0].strip()
            var_name = match[1].strip()
            old_pattern = f"with open({file_var}, 'r') as f:\n.*?{var_name} = json.load(f)"
            new_code = f"{var_name} = load_json_data({file_var})"
            content = re.sub(old_pattern, new_code, content, flags=re.DOTALL)
            replacements_made += 1
        
        # Pattern 2: with open(file, 'w') as f: json.dump(data, f, ...)
        pattern2 = r"with open\(([^,]+),\s*['\"]w['\"].*?\)\s*as\s+\w+:\s*json\.dump\(([^,]+).*?\)"
        matches2 = re.findall(pattern2, content, re.DOTALL)
        
        for match in matches2:
            file_var = match[0].strip()
            data_var = match[1].strip()
            old_pattern = f"with open({file_var}, 'w') as f:\n.*?json.dump({data_var}.*?)"
            new_code = f"save_json_data({data_var}, {file_var})"
            content = re.sub(old_pattern, new_code, content, flags=re.DOTALL)
            replacements_made += 1
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            print(f"   ✅ Updated {replacements_made} JSON operations in {file_path}")
            return True
        else:
            print(f"   ➡️ No JSON operations found in {file_path}")
            return True
            
    except Exception as e:
        print(f"   ❌ Failed to update {file_path}: {e}")
        return False

def main():
    """Main standardization process"""
    
    print("🔧 BATCH JSON STANDARDIZATION")
    print("=" * 40)
    
    # Get files that need updates
    files_to_update = get_files_needing_updates()
    
    if not files_to_update:
        print("✅ No files need JSON standardization updates")
        return
    
    print(f"📊 Found {len(files_to_update)} files to update:")
    for file_path in files_to_update[:10]:  # Show first 10
        print(f"   • {file_path}")
    if len(files_to_update) > 10:
        print(f"   ... and {len(files_to_update) - 10} more")
    
    # Process files in batches
    batch_size = 5
    total_success = 0
    total_failed = 0
    
    for i in range(0, len(files_to_update), batch_size):
        batch = files_to_update[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        
        print(f"\\n📦 Processing Batch {batch_num} ({len(batch)} files):")
        
        batch_success = 0
        for file_path in batch:
            print(f"\\n🔄 Processing {file_path}...")
            
            # Add imports first
            imports_ok = add_imports_to_file(file_path)
            
            # Replace JSON operations
            replacements_ok = replace_json_operations(file_path)
            
            if imports_ok and replacements_ok:
                batch_success += 1
                total_success += 1
            else:
                total_failed += 1
        
        print(f"\\n✅ Batch {batch_num} complete: {batch_success}/{len(batch)} successful")
    
    print(f"\\n🎯 STANDARDIZATION COMPLETE:")
    print(f"   ✅ Successfully updated: {total_success} files")
    print(f"   ❌ Failed to update: {total_failed} files")
    print(f"   📊 Success rate: {(total_success/(total_success+total_failed)*100):.1f}%")
    
    if total_success > 0:
        print("\\n🔧 Next steps:")
        print("   1. Test updated files for functionality")
        print("   2. Run integration tests")
        print("   3. Validate backward compatibility")

if __name__ == "__main__":
    main()