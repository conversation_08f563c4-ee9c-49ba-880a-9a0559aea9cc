#!/usr/bin/env python3
"""
HTF Liquidity Persistence Analyzer
Extracts and analyzes persistent liquidity levels across multiple sessions.

Analyzes:
1. Liquidity levels that appear in multiple sessions
2. Price behavior near these levels (bounce/break/consolidate)
3. Strength scores based on frequency and effectiveness
4. Cross-session persistence patterns
"""

import json
import os
import glob
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict, Counter
import numpy as np
from datetime import datetime

@dataclass
class LiquidityLevel:
    """Individual liquidity level data."""
    price: float
    session_id: str
    timestamp: str
    side: str  # 'buy' or 'sell'
    magnitude: Optional[float] = None
    context: Optional[str] = None

@dataclass
class PersistentLevel:
    """Persistent liquidity level across sessions."""
    price: float
    appearances: int
    sessions: List[str]
    behaviors: Dict[str, int]  # bounce, break, consolidate counts
    strength_score: float
    price_variance: float
    session_types: List[str]
    timestamps: List[str]

class HTFLiquidityPersistenceAnalyzer:
    """
    HTF Liquidity Persistence Analyzer
    
    Analyzes liquidity levels across multiple sessions to identify
    persistent levels and their behavioral patterns.
    """
    
    def __init__(self, price_tolerance: float = 2.0):
        """Initialize analyzer with price clustering tolerance."""
        self.price_tolerance = price_tolerance
        self.liquidity_levels = []
        self.persistent_levels = []
        
        print(f"🔍 HTF LIQUIDITY PERSISTENCE ANALYZER: Initialized")
        print(f"   Price tolerance: ±{price_tolerance} points for clustering")
    
    def analyze_all_sessions(self, directory_path: str = "/Users/<USER>/grok-claude-automation") -> Dict[str, Any]:
        """
        Analyze all session files for persistent liquidity levels.
        
        Args:
            directory_path: Path to directory containing session JSON files
            
        Returns:
            Complete analysis with persistent levels and behaviors
        """
        print(f"🔍 LIQUIDITY ANALYSIS: Processing session files from {directory_path}")
        
        # Find all relevant JSON files
        session_files = self._find_session_files(directory_path)
        print(f"   Found {len(session_files)} session files to analyze")
        
        # Extract liquidity data from each session
        for file_path in session_files:
            session_data = self._load_session_file(file_path)
            if session_data:
                self._extract_liquidity_levels(session_data, file_path)
        
        print(f"   Extracted {len(self.liquidity_levels)} total liquidity levels")
        
        # Cluster levels by price proximity
        price_clusters = self._cluster_liquidity_levels()
        print(f"   Identified {len(price_clusters)} price clusters")
        
        # Analyze persistence and behaviors
        self.persistent_levels = self._analyze_persistence_patterns(price_clusters)
        print(f"   Found {len(self.persistent_levels)} persistent levels")
        
        # Generate final analysis
        analysis_result = self._generate_comprehensive_analysis()
        
        print(f"✅ LIQUIDITY ANALYSIS completed")
        print(f"   Persistent levels: {len(self.persistent_levels)}")
        print(f"   Average appearances: {np.mean([p.appearances for p in self.persistent_levels]):.1f}")
        
        return analysis_result
    
    def _find_session_files(self, directory_path: str) -> List[str]:
        """Find all relevant session JSON files."""
        patterns = [
            "*Lvl*.json",
            "*_session_*.json", 
            "*grokEnhanced*.json"
        ]
        
        session_files = []
        for pattern in patterns:
            files = glob.glob(os.path.join(directory_path, pattern))
            session_files.extend(files)
        
        # Remove duplicates and filter
        session_files = list(set(session_files))
        
        # Filter out tracker and state files
        filtered_files = []
        for file_path in session_files:
            filename = os.path.basename(file_path)
            if not any(exclude in filename.lower() for exclude in 
                      ['fvg_state', 'htf_context', 'liquidity_state', 'tracker']):
                filtered_files.append(file_path)
        
        return filtered_files
    
    def _load_session_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Load and validate session JSON file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"⚠️ Could not load {file_path}: {e}")
            return None
    
    def _extract_liquidity_levels(self, session_data: Dict[str, Any], file_path: str):
        """Extract liquidity levels from session data."""
        filename = os.path.basename(file_path)
        session_id = self._extract_session_id(filename)
        
        # Extract from various possible locations in the JSON
        extraction_paths = [
            ['calculations', 'liquidity_analysis', 'untaken_liquidity'],
            ['calculations', 'untaken_liquidity'],
            ['untaken_liquidity'],
            ['liquidity_analysis', 'untaken_liquidity'],
            ['liquidity_analysis', 'levels'],
            ['price_analysis', 'key_levels'],
            ['session_analysis', 'liquidity_levels']
        ]
        
        for path in extraction_paths:
            liquidity_data = self._get_nested_value(session_data, path)
            if liquidity_data:
                self._process_liquidity_data(liquidity_data, session_id, file_path)
                break
        
        # Also extract from price analysis if available
        price_data = session_data.get('price_data', {})
        if 'high' in price_data and 'low' in price_data:
            # Add session high/low as liquidity levels
            self.liquidity_levels.append(LiquidityLevel(
                price=price_data['high'],
                session_id=session_id,
                timestamp='session_high',
                side='sell',
                context='session_extreme'
            ))
            self.liquidity_levels.append(LiquidityLevel(
                price=price_data['low'],
                session_id=session_id,
                timestamp='session_low', 
                side='buy',
                context='session_extreme'
            ))
    
    def _get_nested_value(self, data: Dict[str, Any], path: List[str]) -> Optional[Any]:
        """Get nested value from dictionary using path."""
        current = data
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current
    
    def _process_liquidity_data(self, liquidity_data: Any, session_id: str, file_path: str):
        """Process extracted liquidity data into LiquidityLevel objects."""
        if not liquidity_data:
            return
            
        if isinstance(liquidity_data, list):
            for item in liquidity_data:
                if isinstance(item, dict):
                    self._process_liquidity_item(item, session_id, file_path)
        elif isinstance(liquidity_data, dict):
            self._process_liquidity_item(liquidity_data, session_id, file_path)
    
    def _process_liquidity_item(self, item: Dict[str, Any], session_id: str, file_path: str):
        """Process individual liquidity item."""
        # Extract price/level
        price = None
        for price_key in ['level', 'price', 'value']:
            if price_key in item:
                try:
                    price = float(item[price_key])
                    break
                except (ValueError, TypeError):
                    continue
        
        if price is None:
            return
            
        # Extract side
        side = item.get('side', 'unknown')
        if side not in ['buy', 'sell']:
            # Try to infer from price or other indicators
            side = 'buy' if item.get('type') == 'support' else 'sell'
        
        # Extract other metadata
        timestamp = item.get('timestamp', item.get('time', 'unknown'))
        magnitude = item.get('magnitude', item.get('size', item.get('volume')))
        context = item.get('context', item.get('type', 'liquidity'))
        
        if isinstance(magnitude, (int, float)):
            magnitude = float(magnitude)
        else:
            magnitude = None
            
        level = LiquidityLevel(
            price=price,
            session_id=session_id,
            timestamp=timestamp,
            side=side,
            magnitude=magnitude,
            context=context
        )
        
        self.liquidity_levels.append(level)
    
    def _extract_session_id(self, filename: str) -> str:
        """Extract session ID from filename."""
        if '_' in filename:
            parts = filename.replace('.json', '').split('_')
            if len(parts) >= 2:
                return f"{parts[0]}_{parts[-1]}" if parts[-1].replace('-', '').isdigit() else parts[0]
        return filename.replace('.json', '')
    
    def _cluster_liquidity_levels(self) -> Dict[float, List[LiquidityLevel]]:
        """Cluster liquidity levels by price proximity."""
        if not self.liquidity_levels:
            return {}
            
        # Sort by price
        sorted_levels = sorted(self.liquidity_levels, key=lambda x: x.price)
        
        clusters = {}
        current_cluster_price = None
        
        for level in sorted_levels:
            # Find if this level belongs to an existing cluster
            cluster_found = False
            
            for cluster_price in clusters.keys():
                if abs(level.price - cluster_price) <= self.price_tolerance:
                    clusters[cluster_price].append(level)
                    cluster_found = True
                    break
            
            if not cluster_found:
                # Create new cluster
                clusters[level.price] = [level]
        
        return clusters
    
    def _analyze_persistence_patterns(self, clusters: Dict[float, List[LiquidityLevel]]) -> List[PersistentLevel]:
        """Analyze persistence patterns in clustered levels."""
        persistent_levels = []
        
        for cluster_price, levels in clusters.items():
            if len(levels) < 2:  # Need at least 2 appearances to be persistent
                continue
                
            # Get unique sessions
            sessions = list(set(level.session_id for level in levels))
            if len(sessions) < 2:  # Must appear in multiple sessions
                continue
                
            # Analyze behaviors (simplified)
            behaviors = self._analyze_level_behaviors(levels, cluster_price)
            
            # Calculate strength score
            strength_score = self._calculate_strength_score(levels, behaviors)
            
            # Calculate price variance within cluster
            prices = [level.price for level in levels]
            price_variance = np.std(prices) if len(prices) > 1 else 0.0
            
            # Extract session types and timestamps
            session_types = [self._extract_session_type(level.session_id) for level in levels]
            timestamps = [level.timestamp for level in levels]
            
            persistent_level = PersistentLevel(
                price=cluster_price,
                appearances=len(levels),
                sessions=sessions,
                behaviors=behaviors,
                strength_score=strength_score,
                price_variance=price_variance,
                session_types=session_types,
                timestamps=timestamps
            )
            
            persistent_levels.append(persistent_level)
        
        # Sort by strength score descending
        persistent_levels.sort(key=lambda x: x.strength_score, reverse=True)
        
        return persistent_levels
    
    def _analyze_level_behaviors(self, levels: List[LiquidityLevel], cluster_price: float) -> Dict[str, int]:
        """Analyze price behaviors at liquidity levels (simplified)."""
        behaviors = defaultdict(int)
        
        # This is a simplified analysis
        # In a real implementation, you would analyze actual price action
        for level in levels:
            if level.context:
                if 'break' in level.context.lower():
                    behaviors['breaks'] += 1
                elif 'bounce' in level.context.lower() or 'support' in level.context.lower():
                    behaviors['bounces'] += 1
                elif 'consolidat' in level.context.lower():
                    behaviors['consolidations'] += 1
                else:
                    # Default behavior based on level characteristics
                    if level.magnitude and level.magnitude > 50:
                        behaviors['bounces'] += 1
                    else:
                        behaviors['consolidations'] += 1
            else:
                # Default to consolidation if no context
                behaviors['consolidations'] += 1
        
        return dict(behaviors)
    
    def _calculate_strength_score(self, levels: List[LiquidityLevel], behaviors: Dict[str, int]) -> float:
        """Calculate strength score for persistent level."""
        # Base score from number of appearances
        appearances_score = min(len(levels) / 10.0, 1.0)  # Max 1.0 for 10+ appearances
        
        # Unique sessions bonus
        unique_sessions = len(set(level.session_id for level in levels))
        session_diversity_score = min(unique_sessions / 5.0, 1.0)  # Max 1.0 for 5+ sessions
        
        # Behavior effectiveness (bounces are stronger than breaks)
        total_behaviors = sum(behaviors.values())
        if total_behaviors > 0:
            bounce_ratio = behaviors.get('bounces', 0) / total_behaviors
            behavior_score = bounce_ratio * 0.8 + 0.2  # 0.2-1.0 range
        else:
            behavior_score = 0.5
        
        # Magnitude bonus (if available)
        magnitudes = [level.magnitude for level in levels if level.magnitude]
        if magnitudes:
            avg_magnitude = np.mean(magnitudes)
            magnitude_score = min(avg_magnitude / 100.0, 1.0)
        else:
            magnitude_score = 0.5
        
        # Weighted combination
        strength_score = (
            0.4 * appearances_score +
            0.3 * session_diversity_score +
            0.2 * behavior_score +
            0.1 * magnitude_score
        )
        
        return round(strength_score, 3)
    
    def _extract_session_type(self, session_id: str) -> str:
        """Extract session type from session ID."""
        session_id_upper = session_id.upper()
        
        session_types = {
            'ASIA': 'ASIA',
            'LONDON': 'LONDON', 
            'LUNCH': 'LUNCH',
            'NYAM': 'NY_AM',
            'NYPM': 'NY_PM',
            'PREMARKET': 'PREMARKET',
            'MIDNIGHT': 'MIDNIGHT',
            'PM': 'NY_PM'
        }
        
        for key, session_type in session_types.items():
            if key in session_id_upper:
                return session_type
        
        return 'UNKNOWN'
    
    def _generate_comprehensive_analysis(self) -> Dict[str, Any]:
        """Generate comprehensive analysis results."""
        # Sort persistent levels by strength score
        top_levels = sorted(self.persistent_levels, key=lambda x: x.strength_score, reverse=True)
        
        # Calculate summary statistics
        total_levels = len(self.persistent_levels)
        if total_levels > 0:
            avg_appearances = np.mean([p.appearances for p in self.persistent_levels])
            avg_strength = np.mean([p.strength_score for p in self.persistent_levels])
            max_appearances = max(p.appearances for p in self.persistent_levels)
        else:
            avg_appearances = avg_strength = max_appearances = 0
        
        # Session type distribution
        all_session_types = []
        for level in self.persistent_levels:
            all_session_types.extend(level.session_types)
        session_type_distribution = dict(Counter(all_session_types))
        
        # Generate final result
        result = {
            "analysis_metadata": {
                "analysis_timestamp": datetime.now().isoformat(),
                "total_sessions_analyzed": len(set(level.session_id for level in self.liquidity_levels)),
                "total_liquidity_levels_extracted": len(self.liquidity_levels),
                "persistent_levels_identified": total_levels,
                "price_tolerance_used": self.price_tolerance
            },
            "summary_statistics": {
                "average_appearances_per_level": round(avg_appearances, 1),
                "average_strength_score": round(avg_strength, 3),
                "maximum_appearances": max_appearances,
                "session_type_distribution": session_type_distribution
            },
            "persistent_levels": []
        }
        
        # Add detailed persistent levels
        for level in top_levels:
            result["persistent_levels"].append({
                "price": level.price,
                "appearances": level.appearances,
                "sessions": level.sessions,
                "behaviors": level.behaviors,
                "strength_score": level.strength_score,
                "price_variance": round(level.price_variance, 2),
                "session_types": list(set(level.session_types)),
                "representative_timestamps": level.timestamps[:5]  # First 5 timestamps
            })
        
        return result
    
    def save_analysis(self, analysis_result: Dict[str, Any], 
                     output_path: str = "/Users/<USER>/grok-claude-automation/htf_liquidity_persistence.json"):
        """Save analysis results to JSON file."""
        try:
            with open(output_path, 'w') as f:
                json.dump(analysis_result, f, indent=2)
            print(f"💾 Analysis saved to: {output_path}")
            return True
        except Exception as e:
            print(f"❌ Failed to save analysis: {e}")
            return False


def main():
    """Main execution function."""
    print("🔍 HTF LIQUIDITY PERSISTENCE ANALYZER")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = HTFLiquidityPersistenceAnalyzer(price_tolerance=2.0)
    
    # Run comprehensive analysis
    analysis_result = analyzer.analyze_all_sessions()
    
    # Save results
    analyzer.save_analysis(analysis_result)
    
    # Display summary
    print(f"\n📊 ANALYSIS SUMMARY:")
    metadata = analysis_result["analysis_metadata"]
    summary = analysis_result["summary_statistics"]
    
    print(f"   Sessions Analyzed: {metadata['total_sessions_analyzed']}")
    print(f"   Total Liquidity Levels: {metadata['total_liquidity_levels_extracted']}")
    print(f"   Persistent Levels Found: {metadata['persistent_levels_identified']}")
    print(f"   Average Appearances: {summary['average_appearances_per_level']}")
    print(f"   Average Strength Score: {summary['average_strength_score']}")
    
    # Show top 5 persistent levels
    persistent_levels = analysis_result["persistent_levels"]
    if persistent_levels:
        print(f"\n🏆 TOP 5 PERSISTENT LEVELS:")
        for i, level in enumerate(persistent_levels[:5], 1):
            print(f"   {i}. Price: {level['price']:.1f} | "
                  f"Appearances: {level['appearances']} | "
                  f"Strength: {level['strength_score']:.3f} | "
                  f"Sessions: {len(level['sessions'])}")
    
    print(f"\n✅ HTF Liquidity Persistence Analysis completed!")
    return analysis_result


if __name__ == "__main__":
    main()