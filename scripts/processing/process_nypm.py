#!/usr/bin/env python3

import json
from src.pipeline import Grok<PERSON>ipeline

def main():
    # Load NY PM session data
    with open('NYPM_Lvl-1_2025_07_25.json', 'r') as f:
        nypm_session_data = json.load(f)

    # Create pipeline and process
    pipeline = GrokPipeline()
    print('Starting NY PM session processing through A→B→C→D pipeline...')

    # Process the session with enhanced pipeline
    result = pipeline.process_session(nypm_session_data)

    # Save enhanced NY PM session data
    with open('NYPM_session_enhanced_2025_07_25.json', 'w') as f:
        json.dump(result, f, indent=2)

    print('✅ NY PM session A→B→C→D pipeline processing completed successfully')
    print('Enhanced data saved to: NYPM_session_enhanced_2025_07_25.json')

    # Quick validation check
    if 'grok_enhanced_calculations' in result:
        unit_b = result['grok_enhanced_calculations'].get('unit_b_energy_structure', {})
        unit_c = result['grok_enhanced_calculations'].get('unit_c_advanced_dynamics', {})
        
        energy_rate = unit_b.get('energy_accumulation', {}).get('energy_rate', 1.0)
        momentum_strength = unit_c.get('temporal_momentum', {}).get('momentum_strength', 0.0)
        
        print('Energy Rate:', energy_rate)
        print('Momentum Strength:', momentum_strength)
        print('Real calculations detected:', energy_rate != 1.0 and momentum_strength > 0)

if __name__ == "__main__":
    main()