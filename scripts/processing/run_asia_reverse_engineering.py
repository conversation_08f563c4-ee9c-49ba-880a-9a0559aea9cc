#!/usr/bin/env python3
"""
Run Reverse Engineering Analysis on Asia Session
Direct execution for Asia session from July 22nd with tracker files
"""

import json
import sys
import os
from typing import Dict

# Add src path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'experimental'))

from reverse_engineer import reverse_engineer_failed_prediction
from monte_carlo_adapter import run_monte_carlo_from_session
from file_manager import discover_session

def run_asia_session_analysis():
    """Run complete reverse engineering analysis on Asia session"""
    
    print("🌏 ASIA SESSION REVERSE ENGINEERING ANALYSIS")
    print("=" * 50)
    print("Running complete workflow with tracker files from July 22nd\n")
    
    session = "asia"
    date = "2025_07_22"
    error_threshold = 25.0
    
    try:
        # Step 1: STRICT FILE DISCOVERY AND VALIDATION
        print("1️⃣ Discovering and validating Asia session files...")
        session_files = discover_session(session, date)
        
        print(f"   ✅ All files discovered and validated:")
        print(f"      Session: {os.path.basename(session_files.session_file)}")
        print(f"      HTF Context: {os.path.basename(session_files.htf_context_file)}")
        print(f"      FVG State: {os.path.basename(session_files.fvg_state_file)}")
        print(f"      Liquidity State: {os.path.basename(session_files.liquidity_state_file)}")
        
    except FileNotFoundError as e:
        print(f"❌ CRITICAL ERROR: File validation failed")
        print(f"   Mathematical integrity requires all tracker files")
        print(f"   {str(e)}")
        
        # Try to find what files exist
        print(f"\n🔍 Checking what Asia files exist...")
        potential_files = [
            "asia_grokEnhanced_2025_07_22.json",
            "HTF_Context_Asia_grokEnhanced_2025_07_22.json",
            "FVG_State_Asia_grokEnhanced_2025_07_22.json", 
            "Liquidity_State_Asia_grokEnhanced_2025_07_22.json"
        ]
        
        for file_path in potential_files:
            if os.path.exists(file_path):
                print(f"   ✅ Found: {file_path}")
            else:
                print(f"   ❌ Missing: {file_path}")
        
        return None
    
    # Step 2: Run Monte Carlo prediction with complete tracker context
    print(f"\n2️⃣ Running Monte Carlo prediction with tracker context...")
    monte_carlo_results = run_monte_carlo_from_session(
        session_files.session_file, 
        n_simulations=1000, 
        duration_minutes=180
    )
    
    # Step 3: Load actual session data
    print(f"3️⃣ Loading actual Asia session data...")
    with open(session_files.session_file, 'r') as f:
        actual_session = json.load(f)
    
    # Step 4: Calculate prediction accuracy
    print(f"4️⃣ Calculating prediction accuracy...")
    
    # Extract predicted vs actual values
    prediction_bands = monte_carlo_results["monte_carlo_results"]["prediction_bands"]
    predicted_price = prediction_bands["final_price_percentiles"]["50th"]
    
    # Get actual price from session data
    price_data = actual_session.get("price_data", {})
    if "original_session_data" in actual_session:
        price_data = actual_session["original_session_data"]["price_data"]
    
    actual_price = price_data.get("close", 0)
    prediction_error = abs(predicted_price - actual_price) if actual_price else 0
    error_percentage = (prediction_error / actual_price * 100) if actual_price else 0
    
    print(f"   📊 Asia Session Results:")
    print(f"      Predicted Price: {predicted_price:.2f}")
    print(f"      Actual Price: {actual_price:.2f}")
    print(f"      Prediction Error: {prediction_error:.2f} points ({error_percentage:.3f}%)")
    print(f"      Session Character: {price_data.get('session_character', 'unknown')}")
    
    # Step 5: Trigger reverse engineering if needed
    if prediction_error > error_threshold:
        print(f"\n🚨 Prediction error ({prediction_error:.2f}) exceeds threshold ({error_threshold})")
        print(f"5️⃣ Triggering reverse engineering analysis...")
        
        # Extract original parameters
        original_parameters = monte_carlo_results.get("parameter_extraction", {}).get("extracted_parameters", {})
        
        # Run reverse engineering
        print(f"   🔍 Analyzing prediction failure...")
        reverse_analysis = reverse_engineer_failed_prediction(
            monte_carlo_results,
            actual_session, 
            original_parameters
        )
        
        # Display reverse engineering results
        print(f"   ✅ Reverse Engineering Analysis Complete:")
        failure_analysis = reverse_analysis["failure_analysis"]
        print(f"      Failure Type: {failure_analysis['failure_type']}")
        print(f"      Session Character: {failure_analysis['session_character']}")
        print(f"      Failure Magnitude: {failure_analysis['failure_magnitude']:.2f}")
        print(f"      Divergence Points: {len(failure_analysis['divergence_points'])}")
        print(f"      State Transitions: {len(failure_analysis['state_transitions'])}")
        print(f"      Broken Chains: {len(failure_analysis['broken_chains'])}")
        
        alternative_formulas = reverse_analysis["alternative_formulas"]
        print(f"      Alternative Formulas Discovered: {len(alternative_formulas)}")
        
        # Display discovered formulas
        if alternative_formulas:
            print(f"   🔧 Top Alternative Formulas:")
            for i, formula in enumerate(alternative_formulas[:3]):
                print(f"      {i+1}. {formula['formula'][:60]}...")
                print(f"         Confidence: {formula['confidence']:.2f}")
                print(f"         Failure Type: {formula['failure_type']}")
                print(f"         Session Character: {formula['session_character']}")
        
        # Save complete results
        output_file = f"asia_reverse_engineering_analysis_{date}.json"
        complete_results = {
            "session": session,
            "date": date,
            "monte_carlo_results": monte_carlo_results,
            "reverse_engineering_analysis": reverse_analysis,
            "prediction_accuracy": {
                "predicted_price": predicted_price,
                "actual_price": actual_price,
                "error_magnitude": prediction_error,
                "error_percentage": error_percentage,
                "reverse_engineering_triggered": True
            },
            "session_files_used": {
                "session_file": session_files.session_file,
                "htf_context_file": session_files.htf_context_file,
                "fvg_state_file": session_files.fvg_state_file,
                "liquidity_state_file": session_files.liquidity_state_file
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(complete_results, f, indent=2, default=str)
        
        print(f"\n💾 Complete analysis saved to: {output_file}")
        
        return complete_results
        
    else:
        print(f"\n✅ Prediction within acceptable range ({prediction_error:.2f} ≤ {error_threshold})")
        print(f"5️⃣ Reverse engineering not needed")
        
        # Still save basic results
        basic_results = {
            "session": session,
            "date": date,
            "monte_carlo_results": monte_carlo_results,
            "prediction_accuracy": {
                "predicted_price": predicted_price,
                "actual_price": actual_price,
                "error_magnitude": prediction_error,
                "error_percentage": error_percentage,
                "reverse_engineering_triggered": False
            }
        }
        
        return basic_results

def display_final_summary(results):
    """Display final summary of Asia session analysis"""
    
    if not results:
        print(f"\n❌ ANALYSIS FAILED")
        print("   Could not complete Asia session analysis")
        return
    
    print(f"\n🎯 ASIA SESSION ANALYSIS SUMMARY")
    print("=" * 35)
    
    accuracy = results["prediction_accuracy"]
    print(f"Session: {results['session'].title()} ({results['date']})")
    print(f"Prediction Error: {accuracy['error_magnitude']:.2f} points ({accuracy['error_percentage']:.3f}%)")
    print(f"Reverse Engineering: {'✅ TRIGGERED' if accuracy['reverse_engineering_triggered'] else '❌ NOT NEEDED'}")
    
    if accuracy['reverse_engineering_triggered']:
        reverse_analysis = results["reverse_engineering_analysis"]
        failure_analysis = reverse_analysis["failure_analysis"]
        
        print(f"\n🔍 Failure Analysis:")
        print(f"   Type: {failure_analysis['failure_type']}")
        print(f"   Character: {failure_analysis['session_character']}")
        print(f"   Magnitude: {failure_analysis['failure_magnitude']:.2f}")
        
        formulas = reverse_analysis["alternative_formulas"]
        print(f"\n🔧 Mathematical Discoveries:")
        print(f"   Alternative Formulas: {len(formulas)}")
        
        high_confidence = [f for f in formulas if f.get('confidence', 0) > 0.8]
        print(f"   High Confidence (>0.8): {len(high_confidence)}")
        
        if high_confidence:
            print(f"   🎯 Ready for Implementation:")
            for formula in high_confidence[:2]:
                print(f"      • {formula['formula'][:50]}...")
                print(f"        Confidence: {formula['confidence']:.2f}")
    
    print(f"\n🚀 ASIA SESSION ANALYSIS COMPLETE")
    print("   Mathematical relationship discovery workflow executed")
    print("   System ready for continuous improvement")

if __name__ == "__main__":
    print("🌏 Starting Asia Session Reverse Engineering Analysis...")
    
    # Run the analysis
    results = run_asia_session_analysis()
    
    # Display summary
    display_final_summary(results)
    
    print(f"\n📋 Next Steps:")
    if results and results["prediction_accuracy"]["reverse_engineering_triggered"]:
        print("1. Review discovered alternative formulas")
        print("2. Validate high-confidence relationships")
        print("3. Implement approved mathematical corrections")
        print("4. Monitor improved prediction accuracy")
    else:
        print("1. Asia predictions within acceptable range")
        print("2. System continues monitoring for failures")
        print("3. Ready to analyze other sessions if needed")
    
    print(f"\n🔬 Reverse engineering system operational and ready for continuous learning.")