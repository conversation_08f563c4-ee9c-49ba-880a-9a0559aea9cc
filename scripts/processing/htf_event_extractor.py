#!/usr/bin/env python3
"""
HTF Event Extractor - Temporal Persistence Pattern Detection
Analyzes sequences of HTF_Context files to identify weekly liquidity cycles
Based on mathematical framework from Opus and Grok specifications
"""

import json
import numpy as np
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import glob

try:
    import pandas as pd
except ImportError:
    print("Warning: pandas not available, using basic data structures")
    pd = None

class HTFEventDetector:
    """
    HTF Event Detector using Multi-Pass Analysis
    Implements the Markov process with memory for HTF event emergence
    """
    
    def __init__(self):
        self.structure_memory = {}  # Level → appearance history
        self.influence_evolution = []  # Time series of htf_influence_factor
        self.session_sequence = []  # Chronological session data
        
        # HTF Parameters (from Opus specifications)
        self.persistence_threshold = 5  # Appears in 5+ sessions = weekly persistence
        self.influence_change_threshold = 0.1  # Significant htf_influence_factor change
        
    def load_session_files(self, data_dir: str = "data") -> List[Dict]:
        """
        Load and sort all session files chronologically
        Combines both grokEnhanced and HTF_Tracker files
        """
        session_files = []
        
        # Load grokEnhanced files
        grok_enhanced_pattern = f"{data_dir}/enhanced/grok_enhanced/*_grokEnhanced_*.json"
        grok_files = glob.glob(grok_enhanced_pattern)
        
        # Load HTF_Tracker files  
        htf_tracker_pattern = f"{data_dir}/trackers/htf/HTF_Tracker_*.json"
        htf_files = glob.glob(htf_tracker_pattern)
        
        # Load HTF_Context files from root directory
        htf_context_files = glob.glob("HTF_Context_*.json")
        
        all_files = grok_files + htf_files + htf_context_files
        
        for file_path in all_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    
                # Extract date from filename
                filename = Path(file_path).name
                date_str = self._extract_date_from_filename(filename)
                
                if date_str:
                    data['file_path'] = file_path
                    data['session_date'] = date_str
                    data['filename'] = filename
                    session_files.append(data)
                    
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"Error loading {file_path}: {e}")
                continue
        
        # Sort chronologically
        session_files.sort(key=lambda x: x['session_date'])
        self.session_sequence = session_files
        
        print(f"Loaded {len(session_files)} session files")
        return session_files
    
    def _extract_date_from_filename(self, filename: str) -> Optional[str]:
        """Extract date string from various filename formats"""
        import re
        
        # Pattern for YYYY_MM_DD or YYYY-MM-DD
        date_pattern = r'(\d{4}[-_]\d{2}[-_]\d{2})'
        match = re.search(date_pattern, filename)
        
        if match:
            date_str = match.group(1).replace('_', '-')
            return date_str
        
        return None
    
    def detect_events(self) -> Dict:
        """
        Main detection pipeline - Multi-Pass Analysis
        Returns comprehensive HTF event analysis
        """
        if not self.session_sequence:
            self.load_session_files()
        
        # Pass 1: Build structure persistence map
        print("Pass 1: Analyzing structure persistence patterns...")
        structure_patterns = self._analyze_structure_sequences()
        
        # Pass 2: Detect influence factor anomalies
        print("Pass 2: Detecting htf_influence_factor inflections...")
        influence_events = self._find_influence_inflections()
        
        # Pass 3: Correlate with liquidity patterns
        print("Pass 3: Mapping liquidity evolution...")
        liquidity_cascades = self._map_liquidity_evolution()
        
        # Pass 4: Synthesize HTF events
        print("Pass 4: Synthesizing HTF events...")
        htf_events = self._synthesize_htf_events(
            structure_patterns,
            influence_events, 
            liquidity_cascades
        )
        
        return {
            'htf_events': htf_events,
            'structure_patterns': structure_patterns,
            'influence_events': influence_events,
            'liquidity_cascades': liquidity_cascades,
            'analysis_metadata': {
                'sessions_analyzed': len(self.session_sequence),
                'analysis_date': datetime.now().isoformat(),
                'persistence_threshold': self.persistence_threshold
            }
        }
    
    def _analyze_structure_sequences(self) -> Dict:
        """
        Analyze structure persistence across sessions
        O(n*m) where n=sessions, m=structures per session
        """
        persistence_scores = defaultdict(lambda: {
            'count': 0, 
            'sessions': [], 
            'levels': [],
            'strengths': [],
            'types': []
        })
        
        for session in self.session_sequence:
            session_date = session['session_date']
            
            # Extract active_structures from various file formats
            active_structures = self._extract_active_structures(session)
            
            for structure in active_structures:
                level = structure.get('level', 0)
                structure_type = structure.get('structure_type', 'unknown')
                strength = structure.get('strength', 0)
                
                # Create key for grouping similar levels (within 5 points)
                level_key = round(level / 5) * 5  # Group to nearest 5 points
                key = (level_key, structure_type)
                
                persistence_scores[key]['count'] += 1
                persistence_scores[key]['sessions'].append(session_date)
                persistence_scores[key]['levels'].append(level)
                persistence_scores[key]['strengths'].append(strength)
                persistence_scores[key]['types'].append(structure_type)
        
        # Filter for weekly+ persistence (appears in 5+ sessions)
        weekly_persistent = {}
        for k, v in persistence_scores.items():
            if v['count'] >= self.persistence_threshold:
                # Convert tuple key to string for JSON serialization
                key_str = f"{k[0]}_{k[1]}"  # level_structureType
                weekly_persistent[key_str] = v
        
        # Calculate persistence metrics
        for key, data in weekly_persistent.items():
            sessions = data['sessions']
            levels = data['levels']
            
            # Calculate persistence score σ(s) = Σ_sessions I(level_s appears) / total_sessions
            persistence_ratio = len(sessions) / len(self.session_sequence)
            
            # Calculate level stability (standard deviation)
            level_stability = np.std(levels) if len(levels) > 1 else 0
            
            # Calculate average strength
            avg_strength = np.mean(data['strengths']) if data['strengths'] else 0
            
            data['persistence_ratio'] = persistence_ratio
            data['level_stability'] = level_stability 
            data['average_strength'] = avg_strength
            data['avg_level'] = np.mean(levels) if levels else key[0]
        
        print(f"Found {len(weekly_persistent)} weekly persistent structures")
        return dict(weekly_persistent)
    
    def _extract_active_structures(self, session: Dict) -> List[Dict]:
        """Extract active_structures from various session file formats"""
        active_structures = []
        
        # Try different possible locations for active_structures
        if 'active_structures' in session:
            active_structures = session['active_structures']
        elif 'htf_context' in session and 'active_structures' in session['htf_context']:
            active_structures = session['htf_context']['active_structures']
        elif 'grok_enhanced_calculations' in session:
            # Try to extract from enhanced calculations
            enhanced = session['grok_enhanced_calculations']
            for unit_key in ['unit_a_foundation', 'unit_b_energy_structure', 
                           'unit_c_advanced_dynamics', 'unit_d_integration_validation']:
                if unit_key in enhanced and 'active_structures' in enhanced[unit_key]:
                    active_structures.extend(enhanced[unit_key]['active_structures'])
        
        # Ensure all structures have required fields
        normalized_structures = []
        for struct in active_structures:
            if isinstance(struct, dict) and 'level' in struct:
                normalized_structures.append({
                    'level': float(struct.get('level', 0)),
                    'structure_type': struct.get('structure_type', 'unknown'),
                    'strength': float(struct.get('strength', 0.5)),
                    'recency_factor': float(struct.get('recency_factor', 1.0))
                })
        
        return normalized_structures
    
    def _find_influence_inflections(self) -> List[Dict]:
        """
        Detect regime changes via htf_influence_factor derivatives
        Identifies trap→sweep→cascade transitions
        """
        influence_series = []
        session_dates = []
        
        # Build time series of htf_influence_factor
        for session in self.session_sequence:
            influence_factor = self._extract_influence_factor(session)
            if influence_factor is not None:
                influence_series.append(influence_factor)
                session_dates.append(session['session_date'])
        
        if len(influence_series) < 3:
            return []
        
        # Calculate derivatives without pandas
        d1_series = []  # First derivative
        d2_series = []  # Second derivative
        
        # Calculate first derivative
        for i in range(1, len(influence_series)):
            d1_series.append(influence_series[i] - influence_series[i-1])
        
        # Calculate second derivative
        for i in range(1, len(d1_series)):
            d2_series.append(d1_series[i] - d1_series[i-1])
        
        # Find inflection points where second derivative changes sign
        events = []
        for i in range(1, len(d2_series)):
            d2_prev = d2_series[i-1]
            d2_curr = d2_series[i]
            d1_curr = d1_series[i] if i < len(d1_series) else 0
            
            # Calculate influence change
            idx_in_series = i + 1  # Adjust for derivative offset
            if idx_in_series < len(influence_series):
                influence_change = abs(influence_series[idx_in_series] - influence_series[idx_in_series-1])
            else:
                continue
            
            # Check for significant inflection points
            if (d2_prev != 0 and d2_curr != 0 and 
                np.sign(d2_prev) != np.sign(d2_curr) and 
                influence_change > self.influence_change_threshold):
                
                # Classify event type based on derivative direction
                if d1_curr < 0:
                    event_type = 'trap'  # Decreasing influence = trap formation
                elif d1_curr > 0:
                    event_type = 'sweep'  # Increasing influence = sweep event
                else:
                    event_type = 'neutral'
                
                # Get the corresponding date
                date_idx = i + 1  # Account for derivative offset
                if date_idx < len(session_dates):
                    events.append({
                        'date': session_dates[date_idx],
                        'type': event_type,
                        'magnitude': abs(d1_curr),
                        'influence_level': influence_series[idx_in_series],
                        'influence_change': influence_change
                    })
        
        print(f"Found {len(events)} influence inflection events")
        return events
    
    def _extract_influence_factor(self, session: Dict) -> Optional[float]:
        """Extract htf_influence_factor from various session formats"""
        
        # Try different possible locations
        if 'htf_influence_factor' in session:
            return float(session['htf_influence_factor'])
        elif 'htf_distance_influence' in session:
            return float(session['htf_distance_influence'])
        elif 'grok_enhanced_calculations' in session:
            enhanced = session['grok_enhanced_calculations']
            for unit_key in enhanced:
                if isinstance(enhanced[unit_key], dict):
                    if 'htf_influence_factor' in enhanced[unit_key]:
                        return float(enhanced[unit_key]['htf_influence_factor'])
                    elif 'htf_distance_influence' in enhanced[unit_key]:
                        return float(enhanced[unit_key]['htf_distance_influence'])
        
        return None
    
    def _map_liquidity_evolution(self) -> List[Dict]:
        """
        Map liquidity evolution patterns
        Identify cascade events >50 points (from micro_timing_analysis)
        """
        liquidity_events = []
        
        for session in self.session_sequence:
            session_date = session['session_date']
            
            # Extract cascade events from micro_timing_analysis
            cascades = self._extract_cascade_events(session)
            
            for cascade in cascades:
                if cascade['magnitude'] > 50:  # Opus specification: >50 points
                    liquidity_events.append({
                        'date': session_date,
                        'type': 'cascade',
                        'magnitude': cascade['magnitude'],
                        'direction': cascade.get('direction', 'unknown'),
                        'session_file': session['filename']
                    })
        
        print(f"Found {len(liquidity_events)} major liquidity cascade events")
        return liquidity_events
    
    def _extract_cascade_events(self, session: Dict) -> List[Dict]:
        """Extract cascade events from micro_timing_analysis"""
        cascades = []
        
        # Try different locations for cascade data
        if 'micro_timing_analysis' in session:
            micro_timing = session['micro_timing_analysis']
            if 'cascade_events' in micro_timing:
                for event in micro_timing['cascade_events']:
                    cascades.append({
                        'magnitude': event.get('magnitude', 0),
                        'direction': event.get('direction', 'unknown')
                    })
        
        # Also check price_data for large movements
        if 'price_data' in session:
            price_data = session['price_data']
            high = price_data.get('high', 0)
            low = price_data.get('low', 0)
            price_range = high - low
            
            if price_range > 50:  # Large session range indicates cascade
                cascades.append({
                    'magnitude': price_range,
                    'direction': 'range_expansion'
                })
        
        return cascades
    
    def _synthesize_htf_events(self, structure_patterns: Dict, 
                              influence_events: List[Dict], 
                              liquidity_cascades: List[Dict]) -> List[Dict]:
        """
        Synthesize final HTF events by correlating all analysis passes
        Implements the mathematical framework: P(Event_t | Structure_history)
        """
        htf_events = []
        
        # Group events by date for correlation
        events_by_date = defaultdict(lambda: {
            'structures': [],
            'influence_events': [],
            'cascades': []
        })
        
        # Add structure persistence events
        for key_str, data in structure_patterns.items():
            # Parse key string back to components
            level_key, struct_type = key_str.split('_', 1)
            level_key = float(level_key)
            
            for session_date in data['sessions']:
                events_by_date[session_date]['structures'].append({
                    'level': data['avg_level'],
                    'type': struct_type,
                    'persistence_ratio': data['persistence_ratio'],
                    'strength': data['average_strength']
                })
        
        # Add influence events
        for event in influence_events:
            events_by_date[event['date']]['influence_events'].append(event)
        
        # Add cascade events
        for event in liquidity_cascades:
            events_by_date[event['date']]['cascades'].append(event)
        
        # Synthesize HTF events
        for date, day_events in events_by_date.items():
            # Calculate HTF event probability
            structure_score = len(day_events['structures']) * 0.3
            influence_score = len(day_events['influence_events']) * 0.5
            cascade_score = len(day_events['cascades']) * 0.7
            
            total_score = structure_score + influence_score + cascade_score
            
            if total_score > 1.0:  # Threshold for HTF event
                # Determine event type
                if day_events['cascades']:
                    event_type = 'cascade'
                    magnitude = max([c['magnitude'] for c in day_events['cascades']])
                elif day_events['influence_events']:
                    event_type = day_events['influence_events'][0]['type']
                    magnitude = day_events['influence_events'][0]['magnitude']
                else:
                    event_type = 'structure_formation'
                    magnitude = total_score
                
                htf_events.append({
                    'date': date,
                    'type': event_type,
                    'magnitude': magnitude,
                    'total_score': total_score,
                    'components': {
                        'structures': len(day_events['structures']),
                        'influence_events': len(day_events['influence_events']),
                        'cascades': len(day_events['cascades'])
                    },
                    'confidence': min(total_score / 3.0, 1.0)  # Normalize confidence
                })
        
        # Sort by date
        htf_events.sort(key=lambda x: x['date'])
        
        print(f"Synthesized {len(htf_events)} HTF events")
        return htf_events

def main():
    """Test the HTF Event Detector"""
    detector = HTFEventDetector()
    
    print("🔍 HTF Event Detector - Temporal Persistence Analysis")
    print("=" * 60)
    
    # Run detection
    results = detector.detect_events()
    
    # Display results
    print(f"\n📊 Analysis Results:")
    print(f"Sessions analyzed: {results['analysis_metadata']['sessions_analyzed']}")
    print(f"Weekly persistent structures: {len(results['structure_patterns'])}")
    print(f"Influence inflection events: {len(results['influence_events'])}")
    print(f"Liquidity cascade events: {len(results['liquidity_cascades'])}")
    print(f"Synthesized HTF events: {len(results['htf_events'])}")
    
    # Show top HTF events
    print(f"\n🎯 Top HTF Events:")
    top_events = sorted(results['htf_events'], key=lambda x: x['total_score'], reverse=True)[:5]
    
    for i, event in enumerate(top_events, 1):
        print(f"{i}. {event['date']}: {event['type']} (magnitude: {event['magnitude']:.2f}, confidence: {event['confidence']:.2f})")
    
    # Save results
    output_file = f"htf_historical_database_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    return results

if __name__ == "__main__":
    main()