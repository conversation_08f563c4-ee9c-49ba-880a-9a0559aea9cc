#!/usr/bin/env python3
"""
Process ALL July 31st Sessions - Complete HTF Intelligence & HAWKS Predictions

This script processes all 5 July 31st Level-1 sessions (PREASIA, ASIA, LONDON, 
MIDNIGHT, PREMARKET) through the complete HTF Intelligence system, handles timezone
issues for Asia, and generates comprehensive HAWKS predictions for remaining sessions.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from htf_session_intelligence_parser import HTFSessionIntelligenceParser
from htf_intelligence_integration import HTFIntelligenceIntegration
from htf_master_controller_enhanced import HTFMasterControllerEnhanced


def extract_session_htf_events(session_file_path: str, session_name: str):
    """Extract HTF events from any July 31st session with timezone awareness."""
    print(f"🔍 Extracting HTF Events from {session_name}...")
    
    with open(session_file_path, 'r') as f:
        session_data = json.load(f)
    
    metadata = session_data.get("session_metadata", {})
    movements = session_data.get("price_movements", [])
    
    # Handle timezone issue - Asia might show as 2025-07-30 but should be 2025-07-31
    actual_date = "2025-07-31"
    if "ASIA" in session_name.upper() and metadata.get("date") == "2025-07-30":
        print(f"  ⚠️ Timezone correction: {session_name} date corrected from {metadata.get('date')} to {actual_date}")
        metadata["date"] = actual_date
    
    htf_events = []
    
    for movement in movements:
        context = movement.get("context", "")
        timestamp = movement.get("timestamp", "")
        price = movement.get("price", 0.0)
        action = movement.get("action", "")
        
        # Enhanced pattern matching for various HTF events
        htf_event = None
        
        # Previous day's session references
        if "Previous day's AM session high taken out" in context:
            htf_event = {
                "event_type": "level_takeout",
                "level": price,
                "htf_significance": "nyam_high_2025-07-30_violated",
                "reference_date": "2025-07-30",
                "violated_on": actual_date,
                "origin_session": "NY_AM_2025-07-30",
                "taken_by_session": f"{session_name}_{actual_date}",
                "confidence": 0.98
            }
            
        elif "Previous day's AM session low taken out" in context:
            htf_event = {
                "event_type": "level_takeout", 
                "level": price,
                "htf_significance": "nyam_low_2025-07-30_violated",
                "reference_date": "2025-07-30",
                "violated_on": actual_date,
                "origin_session": "NY_AM_2025-07-30",
                "taken_by_session": f"{session_name}_{actual_date}",
                "confidence": 0.98
            }
            
        elif "Previous day's PM session high taken out" in context:
            htf_event = {
                "event_type": "level_takeout",
                "level": price,
                "htf_significance": "nypm_high_2025-07-30_violated",
                "reference_date": "2025-07-30", 
                "violated_on": actual_date,
                "origin_session": "NY_PM_2025-07-30",
                "taken_by_session": f"{session_name}_{actual_date}",
                "confidence": 0.98
            }
            
        elif "Previous day's PM session low taken out" in context:
            htf_event = {
                "event_type": "level_takeout",
                "level": price,
                "htf_significance": "nypm_low_2025-07-30_violated",
                "reference_date": "2025-07-30",
                "violated_on": actual_date,
                "origin_session": "NY_PM_2025-07-30", 
                "taken_by_session": f"{session_name}_{actual_date}",
                "confidence": 0.98
            }
            
        # Today's session references (intra-day)
        elif "Today's AM session high taken out" in context:
            htf_event = {
                "event_type": "level_takeout",
                "level": price,
                "htf_significance": f"nyam_high_{actual_date}_violated",
                "reference_date": actual_date,
                "violated_on": actual_date,
                "origin_session": f"NY_AM_{actual_date}",
                "taken_by_session": f"{session_name}_{actual_date}",
                "confidence": 0.95
            }
            
        elif "Today's AM session low taken out" in context:
            htf_event = {
                "event_type": "level_takeout",
                "level": price,
                "htf_significance": f"nyam_low_{actual_date}_violated",
                "reference_date": actual_date,
                "violated_on": actual_date,
                "origin_session": f"NY_AM_{actual_date}",
                "taken_by_session": f"{session_name}_{actual_date}",
                "confidence": 0.95
            }
            
        # Multiple level sweeps
        elif "clears" in context and ("session" in context or "day" in context):
            # Handle complex multi-level sweeps
            if "AM session" in context and "PM" in context:
                # Creates multiple HTF events for multi-session sweeps
                htf_event = {
                    "event_type": "multi_level_sweep",
                    "level": price,
                    "htf_significance": f"multi_session_sweep_{actual_date}",
                    "reference_date": actual_date,
                    "violated_on": actual_date,
                    "origin_session": "multiple_sessions",
                    "taken_by_session": f"{session_name}_{actual_date}",
                    "confidence": 0.92
                }
        
        # If HTF event found, add it with session details
        if htf_event:
            htf_event.update({
                "session_details": {
                    "context": context,
                    "timestamp": timestamp,
                    "price": price,
                    "action": action
                },
                "timestamp": datetime.now().isoformat(),
                "processed_by": f"HTF_Intelligence_{session_name}_July31",
            })
            
            htf_events.append(htf_event)
            print(f"  ✅ Found: {htf_event['htf_significance']} at {price}")
    
    return htf_events, metadata


def create_session_htf_context(htf_events: list, session_metadata: dict, session_name: str):
    """Create HTF context file for any July 31st session."""
    print(f"🔄 Creating {session_name} HTF Context...")
    
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    htf_dir = base_dir / "data" / "trackers" / "htf"
    
    session_type = session_metadata.get("session_type", session_name)
    date = session_metadata.get("date", "2025-07-31")
    
    htf_file = htf_dir / f"HTF_Context_{session_type}_grokEnhanced_{date}.json"
    
    # Create comprehensive HTF context
    htf_context = {
        "active_structures": [],
        "htf_influence_factor": 0.0,
        "last_update": datetime.now().isoformat(),
        "session_continuity": "maintained",
        "htf_events": htf_events,
        "created_by": f"HTF_Intelligence_{session_name}_Processing",
        "session_type": session_type,
        "date": date,
        "intelligence_processed": True,
        "intelligence_events_added": len(htf_events),
        "processing_session": session_name,
        "session_summary": {
            "htf_violations": len(htf_events),
            "processing_complete": True,
            "timezone_corrected": "ASIA" in session_name.upper()
        }
    }
    
    # Save HTF context
    with open(htf_file, 'w') as f:
        json.dump(htf_context, f, indent=2)
    
    print(f"  ✅ Created HTF context: {htf_file.name}")
    print(f"  📊 HTF Events: {len(htf_events)}")
    
    return htf_file


def generate_complete_july30_eod_context():
    """Generate complete July 30th end-of-day HTF context."""
    print("📈 Generating Complete July 30th End-of-Day HTF Context...")
    
    parser = HTFSessionIntelligenceParser()
    
    # Analyze July 30th daily structures
    if "2025-07-30" in parser.daily_structures:
        daily_data = parser.daily_structures["2025-07-30"]
        
        print(f"  📊 July 30th Complete Daily Structure:")
        print(f"    • Daily High: {daily_data['daily_high']} (Session: {daily_data['daily_high_session']})")
        print(f"    • Daily Low: {daily_data['daily_low']} (Session: {daily_data['daily_low_session']})")
        print(f"    • Range: {daily_data['daily_high'] - daily_data['daily_low']:.2f} points")
        print(f"    • Sessions Completed: {len(daily_data['sessions'])}")
        
        # Determine HTF significance for July 31st
        daily_range = daily_data['daily_high'] - daily_data['daily_low']
        
        print(f"\n  🎯 July 30th HTF Context for July 31st:")
        print(f"    • Daily High Status: {daily_data['daily_high']} ({'Active Reference' if daily_data['daily_high_session'] else 'Unknown Session'})")
        print(f"    • Daily Low Status: {daily_data['daily_low']} ({'Active Reference' if daily_data['daily_low_session'] else 'Unknown Session'})")
        print(f"    • Volatility Profile: {'High' if daily_range > 200 else 'Moderate' if daily_range > 100 else 'Low'} ({daily_range:.1f} points)")
        
        return {
            "date": "2025-07-30",
            "daily_high": daily_data['daily_high'],
            "daily_low": daily_data['daily_low'],
            "daily_range": daily_range,
            "daily_high_session": daily_data['daily_high_session'],
            "daily_low_session": daily_data['daily_low_session'],
            "sessions_count": len(daily_data['sessions']),
            "htf_ready": True
        }
    else:
        print(f"  ❌ July 30th daily structures not found")
        return None


def calculate_final_htf_intensity_july31():
    """Calculate final HTF intensity for July 31st predictions."""
    print("🧠 Calculating Final HTF Intensity for July 31st...")
    
    controller = HTFMasterControllerEnhanced()
    
    # Force reload to include all new data
    intelligence_events = controller.load_intelligence_htf_events(force_reload=True)
    
    print(f"  📊 Total Intelligence Events: {len(intelligence_events)}")
    
    # Calculate HTF intensity for July 31st prediction
    target_time = datetime(2025, 7, 31, 9, 0)  # July 31st 9 AM ET
    htf_intensity = controller.calculate_htf_intensity(target_time)
    
    print(f"  🎯 HTF Intensity (July 31st 9 AM): {htf_intensity:.2f}")
    print(f"  🚨 Activation Threshold: {controller.threshold_h}")
    print(f"  ⚡ Activation Status: {'✅ ACTIVE' if htf_intensity > controller.threshold_h else '❌ INACTIVE'}")
    
    if htf_intensity > controller.threshold_h:
        # Generate activation signal for July 31st
        activation_signal = controller.generate_intelligence_activation_signal(target_time)
        
        if activation_signal:
            print(f"\n  🚀 July 31st HAWKS Activation Signal:")
            print(f"    • Target Sessions: {activation_signal.target_sessions}")
            print(f"    • Cascade Type: {activation_signal.cascade_type}")
            print(f"    • HTF Intensity: {activation_signal.htf_intensity:.2f}")
            print(f"    • Confidence Boost: {activation_signal.confidence_boost:.2f}")
            print(f"    • Active Intelligence Events: {len(activation_signal.intelligence_events)}")
            
            # Analyze event types for prediction focus
            event_types = {}
            for event in activation_signal.intelligence_events:
                sig_parts = event.htf_significance.split('_')
                if len(sig_parts) >= 2:
                    event_type = f"{sig_parts[0]}_{sig_parts[1]}"
                    event_types[event_type] = event_types.get(event_type, 0) + 1
            
            print(f"\n    📊 Dominant HTF Event Types:")
            for event_type, count in sorted(event_types.items(), key=lambda x: x[1], reverse=True):
                print(f"      • {event_type}: {count} events")
            
            return {
                "htf_intensity": htf_intensity,
                "activation_active": True,
                "target_sessions": activation_signal.target_sessions,
                "cascade_type": activation_signal.cascade_type,
                "confidence_boost": activation_signal.confidence_boost,
                "intelligence_events": len(activation_signal.intelligence_events),
                "dominant_event_types": event_types
            }
    
    return {
        "htf_intensity": htf_intensity,
        "activation_active": False,
        "intelligence_events": len(intelligence_events)
    }


def generate_complete_july31_daily_context(processed_sessions: list):
    """Generate complete July 31st daily context from processed sessions."""
    print("📈 Generating Complete July 31st Daily Context...")
    
    total_htf_events = sum(session["htf_events"] for session in processed_sessions)
    timezone_corrections = sum(1 for session in processed_sessions if "ASIA" in session["session_name"])
    
    # Build comprehensive daily context
    july31_context = {
        "date": "2025-07-31",
        "sessions_processed": [s["session_name"] for s in processed_sessions],
        "sessions_count": len(processed_sessions),
        "total_htf_events": total_htf_events,
        "timezone_corrections": timezone_corrections,
        "processing_complete": True,
        "htf_intelligence_ready": True
    }
    
    print(f"  📊 July 31st Context Summary:")
    print(f"    • Sessions Processed: {july31_context['sessions_count']}")
    print(f"    • Total HTF Events: {july31_context['total_htf_events']}")
    print(f"    • Timezone Corrections: {july31_context['timezone_corrections']}")
    
    return july31_context


def generate_july31_remaining_predictions(hawks_data: dict, july31_context: dict, processed_sessions: list):
    """Generate predictions for any remaining July 31st sessions."""
    print("🎯 Generating July 31st Remaining Session Predictions...")
    
    if not hawks_data.get("activation_active"):
        print("  ❌ HTF activation not active - no predictions generated")
        return None
    
    processed_names = [s["session_name"] for s in processed_sessions]
    
    # Determine which major sessions still need predictions
    remaining_sessions = []
    all_trading_sessions = ["NY_AM", "LUNCH", "NY_PM"]
    
    for session in all_trading_sessions:
        if session not in processed_names:
            remaining_sessions.append(session)
    
    if not remaining_sessions:
        print("  ✅ All major trading sessions have been processed")
        return {"message": "All sessions processed", "processed_sessions": processed_names}
    
    print(f"\n  📋 Predictions for Remaining Sessions: {remaining_sessions}")
    
    # Generate predictions for remaining sessions
    predictions = {}
    for session in remaining_sessions:
        if session == "NY_AM":
            predictions[session] = {
                "time_window": "09:30-11:59 ET",
                "htf_influence": "very_high",
                "prediction": f"Critical AM session - HTF intensity {hawks_data.get('htf_intensity', 0):.1f}x threshold",
                "confidence_boost": hawks_data.get('confidence_boost', 1.0),
                "intelligence_events": hawks_data.get('intelligence_events', 0)
            }
        elif session == "LUNCH":  
            predictions[session] = {
                "time_window": "12:00-13:29 ET",
                "htf_influence": "high",
                "prediction": "Consolidation phase with potential HTF level tests",
                "confidence_boost": hawks_data.get('confidence_boost', 1.0) * 0.8,
                "intelligence_events": hawks_data.get('intelligence_events', 0)
            }
        elif session == "NY_PM":
            predictions[session] = {
                "time_window": "13:30-16:09 ET", 
                "htf_influence": "extreme",
                "prediction": f"Primary target session with maximum HTF activation",
                "confidence_boost": hawks_data.get('confidence_boost', 1.0) * 1.2,
                "intelligence_events": hawks_data.get('intelligence_events', 0)
            }
    
    # Display predictions
    for session, prediction in predictions.items():
        print(f"\n    🎯 {session} Session Prediction:")
        print(f"      • Time Window: {prediction['time_window']}")
        print(f"      • HTF Influence: {prediction['htf_influence']}") 
        print(f"      • Prediction: {prediction['prediction']}")
        print(f"      • Confidence Boost: {prediction['confidence_boost']:.1f}x")
    
    return predictions


def main():
    """Process all 5 July 31st sessions and generate complete HAWKS predictions."""
    print("🌟 Complete July 31st Processing & HAWKS Predictions")
    print("=" * 70)
    
    # Define all 5 July 31st sessions to process
    sessions_to_process = [
        ("PREASIA_Lvl-1_2025_07_31.json", "PREASIA"),
        ("ASIA_Lvl-1_2025_07_31.json", "ASIA"), 
        ("LONDON_Lvl-1_2025_07_31.json", "LONDON"),
        ("MIDNIGHT_Lvl-1_2025_07_31.json", "MIDNIGHT"),
        ("PREMARKET_Lvl-1_2025_07_31.json", "PREMARKET")
    ]
    
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    all_htf_events = []
    processed_sessions = []
    
    # Step 1: Process each session through HTF Intelligence system
    print(f"\n📋 Processing {len(sessions_to_process)} July 31st Sessions:")
    for session_file, session_name in sessions_to_process:
        session_path = base_dir / session_file
        
        if session_path.exists():
            print(f"\n  🔄 Processing {session_name}...")
            
            # Extract HTF events with timezone awareness
            htf_events, metadata = extract_session_htf_events(str(session_path), session_name)
            
            # Create HTF context file  
            htf_file = create_session_htf_context(htf_events, metadata, session_name)
            
            # Track results
            all_htf_events.extend(htf_events)
            processed_sessions.append({
                "session_name": session_name,
                "htf_events": len(htf_events),
                "htf_file": htf_file.name,
                "metadata": metadata
            })
            
            print(f"    ✅ {session_name}: {len(htf_events)} HTF events processed")
        else:
            print(f"    ❌ {session_name}: File not found at {session_path}")
    
    # Step 2: Generate complete July 31st daily context
    july31_context = generate_complete_july31_daily_context(processed_sessions)
    
    # Step 3: Calculate final HTF intensity with all July 31st data
    hawks_data = calculate_final_htf_intensity_july31()
    
    # Step 4: Generate remaining session predictions (if any sessions not yet completed)
    if july31_context:
        predictions = generate_july31_remaining_predictions(hawks_data, july31_context, processed_sessions)
    
    # Final Summary
    print(f"\n" + "="*70)
    print(f"🎯 COMPLETE JULY 31ST HTF INTELLIGENCE PROCESSING RESULTS")
    print(f"="*70)
    
    print(f"\n📊 Session Processing Status:")
    for session in processed_sessions:
        print(f"  ✅ {session['session_name']}: {session['htf_events']} HTF events → {session['htf_file']}")
    
    print(f"\n🧠 HTF Intelligence Summary:")
    print(f"  ✅ Total July 31st Sessions Processed: {len(processed_sessions)}")
    print(f"  ✅ Total HTF Events Extracted: {len(all_htf_events)}")
    print(f"  ✅ Complete HTF Intelligence Events: {hawks_data.get('intelligence_events', 0)}")
    print(f"  ✅ HTF Intensity: {hawks_data.get('htf_intensity', 0):.2f}")
    print(f"  ✅ HAWKS Activation: {'ACTIVE' if hawks_data.get('activation_active') else 'INACTIVE'}")
    
    if july31_context:
        print(f"\n📈 July 31st Daily Context:")
        print(f"  • Sessions Processed: {july31_context.get('sessions_count', 0)}")
        print(f"  • HTF Events: {july31_context.get('total_htf_events', 0)}")
        print(f"  • Timezone Corrections Applied: {july31_context.get('timezone_corrections', 0)}")
    
    print(f"\n🚀 HAWKS Prediction Status:")
    if hawks_data.get('activation_active'):
        print(f"  ✅ HAWKS system fully activated with July 31st intelligence")
        print(f"  ✅ Primary target sessions: {hawks_data.get('target_sessions', ['Unknown'])}")
        print(f"  ✅ Confidence boost: {hawks_data.get('confidence_boost', 1.0):.1f}x")
        print(f"  ✅ Ready for partial AM prediction when data arrives")
    else:
        print(f"  ⚠️ HTF intensity below activation threshold")
    
    print(f"\n🎯 JULY 31ST HTF INTELLIGENCE SYSTEM FULLY OPERATIONAL!")


if __name__ == "__main__":
    main()