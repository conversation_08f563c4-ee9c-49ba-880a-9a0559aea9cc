#!/usr/bin/env python3
"""
July 29th Lunch Session Cascade Analysis
========================================
Analyzes late-stage cascading events and their influence on PM session using Hawkes process framework.

Key Events:
1. 12:59:00 ET - Multiple FPFVG redeliveries at 23508.0
2. 13:02:00 ET - Previous day's Asia FPFVG redelivered at 23515.375
3. 13:34:36 ET - PM cascade prediction (4.6 min into PM session)

Mathematical Framework:
- λ(t) = μ + Σ α·exp(-β(t-t_i)) for self-exciting cascade intensity
- β_h = 0.00442 (HTF decay parameter from calibration)
- Calculate temporal influence strength from Lunch events to PM cascade
"""

import numpy as np
from datetime import datetime, timedelta
import json
from typing import Dict, List, Tuple

class LunchCascadeAnalyzer:
    """Analyzes July 29th Lunch session cascading events using Hawkes process framework."""
    
    def __init__(self):
        # HTF Hawkes parameters from calibration
        self.beta_h = 0.00442  # HTF decay rate (3.77-hour half-life)
        self.alpha_h = 0.720   # HTF excitation strength
        self.mu_h = 0.246      # HTF baseline intensity
        
        # Key timestamps
        self.lunch_end = "12:59:00"
        self.asia_fpfvg_event = "13:02:00" 
        self.pm_start = "13:30:00"
        self.pm_cascade = "13:34:36"
        
        # Event data from Lunch session
        self.lunch_events = [
            {
                "timestamp": "12:59:00",
                "price": 23508.0,
                "context": "Multiple FPFVG redeliveries: Lunch, Previous day's Pre-market, Previous day's NY AM",
                "intensity_factor": 3.0  # Multiple simultaneous redeliveries
            },
            {
                "timestamp": "13:02:00", 
                "price": 23515.375,
                "context": "Previous day's Asia FPFVG redelivered, today's lunch high taken",
                "intensity_factor": 2.5  # Major structural level breach
            }
        ]
    
    def time_to_minutes(self, time_str: str) -> float:
        """Convert time string to minutes from midnight."""
        h, m, s = map(int, time_str.split(':'))
        return h * 60 + m + s / 60
    
    def calculate_time_delay(self, start_time: str, end_time: str) -> float:
        """Calculate delay in minutes between two timestamps."""
        start_min = self.time_to_minutes(start_time)
        end_min = self.time_to_minutes(end_time)
        return end_min - start_min
    
    def hawkes_influence_strength(self, delay_minutes: float) -> float:
        """Calculate Hawkes influence strength using exp(-β * delay)."""
        return np.exp(-self.beta_h * delay_minutes)
    
    def analyze_temporal_influences(self) -> Dict:
        """Analyze temporal influence of Lunch events on PM cascade."""
        analysis = {
            "hawkes_parameters": {
                "beta_h": self.beta_h,
                "alpha_h": self.alpha_h, 
                "mu_h": self.mu_h,
                "half_life_hours": np.log(2) / self.beta_h / 60
            },
            "event_analysis": [],
            "cascade_influence": {}
        }
        
        # Analyze each Lunch event
        for event in self.lunch_events:
            delay_to_pm_cascade = self.calculate_time_delay(event["timestamp"], self.pm_cascade)
            influence_strength = self.hawkes_influence_strength(delay_to_pm_cascade)
            
            event_analysis = {
                "timestamp": event["timestamp"],
                "price": event["price"],
                "context": event["context"],
                "delay_to_pm_cascade_minutes": delay_to_pm_cascade,
                "hawkes_influence_strength": influence_strength,
                "intensity_contribution": self.alpha_h * event["intensity_factor"] * influence_strength,
                "decay_percentage": (1 - influence_strength) * 100
            }
            analysis["event_analysis"].append(event_analysis)
        
        # Calculate total cascade influence
        total_intensity = self.mu_h  # Baseline
        for event_data in analysis["event_analysis"]:
            total_intensity += event_data["intensity_contribution"]
        
        analysis["cascade_influence"] = {
            "baseline_intensity": self.mu_h,
            "total_intensity_at_pm_cascade": total_intensity,
            "lunch_contribution_percentage": ((total_intensity - self.mu_h) / total_intensity) * 100,
            "intensity_amplification_factor": total_intensity / self.mu_h
        }
        
        return analysis
    
    def analyze_session_gaps(self) -> Dict:
        """Analyze critical timing gaps in the cascade sequence."""
        gaps = {
            "lunch_end_to_asia_fpfvg": self.calculate_time_delay(self.lunch_end, self.asia_fpfvg_event),
            "asia_fpfvg_to_pm_start": self.calculate_time_delay(self.asia_fpfvg_event, self.pm_start),
            "pm_start_to_cascade": self.calculate_time_delay(self.pm_start, self.pm_cascade),
            "total_lunch_to_pm_cascade": self.calculate_time_delay(self.lunch_end, self.pm_cascade)
        }
        
        # Calculate influence decay over gaps
        gap_analysis = {}
        for gap_name, gap_minutes in gaps.items():
            gap_analysis[gap_name] = {
                "duration_minutes": gap_minutes,
                "influence_retention": self.hawkes_influence_strength(gap_minutes),
                "influence_loss_percentage": (1 - self.hawkes_influence_strength(gap_minutes)) * 100
            }
        
        return {
            "timing_gaps": gaps,
            "gap_analysis": gap_analysis
        }
    
    def identify_structural_cascade_completion(self) -> Dict:
        """Identify if late Lunch events constitute structural cascade completion."""
        
        # Analyze FPFVG redelivery pattern
        fpfvg_analysis = {
            "multiple_historical_redeliveries": True,
            "redelivered_origins": [
                "Lunch FPFVG",
                "Previous day's Pre-market FPFVG", 
                "Previous day's NY AM FPFVG",
                "Previous day's Asia FPFVG"
            ],
            "structural_significance": "Complete historical liquidity clearance",
            "timing_pattern": "Sequential completion within 3-minute window"
        }
        
        # Calculate cascade completion score
        completion_factors = {
            "multiple_fpfvg_redeliveries": 0.3,
            "session_high_breach": 0.25,
            "timing_concentration": 0.2,
            "historical_depth": 0.25  # Previous day's Asia FPFVG
        }
        
        completion_score = sum(completion_factors.values())
        
        return {
            "fpfvg_redelivery_analysis": fpfvg_analysis,
            "completion_factors": completion_factors,
            "structural_completion_score": completion_score,
            "cascade_completion_status": "COMPLETE" if completion_score >= 0.8 else "PARTIAL"
        }
    
    def generate_comprehensive_report(self) -> Dict:
        """Generate comprehensive analysis report."""
        
        print("🎯 JULY 29TH LUNCH CASCADE ANALYSIS")
        print("=" * 50)
        
        # Run all analyses
        temporal_analysis = self.analyze_temporal_influences()
        gap_analysis = self.analyze_session_gaps()
        structural_analysis = self.identify_structural_cascade_completion()
        
        report = {
            "session_metadata": {
                "session": "Lunch",
                "date": "2025-07-29",
                "analysis_focus": "Late-stage cascading events (12:54-13:02 ET)",
                "pm_cascade_prediction": "13:34:36 ET (4.6 min into PM session)"
            },
            "hawkes_mathematical_framework": temporal_analysis["hawkes_parameters"],
            "critical_events": temporal_analysis["event_analysis"],
            "cascade_influence_analysis": temporal_analysis["cascade_influence"],
            "timing_gap_analysis": gap_analysis,
            "structural_completion_analysis": structural_analysis
        }
        
        # Print key findings
        print("\n📊 KEY FINDINGS:")
        print(f"• HTF decay parameter (β_h): {self.beta_h}")
        print(f"• Half-life of HTF influence: {temporal_analysis['hawkes_parameters']['half_life_hours']:.2f} hours")
        
        print(f"\n🕐 TEMPORAL INFLUENCE CALCULATIONS:")
        for event in temporal_analysis["event_analysis"]:
            print(f"• {event['timestamp']}: {event['hawkes_influence_strength']:.4f} influence strength")
            print(f"  └── {event['delay_to_pm_cascade_minutes']:.1f} min delay, {event['decay_percentage']:.1f}% decay")
        
        print(f"\n🎯 CASCADE INFLUENCE:")
        cascade_inf = temporal_analysis["cascade_influence"]
        print(f"• Total intensity at PM cascade: {cascade_inf['total_intensity_at_pm_cascade']:.4f}")
        print(f"• Lunch contribution: {cascade_inf['lunch_contribution_percentage']:.1f}%")
        print(f"• Intensity amplification: {cascade_inf['intensity_amplification_factor']:.2f}x")
        
        print(f"\n🏗️ STRUCTURAL ANALYSIS:")
        struct_completion = structural_analysis['structural_completion_score']
        print(f"• Structural completion score: {struct_completion:.2f}")
        print(f"• Status: {structural_analysis['cascade_completion_status']}")
        print(f"• FPFVG origins cleared: {len(structural_analysis['fpfvg_redelivery_analysis']['redelivered_origins'])}")
        
        return report

def main():
    """Run the comprehensive Lunch cascade analysis."""
    analyzer = LunchCascadeAnalyzer()
    report = analyzer.generate_comprehensive_report()
    
    # Save report
    output_file = "lunch_cascade_analysis_report_20250729.json"
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📁 Analysis saved to: {output_file}")
    return report

if __name__ == "__main__":
    main()