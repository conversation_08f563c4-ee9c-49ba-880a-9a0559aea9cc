#!/usr/bin/env python3
"""
Performance Tracker for Adaptive Ensemble System
Tracks predictor accuracy and adjusts weights based on historical performance
"""

import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

@dataclass
class PredictionRecord:
    """Record of a single prediction and its accuracy"""
    predictor_name: str
    predicted_value: float
    actual_value: float
    error_points: float
    range_error_pct: float
    session_character: str
    timestamp: str
    context: str

@dataclass  
class PredictorPerformance:
    """Performance statistics for a predictor"""
    name: str
    total_predictions: int
    average_error_pct: float
    best_error_pct: float
    worst_error_pct: float
    context_performance: Dict[str, float]  # context -> avg_error_pct
    recent_trend: str  # improving, declining, stable
    confidence_score: float
    recommended_weight: float

class PerformanceTracker:
    """Tracks and analyzes predictor performance over time"""
    
    def __init__(self, history_file: str = "predictor_performance_history.json"):
        self.history_file = history_file
        self.prediction_history: Dict[str, List[PredictionRecord]] = {}
        self.performance_stats: Dict[str, PredictorPerformance] = {}
        self.load_history()
    
    def load_history(self):
        """Load prediction history from file"""
        try:
            with open(self.history_file, 'r') as f:
                data = json.load(f)
                # Convert back to PredictionRecord objects
                for predictor_name, records in data.get('prediction_history', {}).items():
                    self.prediction_history[predictor_name] = [
                        PredictionRecord(**record) for record in records
                    ]
        except FileNotFoundError:
            print(f"📝 No existing performance history found - starting fresh")
            self.prediction_history = {}
    
    def save_history(self):
        """Save prediction history to file"""
        # Convert PredictionRecord objects to dicts for JSON serialization
        serializable_history = {}
        for predictor_name, records in self.prediction_history.items():
            serializable_history[predictor_name] = [
                {
                    'predictor_name': r.predictor_name,
                    'predicted_value': r.predicted_value,
                    'actual_value': r.actual_value,
                    'error_points': r.error_points,
                    'range_error_pct': r.range_error_pct,
                    'session_character': r.session_character,
                    'timestamp': r.timestamp,
                    'context': r.context
                }
                for r in records
            ]
        
        data = {
            'prediction_history': serializable_history,
            'last_updated': datetime.now().isoformat()
        }
        
        with open(self.history_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def record_prediction(self, predictor_name: str, predicted_value: float, 
                         actual_value: float, session_range: float,
                         session_character: str, context: str = ""):
        """Record a new prediction and its accuracy"""
        
        error_points = abs(predicted_value - actual_value)
        range_error_pct = (error_points / session_range) * 100
        
        record = PredictionRecord(
            predictor_name=predictor_name,
            predicted_value=predicted_value,
            actual_value=actual_value,
            error_points=error_points,
            range_error_pct=range_error_pct,
            session_character=session_character,
            timestamp=datetime.now().isoformat(),
            context=context
        )
        
        if predictor_name not in self.prediction_history:
            self.prediction_history[predictor_name] = []
            
        self.prediction_history[predictor_name].append(record)
        
        # Keep only last 50 records per predictor
        if len(self.prediction_history[predictor_name]) > 50:
            self.prediction_history[predictor_name] = self.prediction_history[predictor_name][-50:]
        
        # Update performance stats
        self.update_performance_stats(predictor_name)
        
        # Save to file
        self.save_history()
        
        return record
    
    def update_performance_stats(self, predictor_name: str):
        """Update performance statistics for a predictor"""
        
        if predictor_name not in self.prediction_history:
            return
            
        records = self.prediction_history[predictor_name]
        if not records:
            return
        
        # Calculate basic statistics
        error_pcts = [r.range_error_pct for r in records]
        avg_error = np.mean(error_pcts)
        best_error = np.min(error_pcts)
        worst_error = np.max(error_pcts)
        
        # Context-specific performance
        context_performance = {}
        contexts = set(r.session_character for r in records)
        for context in contexts:
            context_records = [r for r in records if r.session_character == context]
            if context_records:
                context_avg = np.mean([r.range_error_pct for r in context_records])
                context_performance[context] = context_avg
        
        # Analyze recent trend (last 10 vs previous 10)
        recent_trend = "stable"
        if len(records) >= 20:
            recent_10 = np.mean([r.range_error_pct for r in records[-10:]])
            previous_10 = np.mean([r.range_error_pct for r in records[-20:-10]])
            
            if recent_10 < previous_10 * 0.9:
                recent_trend = "improving"
            elif recent_10 > previous_10 * 1.1:
                recent_trend = "declining"
        
        # Calculate confidence score (inverse of average error, scaled 0-1)
        confidence_score = max(0.1, min(1.0, 1.0 - (avg_error / 100.0)))
        
        # Calculate recommended weight based on performance
        recommended_weight = self.calculate_recommended_weight(avg_error, recent_trend, confidence_score)
        
        self.performance_stats[predictor_name] = PredictorPerformance(
            name=predictor_name,
            total_predictions=len(records),
            average_error_pct=avg_error,
            best_error_pct=best_error,
            worst_error_pct=worst_error,
            context_performance=context_performance,
            recent_trend=recent_trend,
            confidence_score=confidence_score,
            recommended_weight=recommended_weight
        )
    
    def calculate_recommended_weight(self, avg_error: float, trend: str, confidence: float) -> float:
        """Calculate recommended weight based on performance metrics"""
        
        # Base weight from accuracy (lower error = higher weight)
        if avg_error < 10:
            base_weight = 0.8
        elif avg_error < 20:
            base_weight = 0.6
        elif avg_error < 40:
            base_weight = 0.4
        else:
            base_weight = 0.2
        
        # Trend adjustment
        trend_modifier = 1.0
        if trend == "improving":
            trend_modifier = 1.2
        elif trend == "declining":
            trend_modifier = 0.8
        
        # Confidence adjustment
        confidence_modifier = confidence
        
        recommended_weight = base_weight * trend_modifier * confidence_modifier
        return min(0.9, max(0.1, recommended_weight))  # Clamp between 0.1 and 0.9
    
    def get_adaptive_weights(self, active_predictors: List[str], 
                           session_character: str) -> Dict[str, float]:
        """Get adaptive weights based on historical performance"""
        
        weights = {}
        total_performance_weight = 0.0
        
        # Calculate performance-based weights
        for predictor_name in active_predictors:
            if predictor_name in self.performance_stats:
                stats = self.performance_stats[predictor_name]
                
                # Use context-specific performance if available
                if session_character in stats.context_performance:
                    context_error = stats.context_performance[session_character]
                    performance_weight = max(0.1, 1.0 - (context_error / 100.0))
                else:
                    performance_weight = stats.confidence_score
                
                # Apply trend modifier
                if stats.recent_trend == "improving":
                    performance_weight *= 1.2
                elif stats.recent_trend == "declining":
                    performance_weight *= 0.8
                    
                weights[predictor_name] = performance_weight
                total_performance_weight += performance_weight
            else:
                # No history - use neutral weight
                weights[predictor_name] = 0.5
                total_performance_weight += 0.5
        
        # Normalize weights
        if total_performance_weight > 0:
            for predictor_name in weights:
                weights[predictor_name] /= total_performance_weight
        
        return weights
    
    def get_performance_report(self) -> str:
        """Generate a formatted performance report"""
        
        if not self.performance_stats:
            return "📊 No performance data available yet"
        
        report = ["📊 PREDICTOR PERFORMANCE REPORT", "=" * 35]
        
        # Sort predictors by average error (best first)
        sorted_predictors = sorted(
            self.performance_stats.values(),
            key=lambda x: x.average_error_pct
        )
        
        for i, stats in enumerate(sorted_predictors, 1):
            report.extend([
                f"\n{i}️⃣ {stats.name.upper()}:",
                f"   Predictions: {stats.total_predictions}",
                f"   Avg Error: {stats.average_error_pct:.1f}%",
                f"   Best: {stats.best_error_pct:.1f}%",
                f"   Worst: {stats.worst_error_pct:.1f}%",
                f"   Trend: {stats.recent_trend}",
                f"   Confidence: {stats.confidence_score:.2f}",
                f"   Recommended Weight: {stats.recommended_weight:.2f}"
            ])
            
            # Show context-specific performance
            if stats.context_performance:
                report.append("   Context Performance:")
                for context, error in sorted(stats.context_performance.items()):
                    report.append(f"     {context}: {error:.1f}%")
        
        return "\n".join(report)
    
    def recommend_ensemble_strategy(self, session_character: str) -> Dict[str, float]:
        """Recommend ensemble strategy based on historical performance"""
        
        recommendations = {}
        
        # Check which predictors perform best for this session character
        best_performers = []
        for predictor_name, stats in self.performance_stats.items():
            if session_character in stats.context_performance:
                error = stats.context_performance[session_character]
                best_performers.append((predictor_name, error, stats.confidence_score))
        
        # Sort by error (ascending) then confidence (descending)
        best_performers.sort(key=lambda x: (x[1], -x[2]))
        
        if best_performers:
            # Give highest weight to best performer, distribute rest
            total_weight = 1.0
            for i, (predictor_name, error, confidence) in enumerate(best_performers):
                if i == 0:
                    # Best performer gets 60% weight
                    recommendations[predictor_name] = 0.6
                    total_weight -= 0.6
                else:
                    # Distribute remaining weight among others
                    remaining_predictors = len(best_performers) - 1
                    if remaining_predictors > 0:
                        recommendations[predictor_name] = total_weight / remaining_predictors
        
        return recommendations

def main():
    """Test performance tracking system"""
    
    print("🧪 PERFORMANCE TRACKER TEST")
    print("=" * 35)
    
    tracker = PerformanceTracker()
    
    # Simulate some prediction records
    test_records = [
        ("expansion_enhanced", 23348.68, 23350.50, 162.5, "expansion_consolidation_final_expansion"),
        ("base_monte_carlo", 23152.31, 23350.50, 162.5, "expansion_consolidation_final_expansion"),
        ("consolidation_scaled", 23260.00, 23350.50, 162.5, "expansion_consolidation_final_expansion"),
    ]
    
    print("\n📝 Recording test predictions:")
    for predictor, predicted, actual, range_val, character in test_records:
        record = tracker.record_prediction(predictor, predicted, actual, range_val, character)
        print(f"   {predictor}: {record.range_error_pct:.1f}% error")
    
    # Generate performance report
    print(f"\n{tracker.get_performance_report()}")
    
    # Get adaptive weights
    print(f"\n🎯 ADAPTIVE WEIGHT RECOMMENDATIONS:")
    adaptive_weights = tracker.get_adaptive_weights(
        ["expansion_enhanced", "base_monte_carlo", "consolidation_scaled"],
        "expansion_consolidation_final_expansion"
    )
    
    for predictor, weight in adaptive_weights.items():
        print(f"   {predictor}: {weight:.2f}")
    
    # Strategy recommendation
    print(f"\n💡 ENSEMBLE STRATEGY RECOMMENDATION:")
    strategy = tracker.recommend_ensemble_strategy("expansion_consolidation_final_expansion")
    for predictor, weight in strategy.items():
        print(f"   {predictor}: {weight:.2f}")

if __name__ == "__main__":
    main()