#!/usr/bin/env python3
"""
HTF Influence Analysis - July 29 Specific Events
Analyzes the exact HTF events and times that contributed to the July 29 PM cascade prediction.
"""

import json
import math
from datetime import datetime, timedelta
from pathlib import Path

class HTFInfluenceAnalyzer:
    """Analyze specific HTF events that influenced July 29 PM cascade prediction."""
    
    def __init__(self):
        self.base_dir = Path("/Users/<USER>/grok-claude-automation")
        self.prediction_time = datetime(2025, 7, 29, 15, 17, 0)
        self.htf_params = {"mu_h": 0.02, "alpha_h": 35.51, "beta_h": 0.00442}
        
    def get_specific_htf_events_july29(self):
        """Get the specific HTF events that influenced July 29."""
        
        # PRIMARY HTF EVENT: July 29 Lunch Session Structural Completion
        primary_event = {
            "time": datetime(2025, 7, 29, 12, 59, 0),
            "event": "Multiple FPFVG redeliveries: Lunch, Previous day's Pre-market, Previous day's NY AM",
            "price": 23508.0,
            "magnitude": 2.5,  # Strong structural completion
            "type": "htf_structural_completion",
            "influence_calculation": "Direct same-day influence"
        }
        
        # SECONDARY HTF EVENT: July 29 Lunch High Taken
        secondary_event = {
            "time": datetime(2025, 7, 29, 13, 2, 0),
            "event": "Previous day's Asia FPFVG redelivered, today's lunch high taken",
            "price": 23515.375,
            "magnitude": 2.2,
            "type": "htf_session_high_completion",
            "influence_calculation": "Direct same-day influence"
        }
        
        # HISTORICAL HTF EVENTS: July 28 Friday Events (Weekend Carryover)
        friday_close_event = {
            "time": datetime(2025, 7, 28, 17, 0, 0),
            "event": "Friday weekly close HTF influence",
            "price": 23496.50,
            "magnitude": 3.5,
            "type": "friday_close_htf",
            "influence_calculation": "Weekend carryover (22-hour delay)"
        }
        
        friday_setup_event = {
            "time": datetime(2025, 7, 28, 15, 0, 0),
            "event": "Friday afternoon weekly setup",
            "price": 23461.50,
            "magnitude": 3.0,
            "type": "friday_weekly_setup",
            "influence_calculation": "Weekend carryover (24.3-hour delay)"
        }
        
        london_high_event = {
            "time": datetime(2025, 7, 28, 2, 14, 0),
            "event": "London session high created, reversal point",
            "price": 23585.25,
            "magnitude": 2.8,
            "type": "session_high_htf",
            "influence_calculation": "Multi-day influence (37-hour delay)"
        }
        
        return [
            primary_event,
            secondary_event,
            friday_close_event,
            friday_setup_event,
            london_high_event
        ]
    
    def calculate_individual_htf_contributions(self, htf_events):
        """Calculate each HTF event's individual contribution to intensity."""
        contributions = []
        
        for event in htf_events:
            # Calculate time delay in hours
            delta_t = (self.prediction_time - event["time"]).total_seconds() / 3600.0
            
            # HTF intensity contribution: α_h * exp(-β_h * Δt) * magnitude
            raw_contribution = (self.htf_params["alpha_h"] * 
                              math.exp(-self.htf_params["beta_h"] * delta_t) * 
                              event["magnitude"])
            
            # Calculate decay factor
            decay_factor = math.exp(-self.htf_params["beta_h"] * delta_t)
            
            contribution_data = {
                "event_time": event["time"].strftime("%Y-%m-%d %H:%M ET"),
                "event_description": event["event"],
                "price": event["price"],
                "magnitude": event["magnitude"],
                "delay_hours": delta_t,
                "decay_factor": decay_factor,
                "raw_contribution": raw_contribution,
                "percentage_of_total": 0  # Will calculate after totaling
            }
            
            contributions.append(contribution_data)
        
        # Calculate total and percentages
        total_htf_contribution = sum(c["raw_contribution"] for c in contributions)
        for contrib in contributions:
            contrib["percentage_of_total"] = (contrib["raw_contribution"] / total_htf_contribution) * 100
        
        return contributions, total_htf_contribution
    
    def analyze_temporal_causality(self, contributions):
        """Analyze the temporal causality chain."""
        causality_analysis = {
            "immediate_triggers": [],
            "same_day_influences": [],
            "weekend_carryovers": [],
            "multi_day_influences": []
        }
        
        for contrib in contributions:
            delay_hours = contrib["delay_hours"]
            
            if delay_hours <= 1:
                causality_analysis["immediate_triggers"].append(contrib)
            elif delay_hours <= 12:
                causality_analysis["same_day_influences"].append(contrib)
            elif delay_hours <= 48:
                causality_analysis["weekend_carryovers"].append(contrib)
            else:
                causality_analysis["multi_day_influences"].append(contrib)
        
        return causality_analysis
    
    def generate_influence_report(self):
        """Generate complete HTF influence report for July 29."""
        print("🔍 HTF INFLUENCE ANALYSIS - JULY 29 PM CASCADE")
        print("=" * 65)
        
        # Get HTF events
        htf_events = self.get_specific_htf_events_july29()
        print(f"\n📊 ANALYZING {len(htf_events)} HTF EVENTS")
        
        # Calculate contributions
        contributions, total_htf = self.calculate_individual_htf_contributions(htf_events)
        
        # Add baseline
        baseline = self.htf_params["mu_h"]
        total_intensity = baseline + total_htf
        
        print(f"\n⚡ HTF INTENSITY BREAKDOWN:")
        print(f"   Baseline (μ_h): {baseline:.4f}")
        print(f"   HTF Events Contribution: {total_htf:.4f}")
        print(f"   TOTAL HTF INTENSITY: {total_intensity:.4f}")
        print(f"   Activation Threshold: 0.5000")
        print(f"   Activation Status: {'✅ ACTIVE' if total_intensity > 0.5 else '❌ DORMANT'}")
        
        print(f"\n🎯 INDIVIDUAL HTF EVENT CONTRIBUTIONS:")
        print("-" * 65)
        
        # Sort by contribution (highest first)
        sorted_contributions = sorted(contributions, key=lambda x: x["raw_contribution"], reverse=True)
        
        for i, contrib in enumerate(sorted_contributions, 1):
            print(f"\n{i}. {contrib['event_time']}")
            print(f"   Event: {contrib['event_description']}")
            print(f"   Price: {contrib['price']}")
            print(f"   Magnitude: {contrib['magnitude']}")
            print(f"   Delay: {contrib['delay_hours']:.1f} hours")
            print(f"   Decay Factor: {contrib['decay_factor']:.4f}")
            print(f"   Contribution: {contrib['raw_contribution']:.4f} ({contrib['percentage_of_total']:.1f}%)")
        
        # Temporal causality analysis
        causality = self.analyze_temporal_causality(contributions)
        
        print(f"\n🕒 TEMPORAL CAUSALITY ANALYSIS:")
        print("-" * 65)
        
        if causality["same_day_influences"]:
            print(f"\n📅 SAME-DAY INFLUENCES ({len(causality['same_day_influences'])} events):")
            for contrib in causality["same_day_influences"]:
                print(f"   • {contrib['event_time']}: {contrib['raw_contribution']:.4f} intensity")
        
        if causality["weekend_carryovers"]:
            print(f"\n🎃 WEEKEND CARRYOVERS ({len(causality['weekend_carryovers'])} events):")
            for contrib in causality["weekend_carryovers"]:
                print(f"   • {contrib['event_time']}: {contrib['raw_contribution']:.4f} intensity")
        
        if causality["multi_day_influences"]:
            print(f"\n🌊 MULTI-DAY INFLUENCES ({len(causality['multi_day_influences'])} events):")
            for contrib in causality["multi_day_influences"]:
                print(f"   • {contrib['event_time']}: {contrib['raw_contribution']:.4f} intensity")
        
        # Key findings
        primary_contributor = sorted_contributions[0]
        
        print(f"\n🔑 KEY FINDINGS:")
        print("-" * 65)
        print(f"   🎯 PRIMARY HTF TRIGGER:")
        print(f"      {primary_contributor['event_time']}")
        print(f"      {primary_contributor['event_description']}")
        print(f"      Contribution: {primary_contributor['percentage_of_total']:.1f}% of total HTF intensity")
        
        print(f"\n   ⏰ TEMPORAL DYNAMICS:")
        same_day_total = sum(c["raw_contribution"] for c in causality["same_day_influences"])
        weekend_total = sum(c["raw_contribution"] for c in causality["weekend_carryovers"])
        
        print(f"      Same-day influences: {same_day_total:.4f} ({same_day_total/total_htf*100:.1f}%)")
        print(f"      Weekend carryovers: {weekend_total:.4f} ({weekend_total/total_htf*100:.1f}%)")
        
        print(f"\n   🚀 FRACTAL CASCADE CHAIN:")
        print(f"      1. July 28 Friday setup events create weekend carryover")
        print(f"      2. July 29 Lunch structural completion (12:59) triggers immediate intensity spike")
        print(f"      3. Combined HTF intensity ({total_intensity:.2f}) exceeds threshold (0.5)")
        print(f"      4. HTF Master Controller activates Session Subordinate Executor")
        print(f"      5. Session-level prediction enhanced by {total_intensity/0.5:.1f}x factor")
        
        # Save detailed report
        detailed_report = {
            "analysis_timestamp": datetime.now().isoformat(),
            "prediction_target": "July 29, 2025 15:17 ET PM Session",
            "htf_intensity_breakdown": {
                "baseline": baseline,
                "htf_events_contribution": total_htf,
                "total_intensity": total_intensity,
                "activation_threshold": 0.5,
                "activation_achieved": total_intensity > 0.5
            },
            "individual_contributions": sorted_contributions,
            "temporal_causality": causality,
            "key_findings": {
                "primary_trigger": primary_contributor,
                "same_day_influence_percentage": same_day_total/total_htf*100,
                "weekend_carryover_percentage": weekend_total/total_htf*100,
                "total_enhancement_factor": total_intensity/0.5
            }
        }
        
        output_file = self.base_dir / "htf_influence_analysis_july29_detailed.json"
        with open(output_file, 'w') as f:
            json.dump(detailed_report, f, indent=2, default=str)
        
        print(f"\n💾 Detailed analysis saved to: {output_file}")
        
        return detailed_report


def main():
    """Run HTF influence analysis for July 29."""
    analyzer = HTFInfluenceAnalyzer()
    report = analyzer.generate_influence_report()
    
    return report


if __name__ == "__main__":
    main()