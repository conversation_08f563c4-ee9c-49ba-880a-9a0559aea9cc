#!/usr/bin/env python3
"""
Process PM July 29th with Enhanced HTF Intelligence

This script processes the critical PM session from July 29th through the
HTF Intelligence system with enhanced pattern matching to capture all
session reference takeouts for complete HTF context generation.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from htf_session_intelligence_parser import HTFSessionIntelligenceParser
from htf_intelligence_integration import HTFIntelligenceIntegration
from htf_master_controller_enhanced import HTFMasterControllerEnhanced


def manually_extract_htf_events(session_file_path: str):
    """Manually extract HTF events from PM July 29th session."""
    print("🔍 Manually Extracting HTF Events from PM July 29th...")
    
    with open(session_file_path, 'r') as f:
        session_data = json.load(f)
    
    metadata = session_data.get("session_metadata", {})
    movements = session_data.get("price_movements", [])
    
    htf_events = []
    
    for movement in movements:
        context = movement.get("context", "")
        timestamp = movement.get("timestamp", "")
        price = movement.get("price", 0.0)
        action = movement.get("action", "")
        
        # Enhanced pattern matching for session takeouts
        if "Today's AM session low taken out" in context:
            htf_events.append({
                "event_type": "level_takeout",
                "level": price,
                "htf_significance": "nyam_low_2025-07-29_violated",
                "reference_date": "2025-07-29",
                "violated_on": "2025-07-29", 
                "origin_session": "NY_AM_2025-07-29",
                "taken_by_session": "NY_PM_2025-07-29",
                "session_details": {
                    "context": context,
                    "timestamp": timestamp,
                    "price": price,
                    "action": action
                },
                "timestamp": datetime.now().isoformat(),
                "processed_by": "Manual_HTF_Intelligence_Extraction",
                "confidence": 0.98
            })
            print(f"  ✅ Found: {htf_events[-1]['htf_significance']} at {price}")
            
        elif "Previous day's AM session low taken out" in context:
            htf_events.append({
                "event_type": "level_takeout", 
                "level": price,
                "htf_significance": "nyam_low_2025-07-28_violated",
                "reference_date": "2025-07-28",
                "violated_on": "2025-07-29",
                "origin_session": "NY_AM_2025-07-28", 
                "taken_by_session": "NY_PM_2025-07-29",
                "session_details": {
                    "context": context,
                    "timestamp": timestamp,
                    "price": price,
                    "action": action
                },
                "timestamp": datetime.now().isoformat(),
                "processed_by": "Manual_HTF_Intelligence_Extraction",
                "confidence": 0.98
            })
            print(f"  ✅ Found: {htf_events[-1]['htf_significance']} at {price}")
    
    return htf_events, metadata


def update_htf_context_manually(htf_events: list, session_metadata: dict):
    """Manually update HTF context files with extracted events."""
    print("🔄 Updating HTF Context Files...")
    
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    htf_dir = base_dir / "data" / "trackers" / "htf"
    
    # Create HTF context file for PM July 29th
    session_type = session_metadata.get("session_type", "NY_PM")
    date = session_metadata.get("date", "2025-07-29")
    
    htf_file = htf_dir / f"HTF_Context_{session_type}_grokEnhanced_{date}.json"
    
    # Create HTF context structure
    htf_context = {
        "active_structures": [],
        "htf_influence_factor": 0.0,
        "last_update": datetime.now().isoformat(),
        "session_continuity": "maintained",
        "htf_events": htf_events,
        "created_by": "HTF_Intelligence_Manual_Processing",
        "session_type": session_type,
        "date": date,
        "intelligence_processed": True,
        "intelligence_events_added": len(htf_events)
    }
    
    # Save HTF context file
    with open(htf_file, 'w') as f:
        json.dump(htf_context, f, indent=2)
    
    print(f"  ✅ Created HTF context file: {htf_file.name}")
    print(f"  📊 Added {len(htf_events)} HTF intelligence events")
    
    return htf_file


def calculate_updated_htf_intensity():
    """Calculate updated HTF intensity with new PM July 29th data."""
    print("🧠 Calculating Updated HTF Intensity...")
    
    controller = HTFMasterControllerEnhanced()
    
    # Force reload intelligence events to include new PM data
    intelligence_events = controller.load_intelligence_htf_events(force_reload=True)
    
    print(f"  📊 Total Intelligence Events: {len(intelligence_events)}")
    
    # Calculate HTF intensity
    htf_intensity = controller.calculate_htf_intensity()
    
    print(f"  🎯 Current HTF Intensity: {htf_intensity:.2f}")
    print(f"  🚨 Threshold: {controller.threshold_h}")
    print(f"  ⚡ Activation: {'✅ ACTIVE' if htf_intensity > controller.threshold_h else '❌ INACTIVE'}")
    
    # Generate activation signal if threshold exceeded
    if htf_intensity > controller.threshold_h:
        activation_signal = controller.generate_intelligence_activation_signal()
        if activation_signal:
            print(f"  🎯 Activation Signal Generated:")
            print(f"    • Target Sessions: {activation_signal.target_sessions}")
            print(f"    • Cascade Type: {activation_signal.cascade_type}")
            print(f"    • Confidence Boost: {activation_signal.confidence_boost:.2f}")
            print(f"    • Intelligence Events: {len(activation_signal.intelligence_events)}")
    
    return htf_intensity, intelligence_events


def analyze_daily_structures():
    """Analyze daily structures for July 29th completion."""
    print("📈 Analyzing July 29th Daily Structures...")
    
    parser = HTFSessionIntelligenceParser()
    
    # Check July 29th daily structures
    if "2025-07-29" in parser.daily_structures:
        daily_data = parser.daily_structures["2025-07-29"]
        
        print(f"  📊 July 29th Daily Summary:")
        print(f"    • Daily High: {daily_data['daily_high']} (Session: {daily_data['daily_high_session']})")
        print(f"    • Daily Low: {daily_data['daily_low']} (Session: {daily_data['daily_low_session']})")
        print(f"    • Sessions: {len(daily_data['sessions'])}")
        
        # Check if PM session completed the day
        pm_sessions = [s for s in daily_data['sessions'] if s['session_type'] in ['NY_PM', 'PM']]
        if pm_sessions:
            print(f"  ✅ PM Session Found: Completes July 29th daily structure")
            
            # Check for daily high/low significance
            for pm_session in pm_sessions:
                if pm_session['high'] == daily_data['daily_high']:
                    print(f"    🎯 PM Session Created Daily High: {pm_session['high']}")
                if pm_session['low'] == daily_data['daily_low']:
                    print(f"    🎯 PM Session Created Daily Low: {pm_session['low']}")
        else:
            print(f"  ⚠️ No PM Session Found in daily structures")
    else:
        print(f"  ❌ July 29th not found in daily structures")


def main():
    """Process PM July 29th through enhanced HTF Intelligence."""
    print("🌟 Processing PM July 29th with Enhanced HTF Intelligence")
    print("=" * 60)
    
    # Step 1: Manual HTF event extraction
    session_file = "/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYPM_Lvl-1_2025_07_29.json"
    htf_events, metadata = manually_extract_htf_events(session_file)
    
    if not htf_events:
        print("❌ No HTF events found in PM session")
        return
    
    # Step 2: Update HTF context files
    htf_file = update_htf_context_manually(htf_events, metadata)
    
    # Step 3: Calculate updated HTF intensity
    htf_intensity, intelligence_events = calculate_updated_htf_intensity()
    
    # Step 4: Analyze daily structures
    analyze_daily_structures()
    
    # Step 5: Summary
    print(f"\n🎯 PM July 29th Processing Complete:")
    print(f"  ✅ HTF Events Extracted: {len(htf_events)}")
    print(f"  ✅ HTF Context Updated: {htf_file.name}")
    print(f"  ✅ HTF Intensity Calculated: {htf_intensity:.2f}")
    print(f"  ✅ Total Intelligence Events: {len(intelligence_events)}")
    
    # Critical HTF Events Summary
    print(f"\n🔥 Critical HTF Events from PM July 29th:")
    for event in htf_events:
        print(f"  • {event['htf_significance']}")
        print(f"    Level: {event['level']}")
        print(f"    Context: \"{event['session_details']['context']}\"")
    
    print(f"\n🚀 Ready for July 30th Data Integration and July 31st HAWKS Predictions!")


if __name__ == "__main__":
    main()