#!/usr/bin/env python3
"""
SessionOrganism - Living Market System Coordinator
Integrates ParallelEventStreams with existing Grok pipeline systems to create
a unified, real-time market event timing prediction organism.
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
import threading
import time
from queue import Queue, Empty
import logging

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
try:
    from parallel_event_streams import ParallelEventStreams, ConvergedPrediction
    from market_state_hmm import MarketStateHMM, MarketState
    from src.tracker_state import TrackerStateManager
    from src.pipeline import GrokPipeline
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

@dataclass
class OrganismVitals:
    """Current vital signs of the session organism"""
    energy_level: float
    state_coherence: float  # How well all systems agree
    prediction_confidence: float
    system_health: float  # Overall system health (0-1)
    alert_status: str
    active_streams: int
    last_heartbeat: datetime

@dataclass
class EventAlert:
    """Real-time event alert from the organism"""
    alert_id: str
    event_type: str
    severity: str  # low, medium, high, critical
    countdown_timer: str
    time_window: Tuple[datetime, datetime]
    confidence: float
    triggering_systems: List[str]
    alert_message: str
    timestamp: datetime
    expires_at: datetime

@dataclass
class OrganismMemory:
    """Memory system for learning and adaptation"""
    successful_predictions: List[Dict[str, Any]]
    failed_predictions: List[Dict[str, Any]]
    pattern_recognition: Dict[str, float]
    adaptation_weights: Dict[str, float]
    last_learning_update: datetime

class SessionOrganism:
    """
    Central nervous system for the market analysis organism.
    Coordinates all subsystems for unified event timing prediction.
    """
    
    def __init__(self, session_data: dict, tracker_context: tuple = None):
        # Core session data
        self.session_data = session_data
        self.tracker_context = tracker_context
        
        # Initialize subsystems
        self.parallel_streams = ParallelEventStreams()
        self.tracker_manager = TrackerStateManager()
        self.grok_pipeline = None  # Will initialize if needed
        
        # Organism state
        self.is_alive = False
        self.vitals = OrganismVitals(
            energy_level=0.5,
            state_coherence=0.5,
            prediction_confidence=0.5,
            system_health=1.0,
            alert_status="monitoring",
            active_streams=0,
            last_heartbeat=datetime.now()
        )
        
        # Memory and learning system
        self.memory = OrganismMemory(
            successful_predictions=[],
            failed_predictions=[],
            pattern_recognition={},
            adaptation_weights={
                'hmm_weight': 0.33,
                'energy_weight': 0.33,
                'fvg_weight': 0.34
            },
            last_learning_update=datetime.now()
        )
        
        # Real-time systems
        self.event_queue = Queue()
        self.alert_callbacks = []
        self.heartbeat_interval = 30  # seconds
        self.prediction_interval = 60  # seconds
        
        # Threading control
        self.organism_thread = None
        self.heartbeat_thread = None
        self.stop_event = threading.Event()
        
        # Logging
        logging.basicConfig(level=logging.INFO, 
                          format='%(asctime)s - SessionOrganism - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
    def start_organism(self) -> bool:
        """Start the living market organism"""
        
        print("🌱 STARTING SESSION ORGANISM")
        print("=" * 35)
        
        if self.is_alive:
            print("⚠️ Organism already running")
            return False
            
        try:
            # Validate session data
            if not self._validate_session_data():
                print("❌ Invalid session data - cannot start organism")
                return False
                
            # Initialize subsystems
            self._initialize_subsystems()
            
            # Start background threads
            self.is_alive = True
            self.organism_thread = threading.Thread(target=self._organism_main_loop, daemon=True)
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            
            self.organism_thread.start()
            self.heartbeat_thread.start()
            
            print("✅ Session organism started successfully")
            print(f"   Session: {self.session_data.get('session_metadata', {}).get('session_type', 'Unknown')}")
            print(f"   Heartbeat: Every {self.heartbeat_interval}s")
            print(f"   Predictions: Every {self.prediction_interval}s")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to start organism: {e}")
            self.logger.error(f"Organism startup failed: {e}")
            return False
    
    def stop_organism(self):
        """Stop the session organism gracefully"""
        
        print("\n🛑 STOPPING SESSION ORGANISM")
        
        if not self.is_alive:
            print("⚠️ Organism not running")
            return
            
        # Signal stop
        self.stop_event.set()
        self.is_alive = False
        
        # Wait for threads to finish
        if self.organism_thread and self.organism_thread.is_alive():
            self.organism_thread.join(timeout=5)
            
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.heartbeat_thread.join(timeout=5)
            
        print("✅ Session organism stopped")
        
        # Generate final report
        self._generate_session_report()
    
    def _validate_session_data(self) -> bool:
        """Validate that session data contains required fields"""
        
        required_fields = ['session_metadata', 'price_data']
        
        for field in required_fields:
            if field not in self.session_data:
                self.logger.error(f"Missing required field: {field}")
                return False
                
        # Check for Grok enhanced data
        if 'grok_enhanced_calculations' not in self.session_data:
            self.logger.warning("No Grok enhanced calculations found - limited functionality")
            
        return True
    
    def _initialize_subsystems(self):
        """Initialize all organism subsystems"""
        
        print("🔧 Initializing organism subsystems...")
        
        # Initialize tracker state if context available
        if self.tracker_context:
            htf_tracker, fvg_tracker, liquidity_tracker = self.tracker_context
            self.tracker_state = self.tracker_manager.extract_tracker_context(
                htf_tracker, fvg_tracker, liquidity_tracker
            )
            print("   ✅ Tracker state initialized")
        else:
            self.tracker_state = {}
            print("   ⚠️ No tracker context - using defaults")
            
        # Test parallel streams
        try:
            test_results = self.parallel_streams.analyze_all_streams(
                self.session_data, self.tracker_context
            )
            self.vitals.active_streams = len(test_results)
            print(f"   ✅ Parallel streams initialized ({len(test_results)} active)")
        except Exception as e:
            print(f"   ⚠️ Parallel streams initialization issue: {e}")
            self.vitals.active_streams = 0
            
        # Initialize adaptation weights based on session character
        session_character = self.session_data.get('price_data', {}).get('session_character', '')
        self._adapt_weights_for_session(session_character)
        
        print("   ✅ All subsystems initialized")
    
    def _adapt_weights_for_session(self, session_character: str):
        """Adapt organism weights based on session character"""
        
        if 'expansion' in session_character.lower():
            self.memory.adaptation_weights['energy_weight'] = 0.4
            self.memory.adaptation_weights['hmm_weight'] = 0.35
            self.memory.adaptation_weights['fvg_weight'] = 0.25
            print("   🎯 Adapted for expansion session (energy priority)")
            
        elif 'consolidation' in session_character.lower():
            self.memory.adaptation_weights['hmm_weight'] = 0.4
            self.memory.adaptation_weights['fvg_weight'] = 0.35
            self.memory.adaptation_weights['energy_weight'] = 0.25
            print("   📊 Adapted for consolidation session (HMM priority)")
            
        else:
            # Balanced weights for neutral sessions
            self.memory.adaptation_weights = {
                'hmm_weight': 0.33,
                'energy_weight': 0.33,
                'fvg_weight': 0.34
            }
            print("   ⚖️ Using balanced weights for neutral session")
    
    def _organism_main_loop(self):
        """Main organism processing loop"""
        
        self.logger.info("Organism main loop started")
        
        while self.is_alive and not self.stop_event.is_set():
            try:
                # Update vitals
                self._update_vitals()
                
                # Run stream convergence analysis
                convergence = self._run_convergence_analysis()
                
                # Process convergence results
                if convergence:
                    self._process_convergence(convergence)
                
                # Check for alerts
                self._check_alert_conditions()
                
                # Update memory and learning
                if datetime.now().minute % 5 == 0:  # Every 5 minutes
                    self._update_organism_memory()
                
                # Sleep until next prediction cycle
                time.sleep(self.prediction_interval)
                
            except Exception as e:
                self.logger.error(f"Error in organism main loop: {e}")
                self.vitals.system_health *= 0.9  # Degrade health on errors
                time.sleep(self.prediction_interval)
    
    def _heartbeat_loop(self):
        """Organism heartbeat for vitals monitoring"""
        
        while self.is_alive and not self.stop_event.is_set():
            try:
                # Update heartbeat timestamp
                self.vitals.last_heartbeat = datetime.now()
                
                # Log vitals periodically
                if datetime.now().second % 60 == 0:  # Every minute
                    self._log_vitals()
                
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                self.logger.error(f"Heartbeat error: {e}")
                time.sleep(self.heartbeat_interval)
    
    def _update_vitals(self):
        """Update organism vital signs"""
        
        # Extract current energy from session data
        grok_data = self.session_data.get('grok_enhanced_calculations', {})
        unit_b = grok_data.get('unit_b_energy_structure', {})
        energy_accumulation = unit_b.get('energy_accumulation', {})
        energy_rate = energy_accumulation.get('energy_rate', 0.5)
        
        self.vitals.energy_level = min(1.0, energy_rate / 2.0)  # Normalize
        
        # Calculate state coherence (how well all systems agree)
        # This would be based on stream agreement from last convergence
        self.vitals.state_coherence = 0.8  # Placeholder - would be calculated from stream agreement
        
        # Update system health (degrades over time without successful predictions)
        time_since_heartbeat = (datetime.now() - self.vitals.last_heartbeat).total_seconds()
        if time_since_heartbeat > self.heartbeat_interval * 2:
            self.vitals.system_health *= 0.95  # Slight degradation if heartbeat is late
        
        # Overall system health
        self.vitals.system_health = min(1.0, max(0.1, self.vitals.system_health))
    
    def _run_convergence_analysis(self) -> Optional[ConvergedPrediction]:
        """Run parallel stream convergence analysis"""
        
        try:
            # Get stream results
            stream_results = self.parallel_streams.analyze_all_streams(
                self.session_data, self.tracker_context
            )
            
            if not stream_results:
                return None
                
            # Update active streams count
            self.vitals.active_streams = len(stream_results)
            
            # Attempt convergence
            convergence = self.parallel_streams.converge_predictions(stream_results)
            
            if convergence:
                self.vitals.prediction_confidence = convergence.overall_confidence
                self.vitals.state_coherence = convergence.stream_agreement
                
            return convergence
            
        except Exception as e:
            self.logger.error(f"Convergence analysis failed: {e}")
            return None
    
    def _process_convergence(self, convergence: ConvergedPrediction):
        """Process convergence results and generate alerts if needed"""
        
        # Update alert status based on convergence
        if convergence.alert_level == "critical":
            self.vitals.alert_status = "critical"
        elif convergence.alert_level == "high":
            self.vitals.alert_status = "high_alert"
        elif convergence.alert_level == "medium":
            self.vitals.alert_status = "monitoring_enhanced"
        else:
            self.vitals.alert_status = "monitoring"
        
        # Generate alert if significant
        if convergence.alert_level in ["high", "critical"]:
            alert = self._create_event_alert(convergence)
            self.event_queue.put(alert)
            
            # Trigger callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Alert callback failed: {e}")
    
    def _create_event_alert(self, convergence: ConvergedPrediction) -> EventAlert:
        """Create an event alert from convergence results"""
        
        alert_id = f"alert_{datetime.now().strftime('%H%M%S')}"
        
        # Create alert message
        time_window_str = f"{convergence.consensus_time_window[0].strftime('%H:%M')}-{convergence.consensus_time_window[1].strftime('%H:%M')}"
        alert_message = f"{convergence.alert_level.upper()}: {convergence.unified_event_type} predicted {time_window_str} (confidence: {convergence.overall_confidence:.0%})"
        
        # Set expiry time
        expires_at = convergence.consensus_time_window[1] + timedelta(minutes=10)
        
        return EventAlert(
            alert_id=alert_id,
            event_type=convergence.unified_event_type,
            severity=convergence.alert_level,
            countdown_timer=convergence.countdown_timer,
            time_window=convergence.consensus_time_window,
            confidence=convergence.overall_confidence,
            triggering_systems=convergence.contributing_streams,
            alert_message=alert_message,
            timestamp=datetime.now(),
            expires_at=expires_at
        )
    
    def _check_alert_conditions(self):
        """Check for special alert conditions"""
        
        # Check for system health issues
        if self.vitals.system_health < 0.7:
            health_alert = EventAlert(
                alert_id=f"health_{datetime.now().strftime('%H%M%S')}",
                event_type="system_health_degraded",
                severity="medium",
                countdown_timer="System health monitoring",
                time_window=(datetime.now(), datetime.now() + timedelta(minutes=5)),
                confidence=1.0 - self.vitals.system_health,
                triggering_systems=["organism_vitals"],
                alert_message=f"System health degraded to {self.vitals.system_health:.0%}",
                timestamp=datetime.now(),
                expires_at=datetime.now() + timedelta(minutes=10)
            )
            self.event_queue.put(health_alert)
        
        # Check for stream failures
        if self.vitals.active_streams < 2:
            stream_alert = EventAlert(
                alert_id=f"streams_{datetime.now().strftime('%H%M%S')}",
                event_type="insufficient_streams",
                severity="high",
                countdown_timer="Stream monitoring",
                time_window=(datetime.now(), datetime.now() + timedelta(minutes=2)),
                confidence=0.9,
                triggering_systems=["parallel_streams"],
                alert_message=f"Only {self.vitals.active_streams} streams active (need ≥2)",
                timestamp=datetime.now(),
                expires_at=datetime.now() + timedelta(minutes=5)
            )
            self.event_queue.put(stream_alert)
    
    def _update_organism_memory(self):
        """Update organism memory and learning systems"""
        
        # This would analyze recent predictions vs actual outcomes
        # For now, just update timestamp
        self.memory.last_learning_update = datetime.now()
        
        # In a full implementation, this would:
        # 1. Compare recent predictions to actual market events
        # 2. Update pattern recognition weights
        # 3. Adjust adaptation weights based on success/failure
        # 4. Store successful/failed predictions for future learning
    
    def _log_vitals(self):
        """Log current organism vitals"""
        
        self.logger.info(f"Organism Vitals - Energy: {self.vitals.energy_level:.2f}, "
                        f"Coherence: {self.vitals.state_coherence:.2f}, "
                        f"Health: {self.vitals.system_health:.2f}, "
                        f"Alert: {self.vitals.alert_status}")
    
    def get_current_vitals(self) -> OrganismVitals:
        """Get current organism vitals"""
        return self.vitals
    
    def get_recent_alerts(self, max_alerts: int = 10) -> List[EventAlert]:
        """Get recent alerts from the event queue"""
        
        alerts = []
        try:
            while len(alerts) < max_alerts:
                alert = self.event_queue.get_nowait()
                
                # Check if alert is still valid
                if datetime.now() < alert.expires_at:
                    alerts.append(alert)
                    
        except Empty:
            pass  # No more alerts in queue
            
        return alerts
    
    def add_alert_callback(self, callback: Callable[[EventAlert], None]):
        """Add callback function for real-time alerts"""
        self.alert_callbacks.append(callback)
    
    def get_organism_status(self) -> Dict[str, Any]:
        """Get comprehensive organism status"""
        
        return {
            'is_alive': self.is_alive,
            'session_info': {
                'session_type': self.session_data.get('session_metadata', {}).get('session_type', 'Unknown'),
                'session_character': self.session_data.get('price_data', {}).get('session_character', 'Unknown')
            },
            'vitals': asdict(self.vitals),
            'memory_stats': {
                'successful_predictions': len(self.memory.successful_predictions),
                'failed_predictions': len(self.memory.failed_predictions),
                'adaptation_weights': self.memory.adaptation_weights,
                'last_learning_update': self.memory.last_learning_update.isoformat()
            },
            'stream_performance': self.parallel_streams.get_stream_performance_summary(),
            'active_alerts': len(self.get_recent_alerts())
        }
    
    def _generate_session_report(self):
        """Generate final session report"""
        
        report = {
            'session_organism_report': {
                'session_metadata': self.session_data.get('session_metadata', {}),
                'final_vitals': asdict(self.vitals),
                'memory_summary': {
                    'total_predictions': len(self.memory.successful_predictions) + len(self.memory.failed_predictions),
                    'success_rate': len(self.memory.successful_predictions) / max(1, len(self.memory.successful_predictions) + len(self.memory.failed_predictions)),
                    'final_weights': self.memory.adaptation_weights
                },
                'stream_performance': self.parallel_streams.get_stream_performance_summary(),
                'report_timestamp': datetime.now().isoformat()
            }
        }
        
        output_file = f"session_organism_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
            
        print(f"📊 Session report saved to: {output_file}")

def main():
    """Test the SessionOrganism system"""
    
    print("🧪 SESSION ORGANISM TEST")
    print("=" * 30)
    
    # Load test data
    try:
        with open('ny_pm_grokEnhanced_2025_07_23.json', 'r') as f:
            session_data = json.load(f)
        with open('HTF_Context_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            htf_tracker = json.load(f)
        with open('FVG_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            fvg_tracker = json.load(f)
        with open('Liquidity_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            liquidity_tracker = json.load(f)
            
        tracker_context = (htf_tracker, fvg_tracker, liquidity_tracker)
        
    except FileNotFoundError as e:
        print(f"❌ Test data not found: {e}")
        return
    
    # Create and start organism
    organism = SessionOrganism(session_data, tracker_context)
    
    # Add alert callback for testing
    def alert_handler(alert: EventAlert):
        print(f"🚨 ALERT: {alert.alert_message}")
    
    organism.add_alert_callback(alert_handler)
    
    # Start organism
    if organism.start_organism():
        try:
            # Let it run for a test period
            print("\n⏱️ Running organism for 3 minutes (demo)...")
            time.sleep(180)  # 3 minutes
            
            # Check status
            status = organism.get_organism_status()
            print(f"\n📊 Organism Status:")
            print(f"   Health: {status['vitals']['system_health']:.2f}")
            print(f"   Alert Status: {status['vitals']['alert_status']}")
            print(f"   Active Streams: {status['vitals']['active_streams']}")
            
            # Check recent alerts
            alerts = organism.get_recent_alerts()
            print(f"\n🚨 Recent Alerts: {len(alerts)}")
            for alert in alerts[-3:]:  # Show last 3
                print(f"   {alert.severity.upper()}: {alert.event_type}")
            
        finally:
            # Always stop organism
            organism.stop_organism()
    
    print("\n✅ SessionOrganism test completed")

if __name__ == "__main__":
    main()