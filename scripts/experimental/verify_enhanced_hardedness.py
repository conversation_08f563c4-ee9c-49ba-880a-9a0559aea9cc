#!/usr/bin/env python3
"""
Enhanced Files Hardedness Verification Script
Checks accuracy, hardedness, and API truth of enhanced session files
"""

import json
from datetime import datetime

def check_hardedness(data, session_name):
    """Check if enhanced data contains real calculations vs fallback values"""
    print(f'\n🔍 {session_name} Session Analysis:')
    
    unit_a = data.get('grok_enhanced_calculations', {}).get('unit_a_foundation', {})
    unit_b = data.get('grok_enhanced_calculations', {}).get('unit_b_energy_structure', {})
    unit_c = data.get('grok_enhanced_calculations', {}).get('unit_c_advanced_dynamics', {})
    unit_d = data.get('grok_enhanced_calculations', {}).get('unit_d_integration', {})
    
    issues = []
    valid_indicators = []
    
    # 1. Check Unit A foundation_calculations (critical)
    foundation_calcs = unit_a.get('foundation_calculations', {})
    if not foundation_calcs:
        issues.append("❌ CRITICAL: foundation_calculations is EMPTY - API failure")
    else:
        valid_indicators.append(f"✅ Foundation calculations present: {len(foundation_calcs)} fields")
        print(f"   Foundation keys: {list(foundation_calcs.keys())}")
    
    # 2. Check energy rate (should not be fallback 1.0)
    energy_rate = unit_b.get('energy_accumulation', {}).get('energy_rate', 1.0)
    if energy_rate == 1.0:
        issues.append(f"❌ SUSPECT: energy_rate = {energy_rate} (fallback value)")
    else:
        valid_indicators.append(f"✅ Real energy_rate: {energy_rate}")
    
    # 3. Check momentum strength from Unit D
    extracted = unit_d.get('extracted_values', {})
    momentum_strength = extracted.get('momentum_strength', 0)
    if momentum_strength <= 0:
        issues.append(f"❌ SUSPECT: momentum_strength = {momentum_strength} (missing/fallback)")
    else:
        valid_indicators.append(f"✅ Real momentum_strength: {momentum_strength}")
    
    # 4. Check alpha_t bounds (should be realistic)
    alpha_t = unit_a.get('hybrid_volume', {}).get('alpha_t', 0)
    if not (0.8 <= alpha_t <= 1.0):
        issues.append(f"❌ SUSPECT: alpha_t = {alpha_t} (outside normal bounds 0.8-1.0)")
    else:
        valid_indicators.append(f"✅ Valid alpha_t: {alpha_t}")
    
    # 5. Check v_synthetic (should not be static ~128.66)
    v_synthetic = unit_a.get('hybrid_volume', {}).get('v_synthetic', 0)
    if abs(v_synthetic - 128.66) < 1.0:
        issues.append(f"❌ SUSPECT: v_synthetic = {v_synthetic} (static fallback ~128.66)")
    else:
        valid_indicators.append(f"✅ Calculated v_synthetic: {v_synthetic}")
    
    # 6. Check Unit C calculations exist
    unit_c_keys = list(unit_c.keys()) if unit_c else []
    if not unit_c_keys:
        issues.append("❌ SUSPECT: Unit C calculations missing")
    else:
        valid_indicators.append(f"✅ Unit C calculations: {len(unit_c_keys)} sections")
    
    # 7. Check structural integrity
    structural_integrity = unit_b.get('structural_integrity', 0)
    if structural_integrity != 1.0:
        issues.append(f"❌ SUSPECT: structural_integrity = {structural_integrity} (should be 1.0)")
    else:
        valid_indicators.append(f"✅ Structural integrity: {structural_integrity}")
    
    # Print results
    for indicator in valid_indicators:
        print(f"   {indicator}")
    
    for issue in issues:
        print(f"   {issue}")
    
    # API truth check - look for signs of real API processing
    processing_times = []
    for unit_name in ['unit_a_foundation', 'unit_b_energy_structure', 'unit_c_advanced_dynamics', 'unit_d_integration']:
        unit_data = data.get('grok_enhanced_calculations', {}).get(unit_name, {})
        proc_time = unit_data.get('processing_time_ms', 0)
        if proc_time > 0:
            processing_times.append(proc_time)
    
    total_processing_time = sum(processing_times)
    if total_processing_time > 0:
        valid_indicators.append(f"✅ API processing verified: {total_processing_time}ms total")
        print(f"   ✅ Processing times: {processing_times}ms")
    else:
        issues.append("❌ SUSPECT: No processing times recorded - possible local fallback")
    
    return len(issues) == 0, issues, valid_indicators

def main():
    print('=== ENHANCED FILES HARDEDNESS VERIFICATION ===')
    print(f'Timestamp: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    # Load both enhanced files
    try:
        with open('Lunch_session_enhanced_2025_07_25.json', 'r') as f:
            lunch_data = json.load(f)
        
        with open('NYPM_session_enhanced_2025_07_25.json', 'r') as f:
            pm_data = json.load(f)
    except FileNotFoundError as e:
        print(f"❌ ERROR: Could not load enhanced files: {e}")
        return
    
    # Check both sessions
    lunch_valid, lunch_issues, lunch_valid_indicators = check_hardedness(lunch_data, 'LUNCH')
    pm_valid, pm_issues, pm_valid_indicators = check_hardedness(pm_data, 'NYPM')
    
    print(f'\n🏁 OVERALL VALIDATION SUMMARY:')
    print(f'Lunch Session: {"✅ VALID" if lunch_valid else "❌ HAS ISSUES"}')
    print(f'PM Session: {"✅ VALID" if pm_valid else "❌ HAS ISSUES"}')
    
    if lunch_valid and pm_valid:
        print('\n🎉 RESULT: Both enhanced files contain REAL calculations and API processing')
        print('✅ Ready for production use')
    else:
        print('\n⚠️  RESULT: Issues detected - files may contain fallback values')
        print('🔧 Recommend re-processing through preprocessing agent')
    
    # Summary statistics
    total_valid_indicators = len(lunch_valid_indicators) + len(pm_valid_indicators)
    total_issues = len(lunch_issues) + len(pm_issues)
    
    print(f'\n📊 STATISTICS:')
    print(f'Valid indicators: {total_valid_indicators}')
    print(f'Issues found: {total_issues}')
    print(f'Quality score: {total_valid_indicators}/{total_valid_indicators + total_issues} ({100 * total_valid_indicators/(total_valid_indicators + total_issues):.1f}%)')

if __name__ == "__main__":
    main()