#!/usr/bin/env python3
"""
Enhanced Ensemble with Feedback Learning
Implements Opus 4's federated learning concept - systems learn from outcomes
without sharing raw data, building collective intelligence.
"""

import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import pickle
import os

try:
    from adaptive_ensemble_predictor import AdaptivePredictor, EnsemblePrediction
    from hmm_monte_carlo_integration import HMMMonteCarloIntegrator
    from performance_tracker import PerformanceTracker
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

@dataclass
class FeedbackRecord:
    """Record of prediction outcome for federated learning"""
    prediction_id: str
    predictor_name: str
    predicted_value: float
    actual_value: float
    error: float
    accuracy_score: float
    market_context: str
    timestamp: datetime
    session_characteristics: Dict[str, Any]

class FederatedLearningEnsemble:
    """Ensemble with federated learning capabilities"""
    
    def __init__(self, learning_rate: float = 0.1):
        self.learning_rate = learning_rate
        
        # Core predictors
        self.adaptive_ensemble = AdaptivePredictor()
        self.hmm_monte_carlo = HMMMonteCarloIntegrator()
        
        # Federated learning components
        self.feedback_history = []
        self.predictor_trust_scores = {
            'monte_carlo': 1.0,
            'hmm': 1.0,
            'ensemble': 1.0,
            'integrated': 1.0
        }
        
        # Collective intelligence patterns
        self.market_patterns = {
            'high_volatility': {'best_predictor': 'monte_carlo', 'confidence': 0.0},
            'pre_cascade': {'best_predictor': 'hmm', 'confidence': 0.0},
            'consolidation': {'best_predictor': 'ensemble', 'confidence': 0.0},
            'news_impacted': {'best_predictor': 'integrated', 'confidence': 0.0}
        }
        
        # Performance tracking
        self.prediction_count = 0
        self.learning_episodes = 0
        
    def federated_predict(self, session_data: dict, tracker_context: tuple = None,
                         news_impacted: bool = False, news_data: Dict = None) -> Dict:
        """Generate prediction using federated learning insights"""
        
        print("🧠 FEDERATED LEARNING ENSEMBLE PREDICTION")
        print("=" * 50)
        
        # Step 1: Generate predictions from all systems
        predictions = {}
        
        # Monte Carlo prediction
        try:
            from grok_monte_carlo_package import universal_timing_formula
            monte_carlo_result = universal_timing_formula(
                volatility=0.1,  # Would extract from session_data
                distance=1.0,
                news_impacted=news_impacted,
                news_data=news_data
            )
            predictions['monte_carlo'] = monte_carlo_result['predicted_minutes']
        except Exception as e:
            print(f"⚠️ Monte Carlo prediction failed: {e}")
            predictions['monte_carlo'] = 0.5
        
        # HMM prediction
        try:
            hmm_result = self.hmm_monte_carlo.integrated_predict(
                session_data, tracker_context, news_impacted, news_data
            )
            predictions['hmm'] = hmm_result.state_adjusted_prediction
        except Exception as e:
            print(f"⚠️ HMM prediction failed: {e}")
            predictions['hmm'] = 0.8
        
        # Ensemble prediction
        try:
            ensemble_result = self.adaptive_ensemble.predict(
                session_data, tracker_context
            )
            predictions['ensemble'] = ensemble_result.predicted_close
        except Exception as e:
            print(f"⚠️ Ensemble prediction failed: {e}")
            predictions['ensemble'] = 0.6
        
        # Step 2: Determine market context
        market_context = self._classify_market_context(session_data, news_impacted)
        
        # Step 3: Apply federated learning weights
        federated_weights = self._calculate_federated_weights(market_context)
        
        # Step 4: Generate weighted prediction
        weighted_prediction = 0.0
        total_weight = 0.0
        
        for predictor, prediction in predictions.items():
            if predictor in federated_weights:
                weight = federated_weights[predictor]
                weighted_prediction += prediction * weight
                total_weight += weight
        
        final_prediction = weighted_prediction / total_weight if total_weight > 0 else 0.5
        
        print(f"1️⃣ Market Context: {market_context}")
        print(f"2️⃣ Individual Predictions:")
        for predictor, prediction in predictions.items():
            weight = federated_weights.get(predictor, 0.0)
            trust = self.predictor_trust_scores.get(predictor, 1.0)
            print(f"   {predictor}: {prediction:.3f} (weight: {weight:.2f}, trust: {trust:.2f})")
        
        print(f"3️⃣ Federated Prediction: {final_prediction:.3f}")
        
        # Step 5: Generate confidence based on agreement
        prediction_values = list(predictions.values())
        agreement_score = 1.0 - (np.std(prediction_values) / np.mean(prediction_values))
        confidence = min(0.95, agreement_score * 0.8 + 0.2)
        
        # Step 6: Create prediction record for learning
        prediction_id = f"fed_{datetime.now().strftime('%H%M%S%f')}"
        
        result = {
            'prediction_id': prediction_id,
            'federated_prediction': final_prediction,
            'individual_predictions': predictions,
            'federated_weights': federated_weights,
            'market_context': market_context,
            'confidence': confidence,
            'trust_scores': self.predictor_trust_scores.copy(),
            'collective_intelligence': self._get_collective_insights(market_context),
            'learning_metadata': {
                'prediction_count': self.prediction_count,
                'learning_episodes': self.learning_episodes,
                'patterns_learned': len([p for p in self.market_patterns.values() if p['confidence'] > 0.5])
            }
        }
        
        self.prediction_count += 1
        return result
    
    def _classify_market_context(self, session_data: dict, news_impacted: bool) -> str:
        """Classify market context for federated learning"""
        
        price_data = session_data.get('price_data', {})
        session_range = price_data.get('range', 100)
        
        if news_impacted:
            return 'news_impacted'
        elif session_range > 150:
            return 'high_volatility'
        elif session_range < 80:
            return 'consolidation'
        else:
            # Check for HMM state if available
            grok_data = session_data.get('grok_enhanced_calculations', {})
            if grok_data:
                # Simulate HMM state detection
                unit_b = grok_data.get('unit_b_energy_structure', {})
                energy_rate = unit_b.get('energy_accumulation', {}).get('energy_rate', 0.5)
                if energy_rate > 0.7:
                    return 'pre_cascade'
        
        return 'consolidation'
    
    def _calculate_federated_weights(self, market_context: str) -> Dict[str, float]:
        """Calculate weights based on federated learning insights"""
        
        # Start with trust-based weights
        base_weights = {
            'monte_carlo': self.predictor_trust_scores['monte_carlo'] * 0.4,
            'hmm': self.predictor_trust_scores['hmm'] * 0.3,
            'ensemble': self.predictor_trust_scores['ensemble'] * 0.2,
            'integrated': self.predictor_trust_scores['integrated'] * 0.1
        }
        
        # Apply context-specific learning
        if market_context in self.market_patterns:
            pattern = self.market_patterns[market_context]
            if pattern['confidence'] > 0.5:  # Learned pattern
                best_predictor = pattern['best_predictor']
                confidence = pattern['confidence']
                
                # Boost best predictor weight
                if best_predictor in base_weights:
                    boost = confidence * 0.3  # Up to 30% boost
                    base_weights[best_predictor] += boost
                    
                    # Reduce others proportionally
                    reduction_per_other = boost / (len(base_weights) - 1)
                    for predictor in base_weights:
                        if predictor != best_predictor:
                            base_weights[predictor] = max(0.05, base_weights[predictor] - reduction_per_other)
        
        # Normalize weights
        total_weight = sum(base_weights.values())
        if total_weight > 0:
            for predictor in base_weights:
                base_weights[predictor] /= total_weight
        
        return base_weights
    
    def record_outcome(self, prediction_result: Dict, actual_minutes: float) -> Dict:
        """Record prediction outcome for federated learning"""
        
        prediction_id = prediction_result['prediction_id']
        federated_prediction = prediction_result['federated_prediction']
        individual_predictions = prediction_result['individual_predictions']
        market_context = prediction_result['market_context']
        
        # Calculate errors
        federated_error = abs(federated_prediction - actual_minutes)
        individual_errors = {
            predictor: abs(prediction - actual_minutes)
            for predictor, prediction in individual_predictions.items()
        }
        
        print(f"📚 FEDERATED LEARNING OUTCOME RECORDED")
        print(f"   Prediction ID: {prediction_id}")
        print(f"   Actual: {actual_minutes:.3f} minutes")
        print(f"   Federated Error: {federated_error:.3f} minutes")
        
        # Update trust scores based on individual performance
        for predictor, error in individual_errors.items():
            if predictor in self.predictor_trust_scores:
                # Simple learning: reduce trust for higher errors
                if error < 0.5:  # Good prediction
                    self.predictor_trust_scores[predictor] = min(1.5, 
                        self.predictor_trust_scores[predictor] + self.learning_rate * 0.1)
                elif error > 1.0:  # Poor prediction
                    self.predictor_trust_scores[predictor] = max(0.1, 
                        self.predictor_trust_scores[predictor] - self.learning_rate * 0.2)
                
                print(f"   {predictor}: {error:.3f} error → trust: {self.predictor_trust_scores[predictor]:.2f}")
        
        # Update market pattern learning
        best_predictor = min(individual_errors, key=individual_errors.get)
        best_error = individual_errors[best_predictor]
        
        if best_error < 0.8:  # Good enough to learn from
            pattern = self.market_patterns[market_context]
            if pattern['best_predictor'] == best_predictor:
                # Reinforce existing pattern
                pattern['confidence'] = min(1.0, pattern['confidence'] + self.learning_rate)
            else:
                # New pattern emerging
                pattern['best_predictor'] = best_predictor
                pattern['confidence'] = self.learning_rate
            
            print(f"   Pattern Learning: {market_context} → {best_predictor} (confidence: {pattern['confidence']:.2f})")
        
        # Record feedback
        feedback = FeedbackRecord(
            prediction_id=prediction_id,
            predictor_name='federated_ensemble',
            predicted_value=federated_prediction,
            actual_value=actual_minutes,
            error=federated_error,
            accuracy_score=max(0, 1.0 - federated_error),
            market_context=market_context,
            timestamp=datetime.now(),
            session_characteristics={
                'trust_scores': self.predictor_trust_scores.copy(),
                'pattern_confidences': {k: v['confidence'] for k, v in self.market_patterns.items()}
            }
        )
        
        self.feedback_history.append(feedback)
        self.learning_episodes += 1
        
        # Keep only last 100 feedback records
        if len(self.feedback_history) > 100:
            self.feedback_history = self.feedback_history[-100:]
        
        return {
            'federated_error': federated_error,
            'best_individual_predictor': best_predictor,
            'best_individual_error': best_error,
            'trust_scores_updated': self.predictor_trust_scores.copy(),
            'pattern_learning_progress': {k: v['confidence'] for k, v in self.market_patterns.items()}
        }
    
    def _get_collective_insights(self, market_context: str) -> Dict:
        """Get collective intelligence insights"""
        
        insights = {
            'learned_patterns': 0,
            'most_trusted_predictor': max(self.predictor_trust_scores, key=self.predictor_trust_scores.get),
            'context_specific_best': 'unknown',
            'learning_progress': 0.0
        }
        
        # Count learned patterns
        insights['learned_patterns'] = len([p for p in self.market_patterns.values() if p['confidence'] > 0.5])
        
        # Context-specific best
        if market_context in self.market_patterns:
            pattern = self.market_patterns[market_context]
            if pattern['confidence'] > 0.5:
                insights['context_specific_best'] = pattern['best_predictor']
        
        # Overall learning progress
        total_confidence = sum(p['confidence'] for p in self.market_patterns.values())
        max_confidence = len(self.market_patterns) * 1.0
        insights['learning_progress'] = total_confidence / max_confidence
        
        return insights
    
    def save_learning_state(self, filepath: str = "federated_learning_state.pkl"):
        """Save learning state for persistence"""
        
        state = {
            'predictor_trust_scores': self.predictor_trust_scores,
            'market_patterns': self.market_patterns,
            'feedback_history': self.feedback_history[-50:],  # Save last 50
            'learning_metadata': {
                'prediction_count': self.prediction_count,
                'learning_episodes': self.learning_episodes,
                'learning_rate': self.learning_rate
            }
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
        
        print(f"💾 Federated learning state saved to {filepath}")
    
    def load_learning_state(self, filepath: str = "federated_learning_state.pkl"):
        """Load learning state for persistence"""
        
        if os.path.exists(filepath):
            with open(filepath, 'rb') as f:
                state = pickle.load(f)
            
            self.predictor_trust_scores = state['predictor_trust_scores']
            self.market_patterns = state['market_patterns']
            self.feedback_history = state['feedback_history']
            
            metadata = state['learning_metadata']
            self.prediction_count = metadata['prediction_count']
            self.learning_episodes = metadata['learning_episodes']
            self.learning_rate = metadata['learning_rate']
            
            print(f"📚 Federated learning state loaded from {filepath}")
            print(f"   Predictions: {self.prediction_count}, Episodes: {self.learning_episodes}")
        else:
            print(f"⚠️ Learning state file not found: {filepath}")

def test_federated_ensemble():
    """Test federated learning ensemble"""
    
    print("🧪 TESTING FEDERATED LEARNING ENSEMBLE")
    print("=" * 45)
    
    # Initialize ensemble
    fed_ensemble = FederatedLearningEnsemble()
    
    # Test prediction with simulated data
    session_data = {
        'price_data': {'range': 180},  # High volatility
        'grok_enhanced_calculations': {
            'unit_b_energy_structure': {
                'energy_accumulation': {'energy_rate': 0.9}
            }
        }
    }
    
    tracker_context = ({}, {'t_memory': 4.0}, {})
    
    # Generate prediction
    result = fed_ensemble.federated_predict(session_data, tracker_context, news_impacted=True)
    
    # Simulate outcome and record learning
    actual_outcome = 0.3  # Simulated actual timing
    learning_result = fed_ensemble.record_outcome(result, actual_outcome)
    
    print(f"\n📊 LEARNING RESULTS:")
    for key, value in learning_result.items():
        if key != 'trust_scores_updated':
            print(f"   {key}: {value}")
    
    # Show collective insights
    insights = result['collective_intelligence']
    print(f"\n🧠 COLLECTIVE INTELLIGENCE:")
    for key, value in insights.items():
        print(f"   {key}: {value}")
    
    # Save learning state
    fed_ensemble.save_learning_state()
    
    return fed_ensemble, result

if __name__ == "__main__":
    test_federated_ensemble()