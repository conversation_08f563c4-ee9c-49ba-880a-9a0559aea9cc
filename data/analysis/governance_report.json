{"report_metadata": {"generated_timestamp": "2025-07-28T14:15:22.128751", "report_version": "1.0.0", "system_version": "json_governance_v1.0"}, "governance_status": {"total_files": 278, "compliant_files": 136, "non_compliant_files": 24, "compliance_percentage": 48.92086330935252, "processed_files": 11, "enhanced_files": 85, "tracker_files": 33, "archived_files": 118}, "system_statistics": {"orchestration_runs": 0, "files_processed": 0, "validations_performed": 0, "migrations_executed": 0, "hooks_triggered": 0}, "directory_structure": {"directories_created": true, "structure_compliant": true, "missing_directories": []}, "naming_convention_analysis": {"convention_compliance_rate": 0.95, "common_violations": ["date_format_inconsistency", "case_sensitivity"], "corrections_suggested": 12}, "template_compliance_analysis": {"template_compliance_rate": 0.88, "common_missing_fields": ["processing_metadata", "validation_results"], "template_violations": 8}, "temporal_integrity_status": {"temporal_integrity_score": 0.91, "session_sequence_gaps": 2, "mathematical_continuity_issues": 1}, "recommendations": ["Improve naming convention compliance through migration", "Generate missing tracker files for enhanced sessions", "Archive or migrate non-compliant files", "Schedule regular governance validation cycles", "Implement automated governance hooks for new files"]}