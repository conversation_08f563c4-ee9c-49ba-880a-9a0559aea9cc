{"evolution_metadata": {"generations_run": 1, "population_size": 20, "success_threshold": 5.0, "base_formula": "T_memory^1.5 * FVG_density", "evolution_completed": "2025-07-25T14:59:02.442655"}, "best_formula": {"formula_id": "grok4_enhanced_001", "formula_code": "\n# Grok 4's Enhanced Formula\ntime_to_event = 22.5 / max(0.1, gamma_enhanced) * (1 - 0.2 * fvg_proximity) * math.exp(-volatility_index / max(0.1, t_memory))\n\n# Options expiry magnetism (when < 10 minutes to :00 or :30)\nminutes_to_half_hour = min(abs(30 - (predicted_time_minutes % 30)), predicted_time_minutes % 30)\nif minutes_to_half_hour < 10:\n    time_to_event -= (minutes_to_half_hour / 30) * 0.3\n\nresult = time_to_event\n", "exponent": 1.0, "additional_factors": ["volatility_adjustment", "options_expiry_magnetism"], "combination_type": "enhanced", "performance_score": 0.9521471048333334, "timing_accuracy_minutes": 0.7852895166666667, "generation": 0}, "evolution_progress": [{"generation": 0, "best_fitness": 0.9521471048333334, "best_timing_accuracy": 0.7852895166666667, "formula_id": "grok4_enhanced_001"}], "final_population": [{"formula_id": "grok4_enhanced_001", "performance_score": 0.9521471048333334, "timing_accuracy_minutes": 0.7852895166666667, "formula_code": "\n# Grok 4's Enhanced Formula\ntime_to_event = 22.5 / max(0.1, gamma_enhanced) * (1 - 0.2 * fvg_proximity) * math.exp(-volatility_index / max(0.1, t_memory))\n\n# Options expiry magnetism (when < 10 minutes to :00 or :30)\nminutes_to_half_hour = min(abs(30 - (predicted_time_minutes % 30)), predicted_time_minutes % 30)\nif minutes_to_half_hour < 10:\n    time_to_event -= (minutes_to_half_hour / 30) * 0.3\n\nresult = time_to_event\n"}, {"formula_id": "gen0_formula_04", "performance_score": 0.9370513888333334, "timing_accuracy_minutes": 2.2948611166666666, "formula_code": "result = t_memory**1.3 * fvg_density * consolidation_strength * fvg_cluster_density"}, {"formula_id": "gen0_formula_10", "performance_score": 0.6141592165, "timing_accuracy_minutes": 14.58407835, "formula_code": "result = t_memory**1.5 * fvg_density * math.log(1 + options_proximity) * math.log(1 + session_character_weight)"}, {"formula_id": "gen0_formula_01", "performance_score": 0.42910323516666665, "timing_accuracy_minutes": 33.089676483333335, "formula_code": "result = t_memory**1.5 * fvg_density * math.log(1 + session_character_weight)"}, {"formula_id": "gen0_formula_18", "performance_score": 0.4269537711666667, "timing_accuracy_minutes": 33.30462288333333, "formula_code": "result = t_memory**1.5 * fvg_density * fvg_cluster_density * liquidity_magnetic_pull"}]}