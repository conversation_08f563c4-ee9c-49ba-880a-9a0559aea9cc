{"analysis_metadata": {"total_formulas_tested": 4, "test_cases_used": 1, "analysis_timestamp": "2025-07-25T10:36:31.723799"}, "formula_rankings": [{"rank": 1, "formula_id": "exponential_v1", "fitness_score": 0.5128082389999999, "mean_timing_error": 28.719176100000002, "success_rate": 0.0, "quality": "Poor - Significant timing errors"}, {"rank": 2, "formula_id": "base_formula", "fitness_score": 0.35, "mean_timing_error": 45.0, "success_rate": 0.0, "quality": "Poor - Significant timing errors"}, {"rank": 3, "formula_id": "enhanced_v1", "fitness_score": 0.35, "mean_timing_error": 45.0, "success_rate": 0.0, "quality": "Poor - Significant timing errors"}, {"rank": 4, "formula_id": "enhanced_v2", "fitness_score": 0.35, "mean_timing_error": 45.0, "success_rate": 0.0, "quality": "Poor - Significant timing errors"}], "best_formula": {"formula_id": "exponential_v1", "formula_code": "result = t_memory**1.3 * fvg_density * math.exp(options_proximity * 0.1)", "fitness_metrics": {"fitness_score": 0.5128082389999999, "mean_timing_error_minutes": 28.719176100000002, "median_timing_error_minutes": 28.719176100000002, "success_rate": 0.0, "consistency_score": 1.0, "total_tests": 1, "successful_tests": 0, "quality_assessment": "Poor - Significant timing errors"}}, "detailed_results": [{"formula_id": "exponential_v1", "formula_code": "result = t_memory**1.3 * fvg_density * math.exp(options_proximity * 0.1)", "test_results": [{"test_case": "july23_cascade_validation", "predicted_timing": "14:13:43", "actual_timing": "13:45:00", "timing_error_minutes": 28.719176100000002, "success": false, "formula_result": 29.14611740053309, "parameters_used": {"t_memory": 15.0, "fvg_density": 0.8, "volatility_index": 1.7809723157308166}, "context": "July 23rd 13:45 cascade - proven validation case"}], "fitness_metrics": {"fitness_score": 0.5128082389999999, "mean_timing_error_minutes": 28.719176100000002, "median_timing_error_minutes": 28.719176100000002, "success_rate": 0.0, "consistency_score": 1.0, "total_tests": 1, "successful_tests": 0, "quality_assessment": "Poor - Significant timing errors"}, "evaluation_timestamp": "2025-07-25T10:36:31.723789"}, {"formula_id": "base_formula", "formula_code": "result = t_memory**1.5 * fvg_density", "test_results": [{"test_case": "july23_cascade_validation", "predicted_timing": "14:30:00", "actual_timing": "13:45:00", "timing_error_minutes": 45.0, "success": false, "formula_result": 46.47580015448901, "parameters_used": {"t_memory": 15.0, "fvg_density": 0.8, "volatility_index": 1.7809723157308166}, "context": "July 23rd 13:45 cascade - proven validation case"}], "fitness_metrics": {"fitness_score": 0.35, "mean_timing_error_minutes": 45.0, "median_timing_error_minutes": 45.0, "success_rate": 0.0, "consistency_score": 1.0, "total_tests": 1, "successful_tests": 0, "quality_assessment": "Poor - Significant timing errors"}, "evaluation_timestamp": "2025-07-25T10:36:31.723000"}, {"formula_id": "enhanced_v1", "formula_code": "result = t_memory**1.7 * fvg_density * volatility_index", "test_results": [{"test_case": "july23_cascade_validation", "predicted_timing": "14:30:00", "actual_timing": "13:45:00", "timing_error_minutes": 45.0, "success": false, "formula_result": 142.26638494469734, "parameters_used": {"t_memory": 15.0, "fvg_density": 0.8, "volatility_index": 1.7809723157308166}, "context": "July 23rd 13:45 cascade - proven validation case"}], "fitness_metrics": {"fitness_score": 0.35, "mean_timing_error_minutes": 45.0, "median_timing_error_minutes": 45.0, "success_rate": 0.0, "consistency_score": 1.0, "total_tests": 1, "successful_tests": 0, "quality_assessment": "Poor - Significant timing errors"}, "evaluation_timestamp": "2025-07-25T10:36:31.723292"}, {"formula_id": "enhanced_v2", "formula_code": "result = t_memory**2.0 * fvg_density + consolidation_strength", "test_results": [{"test_case": "july23_cascade_validation", "predicted_timing": "14:30:00", "actual_timing": "13:45:00", "timing_error_minutes": 45.0, "success": false, "formula_result": 180.1, "parameters_used": {"t_memory": 15.0, "fvg_density": 0.8, "volatility_index": 1.7809723157308166}, "context": "July 23rd 13:45 cascade - proven validation case"}], "fitness_metrics": {"fitness_score": 0.35, "mean_timing_error_minutes": 45.0, "median_timing_error_minutes": 45.0, "success_rate": 0.0, "consistency_score": 1.0, "total_tests": 1, "successful_tests": 0, "quality_assessment": "Poor - Significant timing errors"}, "evaluation_timestamp": "2025-07-25T10:36:31.723538"}]}