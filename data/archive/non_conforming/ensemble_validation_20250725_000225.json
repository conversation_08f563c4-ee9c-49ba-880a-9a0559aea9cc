{"validation_metadata": {"validation_type": "comprehensive_ensemble_validation", "timestamp": "2025-07-25T00:02:25.033766", "total_tests": 3}, "test_results": {"consolidation_prevention": {"test_name": "consolidation_prevention", "ensemble_error_pct": 44.468434604173616, "best_component_error_pct": 1.1181538461555296, "worst_component_error_pct": 99.64151920528876, "ensemble_vs_best_component": 43.35028075801809, "active_predictors": {"base_monte_carlo": 0.44, "expansion_enhanced": 0.56}, "strategy_used": "expansion_focused", "prevented_misapplication": true, "quality_grade": "poor"}, "performance_learning": {"test_name": "performance_learning", "ensemble_error_pct": 0.0, "best_component_error_pct": 1.1, "worst_component_error_pct": 123.1, "ensemble_vs_best_component": 0.0, "active_predictors": {"expansion_enhanced": 0.6447730461012318, "base_monte_carlo": 0.06522528814525405, "consolidation_scaled": 0.29000166575351416}, "strategy_used": "performance_based_weighting", "prevented_misapplication": "True", "quality_grade": "excellent"}, "context_detection": {"test_name": "context_detection", "ensemble_error_pct": 0.0, "best_component_error_pct": 0.0, "worst_component_error_pct": 0.0, "ensemble_vs_best_component": 0.0, "active_predictors": {}, "strategy_used": "context_detection", "prevented_misapplication": true, "quality_grade": "excellent"}}, "validation_report": "📋 ENSEMBLE VALIDATION REPORT\n===================================\nGenerated: 2025-07-25 00:02:25\n\n🧪 CONSOLIDATION PREVENTION:\n   Quality: Poor\n   Prevention Success: ✅\n   Strategy: expansion_focused\n   Ensemble Error: 44.5%\n   Best Component: 1.1%\n   Vs Best Component: +43.4%\n\n🧪 PERFORMANCE LEARNING:\n   Quality: Excellent\n   Prevention Success: ✅\n   Strategy: performance_based_weighting\n\n🧪 CONTEXT DETECTION:\n   Quality: Excellent\n   Prevention Success: ✅\n   Strategy: context_detection\n\n🏆 OVERALL ASSESSMENT:\n   Score: 2.0/3 (67%)\n   Grade: D (Needs Improvement)\n\n🎯 KEY ACHIEVEMENTS:\n   ✅ Prevented consolidation→expansion misapplication\n   ✅ Performance-based weight adaptation working\n   ✅ Accurate context detection"}