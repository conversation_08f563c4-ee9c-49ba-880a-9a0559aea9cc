{"evolution_metadata": {"generations_run": 5, "population_size": 20, "success_threshold": 5.0, "base_formula": "T_memory^1.5 * FVG_density", "evolution_completed": "2025-07-25T14:54:22.188942"}, "best_formula": {"formula_id": "gen4_formula_18", "formula_code": "result = t_memory**1.3 * fvg_density * volatility_index * liquidity_magnetic_pull * consolidation_strength", "exponent": 1.3, "additional_factors": ["volatility_index", "liquidity_magnetic_pull", "consolidation_strength"], "combination_type": "multiplicative", "performance_score": 0.9454879064999999, "timing_accuracy_minutes": 1.4512093499999998, "generation": 4}, "evolution_progress": [{"generation": 0, "best_fitness": 0.6302090881666667, "best_timing_accuracy": 12.979091183333333, "formula_id": "gen0_formula_02"}, {"generation": 1, "best_fitness": 0.657143924, "best_timing_accuracy": 10.285607599999999, "formula_id": "gen1_formula_17"}, {"generation": 2, "best_fitness": 0.657143924, "best_timing_accuracy": 10.285607599999999, "formula_id": "gen1_formula_17"}, {"generation": 3, "best_fitness": 0.657143924, "best_timing_accuracy": 10.285607599999999, "formula_id": "gen1_formula_17"}, {"generation": 4, "best_fitness": 0.9454879064999999, "best_timing_accuracy": 1.4512093499999998, "formula_id": "gen4_formula_18"}], "final_population": [{"formula_id": "gen4_formula_18", "performance_score": 0.9454879064999999, "timing_accuracy_minutes": 1.4512093499999998, "formula_code": "result = t_memory**1.3 * fvg_density * volatility_index * liquidity_magnetic_pull * consolidation_strength"}, {"formula_id": "gen1_formula_17", "performance_score": 0.657143924, "timing_accuracy_minutes": 10.285607599999999, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + session_character_weight) * math.log(1 + liquidity_magnetic_pull) * math.log(1 + consolidation_strength)"}, {"formula_id": "gen2_formula_11", "performance_score": 0.657143924, "timing_accuracy_minutes": 10.285607599999999, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + session_character_weight) * math.log(1 + liquidity_magnetic_pull) * math.log(1 + consolidation_strength)"}, {"formula_id": "gen2_formula_19", "performance_score": 0.657143924, "timing_accuracy_minutes": 10.285607599999999, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + session_character_weight) * math.log(1 + liquidity_magnetic_pull) * math.log(1 + consolidation_strength)"}, {"formula_id": "gen2_formula_20", "performance_score": 0.657143924, "timing_accuracy_minutes": 10.285607599999999, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + session_character_weight) * math.log(1 + liquidity_magnetic_pull) * math.log(1 + consolidation_strength)"}]}