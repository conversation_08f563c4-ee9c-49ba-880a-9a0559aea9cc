{"evolution_metadata": {"generations_run": 5, "population_size": 20, "success_threshold": 5.0, "base_formula": "T_memory^1.5 * FVG_density", "evolution_completed": "2025-07-25T10:36:26.573841"}, "best_formula": {"formula_id": "gen0_formula_15", "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + t_memory_acceleration) * math.log(1 + session_character_weight) * math.log(1 + expansion_momentum)", "exponent": 1.3, "additional_factors": ["t_memory_acceleration", "session_character_weight", "expansion_momentum"], "combination_type": "logarithmic", "performance_score": 0.646337596, "timing_accuracy_minutes": 11.3662404, "generation": 0}, "evolution_progress": [{"generation": 0, "best_fitness": 0.646337596, "best_timing_accuracy": 11.3662404, "formula_id": "gen0_formula_15"}, {"generation": 1, "best_fitness": 0.646337596, "best_timing_accuracy": 11.3662404, "formula_id": "gen0_formula_15"}, {"generation": 2, "best_fitness": 0.646337596, "best_timing_accuracy": 11.3662404, "formula_id": "gen0_formula_15"}, {"generation": 3, "best_fitness": 0.646337596, "best_timing_accuracy": 11.3662404, "formula_id": "gen0_formula_15"}, {"generation": 4, "best_fitness": 0.646337596, "best_timing_accuracy": 11.3662404, "formula_id": "gen0_formula_15"}], "final_population": [{"formula_id": "gen0_formula_15", "performance_score": 0.646337596, "timing_accuracy_minutes": 11.3662404, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + t_memory_acceleration) * math.log(1 + session_character_weight) * math.log(1 + expansion_momentum)"}, {"formula_id": "gen1_formula_12", "performance_score": 0.646337596, "timing_accuracy_minutes": 11.3662404, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + t_memory_acceleration) * math.log(1 + session_character_weight) * math.log(1 + expansion_momentum)"}, {"formula_id": "gen1_formula_14", "performance_score": 0.646337596, "timing_accuracy_minutes": 11.3662404, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + t_memory_acceleration) * math.log(1 + session_character_weight) * math.log(1 + expansion_momentum)"}, {"formula_id": "gen1_formula_18", "performance_score": 0.646337596, "timing_accuracy_minutes": 11.3662404, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + t_memory_acceleration) * math.log(1 + session_character_weight) * math.log(1 + expansion_momentum)"}, {"formula_id": "gen1_formula_20", "performance_score": 0.646337596, "timing_accuracy_minutes": 11.3662404, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + t_memory_acceleration) * math.log(1 + session_character_weight) * math.log(1 + expansion_momentum)"}]}