{"index_metadata": {"created_timestamp": "2025-07-28T10:42:27.491351", "index_version": "1.0.0"}, "governance_components": {"naming_validator": "governance/naming_conventions.py", "template_validator": "governance/template_validator.py", "file_organizer": "governance/file_organizer.py", "migration_engine": "governance/migration_engine.py", "preprocessing_agent": "agents/preprocessing/preprocessing_manager.py", "enhancement_agent": "agents/enhancement/enhancement_manager.py", "temporal_hooks": "hooks/temporal_hooks.py", "lifecycle_hooks": "hooks/lifecycle_hooks.py", "orchestrator": "orchestration/governance_orchestrator.py"}, "directory_structure": {"governance": "Core governance framework", "agents": "Processing and enhancement agents", "hooks": "System hooks and validators", "orchestration": "System orchestration", "templates": "File templates", "data/preprocessing": "Level 1 and Level 3 processed files", "data/enhanced": "Enhanced and predicted files", "data/trackers": "HTF, FVG, and Liquidity tracker files", "data/archive": "Non-conforming and historical files"}}