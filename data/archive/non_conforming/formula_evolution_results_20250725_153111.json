{"evolution_metadata": {"generations_run": 1, "population_size": 20, "success_threshold": 5.0, "base_formula": "T_memory^1.5 * FVG_density", "evolution_completed": "2025-07-25T15:31:11.504574"}, "best_formula": {"formula_id": "grok4_enhanced_001", "formula_code": "\n# Grok 4's Enhanced Formula\ntime_to_event = 22.5 / max(0.1, gamma_enhanced) * (1 - 0.2 * fvg_proximity) * math.exp(-volatility_index / max(0.1, t_memory))\n\n# Options expiry magnetism (when < 10 minutes to :00 or :30)\nminutes_to_half_hour = min(abs(30 - (predicted_time_minutes % 30)), predicted_time_minutes % 30)\nif minutes_to_half_hour < 10:\n    time_to_event -= (minutes_to_half_hour / 30) * 0.3\n\nresult = time_to_event\n", "exponent": 1.0, "additional_factors": ["volatility_adjustment", "options_expiry_magnetism"], "combination_type": "enhanced", "performance_score": 0.9521471048333334, "timing_accuracy_minutes": 0.7852895166666667, "generation": 0}, "evolution_progress": [{"generation": 0, "best_fitness": 0.9521471048333334, "best_timing_accuracy": 0.7852895166666667, "formula_id": "grok4_enhanced_001"}], "final_population": [{"formula_id": "grok4_enhanced_001", "performance_score": 0.9521471048333334, "timing_accuracy_minutes": 0.7852895166666667, "formula_code": "\n# Grok 4's Enhanced Formula\ntime_to_event = 22.5 / max(0.1, gamma_enhanced) * (1 - 0.2 * fvg_proximity) * math.exp(-volatility_index / max(0.1, t_memory))\n\n# Options expiry magnetism (when < 10 minutes to :00 or :30)\nminutes_to_half_hour = min(abs(30 - (predicted_time_minutes % 30)), predicted_time_minutes % 30)\nif minutes_to_half_hour < 10:\n    time_to_event -= (minutes_to_half_hour / 30) * 0.3\n\nresult = time_to_event\n"}, {"formula_id": "gen0_formula_18", "performance_score": 0.9454879064999999, "timing_accuracy_minutes": 1.4512093499999998, "formula_code": "result = t_memory**1.3 * fvg_density * volatility_index * liquidity_magnetic_pull * consolidation_strength"}, {"formula_id": "gen0_formula_06", "performance_score": 0.7018078566666667, "timing_accuracy_minutes": 5.819214333333333, "formula_code": "result = t_memory**1.3 * fvg_density * math.log(1 + liquidity_magnetic_pull) * math.log(1 + volatility_index)"}, {"formula_id": "gen0_formula_03", "performance_score": 0.594188685, "timing_accuracy_minutes": 16.5811315, "formula_code": "result = t_memory**1.5 * fvg_density * math.log(1 + expansion_momentum) * math.log(1 + session_character_weight) * math.log(1 + volatility_index)"}, {"formula_id": "gen0_formula_16", "performance_score": 0.5347203766666667, "timing_accuracy_minutes": 22.527962333333335, "formula_code": "result = t_memory**2.0 * fvg_density * math.log(1 + consolidation_strength) * math.log(1 + fvg_cluster_density) * math.log(1 + session_character_weight)"}]}