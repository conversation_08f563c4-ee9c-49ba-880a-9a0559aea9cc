#!/usr/bin/env python3
"""
Unified HTF Event Significance Multipliers Configuration
Consolidates multipliers from architecture documentation and controller implementations.
"""

# Current multipliers from htf_master_controller_enhanced.py:291
CURRENT_MULTIPLIERS = {
    'daily_high': 2.5,
    'daily_low': 2.5,
    'weekly_high': 3.0,
    'weekly_low': 3.0,
    'session_high': 1.5,
    'session_low': 1.5,
    'premarket_high': 1.2,
    'premarket_low': 1.2
}

# Missing multipliers from HAWKES_ARCHITECTURE_MAP.md:223
MISSING_MULTIPLIERS = {
    'asia_session_low': 2.2,
    'london_session_high': 2.3,
    'asia_session_high': 2.1,
    'london_session_low': 2.0,
    'ny_am_session_high': 1.8,
    'ny_am_session_low': 1.7,
    'ny_pm_session_high': 1.9,
    'ny_pm_session_low': 1.8
}

# Complete unified configuration
UNIFIED_HTF_EVENT_MULTIPLIERS = {
    # Weekly level events (highest significance)
    'weekly_high': 3.0,
    'weekly_low': 3.0,
    
    # Daily level events (high significance)
    'daily_high': 2.5,
    'daily_low': 2.5,
    
    # Session-specific events (medium-high significance)
    'asia_session_low': 2.2,      # 🔥 CRITICAL MISSING MULTIPLIER
    'london_session_high': 2.3,   # 🔥 CRITICAL MISSING MULTIPLIER
    'asia_session_high': 2.1,
    'london_session_low': 2.0,
    'ny_pm_session_high': 1.9,
    'ny_am_session_high': 1.8,
    'ny_pm_session_low': 1.8,
    'ny_am_session_low': 1.7,
    
    # Generic session events (medium significance)
    'session_high': 1.5,
    'session_low': 1.5,
    
    # Premarket events (lower significance)
    'premarket_high': 1.2,
    'premarket_low': 1.2
}

def calculate_expected_htf_intensity():
    """
    Calculate expected HTF intensity for Asia session low violation.
    Based on Core Insight mathematical foundation.
    """
    # HTF parameters from htf_calibrated_gammas_july28.json
    mu_h = 0.02           # baseline intensity
    alpha_h = 35.51       # excitation strength  
    beta_h = 0.00442      # decay rate (16.7-hour half-life)
    
    # Asia session low event parameters
    asia_low_multiplier = UNIFIED_HTF_EVENT_MULTIPLIERS['asia_session_low']
    delta_t_hours = 10    # Time since event (10 hours example)
    
    # Calculate HTF intensity: λ_HTF(t) = μ_h + α_h·exp(-β_h·Δt)·magnitude
    exponential_term = alpha_h * np.exp(-beta_h * delta_t_hours) * asia_low_multiplier
    htf_intensity = mu_h + exponential_term
    
    return {
        'baseline': mu_h,
        'excitation_component': exponential_term,
        'total_intensity': htf_intensity,
        'threshold': 0.5,
        'activation_status': 'ACTIVE' if htf_intensity > 0.5 else 'DORMANT',
        'threshold_ratio': htf_intensity / 0.5
    }

def validate_multiplier_coverage():
    """Validate that all session types have appropriate multipliers."""
    required_session_patterns = [
        'asia_session_low',
        'asia_session_high', 
        'london_session_low',
        'london_session_high',
        'ny_am_session_low',
        'ny_am_session_high',
        'ny_pm_session_low', 
        'ny_pm_session_high'
    ]
    
    results = {
        'covered': [],
        'missing': [],
        'coverage_percentage': 0
    }
    
    for pattern in required_session_patterns:
        if pattern in UNIFIED_HTF_EVENT_MULTIPLIERS:
            results['covered'].append(pattern)
        else:
            results['missing'].append(pattern)
    
    results['coverage_percentage'] = (len(results['covered']) / len(required_session_patterns)) * 100
    
    return results

if __name__ == "__main__":
    import numpy as np
    
    print("🔧 HTF Event Multipliers Unified Configuration")
    print("=" * 60)
    
    print(f"\n📊 CURRENT vs UNIFIED COMPARISON:")
    print(f"Current multipliers: {len(CURRENT_MULTIPLIERS)}")
    print(f"Missing multipliers: {len(MISSING_MULTIPLIERS)}")
    print(f"Unified total: {len(UNIFIED_HTF_EVENT_MULTIPLIERS)}")
    
    print(f"\n🎯 CRITICAL MISSING MULTIPLIERS:")
    for event, multiplier in MISSING_MULTIPLIERS.items():
        print(f"   • {event}: {multiplier}x")
    
    print(f"\n🧮 HTF INTENSITY CALCULATION TEST:")
    intensity_test = calculate_expected_htf_intensity()
    print(f"   Expected HTF Intensity: {intensity_test['total_intensity']:.2f}")
    print(f"   Activation Threshold: {intensity_test['threshold']}")
    print(f"   Status: {intensity_test['activation_status']}")
    print(f"   Threshold Ratio: {intensity_test['threshold_ratio']:.1f}x")
    
    print(f"\n✅ COVERAGE VALIDATION:")
    coverage = validate_multiplier_coverage()
    print(f"   Covered patterns: {len(coverage['covered'])}")
    print(f"   Missing patterns: {len(coverage['missing'])}")
    print(f"   Coverage: {coverage['coverage_percentage']:.1f}%")
    
    if coverage['missing']:
        print(f"   ❌ Still missing: {coverage['missing']}")
    else:
        print(f"   ✅ Complete coverage achieved!")