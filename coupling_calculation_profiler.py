#!/usr/bin/env python3
"""
Coupling Calculation Profiling
Profile complete cascade prediction timing with debug logging at each stage
"""

import sys
sys.path.append('/Users/<USER>/grok-claude-automation/src')

import time
import cProfile
import pstats
import io
import json
import math
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple
import logging

# Import our components
from htf_master_controller_enhanced import HTFMasterControllerEnhanced, ActivationSignal
from session_subordinate_executor import SessionHawkesExecutor, CascadePrediction
from fractal_cascade_integrator import FractalCascadeIntegrator
from cache_manager import UnifiedHawkesCache, get_unified_cache

class CouplingProfiler:
    """Profiles coupling calculation performance with detailed timing."""
    
    def __init__(self):
        self.setup_debug_logging()
        self.timing_data = {}
        self.profiler = cProfile.Profile()
        
    def setup_debug_logging(self):
        """Setup detailed debug logging."""
        self.logger = logging.getLogger("CouplingProfiler")
        self.logger.setLevel(logging.DEBUG)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s.%(msecs)03d - COUPLING_PROFILER - %(levelname)s - %(message)s',
                datefmt='%H:%M:%S'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def time_operation(self, operation_name: str, func, *args, **kwargs):
        """Time an operation and log results."""
        self.logger.debug(f"🕐 STARTING: {operation_name}")
        start_time = time.perf_counter()
        
        try:
            result = func(*args, **kwargs)
            end_time = time.perf_counter()
            duration = (end_time - start_time) * 1000  # Convert to milliseconds
            
            self.timing_data[operation_name] = {
                'duration_ms': duration,
                'success': True,
                'result_size': len(str(result)) if result else 0
            }
            
            self.logger.debug(f"✅ COMPLETED: {operation_name} in {duration:.2f}ms")
            return result
            
        except Exception as e:
            end_time = time.perf_counter()
            duration = (end_time - start_time) * 1000
            
            self.timing_data[operation_name] = {
                'duration_ms': duration,
                'success': False,
                'error': str(e)
            }
            
            self.logger.error(f"❌ FAILED: {operation_name} in {duration:.2f}ms - {e}")
            return None
    
    def create_test_session_data(self):
        """Create test session data for profiling."""
        return {
            "session_id": "LONDON_2025_07_31_PROFILE_TEST",
            "session_name": "London",
            "session_date": "2025-07-31",
            "range": 125.5,
            "high": 23750.0,
            "low": 23624.5,
            "open": 23680.0,
            "current_level": 23695.0,
            "volatility": 0.85,
            "momentum": 0.72
        }
    
    def profile_htf_intensity_calculation(self, controller: HTFMasterControllerEnhanced) -> float:
        """Profile HTF intensity calculation with detailed breakdown."""
        self.logger.debug("🔥 PROFILING HTF INTENSITY CALCULATION")
        
        # Phase 1: Load intelligence events
        intelligence_events = self.time_operation(
            "htf_load_intelligence_events",
            controller.load_intelligence_htf_events,
            force_reload=True
        )
        
        if not intelligence_events:
            self.logger.warning("No intelligence events - creating synthetic events for profiling")
            # Create synthetic events for profiling
            synthetic_events = self.create_synthetic_htf_events()
            controller.intelligence_events_cache = synthetic_events
            intelligence_events = synthetic_events
        
        # Phase 2: HTF parameter retrieval
        htf_params = self.time_operation(
            "htf_parameter_retrieval",
            lambda: controller.htf_params
        )
        
        # Phase 3: HTF intensity calculation
        target_time = datetime.now()
        
        def calculate_htf_intensity():
            mu_h = htf_params["mu_h"]
            alpha_h = htf_params["alpha_h"] 
            beta_h = htf_params["beta_h"]
            
            intensity = mu_h
            valid_events = 0
            
            for event in intelligence_events:
                if event.time > target_time:
                    continue
                    
                time_diff_hours = (target_time - event.time).total_seconds() / 3600
                if time_diff_hours > 168:  # 7 days
                    continue
                
                decay = math.exp(-beta_h * time_diff_hours)
                event_influence = alpha_h * decay * event.magnitude
                intensity += event_influence
                valid_events += 1
            
            return intensity, valid_events
        
        htf_result = self.time_operation(
            "htf_hawkes_calculation",
            calculate_htf_intensity
        )
        
        if htf_result:
            htf_intensity, event_count = htf_result
            self.logger.debug(f"HTF intensity: {htf_intensity:.4f} from {event_count} events")
            return htf_intensity
        
        return 0.02  # Fallback baseline
    
    def profile_session_intensity_calculation(self, executor: SessionHawkesExecutor, session_data: Dict) -> float:
        """Profile session-level intensity calculation."""
        self.logger.debug("🎯 PROFILING SESSION INTENSITY CALCULATION")
        
        # Phase 1: Session parameter loading
        session_params = self.time_operation(
            "session_parameter_loading",
            lambda: executor.current_params.copy()
        )
        
        # Phase 2: Session event processing (simulate with current session data)
        def calculate_session_intensity():
            mu_s = session_params.get("baseline_intensity", 0.15)
            alpha_s = session_params.get("excitation_strength", 0.6)
            beta_s = session_params.get("decay_rate", 0.02)
            
            # Simulate session events based on volatility and momentum
            session_intensity = mu_s
            
            # Add volatility-based excitation
            volatility_factor = session_data.get("volatility", 0.5)
            momentum_factor = session_data.get("momentum", 0.5)
            
            # Synthetic session excitation
            session_excitation = alpha_s * volatility_factor * momentum_factor
            session_intensity += session_excitation
            
            return session_intensity
        
        session_intensity = self.time_operation(
            "session_hawkes_calculation", 
            calculate_session_intensity
        )
        
        self.logger.debug(f"Session intensity: {session_intensity:.4f}")
        return session_intensity or 0.15
    
    def profile_gamma_adaptive_coupling(self, htf_intensity: float, session_data: Dict) -> float:
        """Profile γ(t) adaptive coupling calculation."""
        self.logger.debug("⚡ PROFILING GAMMA ADAPTIVE COUPLING")
        
        # Phase 1: Base gamma calculation
        def calculate_base_gamma():
            # Session-specific base gamma
            session_name = session_data.get("session_name", "London")
            
            base_gammas = {
                "Asia": 0.0895,
                "Midnight": 0.2987, 
                "London": 0.1934,
                "Premarket": 0.1523,
                "NY_AM": 0.0278,
                "Lunch": 0.2534,
                "NY_PM": 0.000163
            }
            
            return base_gammas.get(session_name, 0.1934)
        
        base_gamma = self.time_operation(
            "gamma_base_calculation",
            calculate_base_gamma
        )
        
        # Phase 2: Adaptive enhancement factors
        def calculate_adaptive_factors():
            volatility = session_data.get("volatility", 0.5)
            momentum = session_data.get("momentum", 0.5)
            range_factor = min(session_data.get("range", 50) / 100, 2.0)  # Normalize range
            
            # HTF intensity boost
            htf_boost = max(1.0, htf_intensity / 0.5) if htf_intensity > 0.5 else 1.0
            
            # Combined adaptive factor
            adaptive_factor = (1 + volatility * 0.3) * (1 + momentum * 0.2) * range_factor * htf_boost
            
            return adaptive_factor, htf_boost
        
        adaptive_result = self.time_operation(
            "gamma_adaptive_factors",
            calculate_adaptive_factors
        )
        
        # Phase 3: Final gamma calculation
        def calculate_final_gamma():
            if not adaptive_result:
                return base_gamma
            
            adaptive_factor, htf_boost = adaptive_result
            gamma_enhanced = base_gamma * adaptive_factor
            
            # Cap gamma to prevent instability
            gamma_final = min(gamma_enhanced, 1.5)
            
            return gamma_final, adaptive_factor, htf_boost
        
        gamma_result = self.time_operation(
            "gamma_final_calculation",
            calculate_final_gamma
        )
        
        if gamma_result:
            gamma_final, adaptive_factor, htf_boost = gamma_result
            self.logger.debug(f"γ(t): {gamma_final:.4f} (base: {base_gamma:.4f}, adaptive: {adaptive_factor:.2f}x, HTF boost: {htf_boost:.2f}x)")
            return gamma_final
        
        return base_gamma or 0.1934
    
    def profile_coupling_calculation(self, lambda_session: float, lambda_htf: float, gamma: float) -> Tuple[float, bool]:
        """Profile final coupling calculation."""
        self.logger.debug("🔗 PROFILING COUPLING CALCULATION")
        
        # Phase 1: Coupling component calculation  
        def calculate_coupling_component():
            coupling_component = gamma * lambda_htf
            return coupling_component
        
        coupling_component = self.time_operation(
            "coupling_component_calculation",
            calculate_coupling_component
        )
        
        # Phase 2: Total intensity calculation
        def calculate_total_intensity():
            lambda_total = lambda_session + coupling_component
            return lambda_total
        
        lambda_total = self.time_operation(
            "total_intensity_calculation", 
            calculate_total_intensity
        )
        
        # Phase 3: Threshold evaluation
        def evaluate_cascade_threshold():
            threshold = 0.5
            cascade_triggered = lambda_total > threshold
            threshold_ratio = lambda_total / threshold if threshold > 0 else 0
            
            return cascade_triggered, threshold_ratio
        
        threshold_result = self.time_operation(
            "cascade_threshold_evaluation",
            evaluate_cascade_threshold
        )
        
        if threshold_result:
            cascade_triggered, threshold_ratio = threshold_result
            self.logger.debug(f"λ_total: {lambda_total:.4f}, threshold ratio: {threshold_ratio:.2f}x, cascade: {'YES' if cascade_triggered else 'NO'}")
            return lambda_total, cascade_triggered
        
        return lambda_total or 0.0, False
    
    def create_synthetic_htf_events(self):
        """Create synthetic HTF events for profiling when none exist."""
        from htf_master_controller_enhanced import IntelligenceHTFEvent
        
        current_time = datetime.now()
        
        synthetic_events = [
            IntelligenceHTFEvent(
                time=current_time - timedelta(hours=2),
                event_type="level_takeout",
                level=23712.0,
                htf_significance="asia_session_low_violated",
                reference_date="2025-07-31",
                violated_on="2025-07-31", 
                origin_session="Asia_2025-07-31",
                taken_by_session="London_2025-07-31",
                magnitude=2.2,  # asia_session_low multiplier
                confidence=0.95,
                source_file="SYNTHETIC_PROFILE_TEST"
            ),
            IntelligenceHTFEvent(
                time=current_time - timedelta(hours=4),
                event_type="level_takeout",
                level=23698.5,
                htf_significance="london_session_high_violated",
                reference_date="2025-07-30",
                violated_on="2025-07-31",
                origin_session="London_2025-07-30", 
                taken_by_session="London_2025-07-31",
                magnitude=2.3,  # london_session_high multiplier
                confidence=0.92,
                source_file="SYNTHETIC_PROFILE_TEST"
            )
        ]
        
        return synthetic_events
    
    def run_complete_coupling_profile(self) -> Dict[str, Any]:
        """Run complete coupling calculation profile."""
        print("⏱️ COUPLING CALCULATION PROFILING")
        print("=" * 80)
        print("Profiling complete cascade prediction timing with debug logging\n")
        
        # Initialize components
        self.logger.debug("🏗️ INITIALIZING COMPONENTS")
        controller = HTFMasterControllerEnhanced()
        executor = SessionHawkesExecutor()
        session_data = self.create_test_session_data()
        
        # Start full profiling
        self.profiler.enable()
        total_start = time.perf_counter()
        
        # Phase 1: HTF Intensity Calculation
        htf_intensity = self.profile_htf_intensity_calculation(controller)
        
        # Phase 2: Session Intensity Calculation
        session_intensity = self.profile_session_intensity_calculation(executor, session_data)
        
        # Phase 3: Gamma Adaptive Coupling
        gamma_coupling = self.profile_gamma_adaptive_coupling(htf_intensity, session_data)
        
        # Phase 4: Final Coupling Calculation
        lambda_total, cascade_triggered = self.profile_coupling_calculation(
            session_intensity, htf_intensity, gamma_coupling
        )
        
        total_end = time.perf_counter()
        total_duration = (total_end - total_start) * 1000
        
        self.profiler.disable()
        
        # Generate profiling report
        profile_report = self.generate_profiling_report(total_duration, {
            'htf_intensity': htf_intensity,
            'session_intensity': session_intensity,
            'gamma_coupling': gamma_coupling,
            'lambda_total': lambda_total,
            'cascade_triggered': cascade_triggered
        })
        
        return profile_report
    
    def generate_profiling_report(self, total_duration: float, results: Dict) -> Dict[str, Any]:
        """Generate comprehensive profiling report."""
        print(f"\n📊 COUPLING PROFILING RESULTS")
        print("=" * 60)
        
        # Timing breakdown
        print(f"⏱️ TIMING BREAKDOWN:")
        print(f"   Total Duration: {total_duration:.2f}ms")
        
        timing_categories = {
            'HTF Operations': ['htf_load_intelligence_events', 'htf_parameter_retrieval', 'htf_hawkes_calculation'],
            'Session Operations': ['session_parameter_loading', 'session_hawkes_calculation'],
            'Coupling Operations': ['gamma_base_calculation', 'gamma_adaptive_factors', 'gamma_final_calculation', 
                                  'coupling_component_calculation', 'total_intensity_calculation', 'cascade_threshold_evaluation']
        }
        
        category_totals = {}
        for category, operations in timing_categories.items():
            category_total = sum(self.timing_data.get(op, {}).get('duration_ms', 0) for op in operations)
            category_totals[category] = category_total
            percentage = (category_total / total_duration) * 100 if total_duration > 0 else 0
            
            print(f"\n   {category}: {category_total:.2f}ms ({percentage:.1f}%)")
            for op in operations:
                if op in self.timing_data:
                    op_data = self.timing_data[op]
                    status = "✅" if op_data['success'] else "❌"
                    print(f"      • {op}: {op_data['duration_ms']:.2f}ms {status}")
        
        # Results summary
        print(f"\n🎯 CALCULATION RESULTS:")
        print(f"   HTF Intensity: {results['htf_intensity']:.4f}")
        print(f"   Session Intensity: {results['session_intensity']:.4f}")
        print(f"   Gamma Coupling: {results['gamma_coupling']:.4f}")
        print(f"   Total Intensity: {results['lambda_total']:.4f}")
        print(f"   Cascade Triggered: {'YES' if results['cascade_triggered'] else 'NO'}")
        
        # Coupling analysis
        coupling_reached = results['lambda_total'] > (results['session_intensity'] + 0.01)  # Meaningful coupling
        print(f"\n🔗 COUPLING ANALYSIS:")
        print(f"   Coupling Reached: {'YES' if coupling_reached else 'NO'}")
        if coupling_reached:
            coupling_contribution = results['lambda_total'] - results['session_intensity']
            coupling_percentage = (coupling_contribution / results['lambda_total']) * 100
            print(f"   Coupling Contribution: {coupling_contribution:.4f} ({coupling_percentage:.1f}%)")
        
        # Performance insights
        slowest_operation = max(self.timing_data.items(), key=lambda x: x[1].get('duration_ms', 0))
        fastest_operation = min(self.timing_data.items(), key=lambda x: x[1].get('duration_ms', 0))
        
        print(f"\n📈 PERFORMANCE INSIGHTS:")
        print(f"   Slowest Operation: {slowest_operation[0]} ({slowest_operation[1]['duration_ms']:.2f}ms)")
        print(f"   Fastest Operation: {fastest_operation[0]} ({fastest_operation[1]['duration_ms']:.2f}ms)")
        
        # Generate detailed profile statistics
        profile_stats = self.generate_profile_statistics()
        
        return {
            'total_duration_ms': total_duration,
            'timing_breakdown': category_totals,
            'operation_timing': self.timing_data,
            'calculation_results': results,
            'coupling_reached': coupling_reached,
            'performance_insights': {
                'slowest_operation': slowest_operation[0],
                'fastest_operation': fastest_operation[0]
            },
            'profile_statistics': profile_stats
        }
    
    def generate_profile_statistics(self) -> Dict[str, Any]:
        """Generate detailed cProfile statistics."""
        # Capture profile statistics
        stats_stream = io.StringIO()
        stats = pstats.Stats(self.profiler, stream=stats_stream)
        stats.sort_stats('cumulative')
        stats.print_stats(20)  # Top 20 functions
        
        profile_output = stats_stream.getvalue()
        
        # Parse key metrics
        lines = profile_output.split('\n')
        function_calls = 0
        primitive_calls = 0
        
        for line in lines:
            if 'function calls' in line:
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        function_calls = int(parts[0])
                        if 'primitive' in line:
                            primitive_calls = int(parts[2])
                    except:
                        pass
                break
        
        return {
            'total_function_calls': function_calls,
            'primitive_calls': primitive_calls,
            'profile_output': profile_output[:1000]  # First 1000 chars
        }

def main():
    """Run coupling calculation profiling."""
    profiler = CouplingProfiler()
    report = profiler.run_complete_coupling_profile()
    
    # Save detailed report
    report_file = Path("/Users/<USER>/grok-claude-automation/coupling_profiling_report.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n💾 Detailed report saved: {report_file.name}")

if __name__ == "__main__":
    main()