#!/usr/bin/env python3
"""
Test tracker system components without requiring API calls.
Tests the tracker integration architecture and mathematical components.
"""

import json
import time
import sys
from datetime import datetime
from src.tracker_state import TrackerStateManager
from src.opus4_corrected_formulas import create_corrected_opus4_template
from src.unit_e_template import TemplatePopulator

sys.path.append('.')
from src.utils import load_json_data, save_json_data


def create_sample_tracker_data():
    """Create sample tracker data for testing."""
    
    # Sample HTF context
    htf_context = {
        "context_id": "htf_context_test_2025_07_23",
        "active_structures": [
            {
                "structure_id": "daily_support_23300",
                "structure_type": "support",
                "level": 23300.0,
                "strength": 1.2,
                "recency_factor": 0.8,
                "formation_time": "2025-07-22 12:00:00"
            },
            {
                "structure_id": "weekly_resistance_23400",
                "structure_type": "resistance", 
                "level": 23400.0,
                "strength": 1.5,
                "recency_factor": 0.9,
                "formation_time": "2025-07-21 09:00:00"
            }
        ],
        "context_timestamp": datetime.now().isoformat(),
        "precedence_weight": 0.35
    }
    
    # Sample FVG state
    fvg_state = {
        "state_id": "fvg_state_test_2025_07_23",
        "fpfvg_carryover_metadata": {
            "t_memory_final": 25.5,
            "last_interaction_time": "2025-07-22 21:45:00",
            "carryover_strength": 0.85,
            "decay_applied": False
        },
        "active_gaps": [
            {
                "gap_id": "previous_day_fpfvg",
                "level": 23345.0,
                "size": 2.5,
                "status": "pending_redelivery"
            }
        ],
        "state_timestamp": datetime.now().isoformat()
    }
    
    # Sample liquidity state
    liquidity_state = {
        "registry_id": "liquidity_registry_test_2025_07_23",
        "untaken_liquidity_registry": [
            {
                "level_id": "stop_hunt_23280",
                "level": 23280.0,
                "weight": 1.5,  
                "level_type": "stop_hunt",
                "time_created": "2025-07-22 20:00:00",
                "interactions": 0
            },
            {
                "level_id": "gap_fill_23370",
                "level": 23370.0,
                "weight": 1.2,
                "level_type": "gap_fill", 
                "time_created": "2025-07-22 18:30:00",
                "interactions": 1
            }
        ],
        "registry_timestamp": datetime.now().isoformat(),
        "gradient_strength": 0.0
    }
    
    return htf_context, fvg_state, liquidity_state


def create_sample_unit_results():
    """Create sample unit results that would come from a successful pipeline."""
    
    unit_a_results = {
        "hybrid_volume": {
            "alpha_t": 0.9271,
            "v_synthetic": 128.6556,
            "v_traditional": 150.0,
            "v_hybrid": 148.444,
            "q_t": 0.1972,
            "r_t": 0.9
        },
        "time_dilation_base": {
            "gamma_base": 1.5091,
            "alpha_grad_scaled": 0.365,
            "volume_factor": 0.237,
            "h_score": 0.9
        },
        "parameters_validated": {
            "all_within_bounds": True,
            "beta_HTF": 0.35,
            "sigma_HTF": 50.0,
            "alpha_grad": 0.5,
            "d_htf": 10.0
        }
    }
    
    unit_b_results = {
        "energy_accumulation": {
            "energy_rate": 1.56525,
            "total_accumulated": 469.575,
            "accumulation_phase": "ranging_with_liquidity_sweep",
            "efficiency_factor": 0.782625
        },
        "gradient_dynamics": {
            "intensity_coefficient": 1.3991,
            "direction_factor": -1.3207,
            "stability_index": 0.9939,
            "momentum_transfer": 1.1193
        },
        "structural_integrity": 1.0
    }
    
    unit_c_results = {
        "temporal_momentum": {
            "momentum_strength": 1.1810593875,
            "momentum_direction": "downward",
            "decay_coefficient": 0.4585,
            "persistence_factor": 0.69955
        },
        "consolidation_analysis": {
            "consolidation_strength": 0.99695,
            "breakout_probability": 0.00305,
            "consolidation_duration": 79,
            "stability_rating": 0.9908685
        },
        "frequency_analysis": {
            "dominant_freq": 0.55965,
            "harmonic_strength": 0.67158,
            "frequency_stability": 0.89451
        }
    }
    
    unit_d_results = {
        "validation_results": {
            "integration_score": 1.2244697958333333,
            "consistency_check": True,
            "convergence_achieved": True,
            "error_margins": {
                "unit_a": 2.3,
                "unit_b": 1.5,
                "unit_c": 2.8
            }
        },
        "system_integration": {
            "units_synchronized": True,
            "cross_validation_passed": True,
            "final_confidence": 1.0,
            "recommendations": [
                "Monitor FP FVG resolution in subsequent sessions due to lacking redelivery"
            ]
        },
        "quality_metrics": {
            "overall_accuracy": 1.1122348979166667,
            "computational_efficiency": 0.95,
            "data_consistency": 0.99,
            "reliability_score": 1.0
        }
    }
    
    return unit_a_results, unit_b_results, unit_c_results, unit_d_results


def test_tracker_state_manager():
    """Test TrackerStateManager functionality."""
    
    print("1. Testing TrackerStateManager...")
    
    manager = TrackerStateManager()
    htf_context, fvg_state, liquidity_state = create_sample_tracker_data()
    
    # Test T_memory decay
    t_memory_prev = 30.0
    hours_elapsed = 2.5
    decayed = manager.calculate_t_memory_decay(t_memory_prev, hours_elapsed)
    print(f"   T_memory decay: {t_memory_prev} → {decayed:.2f} (after {hours_elapsed}h)")
    
    # Proper expected decay formula: T_memory_prev * e^(-0.06 * hours_elapsed)
    import math
    expected_decay = t_memory_prev * math.exp(-0.06 * hours_elapsed)
    if abs(decayed - expected_decay) < 0.1:  # Within reasonable tolerance
        print("   ✅ T_memory decay calculation working")
    else:
        print(f"   ❌ T_memory decay unexpected: got {decayed:.2f}, expected ~{expected_decay:.2f}")
        return False, None
    
    # Test energy threshold adjustment
    e_threshold_base = 800.0
    t_memory = 25.5
    adjusted = manager.calculate_energy_threshold_adjustment(e_threshold_base, t_memory)
    print(f"   Energy threshold: {e_threshold_base} → {adjusted:.2f} (T_memory: {t_memory})")
    
    if 400 <= adjusted <= 800:  # Should be within reasonable bounds
        print("   ✅ Energy threshold adjustment working")
    else:
        print(f"   ❌ Energy threshold out of bounds: {adjusted:.2f}")
        return False, None
    
    # Test tracker context extraction
    tracker_context = manager.extract_tracker_context(htf_context, fvg_state, liquidity_state)
    print(f"   Tracker context extracted: T_memory={tracker_context['t_memory']:.2f}")
    print(f"   HTF structures: {len(tracker_context['active_structures'])}")
    print(f"   Untaken liquidity: {len(tracker_context['untaken_liquidity'])}")
    
    if (tracker_context['t_memory'] == 25.5 and 
        len(tracker_context['active_structures']) == 2 and
        len(tracker_context['untaken_liquidity']) == 2):
        print("   ✅ Tracker context extraction working")
        return True, tracker_context
    else:
        print("   ❌ Tracker context extraction failed")
        return False, None


def test_tracker_enhanced_opus4():
    """Test tracker-enhanced Opus4 calculations."""
    
    print("\n2. Testing Tracker-Enhanced Opus4 Calculations...")
    
    unit_a, unit_b, unit_c, unit_d = create_sample_unit_results()
    
    # Load sample session data
    try:
        session_data = load_json_data('/Users/<USER>/Desktop/asia_session_full_jul22.json')
    except FileNotFoundError:
        # Create minimal session data for testing
        session_data = {
            "session_metadata": {
                "session_id": "test_session",
                "duration_minutes": 300
            },
            "price_data": {
                "close": 23329.0,
                "range": 46.0
            },
            "micro_timing_analysis": {
                "consolidation_durations": [],
                "expansion_velocities": []
            }
        }
        print("   Using minimal test session data")
    
    # Create tracker context
    htf_context, fvg_state, liquidity_state = create_sample_tracker_data()
    manager = TrackerStateManager()
    tracker_context = manager.extract_tracker_context(htf_context, fvg_state, liquidity_state)
    
    # Test Opus4 calculations without tracker context
    print("   Testing base Opus4 calculations...")
    base_opus4 = create_corrected_opus4_template(
        unit_a, unit_b, unit_c, unit_d, session_data
    )
    
    # Test Opus4 calculations with tracker context
    print("   Testing tracker-enhanced Opus4 calculations...")
    enhanced_opus4 = create_corrected_opus4_template(
        unit_a, unit_b, unit_c, unit_d, session_data, tracker_context
    )
    
    # Compare results
    base_lambda = base_opus4.get('lambda_theta_dynamic', 0)
    enhanced_lambda = enhanced_opus4.get('lambda_theta_dynamic', 0)
    base_gamma = base_opus4.get('gamma_enhanced', 0)
    enhanced_gamma = enhanced_opus4.get('gamma_enhanced', 0)
    
    print(f"   λ_theta_dynamic: base={base_lambda:.4f}, enhanced={enhanced_lambda:.4f}")
    print(f"   γ_enhanced: base={base_gamma:.3f}, enhanced={enhanced_gamma:.3f}")
    
    # Validation checks
    validation_checks = []
    validation_checks.append(('λ_theta_dynamic', 0.02 <= enhanced_lambda <= 0.04))
    validation_checks.append(('γ_enhanced', 0.5 <= enhanced_gamma <= 1.5))
    validation_checks.append(('confidence', enhanced_opus4.get('confidence', 0) < 1.0))
    
    all_valid = True
    for param, is_valid in validation_checks:
        status = "✅" if is_valid else "❌"
        print(f"   {status} {param}: {'PASS' if is_valid else 'FAIL'}")
        all_valid = all_valid and is_valid
    
    # Check that tracker enhancements were applied
    validation = enhanced_opus4.get('_validation', {})
    tracker_enhanced = validation.get('tracker_enhanced', False)
    
    if tracker_enhanced:
        print("   ✅ Tracker enhancements applied")
        print(f"   Tracker context used: {validation.get('tracker_context_used', {})}")
    else:
        print("   ❌ Tracker enhancements not applied")
        return False
    
    if all_valid:
        print("   ✅ Tracker-enhanced Opus4 calculations working")
        return True
    else:
        print("   ❌ Some tracker-enhanced Opus4 validations failed")
        return False


def test_template_population_with_trackers():
    """Test template population with tracker updates."""
    
    print("\n3. Testing Template Population with Tracker Updates...")
    
    unit_a, unit_b, unit_c, unit_d = create_sample_unit_results()
    htf_context, fvg_state, liquidity_state = create_sample_tracker_data()
    
    # Create tracker context
    manager = TrackerStateManager()
    tracker_context = manager.extract_tracker_context(htf_context, fvg_state, liquidity_state)
    
    # Create minimal session data
    session_data = {
        "session_metadata": {
            "session_id": "test_session_template",
            "session_type": "Asia",
            "date": "2025-07-23",
            "duration_minutes": 300
        },
        "price_data": {
            "open": 23343.75,
            "high": 23367.0,
            "low": 23321.0,
            "close": 23329.0,
            "range": 46.0
        },
        "micro_timing_analysis": {},
        "behavioral_building_blocks": {},
        "fpfvg_status_tracking": {},
        "structures_identified": {},
        "phase_transitions": []
    }
    
    # Test template population
    populator = TemplatePopulator()
    result = populator.populate_grok_enhanced_template(
        unit_a, unit_b, unit_c, unit_d, session_data, tracker_context
    )
    
    # Validate result structure
    if 'grok_enhanced_template' not in result:
        print("   ❌ Missing grokEnhanced template")
        return False
    
    if 'updated_tracker_states' not in result:
        print("   ❌ Missing updated tracker states")
        return False
    
    template = result['grok_enhanced_template']
    tracker_updates = result['updated_tracker_states']
    
    # Check template sections
    required_sections = [
        'session_metadata', 'price_data', 'opus4_enhancements',
        'timing_enhancements', 'enhanced_structures'
    ]
    
    missing_sections = []
    for section in required_sections:
        if section not in template:
            missing_sections.append(section)
    
    if missing_sections:
        print(f"   ❌ Missing template sections: {missing_sections}")
        return False
    
    # Check Opus4 enhancements
    opus4 = template.get('opus4_enhancements', {})
    lambda_theta = opus4.get('lambda_theta_dynamic', 0)
    
    if not (0.02 <= lambda_theta <= 0.04):
        print(f"   ❌ λ_theta_dynamic out of bounds: {lambda_theta:.4f}")
        return False
    
    # Check tracker updates
    if tracker_updates:
        updated_t_memory = tracker_updates.get('updated_t_memory', 0)
        print(f"   Updated T_memory: {tracker_context['t_memory']:.2f} → {updated_t_memory:.2f}")
        
        if updated_t_memory <= 0:
            print("   ❌ Invalid updated T_memory")
            return False
    
    print("   ✅ Template population with tracker updates working")
    
    # Save test result
    save_json_data(result, 'test_template_with_trackers.json')
    print("   📁 Test template saved to test_template_with_trackers.json")
    
    return True


def main():
    """Run all tracker system tests."""
    
    print("🧪 Testing Tracker System Components (No API Required)")
    print("=" * 65)
    
    start_time = time.time()
    
    # Test 1: TrackerStateManager
    tracker_test_passed, tracker_context = test_tracker_state_manager()
    if not tracker_test_passed:
        print("❌ TrackerStateManager test failed")
        return False
    
    # Test 2: Tracker-enhanced Opus4
    opus4_test_passed = test_tracker_enhanced_opus4()
    if not opus4_test_passed:
        print("❌ Tracker-enhanced Opus4 test failed")
        return False
    
    # Test 3: Template population with trackers
    template_test_passed = test_template_population_with_trackers()
    if not template_test_passed:
        print("❌ Template population with trackers test failed")
        return False
    
    elapsed_time = time.time() - start_time
    
    print(f"\n🎯 Tracker System Test Summary:")
    print("   ✅ TrackerStateManager functional")
    print("   ✅ Tracker-enhanced Opus4 calculations")
    print("   ✅ Template population with tracker updates")
    print("   ✅ Mathematical bounds validation")
    print("   ✅ State continuity and update logic")
    
    print(f"\n🏁 ALL TRACKER SYSTEM TESTS PASSED!")
    print(f"   Total test time: {elapsed_time:.1f}s")
    print(f"   System ready for integration with live API")
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)