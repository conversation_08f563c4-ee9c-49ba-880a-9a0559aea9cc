#!/usr/bin/env python3
"""
Unit tests for the core pipeline module
Tests the A→B→C→D sequential execution and error handling
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.pipeline import GrokPipeline, process_single_session, PipelineErrorHandler
from src.config import AppConfig, APIKeyError
from conftest import assert_valid_session_data, MockPipelineResult


class TestGrokPipeline:
    """Test cases for GrokPipeline class"""
    
    def test_pipeline_initialization(self, test_config):
        """Test pipeline initializes correctly"""
        pipeline = GrokPipeline(config=test_config)
        
        assert pipeline.config == test_config
        assert pipeline.tracker_context is not None
        assert pipeline.tracker_essentials is not None
        assert hasattr(pipeline, 'error_handler')
    
    def test_pipeline_initialization_with_api_key(self):
        """Test pipeline initialization with API key"""
        pipeline = GrokPipeline(api_key="test-key-123")
        
        assert pipeline.config.api_key == "test-key-123"
        assert pipeline.config.timeout_seconds > 0
    
    def test_pipeline_initialization_no_api_key(self):
        """Test pipeline fails gracefully without API key"""
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(APIKeyError):
                GrokPipeline()
    
    @patch('src.pipeline.create_unit_a')
    @patch('src.pipeline.create_unit_b') 
    @patch('src.pipeline.create_unit_c')
    @patch('src.pipeline.create_unit_d')
    def test_process_session_success(self, mock_unit_d, mock_unit_c, mock_unit_b, mock_unit_a, 
                                   test_config, sample_session_data, sample_micro_timing):
        """Test successful session processing"""
        # Setup mocks
        mock_unit_a.return_value.process.return_value = {"foundation_score": 0.85}
        mock_unit_b.return_value.process.return_value = {"energy_level": 0.72}
        mock_unit_c.return_value.process.return_value = {"dynamics_score": 0.68}
        mock_unit_d.return_value.process.return_value = {"validation_score": 0.91}
        
        pipeline = GrokPipeline(config=test_config)
        result = pipeline.process_session(sample_session_data, sample_micro_timing)
        
        assert result["success"] is True
        assert "unit_a_results" in result
        assert "unit_b_results" in result
        assert "unit_c_results" in result
        assert "unit_d_results" in result
        assert "execution_time" in result
        
        # Verify units were called in sequence
        mock_unit_a.assert_called_once()
        mock_unit_b.assert_called_once()
        mock_unit_c.assert_called_once()
        mock_unit_d.assert_called_once()
    
    @patch('src.pipeline.create_unit_a')
    def test_process_session_unit_failure(self, mock_unit_a, test_config, sample_session_data):
        """Test pipeline handles unit failure gracefully"""
        # Make unit A fail
        mock_unit_a.return_value.process.side_effect = Exception("Unit A failed")
        
        pipeline = GrokPipeline(config=test_config)
        result = pipeline.process_session(sample_session_data)
        
        assert result["success"] is False
        assert "error" in result
        assert "Unit A failed" in str(result["error"])
    
    @patch('src.pipeline.execute_with_timeout')
    def test_process_session_timeout(self, mock_timeout, test_config, sample_session_data):
        """Test pipeline handles timeout correctly"""
        from src.pipeline import TimeoutError
        mock_timeout.side_effect = TimeoutError("Unit processing timed out")
        
        pipeline = GrokPipeline(config=test_config)
        result = pipeline.process_session(sample_session_data)
        
        assert result["success"] is False
        assert "timeout" in str(result["error"]).lower()
    
    def test_get_summary(self, test_config):
        """Test pipeline summary generation"""
        pipeline = GrokPipeline(config=test_config)
        
        # Mock some execution history
        pipeline.execution_history = [
            {"execution_time": 1.5, "success": True},
            {"execution_time": 2.1, "success": True},
            {"execution_time": 1.8, "success": False}
        ]
        
        summary = pipeline.get_summary()
        
        assert "total_executions" in summary
        assert "success_rate" in summary
        assert "average_time_ms" in summary
        assert summary["total_executions"] == 3
        assert summary["success_rate"] == 2/3
    
    def test_validate_session_data_valid(self, test_config, sample_session_data):
        """Test session data validation with valid data"""
        pipeline = GrokPipeline(config=test_config)
        
        # Should not raise exception
        pipeline._validate_session_data(sample_session_data)
    
    def test_validate_session_data_invalid(self, test_config):
        """Test session data validation with invalid data"""
        pipeline = GrokPipeline(config=test_config)
        
        invalid_data = {"invalid": "structure"}
        
        with pytest.raises(ValueError):
            pipeline._validate_session_data(invalid_data)
    
    def test_validate_session_data_missing_price_data(self, test_config):
        """Test session data validation with missing price data"""
        pipeline = GrokPipeline(config=test_config)
        
        invalid_data = {
            "session_metadata": {"session_type": "ASIA"},
            "structures_identified": {}
            # Missing price_data
        }
        
        with pytest.raises(ValueError):
            pipeline._validate_session_data(invalid_data)


class TestProcessSingleSession:
    """Test cases for process_single_session function"""
    
    @patch('src.pipeline.GrokPipeline')
    def test_process_single_session_success(self, mock_pipeline_class, sample_session_data):
        """Test successful single session processing"""
        # Setup mock
        mock_pipeline = Mock()
        mock_pipeline.process_session.return_value = {"success": True, "data": "test"}
        mock_pipeline_class.return_value = mock_pipeline
        
        result = process_single_session(sample_session_data, api_key="test-key")
        
        assert result["success"] is True
        mock_pipeline.process_session.assert_called_once_with(sample_session_data, None)
    
    @patch('src.pipeline.GrokPipeline')
    def test_process_single_session_with_micro_timing(self, mock_pipeline_class, 
                                                    sample_session_data, sample_micro_timing):
        """Test single session processing with micro timing"""
        mock_pipeline = Mock()
        mock_pipeline.process_session.return_value = {"success": True}
        mock_pipeline_class.return_value = mock_pipeline
        
        result = process_single_session(sample_session_data, sample_micro_timing, api_key="test-key")
        
        assert result["success"] is True
        mock_pipeline.process_session.assert_called_once_with(sample_session_data, sample_micro_timing)
    
    def test_process_single_session_no_api_key(self, sample_session_data):
        """Test single session processing without API key"""
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(APIKeyError):
                process_single_session(sample_session_data)


class TestPipelineErrorHandler:
    """Test cases for PipelineErrorHandler"""
    
    def test_handle_unit_error(self):
        """Test unit error handling"""
        error = Exception("Test error")
        result = PipelineErrorHandler.handle_unit_error("unit_a", error)
        
        assert result["success"] is False
        assert result["unit"] == "unit_a"
        assert "Test error" in result["error"]
        assert "timestamp" in result
    
    def test_handle_timeout_error(self):
        """Test timeout error handling"""
        from src.pipeline import TimeoutError
        error = TimeoutError("Timeout occurred")
        result = PipelineErrorHandler.handle_timeout_error("unit_b", error, 30)
        
        assert result["success"] is False
        assert result["unit"] == "unit_b"
        assert result["timeout_seconds"] == 30
        assert "timeout" in result["error"].lower()
    
    def test_handle_validation_error(self):
        """Test validation error handling"""
        error = ValueError("Invalid data structure")
        result = PipelineErrorHandler.handle_validation_error(error, {"test": "data"})
        
        assert result["success"] is False
        assert "validation" in result["error"].lower()
        assert result["invalid_data"] == {"test": "data"}
    
    def test_create_error_response(self):
        """Test error response creation"""
        error = Exception("Generic error")
        result = PipelineErrorHandler.create_error_response(error, "test_context")
        
        assert result["success"] is False
        assert result["context"] == "test_context"
        assert "Generic error" in result["error"]
        assert "timestamp" in result


class TestPipelineIntegration:
    """Integration tests for pipeline components"""
    
    @pytest.mark.integration
    def test_full_pipeline_flow(self, test_config, sample_session_data, sample_micro_timing):
        """Test complete pipeline flow with mocked units"""
        with patch('src.pipeline.create_unit_a') as mock_a, \
             patch('src.pipeline.create_unit_b') as mock_b, \
             patch('src.pipeline.create_unit_c') as mock_c, \
             patch('src.pipeline.create_unit_d') as mock_d:
            
            # Setup unit mocks
            mock_a.return_value.process.return_value = {"foundation_score": 0.85}
            mock_b.return_value.process.return_value = {"energy_level": 0.72}
            mock_c.return_value.process.return_value = {"dynamics_score": 0.68}
            mock_d.return_value.process.return_value = {"validation_score": 0.91}
            
            pipeline = GrokPipeline(config=test_config)
            result = pipeline.process_session(sample_session_data, sample_micro_timing)
            
            # Verify complete flow
            assert result["success"] is True
            assert len(result) >= 5  # At least unit results + metadata
            
            # Verify all units were called
            assert mock_a.called
            assert mock_b.called
            assert mock_c.called
            assert mock_d.called
    
    @pytest.mark.integration
    def test_pipeline_with_tracker_context(self, test_config, sample_session_data, sample_tracker_context):
        """Test pipeline with tracker context integration"""
        with patch('src.pipeline.TrackerStateManager') as mock_tracker:
            mock_tracker.return_value.extract_tracker_context.return_value = sample_tracker_context
            
            pipeline = GrokPipeline(config=test_config)
            
            # Verify tracker context is loaded
            assert pipeline.tracker_context is not None
            assert "t_memory" in pipeline.tracker_context
