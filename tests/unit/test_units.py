#!/usr/bin/env python3
"""
Unit tests for computational units A, B, C, D
Tests individual unit functionality and data processing
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.unit_a import create_unit_a
from src.unit_b import create_unit_b
from src.unit_c import create_unit_c
from src.unit_d import create_unit_d
from conftest import assert_valid_probability, assert_valid_price


class TestUnitA:
    """Test cases for Unit A - Foundation calculations"""
    
    @patch('src.unit_a.GrokClient')
    def test_create_unit_a(self, mock_grok_client, test_config):
        """Test Unit A creation"""
        unit_a = create_unit_a(test_config)
        
        assert unit_a is not None
        assert hasattr(unit_a, 'process')
        assert hasattr(unit_a, 'config')
    
    @patch('src.unit_a.GrokClient')
    def test_unit_a_process_success(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit A successful processing"""
        # Setup mock client
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "foundation_score": 0.85,
                "structural_integrity": 0.78,
                "base_calculations": {"momentum": 0.65, "volatility": 0.42}
            }
        }
        mock_grok_client.return_value = mock_client
        
        unit_a = create_unit_a(test_config)
        result = unit_a.process(sample_session_data)
        
        assert result["success"] is True
        assert "foundation_score" in result
        assert_valid_probability(result["foundation_score"])
    
    @patch('src.unit_a.GrokClient')
    def test_unit_a_process_failure(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit A handles processing failure"""
        mock_client = Mock()
        mock_client.call_grok_api.side_effect = Exception("API Error")
        mock_grok_client.return_value = mock_client
        
        unit_a = create_unit_a(test_config)
        result = unit_a.process(sample_session_data)
        
        assert result["success"] is False
        assert "error" in result
    
    @patch('src.unit_a.GrokClient')
    def test_unit_a_with_tracker_context(self, mock_grok_client, test_config, 
                                       sample_session_data, sample_tracker_context):
        """Test Unit A with tracker context"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {"foundation_score": 0.90}
        }
        mock_grok_client.return_value = mock_client
        
        unit_a = create_unit_a(test_config, tracker_context=sample_tracker_context)
        result = unit_a.process(sample_session_data)
        
        assert result["success"] is True
        # Should have higher score with tracker context
        assert result["foundation_score"] >= 0.85


class TestUnitB:
    """Test cases for Unit B - Energy structure calculations"""
    
    @patch('src.unit_b.GrokClient')
    def test_create_unit_b(self, mock_grok_client, test_config):
        """Test Unit B creation"""
        unit_b = create_unit_b(test_config)
        
        assert unit_b is not None
        assert hasattr(unit_b, 'process')
        assert hasattr(unit_b, 'config')
    
    @patch('src.unit_b.GrokClient')
    def test_unit_b_process_success(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit B successful processing"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "energy_level": 0.72,
                "energy_distribution": {"high": 0.3, "medium": 0.5, "low": 0.2},
                "energy_flow_direction": "bullish"
            }
        }
        mock_grok_client.return_value = mock_client
        
        unit_b = create_unit_b(test_config)
        result = unit_b.process(sample_session_data, {"foundation_score": 0.85})
        
        assert result["success"] is True
        assert "energy_level" in result
        assert_valid_probability(result["energy_level"])
    
    @patch('src.unit_b.GrokClient')
    def test_unit_b_energy_calculations(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit B energy calculations are reasonable"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "energy_level": 0.68,
                "energy_distribution": {"high": 0.25, "medium": 0.55, "low": 0.20}
            }
        }
        mock_grok_client.return_value = mock_client
        
        unit_b = create_unit_b(test_config)
        result = unit_b.process(sample_session_data, {"foundation_score": 0.80})
        
        # Energy distribution should sum to 1.0
        energy_dist = result["energy_distribution"]
        total_energy = sum(energy_dist.values())
        assert abs(total_energy - 1.0) < 0.01  # Allow small floating point errors


class TestUnitC:
    """Test cases for Unit C - Advanced dynamics"""
    
    @patch('src.unit_c.GrokClient')
    def test_create_unit_c(self, mock_grok_client, test_config):
        """Test Unit C creation"""
        unit_c = create_unit_c(test_config)
        
        assert unit_c is not None
        assert hasattr(unit_c, 'process')
        assert hasattr(unit_c, 'config')
    
    @patch('src.unit_c.GrokClient')
    def test_unit_c_process_success(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit C successful processing"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "dynamics_score": 0.68,
                "complexity_analysis": {"fractal_dimension": 1.45, "entropy": 0.73},
                "market_regime": "trending"
            }
        }
        mock_grok_client.return_value = mock_client
        
        unit_c = create_unit_c(test_config)
        previous_results = {
            "foundation_score": 0.85,
            "energy_level": 0.72
        }
        result = unit_c.process(sample_session_data, previous_results)
        
        assert result["success"] is True
        assert "dynamics_score" in result
        assert_valid_probability(result["dynamics_score"])
    
    @patch('src.unit_c.GrokClient')
    def test_unit_c_complexity_analysis(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit C complexity analysis"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "dynamics_score": 0.75,
                "complexity_analysis": {
                    "fractal_dimension": 1.62,
                    "entropy": 0.68,
                    "hurst_exponent": 0.55
                }
            }
        }
        mock_grok_client.return_value = mock_client
        
        unit_c = create_unit_c(test_config)
        result = unit_c.process(sample_session_data, {"foundation_score": 0.80})
        
        complexity = result["complexity_analysis"]
        
        # Fractal dimension should be between 1 and 2 for financial time series
        assert 1.0 <= complexity["fractal_dimension"] <= 2.0
        
        # Entropy should be between 0 and 1
        assert_valid_probability(complexity["entropy"])
        
        # Hurst exponent should be between 0 and 1
        assert_valid_probability(complexity["hurst_exponent"])


class TestUnitD:
    """Test cases for Unit D - Integration validation"""
    
    @patch('src.unit_d.GrokClient')
    def test_create_unit_d(self, mock_grok_client, test_config):
        """Test Unit D creation"""
        unit_d = create_unit_d(test_config)
        
        assert unit_d is not None
        assert hasattr(unit_d, 'process')
        assert hasattr(unit_d, 'config')
    
    @patch('src.unit_d.GrokClient')
    def test_unit_d_process_success(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit D successful processing"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "validation_score": 0.91,
                "integration_quality": 0.87,
                "consistency_check": "passed",
                "final_predictions": {
                    "close_price": 23350.0,
                    "high_probability": 0.78,
                    "low_probability": 0.65
                }
            }
        }
        mock_grok_client.return_value = mock_client
        
        unit_d = create_unit_d(test_config)
        previous_results = {
            "foundation_score": 0.85,
            "energy_level": 0.72,
            "dynamics_score": 0.68
        }
        result = unit_d.process(sample_session_data, previous_results)
        
        assert result["success"] is True
        assert "validation_score" in result
        assert_valid_probability(result["validation_score"])
    
    @patch('src.unit_d.GrokClient')
    def test_unit_d_final_predictions(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit D final predictions are valid"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "validation_score": 0.88,
                "final_predictions": {
                    "close_price": 23420.0,
                    "high_price": 23480.0,
                    "low_price": 23280.0,
                    "confidence": 0.82
                }
            }
        }
        mock_grok_client.return_value = mock_client
        
        unit_d = create_unit_d(test_config)
        result = unit_d.process(sample_session_data, {"validation_score": 0.85})
        
        predictions = result["final_predictions"]
        
        # Validate price predictions
        assert_valid_price(predictions["close_price"], "predicted close")
        assert_valid_price(predictions["high_price"], "predicted high")
        assert_valid_price(predictions["low_price"], "predicted low")
        
        # Validate price relationships
        assert predictions["high_price"] >= predictions["close_price"]
        assert predictions["low_price"] <= predictions["close_price"]
        
        # Validate confidence
        assert_valid_probability(predictions["confidence"])
    
    @patch('src.unit_d.GrokClient')
    def test_unit_d_integration_validation(self, mock_grok_client, test_config, sample_session_data):
        """Test Unit D integration validation logic"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "validation_score": 0.93,
                "integration_quality": 0.89,
                "consistency_metrics": {
                    "unit_coherence": 0.91,
                    "data_integrity": 0.95,
                    "logical_consistency": 0.87
                }
            }
        }
        mock_grok_client.return_value = mock_client
        
        unit_d = create_unit_d(test_config)
        comprehensive_results = {
            "foundation_score": 0.88,
            "energy_level": 0.75,
            "dynamics_score": 0.71,
            "structural_integrity": 0.82
        }
        result = unit_d.process(sample_session_data, comprehensive_results)
        
        consistency = result["consistency_metrics"]
        
        # All consistency metrics should be high probabilities
        for metric_name, value in consistency.items():
            assert_valid_probability(value, f"consistency metric {metric_name}")
            assert value >= 0.5, f"Consistency metric {metric_name} should be reasonably high"


class TestUnitsIntegration:
    """Integration tests for unit interactions"""
    
    @pytest.mark.integration
    def test_units_sequential_processing(self, test_config, sample_session_data):
        """Test units process data sequentially with proper data flow"""
        with patch('src.unit_a.GrokClient') as mock_a, \
             patch('src.unit_b.GrokClient') as mock_b, \
             patch('src.unit_c.GrokClient') as mock_c, \
             patch('src.unit_d.GrokClient') as mock_d:
            
            # Setup sequential mock responses
            mock_a.return_value.call_grok_api.return_value = {
                "success": True, "result": {"foundation_score": 0.85}
            }
            mock_b.return_value.call_grok_api.return_value = {
                "success": True, "result": {"energy_level": 0.72}
            }
            mock_c.return_value.call_grok_api.return_value = {
                "success": True, "result": {"dynamics_score": 0.68}
            }
            mock_d.return_value.call_grok_api.return_value = {
                "success": True, "result": {"validation_score": 0.91}
            }
            
            # Create units
            unit_a = create_unit_a(test_config)
            unit_b = create_unit_b(test_config)
            unit_c = create_unit_c(test_config)
            unit_d = create_unit_d(test_config)
            
            # Process sequentially
            result_a = unit_a.process(sample_session_data)
            result_b = unit_b.process(sample_session_data, result_a)
            result_c = unit_c.process(sample_session_data, {**result_a, **result_b})
            result_d = unit_d.process(sample_session_data, {**result_a, **result_b, **result_c})
            
            # Verify sequential processing
            assert result_a["success"] is True
            assert result_b["success"] is True
            assert result_c["success"] is True
            assert result_d["success"] is True
            
            # Verify data flow
            assert "foundation_score" in result_a
            assert "energy_level" in result_b
            assert "dynamics_score" in result_c
            assert "validation_score" in result_d
