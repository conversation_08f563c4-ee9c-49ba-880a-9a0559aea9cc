#!/usr/bin/env python3
"""
Integration tests for tracker state management system
Tests the interaction between HTF context, FVG state, and liquidity tracking
"""

import pytest
import json
import tempfile
from pathlib import Path
import sys
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.tracker_state import TrackerStateManager, create_tracker_context_from_inputs
from conftest import assert_valid_probability


class TestTrackerStateManager:
    """Test cases for TrackerStateManager"""
    
    def test_tracker_manager_initialization(self):
        """Test tracker manager initializes correctly"""
        manager = TrackerStateManager()
        
        assert manager is not None
        assert hasattr(manager, 'extract_tracker_context')
        assert hasattr(manager, 'calculate_t_memory_decay')
    
    def test_extract_tracker_context_complete(self, sample_tracker_context):
        """Test tracker context extraction with complete data"""
        manager = TrackerStateManager()
        
        htf_context = {"active_structures": sample_tracker_context["active_structures"]}
        fvg_state = {"t_memory": sample_tracker_context["t_memory"]}
        liquidity_state = {"untaken_liquidity": sample_tracker_context["untaken_liquidity"]}
        
        result = manager.extract_tracker_context(htf_context, fvg_state, liquidity_state)
        
        assert "t_memory" in result
        assert "active_structures" in result
        assert "untaken_liquidity" in result
        assert "htf_influence_factor" in result
        assert "liquidity_gradient" in result
        
        # Validate extracted values
        assert result["t_memory"] > 0
        assert_valid_probability(result["htf_influence_factor"])
    
    def test_extract_tracker_context_partial(self):
        """Test tracker context extraction with partial data"""
        manager = TrackerStateManager()
        
        # Only provide HTF context
        htf_context = {"active_structures": [{"price_level": 23400, "strength": 0.8}]}
        fvg_state = {}
        liquidity_state = {}
        
        result = manager.extract_tracker_context(htf_context, fvg_state, liquidity_state)
        
        # Should still return valid structure with defaults
        assert "t_memory" in result
        assert "active_structures" in result
        assert len(result["active_structures"]) > 0
    
    def test_extract_tracker_context_empty(self):
        """Test tracker context extraction with empty data"""
        manager = TrackerStateManager()
        
        result = manager.extract_tracker_context({}, {}, {})
        
        # Should return valid structure with defaults
        assert "t_memory" in result
        assert "active_structures" in result
        assert "untaken_liquidity" in result
        assert result["t_memory"] > 0  # Should have default value
    
    def test_calculate_t_memory_decay(self):
        """Test T_memory decay calculation"""
        manager = TrackerStateManager()
        
        initial_t_memory = 20.0
        hours_elapsed = 2.0
        
        decayed = manager.calculate_t_memory_decay(initial_t_memory, hours_elapsed)
        
        # Should decay over time
        assert decayed < initial_t_memory
        assert decayed > 0  # Should not go negative
        
        # Test with longer time period
        long_decay = manager.calculate_t_memory_decay(initial_t_memory, 10.0)
        assert long_decay < decayed  # More decay over longer time
    
    def test_calculate_t_memory_decay_edge_cases(self):
        """Test T_memory decay with edge cases"""
        manager = TrackerStateManager()
        
        # Zero initial value
        result = manager.calculate_t_memory_decay(0.0, 1.0)
        assert result >= manager.T_MEMORY_MIN
        
        # Negative time (should return minimum)
        result = manager.calculate_t_memory_decay(20.0, -1.0)
        assert result >= manager.T_MEMORY_MIN
        
        # Very large time (should approach minimum)
        result = manager.calculate_t_memory_decay(20.0, 100.0)
        assert result <= manager.T_MEMORY_MIN + 0.1  # Close to minimum
    
    def test_extract_pipeline_essentials(self, sample_tracker_context):
        """Test extraction of pipeline essentials"""
        manager = TrackerStateManager()
        
        essentials = manager.extract_pipeline_essentials(sample_tracker_context)
        
        assert "unit_a" in essentials
        assert "unit_b" in essentials
        assert "unit_c" in essentials
        assert "unit_d" in essentials
        
        # Check Unit A essentials
        unit_a = essentials["unit_a"]
        assert "t_memory" in unit_a
        assert "nearest_htf_distance" in unit_a
        
        # Check Unit B essentials
        unit_b = essentials["unit_b"]
        assert "liquidity_density" in unit_b
        assert "energy_carryover" in unit_b


class TestTrackerContextFromInputs:
    """Test cases for create_tracker_context_from_inputs function"""
    
    def test_create_context_with_files(self, mock_file_system):
        """Test creating tracker context from actual files"""
        context = create_tracker_context_from_inputs(
            htf_context_file=mock_file_system["htf_file"],
            fvg_state_file=mock_file_system["fvg_file"],
            liquidity_state_file=None  # Test with missing file
        )
        
        assert context is not None
        assert "t_memory" in context
        assert "active_structures" in context
        assert "untaken_liquidity" in context
    
    def test_create_context_missing_files(self):
        """Test creating tracker context with missing files"""
        context = create_tracker_context_from_inputs(
            htf_context_file="nonexistent.json",
            fvg_state_file="also_nonexistent.json",
            liquidity_state_file="still_nonexistent.json"
        )
        
        # Should still return valid context with defaults
        assert context is not None
        assert "t_memory" in context
        assert context["t_memory"] > 0
    
    def test_create_context_no_files(self):
        """Test creating tracker context with no files specified"""
        context = create_tracker_context_from_inputs()
        
        # Should return valid context with defaults
        assert context is not None
        assert "t_memory" in context
        assert "active_structures" in context
        assert "untaken_liquidity" in context


class TestTrackerStateUpdates:
    """Test cases for tracker state updates"""
    
    def test_update_tracker_states_from_session(self, sample_session_data, sample_tracker_context):
        """Test updating tracker states from session results"""
        manager = TrackerStateManager()
        
        unit_results = {
            "unit_a_results": {"foundation_score": 0.85},
            "unit_b_results": {"energy_level": 0.72},
            "unit_c_results": {"dynamics_score": 0.68},
            "unit_d_results": {"validation_score": 0.91}
        }
        
        updated_states = manager.update_tracker_states_from_session(
            sample_tracker_context, unit_results, sample_session_data
        )
        
        assert "t_memory" in updated_states
        assert "active_structures" in updated_states
        assert "untaken_liquidity" in updated_states
        
        # T_memory should be updated based on session processing
        assert updated_states["t_memory"] != sample_tracker_context["t_memory"]
    
    def test_generate_output_htf_context(self, sample_session_data):
        """Test generating HTF context output"""
        manager = TrackerStateManager()
        
        pipeline_results = {
            "original_session_data": sample_session_data,
            "grok_enhanced_calculations": {
                "unit_d_integration_validation": {
                    "extracted_values": {"structural_integrity": 0.85}
                }
            }
        }
        
        htf_context = manager.generate_output_htf_context(pipeline_results)
        
        assert "active_structures" in htf_context
        assert "htf_influence_factor" in htf_context
        assert "last_update" in htf_context
        assert "session_continuity" in htf_context
        
        # HTF influence should be a valid probability
        assert_valid_probability(htf_context["htf_influence_factor"])
    
    def test_generate_output_fvg_state(self, sample_session_data):
        """Test generating FVG state output"""
        manager = TrackerStateManager()
        
        pipeline_results = {
            "original_session_data": sample_session_data,
            "grok_enhanced_calculations": {
                "unit_b_energy_structure": {
                    "extracted_values": {"energy_processing_rate": 1.2}
                }
            }
        }
        
        fvg_state = manager.generate_output_fvg_state(pipeline_results)
        
        assert "t_memory" in fvg_state
        assert "energy_carryover" in fvg_state
        assert "fvg_density" in fvg_state
        assert "last_update" in fvg_state
        
        # T_memory should be positive
        assert fvg_state["t_memory"] > 0
        
        # Energy carryover should be reasonable
        assert 0 <= fvg_state["energy_carryover"] <= 2.0
    
    def test_generate_output_liquidity_state(self, sample_session_data):
        """Test generating liquidity state output"""
        manager = TrackerStateManager()
        
        pipeline_results = {
            "original_session_data": sample_session_data,
            "grok_enhanced_calculations": {
                "unit_c_advanced_dynamics": {
                    "extracted_values": {"liquidity_interaction_strength": 0.75}
                }
            }
        }
        
        liquidity_state = manager.generate_output_liquidity_state(pipeline_results)
        
        assert "untaken_liquidity" in liquidity_state
        assert "liquidity_gradient" in liquidity_state
        assert "interaction_history" in liquidity_state
        assert "last_update" in liquidity_state
        
        # Should have some liquidity levels
        assert len(liquidity_state["untaken_liquidity"]) >= 0


class TestTrackerIntegrationWorkflow:
    """Integration tests for complete tracker workflow"""
    
    @pytest.mark.integration
    def test_complete_tracker_workflow(self, mock_file_system, sample_session_data):
        """Test complete tracker workflow from input to output"""
        # Step 1: Load tracker context from files
        input_context = create_tracker_context_from_inputs(
            htf_context_file=mock_file_system["htf_file"],
            fvg_state_file=mock_file_system["fvg_file"]
        )
        
        assert input_context is not None
        
        # Step 2: Extract pipeline essentials
        manager = TrackerStateManager()
        essentials = manager.extract_pipeline_essentials(input_context)
        
        assert essentials is not None
        assert len(essentials) == 4  # All four units
        
        # Step 3: Simulate pipeline processing
        mock_unit_results = {
            "unit_a_results": {"foundation_score": 0.85},
            "unit_b_results": {"energy_level": 0.72},
            "unit_c_results": {"dynamics_score": 0.68},
            "unit_d_results": {"validation_score": 0.91}
        }
        
        # Step 4: Update tracker states
        updated_states = manager.update_tracker_states_from_session(
            input_context, mock_unit_results, sample_session_data
        )
        
        assert updated_states is not None
        
        # Step 5: Generate output files
        pipeline_results = {
            "original_session_data": sample_session_data,
            "grok_enhanced_calculations": mock_unit_results
        }
        
        output_htf = manager.generate_output_htf_context(pipeline_results)
        output_fvg = manager.generate_output_fvg_state(pipeline_results)
        output_liquidity = manager.generate_output_liquidity_state(pipeline_results)
        
        # Verify all outputs are valid
        assert output_htf is not None
        assert output_fvg is not None
        assert output_liquidity is not None
        
        # Verify continuity
        assert "session_continuity" in output_htf
        assert "session_continuity" in output_fvg
        assert "session_continuity" in output_liquidity
    
    @pytest.mark.integration
    def test_tracker_memory_persistence(self):
        """Test that tracker memory persists correctly across sessions"""
        manager = TrackerStateManager()
        
        # Initial T_memory
        initial_t_memory = 20.0
        
        # Simulate 1 hour gap
        decayed_1h = manager.calculate_t_memory_decay(initial_t_memory, 1.0)
        
        # Simulate another 1 hour gap
        decayed_2h = manager.calculate_t_memory_decay(decayed_1h, 1.0)
        
        # Should show progressive decay
        assert initial_t_memory > decayed_1h > decayed_2h
        
        # But should not go below minimum
        very_long_decay = manager.calculate_t_memory_decay(initial_t_memory, 100.0)
        assert very_long_decay >= manager.T_MEMORY_MIN
