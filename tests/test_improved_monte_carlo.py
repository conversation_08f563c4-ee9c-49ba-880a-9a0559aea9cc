#!/usr/bin/env python3
"""
Test Improved Monte Carlo Predictions
Tests the implemented pattern-based corrections discovered from Grok 4 analysis
"""

import json
from monte_carlo_adapter import run_monte_carlo_from_session

def test_session_character_corrections():
    """Test the session character-based corrections on actual session files"""
    
    # Test cases from our analysis
    test_cases = [
        {
            "name": "Midnight Session (expansion_then_consolidation)",
            "file": "midnight_grokEnhanced_2025_07_22.json",
            "expected_character": "expansion_then_consolidation",
            "expected_multiplier": 0.3,
            "actual_price": 23311.25,
            "description": "Should have strong magnitude dampening"
        },
        {
            "name": "London Session (volatile_with_major_redeliveries)", 
            "file": "london_grokEnhanced_2025_07_22.json",
            "expected_character": "volatile_with_major_redeliveries",
            "expected_multiplier": 1.0,
            "actual_price": 23276.75,
            "description": "Should have no magnitude dampening but higher volatility"
        }
    ]
    
    print("🧪 Testing Improved Monte Carlo with Pattern-Based Corrections")
    print("=" * 70)
    
    for test_case in test_cases:
        print(f"\n📊 Testing: {test_case['name']}")
        print(f"Expected Character: {test_case['expected_character']}")
        print(f"Expected Multiplier: {test_case['expected_multiplier']}")
        print(f"Actual Price: {test_case['actual_price']}")
        
        try:
            # Run improved Monte Carlo simulation
            results = run_monte_carlo_from_session(
                test_case["file"], 
                n_simulations=1000,
                duration_minutes=180
            )
            
            # Extract key results
            predictions = results["monte_carlo_results"]
            bands = predictions["prediction_bands"]
            
            predicted_price = bands["final_price_percentiles"]["50th"]
            predicted_range = bands["range_percentiles"]["50th"]
            
            # Extract session character from parameter extraction
            parameter_extraction = results.get("parameter_extraction", {})
            extracted_params = parameter_extraction.get("extracted_parameters", {})
            session_character = extracted_params.get("session_character", "unknown")
            
            # Calculate improvement metrics
            price_error = abs(predicted_price - test_case["actual_price"])
            price_error_pct = (price_error / test_case["actual_price"]) * 100
            
            print(f"\n✅ Results:")
            print(f"   Detected Character: {session_character}")
            print(f"   Predicted Price: {predicted_price:.2f}")
            print(f"   Actual Price: {test_case['actual_price']}")
            print(f"   Price Error: {price_error:.2f} points ({price_error_pct:.3f}%)")
            print(f"   Predicted Range: {predicted_range:.2f}")
            
            # Validate session character detection
            if test_case["expected_character"] in session_character or session_character in test_case["expected_character"]:
                print(f"   ✅ Session character correctly detected")
            else:
                print(f"   ⚠️  Session character mismatch: expected '{test_case['expected_character']}', got '{session_character}'")
            
            # Check if error improved (compare to original 19.15 and 63.88 points)
            original_errors = {"expansion_then_consolidation": 19.15, "volatile_with_major_redeliveries": 63.88}
            original_error = original_errors.get("expansion_then_consolidation" if "consolidation" in session_character else "volatile_with_major_redeliveries", 50.0)
            
            if price_error < original_error:
                improvement = ((original_error - price_error) / original_error) * 100
                print(f"   🎯 IMPROVEMENT: {improvement:.1f}% better than original ({original_error:.2f} → {price_error:.2f})")
            else:
                degradation = ((price_error - original_error) / original_error) * 100
                print(f"   📉 Degradation: {degradation:.1f}% worse than original ({original_error:.2f} → {price_error:.2f})")
            
        except FileNotFoundError:
            print(f"   ❌ File not found: {test_case['file']}")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print(f"\n🔬 Pattern-Based Correction Summary:")
    print(f"Applied mathematical relationships:")
    print(f"• session_character_multiplier = 0.3 if 'consolidation' else 1.0")
    print(f"• volatility_adjustment = 2.5 if 'volatile' else 0.0")
    print(f"• Dynamic magnitude caps based on session character")
    print(f"• Discovered from Asia → MNOR/London analysis workflow")

def compare_original_vs_improved():
    """Compare original vs improved predictions"""
    
    print(f"\n📈 Original vs Improved Comparison")
    print("=" * 50)
    
    # Original results from pattern analysis
    original_results = {
        "midnight": {"error": 19.15, "character": "expansion_then_consolidation"},
        "london": {"error": 63.88, "character": "volatile_with_major_redeliveries"}
    }
    
    for session, original in original_results.items():
        print(f"\n{session.title()} Session:")
        print(f"  Original Error: {original['error']:.2f} points")
        print(f"  Session Character: {original['character']}")
        print(f"  Applied Correction: {'0.3x magnitude dampening' if 'consolidation' in original['character'] else 'No dampening, +2.5 volatility'}")

if __name__ == "__main__":
    # Test the improved Monte Carlo with pattern-based corrections
    test_session_character_corrections()
    
    # Compare with original results
    compare_original_vs_improved()
    
    print(f"\n🎯 Next Steps:")
    print(f"1. Validate improvements on additional session data")
    print(f"2. Fine-tune multipliers based on expanded testing")
    print(f"3. Implement additional discovered relationships")
    print(f"4. Create automated validation pipeline")