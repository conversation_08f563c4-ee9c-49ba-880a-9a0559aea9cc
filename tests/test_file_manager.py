#!/usr/bin/env python3
"""
Test File Manager - Naming Convention Validation
Tests the strict file management system for proper naming conventions
"""

import os
import json
import sys
from typing import Dict, List

# Add src path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'experimental'))

from file_manager import SessionFileManager, discover_session, validate_directory

def test_naming_convention_validation():
    """Test naming convention validation with various file patterns"""
    
    print("🔧 TESTING NAMING CONVENTION VALIDATION")
    print("=" * 50)
    
    manager = SessionFileManager()
    
    # Test cases for different naming patterns
    test_files = [
        # VALID session files
        ("midnight_grokEnhanced_2025_07_22.json", True, "Valid session file"),
        ("london_grokEnhanced_2025_07_23.json", True, "Valid session file"),
        ("newyork_grokEnhanced_2025_07_24.json", True, "Valid session file"),
        
        # VALID tracker files
        ("HTF_Context_Midnight_grokEnhanced_2025_07_22.json", True, "Valid HTF_Context tracker"),
        ("FVG_State_London_grokEnhanced_2025_07_23.json", True, "Valid FVG_State tracker"),
        ("Liquidity_State_Newyork_grokEnhanced_2025_07_24.json", True, "Valid Liquidity_State tracker"),
        
        # INVALID naming patterns (common mistakes)
        ("midnight_grok_enhanced_2025_07_22.json", False, "Wrong separator - should be grokEnhanced"),
        ("midnight_grokenhanced_2025_07_22.json", False, "Wrong case - should be grokEnhanced"),
        ("midnight_grokEnhanced_2025-07-22.json", False, "Wrong date separator - should be underscores"),
        ("midnight_grokEnhanced_25_07_22.json", False, "Wrong year format - should be 4 digits"),
        ("Midnight_grokEnhanced_2025_07_22.json", False, "Session should be lowercase"),
        ("htf_context_Midnight_grokEnhanced_2025_07_22.json", False, "Tracker should be uppercase"),
        ("HTF_Context_midnight_grokEnhanced_2025_07_22.json", False, "Session in tracker should be titlecase"),
        
        # Files that would be missed by yesterday's system
        ("midnight_grokeenhanced_2025_07_22.json", False, "Typo in grokEnhanced"),
        ("midnight_grokEnhanced_2025_7_22.json", False, "Single digit month - should be zero-padded"),
        ("midnight_grokEnhanced_2025_07_2.json", False, "Single digit day - should be zero-padded"),
    ]
    
    print("📋 Testing file naming patterns:")
    
    correct_predictions = 0
    total_tests = len(test_files)
    
    for filename, expected_valid, description in test_files:
        is_valid, message = manager.validate_naming_convention(filename)
        
        status = "✅" if is_valid == expected_valid else "❌"
        result = "VALID" if is_valid else "INVALID"
        
        print(f"   {status} {filename}")
        print(f"      Expected: {'VALID' if expected_valid else 'INVALID'}, Got: {result}")
        print(f"      {description}")
        
        if is_valid == expected_valid:
            correct_predictions += 1
        else:
            print(f"      ⚠️  MISMATCH: {message}")
    
    accuracy = (correct_predictions / total_tests) * 100
    print(f"\n📊 Validation Accuracy: {correct_predictions}/{total_tests} ({accuracy:.1f}%)")
    
    return accuracy == 100.0

def test_file_discovery_with_mock_files():
    """Test file discovery with mock files"""
    
    print(f"\n🔍 TESTING FILE DISCOVERY WITH MOCK DATA")
    print("=" * 45)
    
    # Create mock file structure for testing
    mock_files = {
        "midnight_grokEnhanced_2025_07_22.json": {
            "price_data": {
                "open": 23330.0,
                "close": 23320.0,
                "session_character": "expansion_then_consolidation"
            }
        },
        "HTF_Context_Midnight_grokEnhanced_2025_07_22.json": {
            "htf_structure_registry": [23300.0, 23350.0, 23400.0],
            "e_threshold_adjustment": 1000
        },
        "FVG_State_Midnight_grokEnhanced_2025_07_22.json": {
            "t_memory_carryover": 5.117,
            "fvg_levels": [23325.0, 23355.0]
        },
        "Liquidity_State_Midnight_grokEnhanced_2025_07_22.json": {
            "untaken_liquidity_registry": [23320.0, 23350.0, 23380.0],
            "liquidity_gradient": 0.015,
            "liquidity_interactions": [
                {
                    "timestamp": 0.0,
                    "price": 23330.0,
                    "liquidity_level": 23320.0,
                    "from_state": "approaching",
                    "to_state": "touching"
                }
            ]
        }
    }
    
    # Create temporary mock files
    temp_files = []
    
    try:
        print("📁 Creating mock files for testing...")
        for filename, content in mock_files.items():
            with open(filename, 'w') as f:
                json.dump(content, f, indent=2)
            temp_files.append(filename)
            print(f"   ✅ Created: {filename}")
        
        # Test file discovery
        print(f"\n🔍 Testing strict file discovery...")
        session_files = discover_session("midnight", "2025_07_22")
        
        print(f"   ✅ Discovery successful:")
        print(f"      Session: {os.path.basename(session_files.session_file)}")
        print(f"      HTF Context: {os.path.basename(session_files.htf_context_file)}")
        print(f"      FVG State: {os.path.basename(session_files.fvg_state_file)}")
        print(f"      Liquidity State: {os.path.basename(session_files.liquidity_state_file)}")
        
        # Test strict validation
        print(f"\n✅ Testing strict validation...")
        if session_files.validate_all_exist():
            print("   ✅ All files exist - validation passed")
        else:
            missing = session_files.get_missing_files()
            print(f"   ❌ Missing files: {missing}")
            return False
        
        # Test with missing file (strict validation should fail)
        print(f"\n🚨 Testing strict validation with missing file...")
        os.remove(temp_files[1])  # Remove HTF Context file
        temp_files.remove(temp_files[1])
        
        try:
            discover_session("midnight", "2025_07_22")
            print("   ❌ ERROR: Discovery should have failed with missing file")
            return False
        except FileNotFoundError as e:
            print("   ✅ Strict validation correctly failed with missing file")
            print(f"      Expected behavior: {type(e).__name__}")
        
        return True
        
    finally:
        # Clean up temporary files
        print(f"\n🧹 Cleaning up mock files...")
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                print(f"   🗑️  Removed: {temp_file}")

def test_directory_scanning():
    """Test directory scanning for naming issues"""
    
    print(f"\n📊 TESTING DIRECTORY SCANNING")
    print("=" * 32)
    
    # Create test files with various naming issues
    test_files_content = {
        # Valid files
        "valid_session_grokEnhanced_2025_07_22.json": {"test": "data"},
        "HTF_Context_Valid_grokEnhanced_2025_07_22.json": {"test": "data"},
        
        # Invalid files that should be caught
        "invalid_grok_enhanced_2025_07_22.json": {"test": "data"},  # Wrong separator
        "invalid_grokEnhanced_2025-07-22.json": {"test": "data"},  # Wrong date format
        "Invalid_grokEnhanced_2025_07_22.json": {"test": "data"},  # Wrong case
    }
    
    temp_files = []
    
    try:
        # Create test files
        for filename, content in test_files_content.items():
            with open(filename, 'w') as f:
                json.dump(content, f)
            temp_files.append(filename)
        
        # Scan directory
        issues = validate_directory()
        
        print(f"📋 Scan Results:")
        print(f"   Valid files: {len(issues['valid_files'])}")
        print(f"   Invalid names: {len(issues['invalid_names'])}")
        
        # Should find our test invalid files
        expected_invalid = 3  # The invalid files we created
        actual_invalid = len(issues['invalid_names'])
        
        print(f"\n🎯 Validation Check:")
        print(f"   Expected invalid files: {expected_invalid}")
        print(f"   Found invalid files: {actual_invalid}")
        
        if issues['invalid_names']:
            print(f"   Invalid files found:")
            for issue in issues['invalid_names']:
                print(f"      • {issue}")
        
        return True
        
    finally:
        # Cleanup
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.remove(temp_file)

def run_comprehensive_file_manager_tests():
    """Run complete test suite for file manager"""
    
    print("🧪 FILE MANAGER COMPREHENSIVE TEST SUITE")
    print("=" * 50)
    print("Testing strict naming convention validation and file discovery\n")
    
    test_results = {
        "naming_validation": False,
        "file_discovery": False,
        "directory_scanning": False
    }
    
    try:
        # Test 1: Naming Convention Validation
        print("TEST 1: Naming Convention Validation")
        print("-" * 38)
        test_results["naming_validation"] = test_naming_convention_validation()
        
        # Test 2: File Discovery with Mock Files
        print("TEST 2: File Discovery")
        print("-" * 20)
        test_results["file_discovery"] = test_file_discovery_with_mock_files()
        
        # Test 3: Directory Scanning
        print("TEST 3: Directory Scanning")  
        print("-" * 25)
        test_results["directory_scanning"] = test_directory_scanning()
        
        # Results Summary
        print(f"\n🎯 COMPREHENSIVE TEST RESULTS")
        print("=" * 35)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        overall_success = passed_tests == total_tests
        
        if overall_success:
            print(f"\n🎉 SUCCESS: All {total_tests} tests passed!")
            print("   ✅ Naming convention validation: ACCURATE")
            print("   ✅ Strict file discovery: FUNCTIONAL")
            print("   ✅ Directory scanning: OPERATIONAL")
            print("   ✅ Mathematical integrity: ENSURED")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: {passed_tests}/{total_tests} tests passed")
            print("   Some functionality needs attention")
        
        print(f"\n📋 SYSTEM STATUS:")
        print("   🔒 Strict validation: ENFORCED")
        print("   📁 Automatic discovery: READY")
        print("   🎯 Naming convention: VALIDATED")
        print("   🔬 Mathematical integrity: PROTECTED")
        
        return overall_success
        
    except Exception as e:
        print(f"\n💥 TEST SUITE FAILED: {str(e)}")
        return False

if __name__ == "__main__":
    success = run_comprehensive_file_manager_tests()
    
    if success:
        print(f"\n🚀 FILE MANAGER READY FOR PRODUCTION")
        print("   Strict naming convention enforcement")
        print("   Automatic file discovery with validation")
        print("   Mathematical integrity protection")
        print("   Seamless integration with reverse engineering")
    else:
        print(f"\n🔧 FILE MANAGER NEEDS ATTENTION")
        print("   Please review failed tests before production use")
    
    print(f"\n🎯 NAMING CONVENTION ENFORCED:")
    print("   Sessions: [session]_grokEnhanced_YYYY_MM_DD.json")
    print("   Trackers: [TRACKER]_[Session]_grokEnhanced_YYYY_MM_DD.json")
    print("   Example: midnight_grokEnhanced_2025_07_22.json")
    print("   Example: HTF_Context_Midnight_grokEnhanced_2025_07_22.json")