#!/usr/bin/env python3
"""
End-to-end tests for complete financial prediction workflow
Tests the entire system from CLI input to final output files
"""

import pytest
import json
import tempfile
import subprocess
from pathlib import Path
import sys
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.pipeline import process_single_session
from conftest import assert_valid_session_data, assert_valid_probability


class TestCompleteWorkflow:
    """End-to-end tests for complete workflow"""
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_single_session_complete_workflow(self, mock_grok_client, sample_session_data, 
                                            sample_micro_timing, temp_data_dir):
        """Test complete workflow for single session processing"""
        # Setup mock Grok client responses for all units
        mock_client = Mock()
        mock_responses = [
            # Unit A response
            {"success": True, "result": {"foundation_score": 0.85, "structural_integrity": 0.78}},
            # Unit B response  
            {"success": True, "result": {"energy_level": 0.72, "energy_distribution": {"high": 0.3, "medium": 0.5, "low": 0.2}}},
            # Unit C response
            {"success": True, "result": {"dynamics_score": 0.68, "complexity_analysis": {"fractal_dimension": 1.45}}},
            # Unit D response
            {"success": True, "result": {"validation_score": 0.91, "final_predictions": {"close_price": 23350.0, "confidence": 0.82}}}
        ]
        mock_client.call_grok_api.side_effect = mock_responses
        mock_grok_client.return_value = mock_client
        
        # Process session
        result = process_single_session(
            session_data=sample_session_data,
            micro_timing_data=sample_micro_timing,
            api_key="test-api-key"
        )
        
        # Verify successful processing
        assert result["success"] is True
        assert "unit_a_results" in result
        assert "unit_b_results" in result
        assert "unit_c_results" in result
        assert "unit_d_results" in result
        assert "execution_time" in result
        
        # Verify data quality
        assert_valid_probability(result["unit_a_results"]["foundation_score"])
        assert_valid_probability(result["unit_b_results"]["energy_level"])
        assert_valid_probability(result["unit_c_results"]["dynamics_score"])
        assert_valid_probability(result["unit_d_results"]["validation_score"])
        
        # Verify final predictions
        predictions = result["unit_d_results"]["final_predictions"]
        assert predictions["close_price"] > 0
        assert_valid_probability(predictions["confidence"])
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_cross_session_workflow(self, mock_grok_client, sample_session_data, temp_data_dir):
        """Test cross-session workflow with tracker state persistence"""
        # Setup mock client
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True, 
            "result": {"foundation_score": 0.85, "energy_level": 0.72, "dynamics_score": 0.68, "validation_score": 0.91}
        }
        mock_grok_client.return_value = mock_client
        
        # Create initial tracker files
        htf_file = temp_data_dir / "HTF_Context_ASIA_2025_07_30.json"
        fvg_file = temp_data_dir / "FVG_State_ASIA_2025_07_30.json"
        liquidity_file = temp_data_dir / "Liquidity_State_ASIA_2025_07_30.json"
        
        initial_htf = {
            "active_structures": [{"price_level": 23400, "strength": 0.8}],
            "htf_influence_factor": 0.65,
            "last_update": "2025_07_30_09_00"
        }
        
        initial_fvg = {
            "t_memory": 15.5,
            "energy_carryover": 0.3,
            "fvg_density": 3,
            "last_update": "2025_07_30_09_00"
        }
        
        initial_liquidity = {
            "untaken_liquidity": [{"price": 23450, "volume": 2000, "probability": 0.7}],
            "liquidity_gradient": {"gradient_strength": 0.4, "liquidity_bias": "bullish"},
            "last_update": "2025_07_30_09_00"
        }
        
        # Write initial files
        with open(htf_file, 'w') as f:
            json.dump(initial_htf, f, indent=2)
        with open(fvg_file, 'w') as f:
            json.dump(initial_fvg, f, indent=2)
        with open(liquidity_file, 'w') as f:
            json.dump(initial_liquidity, f, indent=2)
        
        # Process first session
        result1 = process_single_session(sample_session_data, api_key="test-api-key")
        assert result1["success"] is True
        
        # Simulate time gap and process second session
        sample_session_data["session_metadata"]["date"] = "2025_07_30"
        sample_session_data["session_metadata"]["session_type"] = "LONDON"
        
        result2 = process_single_session(sample_session_data, api_key="test-api-key")
        assert result2["success"] is True
        
        # Verify both sessions processed successfully
        assert result1["execution_time"] > 0
        assert result2["execution_time"] > 0
    
    @pytest.mark.e2e
    @pytest.mark.slow
    def test_cli_integration(self, sample_session_data, temp_data_dir):
        """Test CLI integration with actual command execution"""
        # Create test session file
        session_file = temp_data_dir / "test_session.json"
        with open(session_file, 'w') as f:
            json.dump(sample_session_data, f, indent=2)
        
        # Test CLI help command
        result = subprocess.run(
            [sys.executable, "cli.py", "--help"],
            cwd=Path(__file__).parent.parent.parent,
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        assert "usage:" in result.stdout.lower() or "help" in result.stdout.lower()
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_error_recovery_workflow(self, mock_grok_client, sample_session_data):
        """Test workflow error recovery and graceful degradation"""
        # Setup client to fail on Unit B
        mock_client = Mock()
        mock_responses = [
            {"success": True, "result": {"foundation_score": 0.85}},  # Unit A succeeds
            Exception("Unit B API Error"),  # Unit B fails
        ]
        mock_client.call_grok_api.side_effect = mock_responses
        mock_grok_client.return_value = mock_client
        
        result = process_single_session(sample_session_data, api_key="test-api-key")
        
        # Should handle error gracefully
        assert result["success"] is False
        assert "error" in result
        assert "Unit B API Error" in str(result["error"])
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_data_validation_workflow(self, mock_grok_client, temp_data_dir):
        """Test workflow with invalid input data"""
        # Create invalid session data
        invalid_session = {
            "invalid_structure": True,
            "missing_required_fields": "yes"
        }
        
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {"success": True, "result": {}}
        mock_grok_client.return_value = mock_client
        
        result = process_single_session(invalid_session, api_key="test-api-key")
        
        # Should fail validation
        assert result["success"] is False
        assert "validation" in str(result["error"]).lower() or "invalid" in str(result["error"]).lower()
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_performance_workflow(self, mock_grok_client, sample_session_data):
        """Test workflow performance characteristics"""
        # Setup fast mock responses
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {"score": 0.85}
        }
        mock_grok_client.return_value = mock_client
        
        import time
        start_time = time.time()
        
        result = process_single_session(sample_session_data, api_key="test-api-key")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within reasonable time
        assert result["success"] is True
        assert execution_time < 30.0  # Should complete within 30 seconds
        assert "execution_time" in result
        assert result["execution_time"] > 0


class TestWorkflowDataFlow:
    """Test data flow through complete workflow"""
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_data_consistency_through_pipeline(self, mock_grok_client, sample_session_data):
        """Test that data remains consistent through entire pipeline"""
        # Setup mock to return consistent data
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {
                "foundation_score": 0.85,
                "energy_level": 0.72,
                "dynamics_score": 0.68,
                "validation_score": 0.91,
                "session_id": sample_session_data["session_metadata"]["date"]
            }
        }
        mock_grok_client.return_value = mock_client
        
        result = process_single_session(sample_session_data, api_key="test-api-key")
        
        assert result["success"] is True
        
        # Verify session ID consistency
        for unit_key in ["unit_a_results", "unit_b_results", "unit_c_results", "unit_d_results"]:
            if unit_key in result and "session_id" in result[unit_key]:
                assert result[unit_key]["session_id"] == sample_session_data["session_metadata"]["date"]
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_tracker_context_propagation(self, mock_grok_client, sample_session_data, sample_tracker_context):
        """Test that tracker context propagates correctly through workflow"""
        mock_client = Mock()
        mock_client.call_grok_api.return_value = {
            "success": True,
            "result": {"foundation_score": 0.85}
        }
        mock_grok_client.return_value = mock_client
        
        # Process with tracker context
        with patch('src.tracker_state.create_tracker_context_from_inputs') as mock_tracker:
            mock_tracker.return_value = sample_tracker_context
            
            result = process_single_session(sample_session_data, api_key="test-api-key")
            
            assert result["success"] is True
            # Tracker context should have been used
            mock_tracker.assert_called()
    
    @pytest.mark.e2e
    def test_output_file_generation(self, sample_session_data, temp_data_dir):
        """Test that output files are generated correctly"""
        with patch('src.grok_api_client.GrokClient') as mock_grok_client:
            mock_client = Mock()
            mock_client.call_grok_api.return_value = {
                "success": True,
                "result": {"validation_score": 0.91}
            }
            mock_grok_client.return_value = mock_client
            
            # Mock file operations to use temp directory
            with patch('src.utils.get_data_directory', return_value=str(temp_data_dir)):
                result = process_single_session(sample_session_data, api_key="test-api-key")
                
                assert result["success"] is True
                
                # Check if output files would be created
                # (This is a simplified test - actual file creation depends on implementation)
                assert "execution_time" in result


class TestWorkflowRobustness:
    """Test workflow robustness and edge cases"""
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_network_timeout_recovery(self, mock_grok_client, sample_session_data):
        """Test recovery from network timeouts"""
        import socket
        
        mock_client = Mock()
        mock_client.call_grok_api.side_effect = socket.timeout("Network timeout")
        mock_grok_client.return_value = mock_client
        
        result = process_single_session(sample_session_data, api_key="test-api-key")
        
        # Should handle timeout gracefully
        assert result["success"] is False
        assert "timeout" in str(result["error"]).lower()
    
    @pytest.mark.e2e
    @patch('src.grok_api_client.GrokClient')
    def test_partial_success_workflow(self, mock_grok_client, sample_session_data):
        """Test workflow with partial unit success"""
        mock_client = Mock()
        mock_responses = [
            {"success": True, "result": {"foundation_score": 0.85}},  # Unit A succeeds
            {"success": True, "result": {"energy_level": 0.72}},     # Unit B succeeds
            {"success": False, "error": "Unit C failed"},            # Unit C fails
        ]
        mock_client.call_grok_api.side_effect = mock_responses
        mock_grok_client.return_value = mock_client
        
        result = process_single_session(sample_session_data, api_key="test-api-key")
        
        # Should fail overall but provide partial results
        assert result["success"] is False
        # May contain partial results depending on implementation
    
    @pytest.mark.e2e
    def test_concurrent_session_processing(self, sample_session_data):
        """Test handling of concurrent session processing"""
        import threading
        import time
        
        results = []
        
        def process_session():
            with patch('src.grok_api_client.GrokClient') as mock_grok_client:
                mock_client = Mock()
                mock_client.call_grok_api.return_value = {
                    "success": True,
                    "result": {"validation_score": 0.85}
                }
                mock_grok_client.return_value = mock_client
                
                result = process_single_session(sample_session_data, api_key="test-api-key")
                results.append(result)
        
        # Start multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=process_session)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # All should succeed
        assert len(results) == 3
        for result in results:
            assert result["success"] is True
