#!/usr/bin/env python3
"""
Test a single unit to debug Grok responses
"""

import sys
import json
sys.path.insert(0, "src")

from src.unit_a import create_unit_a
sys.path.append('.')
from src.utils import load_json_data, save_json_data

# Load test data
with open('/Users/<USER>/Desktop/asia_session_full_jul22.json', 'r') as f:
    session_data = json.load(f)

with open('extracted_micro_timing.json', 'r') as f:
    micro_timing = json.load(f)

print("Testing Unit A with debug logging...")
print("="*50)

unit_a = create_unit_a()
result = unit_a.process_foundation_calculations(session_data, micro_timing)

print("="*50)
print("Final result:")
print(json.dumps(result, indent=2))