#!/usr/bin/env python3
"""
Test script for complete tracker integration with Asia session data.
Validates the enhanced pipeline with tracker context.
"""

import json
import time
from datetime import datetime
from src.pipeline import GrokPipeline, process_single_session_with_trackers
from src.tracker_state import Tracker<PERSON>tateManager, create_tracker_context_from_inputs
from src.unit_e_template import populate_template_from_pipeline_results

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data

def create_sample_tracker_data():
    """Create sample tracker data for testing."""
    
    # Sample HTF context
    htf_context = {
        "context_id": "htf_context_test_2025_07_23",
        "active_structures": [
            {
                "structure_id": "daily_support_23300",
                "structure_type": "support",
                "level": 23300.0,
                "strength": 1.2,
                "recency_factor": 0.8,
                "formation_time": "2025-07-22 12:00:00"
            },
            {
                "structure_id": "weekly_resistance_23400",
                "structure_type": "resistance", 
                "level": 23400.0,
                "strength": 1.5,
                "recency_factor": 0.9,
                "formation_time": "2025-07-21 09:00:00"
            }
        ],
        "context_timestamp": datetime.now().isoformat(),
        "precedence_weight": 0.35
    }
    
    # Sample FVG state
    fvg_state = {
        "state_id": "fvg_state_test_2025_07_23",
        "fpfvg_carryover_metadata": {
            "t_memory_final": 25.5,
            "last_interaction_time": "2025-07-22 21:45:00",
            "carryover_strength": 0.85,
            "decay_applied": False
        },
        "active_gaps": [
            {
                "gap_id": "previous_day_fpfvg",
                "level": 23345.0,
                "size": 2.5,
                "status": "pending_redelivery"
            }
        ],
        "state_timestamp": datetime.now().isoformat()
    }
    
    # Sample liquidity state
    liquidity_state = {
        "registry_id": "liquidity_registry_test_2025_07_23",
        "untaken_liquidity_registry": [
            {
                "level_id": "stop_hunt_23280",
                "level": 23280.0,
                "weight": 1.5,
                "level_type": "stop_hunt",
                "time_created": "2025-07-22 20:00:00",
                "interactions": 0
            },
            {
                "level_id": "gap_fill_23370",
                "level": 23370.0,
                "weight": 1.2,
                "level_type": "gap_fill",
                "time_created": "2025-07-22 18:30:00",
                "interactions": 1
            }
        ],
        "registry_timestamp": datetime.now().isoformat(),
        "gradient_strength": 0.0
    }
    
    return htf_context, fvg_state, liquidity_state


def test_tracker_integration():
    """Test complete tracker integration with Asia session data."""
    
    print("🧪 Testing Tracker Integration with Asia Session Data")
    print("=" * 60)
    
    # Create sample tracker data
    print("Creating sample tracker data...")
    htf_context, fvg_state, liquidity_state = create_sample_tracker_data()
    
    # Save tracker data to files for testing
    with open('test_htf_context.json', 'w') as f:
        json.dump(htf_context, f, indent=2)
    
    with open('test_fvg_state.json', 'w') as f:
        json.dump(fvg_state, f, indent=2)
    
    with open('test_liquidity_state.json', 'w') as f:  
        json.dump(liquidity_state, f, indent=2)
    
    print("✅ Sample tracker data created")
    
    # Test TrackerStateManager directly
    print("\n1. Testing TrackerStateManager...")
    tracker_manager = TrackerStateManager()
    
    # Test T_memory decay
    t_memory_prev = 30.0
    hours_elapsed = 2.5
    decayed = tracker_manager.calculate_t_memory_decay(t_memory_prev, hours_elapsed)
    print(f"   T_memory decay: {t_memory_prev} → {decayed:.2f} (after {hours_elapsed}h)")
    
    # Test energy threshold adjustment
    e_threshold_base = 800.0
    t_memory = 25.5
    adjusted = tracker_manager.calculate_energy_threshold_adjustment(e_threshold_base, t_memory)
    print(f"   Energy threshold: {e_threshold_base} → {adjusted:.2f} (T_memory: {t_memory})")
    
    # Test tracker context extraction
    tracker_context = tracker_manager.extract_tracker_context(htf_context, fvg_state, liquidity_state)
    print(f"   Tracker context extracted: T_memory={tracker_context['t_memory']:.2f}")
    print(f"   HTF structures: {len(tracker_context['active_structures'])}")
    print(f"   Untaken liquidity: {len(tracker_context['untaken_liquidity'])}")
    
    print("✅ TrackerStateManager tests passed")
    
    # Test enhanced pipeline with tracker integration
    print("\n2. Testing Enhanced Pipeline with Tracker Context...")
    
    try:
        # Use the process_single_session_with_trackers function
        results = process_single_session_with_trackers(
            session_file='/Users/<USER>/Desktop/asia_session_full_jul22.json',
            htf_context_file='test_htf_context.json',
            fvg_state_file='test_fvg_state.json', 
            liquidity_state_file='test_liquidity_state.json',
            output_file='test_tracker_enhanced_pipeline.json'
        )
        
        if 'pipeline_error' in results:
            print(f"❌ Pipeline failed: {results['pipeline_error']}")
            return False
        
        print("✅ Enhanced pipeline completed successfully")
        print(f"   Total processing time: {results['pipeline_metadata']['total_processing_time_ms']}ms")
        print(f"   Tracker context included: {'tracker_context' in results}")
        
        # Test Unit E template population with tracker updates
        print("\n3. Testing Unit E Template Population with Tracker Updates...")
        
        template_result = populate_template_from_pipeline_results(
            'test_tracker_enhanced_pipeline.json',
            'test_tracker_enhanced_template.json'
        )
        
        if 'grok_enhanced_template' not in template_result:
            print("❌ Template population failed")
            return False
        
        template = template_result['grok_enhanced_template']
        updated_trackers = template_result.get('updated_tracker_states')
        
        print("✅ Template population completed")
        print(f"   Template sections: {len(template)}")
        print(f"   Opus4 enhancements: {len(template.get('opus4_enhancements', {}))}")
        print(f"   Updated tracker states: {updated_trackers is not None}")
        
        if updated_trackers:
            print(f"   Updated T_memory: {updated_trackers['updated_t_memory']:.2f}")
            print(f"   Updated liquidity levels: {len(updated_trackers['updated_liquidity_registry'])}")
            print(f"   Updated HTF structures: {len(updated_trackers['updated_htf_structures'])}")
        
        # Validate key Opus4 parameters
        print("\n4. Validating Enhanced Opus4 Parameters...")
        opus4 = template.get('opus4_enhancements', {})
        
        # Check tracker-enhanced parameters
        lambda_theta = opus4.get('lambda_theta_dynamic', 0)
        e_threshold = opus4.get('e_threshold_adj', 0)
        gamma_enhanced = opus4.get('gamma_enhanced', 0)
        confidence = opus4.get('confidence', 0)
        
        print(f"   λ_theta_dynamic: {lambda_theta:.4f}")
        print(f"   E_threshold_adj: {e_threshold:.1f}")
        print(f"   γ_enhanced: {gamma_enhanced:.3f}")
        print(f"   Confidence: {confidence:.3f}")
        
        # Validation checks
        validations = []
        validations.append(('λ_theta_dynamic', 0.02 <= lambda_theta <= 0.04))
        validations.append(('E_threshold_adj', 400 <= e_threshold <= 1000))
        validations.append(('γ_enhanced', 0.5 <= gamma_enhanced <= 1.5))
        validations.append(('Confidence', confidence < 1.0))
        
        all_valid = True
        for param, is_valid in validations:
            status = "✅" if is_valid else "❌"
            print(f"   {status} {param}: {'PASS' if is_valid else 'FAIL'}")
            all_valid = all_valid and is_valid
        
        if all_valid:
            print("✅ All parameter bounds validation passed")
        else:
            print("❌ Some parameter bounds validation failed")
            return False
        
        print("\n🎯 Tracker Integration Test Summary:")
        print("   ✅ TrackerStateManager functional")
        print("   ✅ Enhanced pipeline with tracker context")
        print("   ✅ Unit E template population with tracker updates")
        print("   ✅ Opus4 parameter bounds validation")
        print("   ✅ Updated tracker states calculation")
        
        print(f"\n📁 Generated files:")
        print(f"   • test_tracker_enhanced_pipeline.json")
        print(f"   • test_tracker_enhanced_template.json")
        print(f"   • test_tracker_enhanced_template_template_only.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test files
        import os
        test_files = [
            'test_htf_context.json',
            'test_fvg_state.json', 
            'test_liquidity_state.json'
        ]
        for file in test_files:
            try:
                os.remove(file)
            except FileNotFoundError:
                pass


def test_backwards_compatibility():
    """Test that the system still works without tracker context."""
    
    print("\n🔄 Testing Backwards Compatibility (Legacy Mode)")
    print("=" * 60)
    
    try:
        # Test legacy pipeline without tracker data
        from src.pipeline import process_single_session
        
        results = process_single_session(
            session_file='/Users/<USER>/Desktop/asia_session_full_jul22.json',
            output_file='test_legacy_pipeline.json'
        )
        
        if 'pipeline_error' in results:
            print(f"❌ Legacy pipeline failed: {results['pipeline_error']}")
            return False
        
        print("✅ Legacy pipeline still works")
        
        # Test template population without tracker context
        template_result = populate_template_from_pipeline_results(
            'test_legacy_pipeline.json',
            'test_legacy_template.json'
        )
        
        if 'grok_enhanced_template' not in template_result:
            print("❌ Legacy template population failed")
            return False
        
        print("✅ Legacy template population works")
        print("✅ Backwards compatibility maintained")
        
        return True
        
    except Exception as e:
        print(f"❌ Backwards compatibility test failed: {str(e)}")
        return False


if __name__ == "__main__":
    start_time = time.time()
    
    # Run tracker integration test
    tracker_test_passed = test_tracker_integration()
    
    # Run backwards compatibility test
    compat_test_passed = test_backwards_compatibility()
    
    elapsed_time = time.time() - start_time
    
    print(f"\n🏁 Test Results Summary:")
    print(f"   Tracker Integration: {'✅ PASSED' if tracker_test_passed else '❌ FAILED'}")
    print(f"   Backwards Compatibility: {'✅ PASSED' if compat_test_passed else '❌ FAILED'}")
    print(f"   Total test time: {elapsed_time:.1f}s")
    
    if tracker_test_passed and compat_test_passed:
        print(f"\n🎉 ALL TESTS PASSED - Tracker integration is ready!")
        exit(0)
    else:
        print(f"\n💥 SOME TESTS FAILED - Review and fix issues")
        exit(1)