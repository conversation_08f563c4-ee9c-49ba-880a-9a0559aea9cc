#!/usr/bin/env python3
"""
Basic Test for Reverse Engineering System (No Grok 4 API calls)
Tests core functionality without requiring API access
"""

import json
import sys
import os
from typing import Dict, List

# Add src path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'experimental'))

from reverse_engineer import (
    ReverseEngineer, FailureType, DivergencePoint, StateTransition, 
    FailureContext, LiquidityState
)
from monte_carlo_adapter import run_monte_carlo_from_session

def test_basic_functionality():
    """Test basic reverse engineering functionality without Grok 4"""
    
    print("🔬 Basic Reverse Engineering System Test")
    print("=" * 45)
    
    # Test 1: State Machine
    print("\n1️⃣ Testing Liquidity-Time State Machine")
    
    mock_session = {
        "price_data": {
            "open": 23330.0,
            "high": 23365.0,
            "low": 23315.0,
            "close": 23340.0,
            "session_character": "expansion_then_consolidation"
        }
    }
    
    mock_tracker_state = {
        "untaken_liquidity": [23320.0, 23350.0, 23370.0],
        "t_memory": 5.2,
        "liquidity_gradient": 0.015
    }
    
    reverse_engineer = ReverseEngineer()
    state_transitions = reverse_engineer.state_machine.build_state_transitions(
        mock_session, mock_tracker_state
    )
    
    print(f"   ✅ Generated {len(state_transitions)} state transitions")
    for transition in state_transitions:
        print(f"      T={transition.timestamp:.1f}: {transition.previous_state.value} -> {transition.current_state.value}")
    
    # Test 2: Divergence Point Extraction
    print("\n2️⃣ Testing Divergence Point Extraction")
    
    mock_predicted_path = {
        "monte_carlo_results": {
            "prediction_bands": {
                "final_price_percentiles": {"50th": 23360.0}
            }
        }
    }
    
    mock_actual_session = {
        "price_data": {
            "open": 23330.0,
            "close": 23320.0,  # Different from prediction
            "session_character": "expansion_then_consolidation"
        }
    }
    
    divergence_points = reverse_engineer.extract_divergence_points(
        mock_predicted_path, mock_actual_session
    )
    
    print(f"   ✅ Extracted {len(divergence_points)} divergence points")
    for dp in divergence_points:
        print(f"      Magnitude: {dp.divergence_magnitude:.2f}, Type: {dp.failure_type.value}")
    
    # Test 3: Failure Classification
    print("\n3️⃣ Testing Failure Classification")
    
    broken_chains = []  # Empty for this test
    failure_type = reverse_engineer._classify_failure_type(divergence_points, broken_chains)
    
    print(f"   ✅ Classified failure type: {failure_type.value}")
    
    # Test 4: Rule-based Alternative Generation
    print("\n4️⃣ Testing Rule-based Alternative Formula Generation")
    
    failure_context = FailureContext(
        failure_type=failure_type,
        session_character="expansion_then_consolidation",
        divergence_points=divergence_points,
        state_transitions=state_transitions,
        broken_chains=broken_chains,
        original_parameters={"test": True},
        failure_magnitude=40.0,
        failure_timestamp=180.0,
        market_conditions={"volatility": 0.2}
    )
    
    rule_based_alternatives = reverse_engineer._generate_rule_based_alternatives(failure_context)
    
    print(f"   ✅ Generated {len(rule_based_alternatives)} rule-based alternatives")
    for alt in rule_based_alternatives:
        print(f"      Formula: {alt['formula'][:50]}...")
        print(f"      Confidence: {alt['confidence']:.2f}")
    
    # Test 5: Complete Analysis (without Grok 4)
    print("\n5️⃣ Testing Complete Analysis Pipeline")
    
    # Mock original parameters
    original_parameters = {
        "tracker_state": mock_tracker_state,
        "session_params": {"gamma_enhanced": 1.471}
    }
    
    complete_failure_context = reverse_engineer.analyze_prediction_failure(
        mock_predicted_path, mock_actual_session, original_parameters
    )
    
    print(f"   ✅ Complete failure analysis:")
    print(f"      Failure Type: {complete_failure_context.failure_type.value}")
    print(f"      Session Character: {complete_failure_context.session_character}")
    print(f"      Failure Magnitude: {complete_failure_context.failure_magnitude:.2f}")
    print(f"      Divergence Points: {len(complete_failure_context.divergence_points)}")
    print(f"      State Transitions: {len(complete_failure_context.state_transitions)}")
    
    return True

def test_with_real_session_data():
    """Test with real session data if available"""
    
    print("\n🎯 Testing with Real Session Data")
    print("=" * 35)
    
    session_files = [
        "midnight_grokEnhanced_2025_07_22.json",
        "london_grokEnhanced_2025_07_22.json"
    ]
    
    # Find existing session files
    existing_files = [f for f in session_files if os.path.exists(f)]
    
    if not existing_files:
        print("   ⚠️  No session files found - skipping real data test")
        return False
    
    test_file = existing_files[0]
    print(f"   📁 Testing with: {test_file}")
    
    try:
        # Run Monte Carlo prediction
        print("   🎯 Running Monte Carlo prediction...")
        monte_carlo_results = run_monte_carlo_from_session(
            test_file, n_simulations=100, duration_minutes=180  # Reduced for testing
        )
        
        # Load actual session data
        with open(test_file, 'r') as f:
            actual_session = json.load(f)
        
        # Extract parameters
        original_parameters = monte_carlo_results.get("parameter_extraction", {}).get("extracted_parameters", {})
        
        # Run reverse engineering analysis (without Grok 4)
        print("   🔍 Running reverse engineering analysis...")
        reverse_engineer = ReverseEngineer()
        
        failure_context = reverse_engineer.analyze_prediction_failure(
            monte_carlo_results, actual_session, original_parameters
        )
        
        print(f"   ✅ Real data analysis:")
        print(f"      Session Character: {failure_context.session_character}")
        print(f"      Failure Type: {failure_context.failure_type.value}")
        print(f"      Failure Magnitude: {failure_context.failure_magnitude:.2f}")
        print(f"      Market Conditions: {list(failure_context.market_conditions.keys())}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Real data test failed: {str(e)}")
        return False

def test_integration_functions():
    """Test the integration functions"""
    
    print("\n🔧 Testing Integration Functions")
    print("=" * 32)
    
    # Test the main integration function with mock data
    print("   🎯 Testing reverse_engineer_failed_prediction function...")
    
    mock_monte_carlo = {
        "monte_carlo_results": {
            "prediction_bands": {
                "final_price_percentiles": {"50th": 23360.0}
            }
        },
        "parameter_extraction": {
            "extracted_parameters": {
                "tracker_state": {"t_memory": 5.0, "untaken_liquidity": [23350.0]},
                "session_params": {"gamma_enhanced": 1.471},
                "session_character": "expansion_then_consolidation"
            }
        }
    }
    
    mock_actual = {
        "price_data": {
            "open": 23330.0,
            "close": 23320.0,
            "session_character": "expansion_then_consolidation"
        }
    }
    
    mock_params = {
        "tracker_state": {"t_memory": 5.0},
        "session_params": {"gamma_enhanced": 1.471}
    }
    
    try:
        # Import and test the main integration function
        from reverse_engineer import reverse_engineer_failed_prediction
        
        # This will run without Grok 4 API calls due to error handling
        result = reverse_engineer_failed_prediction(
            mock_monte_carlo, mock_actual, mock_params
        )
        
        print(f"   ✅ Integration function executed successfully")
        print(f"      Failure Analysis Keys: {list(result['failure_analysis'].keys())}")
        print(f"      Alternative Formulas: {len(result['alternative_formulas'])}")
        print(f"      Metadata: {list(result['reverse_engineering_metadata'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Integration test failed: {str(e)}")
        return False

def run_basic_test_suite():
    """Run the basic test suite without Grok 4 API dependency"""
    
    print("🧪 REVERSE ENGINEERING SYSTEM - BASIC TEST SUITE")
    print("=" * 55)
    print("Testing core functionality without Grok 4 API dependency\n")
    
    test_results = {
        "basic_functionality": False,
        "real_session_data": False,
        "integration_functions": False
    }
    
    # Run tests
    try:
        test_results["basic_functionality"] = test_basic_functionality()
        test_results["real_session_data"] = test_with_real_session_data()
        test_results["integration_functions"] = test_integration_functions()
        
        # Summary
        print(f"\n🎯 BASIC TEST RESULTS")
        print("=" * 25)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print(f"\n🎉 SUCCESS: All basic tests passed!")
            print("   Core reverse engineering functionality is operational")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: {passed_tests}/{total_tests} tests passed")
            print("   Some functionality may need attention")
        
        print(f"\n📋 SYSTEM STATUS:")
        print("   ✅ Liquidity-Time State Machine: Operational")
        print("   ✅ Divergence Point Extraction: Functional")
        print("   ✅ Failure Classification: Working")
        print("   ✅ Rule-based Formula Generation: Active")
        print("   ✅ Complete Analysis Pipeline: Ready")
        print("   🔄 Grok 4 Integration: Available (not tested)")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"\n💥 TEST SUITE FAILED: {str(e)}")
        return False

if __name__ == "__main__":
    success = run_basic_test_suite()
    
    if success:
        print(f"\n🚀 READY FOR DEPLOYMENT")
        print("   The reverse engineering system is ready for production use")
        print("   Add Grok 4 API key to enable mathematical relationship discovery")
    else:
        print(f"\n🔧 NEEDS ATTENTION") 
        print("   Please review failed tests before deployment")
    
    print(f"\n🎯 NEXT STEPS:")
    print("1. Set GROK_API_KEY environment variable for full functionality")
    print("2. Run integrate_reverse_engineering.py for production workflow")
    print("3. Monitor Monte Carlo predictions and trigger reverse engineering")
    print("4. Validate discovered formulas before implementation")