#!/usr/bin/env python3
"""
AM Session Pre-flight Test
Verify positive price outputs from cross-session prediction system
"""

import json
import os

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
from src.experimental.cross_session_predictor import CrossSessionPredictionSystem

def test_am_simulation():
    """Test AM session simulation to verify positive price outputs"""
    print("🚀 AM SESSION PRE-FLIGHT TEST")
    print("=" * 40)
    
    # Initialize cross-session system
    system = CrossSessionPredictionSystem(error_threshold=25.0)
    
    # Test with AM session from July 23rd
    session = "nyam"
    date = "2025_07_23"
    target_session = "lunch"  # Predict lunch from AM
    
    try:
        print(f"📊 Testing {session.upper()} → {target_session.upper()} prediction...")
        
        # Run cross-session analysis
        results = system.run_cross_session_analysis(session, date, target_session)
        
        if "cross_session_prediction" in results:
            prediction = results["cross_session_prediction"]
            
            # Check for positive prices
            predicted_close = prediction["predicted_close"] 
            predicted_high = prediction["predicted_high"]
            predicted_low = prediction["predicted_low"]
            
            print(f"\n✅ PRICE OUTPUT VALIDATION:")
            print(f"   Predicted Close: {predicted_close:.2f}")
            print(f"   Predicted High:  {predicted_high:.2f}")
            print(f"   Predicted Low:   {predicted_low:.2f}")
            
            # Validate all prices are positive
            all_positive = all(price > 0 for price in [predicted_close, predicted_high, predicted_low])
            
            if all_positive:
                print(f"   🎯 STATUS: ALL PRICES POSITIVE ✓")
                
                # Check for reasonable price ranges
                price_range = predicted_high - predicted_low
                avg_price = (predicted_high + predicted_low) / 2
                range_pct = (price_range / avg_price) * 100
                
                print(f"   📊 Range: {price_range:.2f} points ({range_pct:.2f}%)")
                
                # Validate event-sequence intelligence
                metadata = prediction.get("prediction_metadata", {})
                asia_events = metadata.get("asia_event_analysis", {})
                london_events = metadata.get("london_event_predictions", {})
                
                if asia_events and london_events:
                    print(f"   🔗 Event Intelligence: ACTIVE ✓")
                    print(f"      Asia Events: {asia_events.get('total_events', 0)}")
                    print(f"      Event Tree Confidence: {london_events.get('event_tree_confidence', 0):.2f}")
                else:
                    print(f"   🔗 Event Intelligence: MISSING ⚠️")
                
                return True, results
            else:
                print(f"   ❌ STATUS: NEGATIVE PRICES DETECTED")
                return False, results
        else:
            print(f"   ❌ No prediction data generated")
            return False, results
            
    except Exception as e:
        print(f"❌ Pre-flight test failed: {str(e)}")
        return False, {"error": str(e)}

if __name__ == "__main__":
    success, results = test_am_simulation()
    
    if success:
        print(f"\n🚀 PRE-FLIGHT TEST: PASSED")
        print(f"✅ Ready for full AM+Lunch→PM simulation")
    else:
        print(f"\n❌ PRE-FLIGHT TEST: FAILED")
        print(f"🔧 System requires debugging before full simulation")
        
        # Save debug results
        with open("am_preflight_debug.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        print(f"💾 Debug data saved to: am_preflight_debug.json")