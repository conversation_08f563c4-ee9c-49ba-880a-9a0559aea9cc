#!/usr/bin/env python3
"""
Pytest configuration and fixtures for Grok-Claude Automation tests
Provides comprehensive test infrastructure for the financial prediction system
"""

import pytest
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from unittest.mock import Mock, MagicMock

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config import AppConfig
from src.utils import get_logger


@pytest.fixture(scope="session")
def test_config():
    """Test configuration with mock API keys"""
    return AppConfig(
        api_key="test-api-key-12345",
        timeout_seconds=30,
        max_retries=3,
        debug_mode=True
    )


@pytest.fixture
def mock_grok_client():
    """Mock Grok API client for testing"""
    client = Mock()
    client.call_grok_api.return_value = {
        "success": True,
        "result": {"test": "data"},
        "execution_time": 1.5
    }
    return client


@pytest.fixture
def sample_session_data():
    """Sample session data for testing"""
    return {
        "session_metadata": {
            "session_type": "ASIA",
            "date": "2025_07_30",
            "duration_minutes": 300,
            "market_conditions": "normal"
        },
        "price_data": {
            "open": 23250.0,
            "high": 23380.0,
            "low": 23180.0,
            "close": 23320.0,
            "session_character": "expansion_dominant"
        },
        "structures_identified": {
            "fair_value_gaps": [
                {"start": 23200, "end": 23220, "strength": 0.8},
                {"start": 23350, "end": 23370, "strength": 0.6}
            ],
            "liquidity_levels": [
                {"price": 23400, "strength": 0.9, "type": "resistance"},
                {"price": 23150, "strength": 0.7, "type": "support"}
            ]
        },
        "micro_timing_data": {
            "event_timestamps": ["09:00", "10:30", "12:15"],
            "volume_profile": [1000, 1500, 800],
            "momentum_shifts": [0.2, -0.1, 0.3]
        }
    }


@pytest.fixture
def sample_tracker_context():
    """Sample tracker context for testing"""
    return {
        "t_memory": 15.5,
        "active_structures": [
            {
                "structure_id": "htf_001",
                "price_level": 23400,
                "strength": 0.85,
                "type": "resistance"
            }
        ],
        "untaken_liquidity": [
            {
                "price": 23450,
                "volume": 2000,
                "probability": 0.7
            }
        ],
        "htf_influence_factor": 0.65,
        "liquidity_gradient": {
            "gradient_strength": 0.4,
            "liquidity_bias": "bullish"
        }
    }


@pytest.fixture
def sample_micro_timing():
    """Sample micro timing data for testing"""
    return {
        "timing_events": [
            {
                "timestamp": "09:00:00",
                "event_type": "market_open",
                "probability": 0.95,
                "impact_score": 0.8
            },
            {
                "timestamp": "10:30:00", 
                "event_type": "momentum_shift",
                "probability": 0.7,
                "impact_score": 0.6
            }
        ],
        "cascade_predictions": [
            {
                "predicted_time": "11:15:00",
                "event_type": "liquidity_sweep",
                "confidence": 0.75
            }
        ]
    }


@pytest.fixture
def temp_data_dir(tmp_path):
    """Temporary directory for test data files"""
    data_dir = tmp_path / "test_data"
    data_dir.mkdir()
    
    # Create subdirectories
    (data_dir / "sessions").mkdir()
    (data_dir / "trackers").mkdir()
    (data_dir / "predictions").mkdir()
    
    return data_dir


@pytest.fixture
def mock_file_system(temp_data_dir, sample_session_data, sample_tracker_context):
    """Mock file system with test data"""
    # Create test session file
    session_file = temp_data_dir / "sessions" / "ASIA_grokEnhanced_2025_07_30.json"
    with open(session_file, 'w') as f:
        json.dump(sample_session_data, f, indent=2)
    
    # Create test tracker files
    htf_file = temp_data_dir / "trackers" / "HTF_Context_ASIA_2025_07_30.json"
    with open(htf_file, 'w') as f:
        json.dump({"htf_context": sample_tracker_context}, f, indent=2)
    
    fvg_file = temp_data_dir / "trackers" / "FVG_State_ASIA_2025_07_30.json"
    with open(fvg_file, 'w') as f:
        json.dump({"t_memory": 15.5, "fvg_density": 3}, f, indent=2)
    
    return {
        "session_file": str(session_file),
        "htf_file": str(htf_file),
        "fvg_file": str(fvg_file),
        "data_dir": str(temp_data_dir)
    }


class MockPipelineResult:
    """Mock pipeline result for testing"""
    
    def __init__(self, success: bool = True, data: Optional[Dict] = None):
        self.success = success
        self.data = data or {}
        self.execution_time = 2.5
        self.errors = [] if success else ["Mock error"]
        
    def to_dict(self):
        return {
            "success": self.success,
            "data": self.data,
            "execution_time": self.execution_time,
            "errors": self.errors
        }


@pytest.fixture
def mock_pipeline_result():
    """Mock successful pipeline result"""
    return MockPipelineResult(success=True, data={
        "unit_a_results": {"foundation_score": 0.85},
        "unit_b_results": {"energy_level": 0.72},
        "unit_c_results": {"dynamics_score": 0.68},
        "unit_d_results": {"validation_score": 0.91}
    })


@pytest.fixture
def mock_failed_pipeline_result():
    """Mock failed pipeline result"""
    return MockPipelineResult(success=False, data={})


# Test utilities
def assert_valid_price(price: float, context: str = ""):
    """Assert that a price is valid (positive and reasonable)"""
    assert price > 0, f"Price must be positive: {price} {context}"
    assert 1000 < price < 100000, f"Price out of reasonable range: {price} {context}"


def assert_valid_probability(prob: float, context: str = ""):
    """Assert that a probability is valid (0-1 range)"""
    assert 0 <= prob <= 1, f"Probability out of range [0,1]: {prob} {context}"


def assert_valid_session_data(session_data: Dict[str, Any]):
    """Assert that session data has required structure"""
    assert "session_metadata" in session_data
    assert "price_data" in session_data
    assert "structures_identified" in session_data
    
    price_data = session_data["price_data"]
    assert_valid_price(price_data["open"], "open price")
    assert_valid_price(price_data["high"], "high price") 
    assert_valid_price(price_data["low"], "low price")
    assert_valid_price(price_data["close"], "close price")
    
    # Price relationships
    assert price_data["high"] >= price_data["open"], "High must be >= open"
    assert price_data["high"] >= price_data["close"], "High must be >= close"
    assert price_data["low"] <= price_data["open"], "Low must be <= open"
    assert price_data["low"] <= price_data["close"], "Low must be <= close"


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "api: mark test as requiring API access"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file location"""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
            
        # Mark API tests
        if "api" in item.name.lower() or "grok" in item.name.lower():
            item.add_marker(pytest.mark.api)
