#!/usr/bin/env python3
"""
Integrated HMM Monte Carlo Test - Post Migration
Tests HMM state detection and Monte Carlo integration with migrated file structure
"""

import sys
import os
import json
sys.path.append('.')

from src.utils import load_json_data
from market_state_hmm import MarketStateHMM

def test_hmm_with_migrated_files():
    """Test HMM functionality with migrated file structure"""
    print("🧪 INTEGRATED HMM MONTE CARLO TEST - POST MIGRATION")
    print("=" * 60)
    
    try:
        # Test 1: HMM State Detection
        print("\n1️⃣ Testing HMM State Detection...")
        hmm = MarketStateHMM()
        
        # Try different session files
        test_files = [
            'NYPM_grokEnhanced_2025_07_23.json',
            'ASIA_grokEnhanced_2025_07_23.json', 
            'LONDON_grokEnhanced_2025_07_23.json'
        ]
        
        for test_file in test_files:
            try:
                print(f"\n📊 Testing with {test_file}...")
                session_data = load_json_data(test_file)
                
                # Test event timing prediction
                timing_prediction = hmm.predict_event_timing(session_data, target_event='cascade')
                
                print(f"   ✅ State detection successful")
                print(f"   🎯 Event: {timing_prediction.event_type}")
                print(f"   ⏰ Time window: {timing_prediction.time_window_start.strftime('%H:%M')} - {timing_prediction.time_window_end.strftime('%H:%M')}")
                print(f"   📊 Confidence: {timing_prediction.confidence:.2f}")
                print(f"   🔄 States: {[s.value for s in timing_prediction.state_sequence]}")
                
                # Check if it's not stuck in consolidating
                states = [s.value for s in timing_prediction.state_sequence]
                if len(set(states)) > 1:
                    print(f"   ✅ FIXED: Multiple states detected (not stuck in consolidating)")
                else:
                    print(f"   ⚠️ WARNING: Only single state: {states[0]}")
                
                break  # Success with this file
                
            except Exception as e:
                print(f"   ❌ Failed with {test_file}: {e}")
                continue
        else:
            print("   ❌ All test files failed")
            return False
            
        # Test 2: File Structure Validation
        print("\n2️⃣ Testing File Structure...")
        data_dirs = [
            'data/enhanced/grok_enhanced',
            'data/trackers/htf',
            'data/trackers/fvg', 
            'data/trackers/liquidity'
        ]
        
        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                file_count = len([f for f in os.listdir(data_dir) if f.endswith('.json')])
                print(f"   ✅ {data_dir}: {file_count} files")
            else:
                print(f"   ❌ Missing: {data_dir}")
        
        # Test 3: Import System Recovery
        print("\n3️⃣ Testing Import System Recovery...")
        try:
            # Test the fixed load_json_data function
            from src.utils import load_json_data, save_json_data
            print("   ✅ load_json_data import successful")
            print("   ✅ save_json_data import successful")
            
            # Test backward compatibility
            test_data = load_json_data('ASIA_grokEnhanced_2025_07_23.json')
            print("   ✅ Backward compatibility working")
            
        except Exception as e:
            print(f"   ❌ Import system still broken: {e}")
            return False
        
        print("\n🎯 INTEGRATION TEST SUMMARY:")
        print("   ✅ HMM state detection working")
        print("   ✅ File structure intact") 
        print("   ✅ Import system recovered")
        print("   ✅ Backward compatibility restored")
        print("\n✅ ALL TESTS PASSED - Migration issues resolved!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ INTEGRATION TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hmm_with_migrated_files()
    exit(0 if success else 1)