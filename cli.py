#!/usr/bin/env python3
"""
Command Line Interface for Grok-Claude Automation
Simple CLI for running the computational pipeline
"""

import argparse
import json
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.pipeline import GrokP<PERSON>eline, process_single_session
from src.preprocessing_agent import create_preprocessing_agent
from src.config import config, AppConfig, APIKeyError


def main():
    parser = argparse.ArgumentParser(
        description="Grok-Claude Computational Pipeline Automation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process single session
  python cli.py process session.json micro_timing.json -o results.json
  
  # Batch process multiple sessions  
  python cli.py batch sessions/ micro_timings/ -o results/
  
  # Test connection
  python cli.py test
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Process single session
    process_parser = subparsers.add_parser('process', help='Process single session')
    process_parser.add_argument('session_file', help='Path to session JSON file')
    process_parser.add_argument('micro_timing_file', nargs='?', help='Path to micro timing JSON file (optional if included in session file)')
    process_parser.add_argument('-o', '--output', help='Output file path')
    process_parser.add_argument('-k', '--api-key', help='Grok API key (or use GROK_API_KEY env var)')
    
    # Batch process
    batch_parser = subparsers.add_parser('batch', help='Batch process multiple sessions')
    batch_parser.add_argument('session_dir', help='Directory containing session JSON files')
    batch_parser.add_argument('micro_timing_dir', help='Directory containing micro timing JSON files')
    batch_parser.add_argument('-o', '--output-dir', help='Output directory for results')
    batch_parser.add_argument('-k', '--api-key', help='Grok API key (or use GROK_API_KEY env var)')
    
    # Test connection
    test_parser = subparsers.add_parser('test', help='Test Grok API connection')
    test_parser.add_argument('-k', '--api-key', help='Grok API key (or use GROK_API_KEY env var)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Set API key using centralized config
    try:
        api_key = config.get_api_key(args.api_key if hasattr(args, 'api_key') else None)
    except APIKeyError as e:
        print(f"❌ API Key Error: {e}")
        return 1
    
    if args.command == 'process':
        process_single_command(args, api_key)
    elif args.command == 'batch':
        batch_process_command(args, api_key)
    elif args.command == 'test':
        test_connection_command(api_key)


def process_single_command(args, api_key):
    """Handle single session processing."""
    try:
        print(f"🚀 Processing session with PreprocessingAgent: {args.session_file}")
        if args.micro_timing_file:
            print(f"⚠️  Note: Micro timing file parameter ignored - using session file data")
        
        # Create preprocessing agent with API key
        # Use the global config which already has the validated API key
        agent = create_preprocessing_agent()
        
        # Process session through the agent
        results = agent.process_session(
            session_file=args.session_file,
            output_file=args.output
        )
        
        # Print agent summary
        print("\n" + "="*60)
        print("🎯 PREPROCESSING AGENT RESULTS SUMMARY")
        print("="*60)
        print(f"Status: {results['status']}")
        print(f"Processing Time: {results['processing_time_seconds']:.1f}s")
        print(f"Session Type: {results['session_info']['session_type']}")
        print(f"Enhanced Fields: {results['validation_results']['extracted_fields']}")
        
        # Print tracker files generated
        if results.get('tracker_files'):
            print("\n📁 Tracker Files Generated:")
            for tracker_type, file_path in results['tracker_files'].items():
                print(f"  {tracker_type}: {file_path}")
        
        # Print pipeline timing
        pipeline_metadata = results['enhanced_results'].get('pipeline_metadata', {})
        if pipeline_metadata.get('individual_unit_times'):
            print("\n⏱️  Unit Processing Times:")
            for unit, time_ms in pipeline_metadata['individual_unit_times'].items():
                print(f"  {unit}: {time_ms}ms")
        
        if args.output:
            print(f"\n💾 Results saved to: {results['output_file']}")
        else:
            print(f"\n💾 Results saved to: {results['output_file']}")
            
    except FileNotFoundError as e:
        print(f"Error: File not found - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error processing session: {e}")
        sys.exit(1)


def batch_process_command(args, api_key):
    """Handle batch processing of multiple sessions."""
    try:
        session_dir = Path(args.session_dir)
        micro_timing_dir = Path(args.micro_timing_dir)
        output_dir = Path(args.output_dir) if args.output_dir else None
        
        if not session_dir.exists():
            print(f"Error: Session directory does not exist: {session_dir}")
            sys.exit(1)
            
        if not micro_timing_dir.exists():
            print(f"Error: Micro timing directory does not exist: {micro_timing_dir}")
            sys.exit(1)
        
        # Find matching session and micro timing files
        session_files = list(session_dir.glob("*.json"))
        
        if not session_files:
            print(f"No JSON files found in {session_dir}")
            sys.exit(1)
        
        print(f"Found {len(session_files)} session files to process")
        
        if output_dir:
            output_dir.mkdir(exist_ok=True)
        
        pipeline = GrokPipeline(api_key)
        successful = 0
        failed = 0
        
        for session_file in session_files:
            # Look for corresponding micro timing file
            base_name = session_file.stem
            micro_timing_file = micro_timing_dir / f"{base_name}_micro_timing.json"
            
            if not micro_timing_file.exists():
                # Try alternative naming patterns
                micro_timing_file = micro_timing_dir / f"{base_name}.json"
            
            if not micro_timing_file.exists():
                print(f"Warning: No micro timing file found for {session_file.name}")
                failed += 1
                continue
            
            try:
                print(f"\nProcessing: {session_file.name}")
                
                results = process_single_session(
                    str(session_file),
                    str(micro_timing_file),
                    str(output_dir / f"{base_name}_results.json") if output_dir else None,
                    api_key
                )
                
                pipeline.results = results
                summary = pipeline.get_summary()
                print(f"  Status: {summary['status']} ({summary['total_time_ms']}ms)")
                
                successful += 1
                
            except Exception as e:
                print(f"  Error: {e}")
                failed += 1
        
        print(f"\n" + "="*50)
        print("BATCH PROCESSING SUMMARY")
        print("="*50)
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        print(f"Total: {successful + failed}")
        
    except Exception as e:
        print(f"Error in batch processing: {e}")
        sys.exit(1)


def test_connection_command(api_key):
    """Test Grok API connection."""
    try:
        from src.grok_client import GrokClient
        
        print("Testing Grok API connection...")
        print(f"✅ API key loaded successfully (length: {len(api_key)})")
        
        # API key validation is handled by GrokClient using centralized config
        client = GrokClient(api_key)
        
        # Simple test prompt
        test_prompt = "Respond with exactly: 'Connection successful'"
        response = client.execute_prompt(test_prompt)
        
        print(f"Response: {response}")
        
        if "Connection successful" in response:
            print("✅ Grok API connection is working!")
        else:
            print("⚠️  Grok API responded but with unexpected output")
            
    except Exception as e:
        print(f"❌ Grok API connection failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()