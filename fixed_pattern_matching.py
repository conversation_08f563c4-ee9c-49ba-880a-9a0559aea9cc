#!/usr/bin/env python3
"""
Fixed HTF Session Intelligence Parser patterns for underscore-separated text
"""
import re

# FIXED patterns with underscore support
fixed_session_patterns = {
    "session_references": [
        # Fixed: Handle both spaces and underscores, make "session" optional
        r"(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]?am|nyam|ny[_\s]?pm|nypm|pm)(?:[_\s]+session)?[_\s]+(?P<structure>high|low)",
        r"(?P<structure>high|low)[_\s]+(?:of[_\s]+(?:the[_\s]+)?)?(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]?am|nyam|ny[_\s]?pm|nypm|pm)(?:[_\s]+session)?",
        r"previous[_\s]+day'?s?[_\s]*(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]?am|nyam|ny[_\s]?pm|nypm|pm)?[_\s]*(?P<structure>high|low)",
        r"yesterday'?s?[_\s]*(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]?am|nyam|ny[_\s]?pm|nypm|pm)?[_\s]*(?P<structure>high|low)",
        # With level capture
        r"(?P<session>asia|london|lunch|ny[_\s]?am|ny[_\s]?pm)(?:[_\s]+session)?[_\s]+(?P<structure>low|high)[_\s]+(?:at[_\s]+)?(?P<level>[0-9,]+\.?[0-9]*)",
    ],
    "takeout_actions": [
        # Fixed: Handle underscores in takeout descriptions
        r"(?P<level>[\d,.]+)[_\s]+(?:gets?|was|is)?[_\s]*taken[_\s]+out",
        r"took[_\s]+(?:out[_\s]+)?(?P<reference>.*?)[_\s]+(?:at[_\s]+)?(?P<level>[\d,.]+)?",
        r"sweep[_\s]+(?:of[_\s]+)?(?P<reference>.*?)(?:[_\s]+at[_\s]+(?P<level>[\d,.]+))?",
        r"(?P<reference>.*?)[_\s]+taken[_\s]+(?:out[_\s]+)?(?:at[_\s]+(?P<level>[\d,.]+))?",
        # New: Direct underscore patterns for HTF event descriptions
        r"(?P<session>asia|london|lunch|ny_am|ny_pm|premarket|midnight)[_]+session[_]+(?P<structure>high|low)[_]+(?P<action>taken|violated|swept|broken)",
        r"(?P<session>asia|london|lunch|ny_am|ny_pm|premarket|midnight)[_]+(?P<structure>high|low)[_]+(?P<action>taken|violated|swept|broken)",
    ]
}

# Test cases from actual HTF event descriptions
test_cases = [
    "Asia_session_low_taken_at_open",
    "asia_session_low_taken_at_open", 
    "london_session_high_violated",
    "London_session_high_violated",
    "ny_am_session_low_swept",
    "NY_AM_session_low_swept",
    "asia_low_taken_at_23450",
    "london_high_violated_at_open",
    "took_out_Asia_session_low_at_23450",
    "Asia_session_low_gets_taken_out",
    "previous_day_Asia_low",
    "yesterday_London_high"
]

def test_fixed_patterns():
    print("🔧 FIXED HTF Pattern Matching Test")
    print("=" * 50)
    
    for test_text in test_cases:
        print(f"\n📝 Testing: '{test_text}'")
        matches_found = 0
        
        # Test fixed session reference patterns
        for i, pattern in enumerate(fixed_session_patterns["session_references"]):
            match = re.search(pattern, test_text.lower(), re.IGNORECASE)
            if match:
                matches_found += 1
                print(f"   ✅ Pattern {i+1} (session_references): {match.groupdict()}")
        
        # Test fixed takeout action patterns  
        for i, pattern in enumerate(fixed_session_patterns["takeout_actions"]):
            match = re.search(pattern, test_text.lower(), re.IGNORECASE)
            if match:
                matches_found += 1
                print(f"   ✅ Pattern {i+1} (takeout_actions): {match.groupdict()}")
        
        if matches_found == 0:
            print("   ❌ No patterns matched!")
        else:
            print(f"   📊 Total matches: {matches_found}")

def demonstrate_fix():
    print(f"\n🎯 KEY IMPROVEMENT DEMONSTRATION")
    print("=" * 50)
    
    critical_test = "Asia_session_low_taken_at_open"
    print(f"Critical Test Case: '{critical_test}'")
    
    # Show the specific pattern that should work
    underscore_pattern = r"(?P<session>asia|london|lunch|ny_am|ny_pm|premarket|midnight)[_]+session[_]+(?P<structure>high|low)[_]+(?P<action>taken|violated|swept|broken)"
    
    match = re.search(underscore_pattern, critical_test.lower(), re.IGNORECASE)
    if match:
        print(f"✅ FIXED: {match.groupdict()}")
        print(f"   Session: {match.group('session')}")
        print(f"   Structure: {match.group('structure')}")  
        print(f"   Action: {match.group('action')}")
    else:
        print("❌ Still not working!")

if __name__ == "__main__":
    test_fixed_patterns()
    demonstrate_fix()