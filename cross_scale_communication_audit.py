#!/usr/bin/env python3
"""
Cross-Scale Communication Audit
Maps all communication pathways between HTF and Session layers
"""

import sys
sys.path.append('/Users/<USER>/grok-claude-automation/src')

import json
import re
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import fields

# Import for introspection
from htf_master_controller_enhanced import HTFMasterControllerEnhanced, ActivationSignal, IntelligenceHTFEvent
from session_subordinate_executor import SessionHawkesExecutor, CascadePrediction
from cache_manager import UnifiedHawkesCache, get_unified_cache

def audit_activation_signal_dataclass():
    """Audit ActivationSignal dataclass - all fields and usage."""
    print("🔍 ACTIVATION SIGNAL DATACLASS AUDIT")
    print("=" * 60)
    
    # Get all fields from the dataclass
    signal_fields = fields(ActivationSignal)
    
    print(f"📊 ACTIVATION SIGNAL STRUCTURE:")
    print(f"   Total Fields: {len(signal_fields)}")
    
    for field in signal_fields:
        print(f"   • {field.name}: {field.type}")
        if hasattr(field, 'default') and field.default != field.default_factory:
            print(f"     Default: {field.default}")
    
    # Search for usage patterns across codebase
    print(f"\n🔄 USAGE ANALYSIS:")
    
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    usage_patterns = {}
    
    # Define patterns to search for each field
    field_patterns = {
        'target_sessions': [r'\.target_sessions', r'target_sessions\s*=', r'target_sessions\s*:'],
        'activation_window': [r'\.activation_window', r'activation_window\s*='],
        'cascade_type': [r'\.cascade_type', r'cascade_type\s*='],
        'param_adjustments': [r'\.param_adjustments', r'param_adjustments\s*='],
        'confidence_boost': [r'\.confidence_boost', r'confidence_boost\s*='],
        'htf_intensity': [r'\.htf_intensity', r'htf_intensity\s*='],
        'activation_time': [r'\.activation_time', r'activation_time\s*='],
        'intelligence_events': [r'\.intelligence_events', r'intelligence_events\s*=']
    }
    
    # Search Python files for usage
    python_files = list(base_dir.glob("**/*.py"))
    
    for field_name, patterns in field_patterns.items():
        usage_patterns[field_name] = {'files': set(), 'count': 0}
        
        for py_file in python_files:
            try:
                with open(py_file, 'r') as f:
                    content = f.read()
                
                for pattern in patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        usage_patterns[field_name]['files'].add(py_file.name)
                        usage_patterns[field_name]['count'] += len(matches)
                        
            except Exception:
                continue
    
    # Report usage
    for field_name, usage in usage_patterns.items():
        print(f"   📋 {field_name}:")
        print(f"      Files: {len(usage['files'])}")
        print(f"      Usage Count: {usage['count']}")
        if usage['files']:
            print(f"      Used in: {', '.join(list(usage['files'])[:3])}")
        print()
    
    return signal_fields, usage_patterns

def audit_session_parameter_adjustment():
    """Audit session parameter adjustment logic."""
    print("🔧 SESSION PARAMETER ADJUSTMENT AUDIT")
    print("=" * 60)
    
    # Find parameter adjustment implementations
    executor = SessionHawkesExecutor()
    
    print(f"📊 DEFAULT SESSION PARAMETERS:")
    for param, value in executor.default_params.items():
        print(f"   • {param}: {value}")
    
    print(f"\n🔄 ADJUSTMENT MECHANISMS:")
    
    # Check _adjust_parameters method
    adjustment_method = executor._adjust_parameters
    print(f"   Method: _adjust_parameters")
    print(f"   Parameters adjusted:")
    
    # Simulate adjustment process
    test_signal = {
        'param_adjustments': {
            'baseline_boost': 2.5,
            'decay_gamma': 0.143,
            'confidence_factor': 1.2
        }
    }
    
    # Save original parameters
    original_params = executor.current_params.copy()
    
    # Apply test adjustments
    executor._adjust_parameters(test_signal)
    
    print(f"   📋 Adjustment Results:")
    for param, new_value in executor.current_params.items():
        original_value = original_params[param]
        change = new_value / original_value if original_value != 0 else "N/A"
        print(f"      • {param}: {original_value:.6f} → {new_value:.6f} ({change:.2f}x)")
    
    # Reset parameters
    executor.current_params = original_params
    
    return executor.default_params, test_signal

def audit_unified_cache_sharing():
    """Audit unified cache state sharing mechanisms."""
    print("💾 UNIFIED CACHE STATE SHARING AUDIT")
    print("=" * 60)
    
    # Get unified cache instance
    cache = get_unified_cache()
    
    print(f"📊 CACHE CONFIGURATION:")
    print(f"   Max Size: {cache.cache.maxsize}")
    print(f"   TTL: {cache.cache.ttl} seconds")
    print(f"   Current Size: {len(cache.cache)}")
    
    # Check cache methods
    cache_methods = [method for method in dir(cache) if not method.startswith('_')]
    
    print(f"\n🔄 CACHE METHODS:")
    for method in cache_methods:
        print(f"   • {method}")
    
    # Find all components using unified cache
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    cache_users = set()
    
    # Search for cache usage patterns
    patterns = [
        r'get_unified_cache\(\)',
        r'from.*cache_manager.*import',
        r'unified_cache\.',
        r'UnifiedHawkesCache'
    ]
    
    python_files = list(base_dir.glob("**/*.py"))
    
    for py_file in python_files:
        try:
            with open(py_file, 'r') as f:
                content = f.read()
            
            for pattern in patterns:
                if re.search(pattern, content):
                    cache_users.add(py_file.name)
                    break
                    
        except Exception:
            continue
    
    print(f"\n📋 COMPONENTS USING UNIFIED CACHE:")
    print(f"   Total Components: {len(cache_users)}")
    for user in sorted(cache_users):
        print(f"   • {user}")
    
    # Test cache functionality
    print(f"\n🧪 CACHE FUNCTIONALITY TEST:")
    
    # Test volume caching
    test_session_data = {"session_id": "TEST_AUDIT", "range": 50.0}
    test_timestamp = "15:30:00"
    test_volume = 125.5
    
    # Set and get volume
    cache.set_volume(test_session_data, test_timestamp, test_volume)
    retrieved_volume = cache.get_volume(test_session_data, test_timestamp)
    
    print(f"   Volume Cache Test: {'✅ PASS' if retrieved_volume == test_volume else '❌ FAIL'}")
    
    # Test cascade prediction caching
    test_params = {"mu": 0.5, "alpha": 0.6, "beta": 0.02}
    test_prediction = {"cascade_time": 23.5, "confidence": 0.85}
    
    cache.set_cascade_prediction(test_session_data, test_params, test_prediction)
    retrieved_prediction = cache.get_cascade_prediction(test_session_data, test_params)
    
    print(f"   Cascade Cache Test: {'✅ PASS' if retrieved_prediction == test_prediction else '❌ FAIL'}")
    
    # Get cache statistics
    stats = cache.get_stats()
    print(f"\n📊 CACHE STATISTICS:")
    for stat, value in stats.items():
        print(f"   • {stat}: {value}")
    
    return cache_users, stats

def audit_weekend_carryover():
    """Audit weekend carryover implementation."""
    print("🎃 WEEKEND CARRYOVER IMPLEMENTATION AUDIT")
    print("=" * 60)
    
    # Find weekend carryover implementations
    base_dir = Path("/Users/<USER>/grok-claude-automation")
    weekend_patterns = [
        r'weekend.*carryover',
        r'friday.*carryover', 
        r'weekend.*gap',
        r'friday.*close',
        r'asia.*weekend'
    ]
    
    weekend_files = set()
    weekend_implementations = {}
    
    python_files = list(base_dir.glob("**/*.py"))
    
    for py_file in python_files:
        try:
            with open(py_file, 'r') as f:
                content = f.read()
            
            file_matches = []
            for pattern in weekend_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    file_matches.extend(matches)
            
            if file_matches:
                weekend_files.add(py_file.name)
                weekend_implementations[py_file.name] = file_matches
                
        except Exception:
            continue
    
    print(f"📋 WEEKEND CARRYOVER FILES:")
    print(f"   Total Files: {len(weekend_files)}")
    
    for filename in sorted(weekend_files):
        print(f"   • {filename}")
        matches = weekend_implementations.get(filename, [])
        if matches:
            print(f"     Patterns: {len(set(matches))} unique")
    
    # Check specific implementations
    print(f"\n🔄 KEY IMPLEMENTATIONS:")
    
    # Weekend Gap Analyzer
    gap_analyzer_file = base_dir / "src" / "weekend_gap_analyzer.py"
    if gap_analyzer_file.exists():
        print(f"   ✅ Weekend Gap Analyzer: {gap_analyzer_file.name}")
        
        # Read and analyze key methods
        with open(gap_analyzer_file, 'r') as f:
            content = f.read()
        
        methods = re.findall(r'def\s+(\w*weekend\w*|\w*carryover\w*|\w*gap\w*)', content, re.IGNORECASE)
        print(f"     Weekend Methods: {methods}")
    
    # HTF Calibration files
    htf_calibration = base_dir / "data" / "htf" / "htf_calibrated_gammas_july28.json"
    if htf_calibration.exists():
        print(f"   ✅ HTF Calibration: {htf_calibration.name}")
        
        with open(htf_calibration, 'r') as f:
            data = json.load(f)
        
        # Check for weekend-related configurations
        weekend_keys = [key for key in data.keys() if 'weekend' in key.lower() or 'friday' in key.lower()]
        print(f"     Weekend Config Keys: {weekend_keys}")
        
        # Check friday event analysis
        if 'friday_event_analysis' in data:
            friday_events = data['friday_event_analysis']
            print(f"     Friday Events: {len(friday_events)}")
            for event, details in friday_events.items():
                decay = details.get('decay_over_weekend', 'N/A')
                print(f"       • {event}: {decay} weekend decay")
    
    # Temporal Marker Matrix
    fractal_file = base_dir / "src" / "fractal_cascade_integrator.py"
    if fractal_file.exists():
        print(f"   ✅ Fractal Cascade Integrator: {fractal_file.name}")
        
        with open(fractal_file, 'r') as f:
            content = f.read()
        
        # Find weekend carryover matrix entries
        weekend_matrix_matches = re.findall(r'"weekend.*?".*?:\s*\(.*?\)', content)
        print(f"     Weekend Matrix Entries: {len(weekend_matrix_matches)}")
        for match in weekend_matrix_matches:
            print(f"       • {match}")
    
    return weekend_files, weekend_implementations

def run_complete_communication_audit():
    """Run complete cross-scale communication audit."""
    print("🔍 CROSS-SCALE COMMUNICATION AUDIT")
    print("=" * 80)
    print("Mapping HTF ↔ Session communication pathways\n")
    
    # Phase 1: ActivationSignal Analysis
    signal_fields, signal_usage = audit_activation_signal_dataclass()
    
    print()
    
    # Phase 2: Session Parameter Adjustments
    default_params, test_adjustments = audit_session_parameter_adjustment()
    
    print()
    
    # Phase 3: Unified Cache Sharing
    cache_users, cache_stats = audit_unified_cache_sharing()
    
    print()
    
    # Phase 4: Weekend Carryover
    weekend_files, weekend_impls = audit_weekend_carryover()
    
    # Summary
    print(f"\n🏆 COMMUNICATION AUDIT SUMMARY")
    print("=" * 50)
    print(f"✅ ActivationSignal Fields: {len(signal_fields)} fields mapped")
    print(f"✅ Signal Usage: {sum(usage['count'] for usage in signal_usage.values())} total references")
    print(f"✅ Parameter Adjustments: {len(default_params)} parameters with boost/gamma/confidence logic")
    print(f"✅ Cache Users: {len(cache_users)} components sharing unified state")
    print(f"✅ Weekend Carryover: {len(weekend_files)} files implementing Friday→Asia transitions")
    
    # Communication pathways map
    print(f"\n🗺️ COMMUNICATION PATHWAYS MAP:")
    print(f"   HTF Master Controller")
    print(f"   ↓ (generates ActivationSignal)")
    print(f"   Session Subordinate Executor")
    print(f"   ↓ (parameter adjustments)")
    print(f"   Unified Cache (shared state)")
    print(f"   ↓ (weekend carryover)")
    print(f"   Cross-Session Memory")
    
    return {
        'activation_signal': {'fields': len(signal_fields), 'usage': signal_usage},
        'parameters': {'count': len(default_params), 'adjustments': test_adjustments},
        'cache': {'users': len(cache_users), 'stats': cache_stats},
        'weekend': {'files': len(weekend_files), 'implementations': len(weekend_impls)}
    }

if __name__ == "__main__":
    audit_results = run_complete_communication_audit()