# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project-specific
# Test results and temporary files
*test*.json
*_test_*.json
temp_*
latest_run.json
temp_cleanup/

# Log files
*.log
logs/

# API keys and secrets
.env
secrets.json
api_keys.txt

# Output files
output_*.json
results_*.json
prediction_*.json
monte_carlo_enhanced_output.txt

# Backup files
*.bak
*.backup
*_backup.*
*_old.*

# Data files (comment out if needed for tracking)
# *.json

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pytest
.pytest_cache/
.coverage
htmlcov/

# Documentation builds
docs/_build/