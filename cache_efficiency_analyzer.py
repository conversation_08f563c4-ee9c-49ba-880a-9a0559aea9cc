#!/usr/bin/env python3
"""
Cache Efficiency Analysis
Analyze UnifiedHawkesCache usage patterns, hit/miss rates, and memory efficiency
"""

import sys
sys.path.append('/Users/<USER>/grok-claude-automation/src')

import time
import gc
import json
# import psutil  # Not available, will use alternative memory tracking
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import logging

# Import cache and related components
from cache_manager import UnifiedHawkesCache, get_unified_cache
from htf_master_controller_enhanced import HTFMasterControllerEnhanced
from session_subordinate_executor import SessionHawkesExecutor

class CacheEfficiencyAnalyzer:
    """Analyzes UnifiedHawkesCache efficiency and usage patterns."""
    
    def __init__(self):
        self.setup_logging()
        self.cache = get_unified_cache()
        self.original_methods = {}
        self.access_patterns = defaultdict(list)
        self.memory_snapshots = []
        self.start_time = time.time()
        
        # Monkey patch cache methods for monitoring
        self.instrument_cache_methods()
        
    def setup_logging(self):
        """Setup cache analysis logging."""
        self.logger = logging.getLogger("CacheEfficiencyAnalyzer")
        self.logger.setLevel(logging.DEBUG)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s.%(msecs)03d - CACHE_ANALYZER - %(levelname)s - %(message)s',
                datefmt='%H:%M:%S'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def instrument_cache_methods(self):
        """Instrument cache methods to track access patterns."""
        # Store original methods (only the ones that exist)
        self.original_methods = {
            'get_volume': self.cache.get_volume,
            'set_volume': self.cache.set_volume,
            'get_cascade_prediction': self.cache.get_cascade_prediction,
            'set_cascade_prediction': self.cache.set_cascade_prediction
        }
        
        # Create instrumented versions
        def create_instrumented_get(method_name, original_method):
            def instrumented_get(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = original_method(*args, **kwargs)
                    end_time = time.perf_counter()
                    
                    hit = result is not None
                    self.record_cache_access(method_name, 'GET', hit, end_time - start_time, args, kwargs)
                    return result
                except Exception as e:
                    end_time = time.perf_counter()
                    self.record_cache_access(method_name, 'GET', False, end_time - start_time, args, kwargs, str(e))
                    raise
            return instrumented_get
        
        def create_instrumented_set(method_name, original_method):
            def instrumented_set(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = original_method(*args, **kwargs)
                    end_time = time.perf_counter()
                    
                    self.record_cache_access(method_name, 'SET', True, end_time - start_time, args, kwargs)
                    return result
                except Exception as e:
                    end_time = time.perf_counter()
                    self.record_cache_access(method_name, 'SET', False, end_time - start_time, args, kwargs, str(e))
                    raise
            return instrumented_set
        
        # Apply instrumentation
        for method_name, original_method in self.original_methods.items():
            if method_name.startswith('get_'):
                instrumented = create_instrumented_get(method_name, original_method)
            else:
                instrumented = create_instrumented_set(method_name, original_method)
            
            setattr(self.cache, method_name, instrumented)
    
    def record_cache_access(self, method: str, operation: str, success: bool, duration: float, 
                           args: tuple, kwargs: dict, error: str = None):
        """Record cache access pattern."""
        access_record = {
            'timestamp': time.time(),
            'method': method,
            'operation': operation,
            'success': success,
            'duration_ms': duration * 1000,
            'args_count': len(args),
            'kwargs_count': len(kwargs),
            'error': error
        }
        
        self.access_patterns[method].append(access_record)
        
        if operation == 'GET':
            status = "HIT" if success and not error else "MISS"
            self.logger.debug(f"🔍 {method} {operation}: {status} ({duration*1000:.2f}ms)")
        else:
            status = "SUCCESS" if success else "FAILED" 
            self.logger.debug(f"💾 {method} {operation}: {status} ({duration*1000:.2f}ms)")
    
    def take_memory_snapshot(self, label: str):
        """Take a memory usage snapshot."""
        # Use gc module for basic memory tracking since psutil not available
        gc.collect()  # Force garbage collection for accurate count
        
        snapshot = {
            'timestamp': time.time(),
            'label': label,
            'cache_size': len(self.cache.cache) if hasattr(self.cache, 'cache') else 0,
            'gc_objects': len(gc.get_objects()),
            'gc_stats': gc.get_stats() if hasattr(gc, 'get_stats') else []
        }
        
        self.memory_snapshots.append(snapshot)
        self.logger.debug(f"📊 Memory snapshot '{label}': GC objects={snapshot['gc_objects']}, Cache size={snapshot['cache_size']}")
        
        return snapshot
    
    def simulate_cache_usage_patterns(self):
        """Simulate various cache usage patterns to analyze efficiency."""
        print("🧪 SIMULATING CACHE USAGE PATTERNS")
        print("=" * 60)
        
        self.take_memory_snapshot("initial")
        
        # Pattern 1: Volume caching patterns
        print("\n📊 Testing Volume Caching Patterns...")
        session_data = {"session_id": "LONDON_TEST", "range": 75.5}
        
        # Multiple volume sets and gets
        for i in range(20):
            timestamp = f"15:{30+i}:00"
            volume = 100.0 + i * 5.0
            
            # Set volume
            self.cache.set_volume(session_data, timestamp, volume)
            
            # Get volume (should hit)
            retrieved = self.cache.get_volume(session_data, timestamp)
            
            # Get non-existent volume (should miss)
            self.cache.get_volume(session_data, f"16:{30+i}:00")
        
        self.take_memory_snapshot("after_volume_test")
        
        # Pattern 2: Cascade prediction caching
        print("\n🎯 Testing Cascade Prediction Caching...")
        
        for i in range(15):
            params = {"mu": 0.15 + i*0.01, "alpha": 0.6, "beta": 0.02}
            prediction = {"cascade_time": 20.0 + i, "confidence": 0.85}
            
            # Set prediction
            self.cache.set_cascade_prediction(session_data, params, prediction)
            
            # Get prediction (should hit)
            retrieved = self.cache.get_cascade_prediction(session_data, params)
            
            # Get with slightly different params (should miss)
            modified_params = {"mu": 0.15 + i*0.01 + 0.001, "alpha": 0.6, "beta": 0.02}
            self.cache.get_cascade_prediction(session_data, modified_params)
        
        self.take_memory_snapshot("after_cascade_test")
        
        # Pattern 3: Additional volume and prediction patterns (intensity methods not available)
        print("\n⚡ Testing Extended Volume and Prediction Patterns...")
        
        for i in range(25):
            # More volume patterns
            volume_timestamp = f"16:{i:02d}:00"
            volume_value = 200.0 + i * 2.5
            
            # Set volume
            self.cache.set_volume(session_data, volume_timestamp, volume_value)
            
            # Get volume (should hit)
            self.cache.get_volume(session_data, volume_timestamp)
            
            # Additional prediction patterns
            extended_params = {"mu": 0.2 + i*0.005, "alpha": 0.7, "beta": 0.025}
            extended_prediction = {"cascade_time": 30.0 + i*0.5, "confidence": 0.80 + i*0.01}
            
            self.cache.set_cascade_prediction(session_data, extended_params, extended_prediction)
            self.cache.get_cascade_prediction(session_data, extended_params)
        
        self.take_memory_snapshot("after_extended_test")
        
        # Pattern 4: Cache thrashing simulation
        print("\n💥 Testing Cache Thrashing Patterns...")
        
        # Create many different sessions to potentially exceed cache limits
        for session_num in range(50):
            session_data_varied = {"session_id": f"SESSION_{session_num:03d}", "range": 50.0 + session_num}
            
            # Set data for each session
            self.cache.set_volume(session_data_varied, "15:30:00", 100.0 + session_num)
            self.cache.set_cascade_prediction(session_data_varied, {"mu": 0.15}, {"cascade_time": 20.0})
            
            # Try to retrieve immediately (should hit if cache is large enough)
            self.cache.get_volume(session_data_varied, "15:30:00")
        
        # Now try to retrieve earlier sessions (might miss if evicted)
        for session_num in range(10):
            session_data_varied = {"session_id": f"SESSION_{session_num:03d}", "range": 50.0 + session_num}
            self.cache.get_volume(session_data_varied, "15:30:00")
        
        self.take_memory_snapshot("after_thrashing_test")
        
        print("✅ Cache usage pattern simulation complete")
    
    def analyze_component_sharing(self):
        """Analyze which components effectively share cache state."""
        print(f"\n🔗 ANALYZING COMPONENT STATE SHARING")
        print("=" * 50)
        
        # Initialize multiple components that should share cache
        components = {
            'htf_controller': HTFMasterControllerEnhanced(),
            'session_executor': SessionHawkesExecutor()
        }
        
        # Test cache sharing by having components access same data
        shared_session_data = {"session_id": "SHARED_TEST", "range": 90.0}
        
        # Component 1 sets data
        self.cache.set_volume(shared_session_data, "15:45:00", 150.0)
        
        # Component 2 tries to access same data
        volume_shared = self.cache.get_volume(shared_session_data, "15:45:00") 
        # Test cascade prediction sharing instead of intensity (not available)
        test_params = {"mu": 0.25, "alpha": 0.6, "beta": 0.02}
        test_prediction = {"cascade_time": 25.0, "confidence": 0.90}
        self.cache.set_cascade_prediction(shared_session_data, test_params, test_prediction)
        prediction_shared = self.cache.get_cascade_prediction(shared_session_data, test_params)
        
        sharing_effective = volume_shared is not None and prediction_shared is not None
        
        print(f"   Cache Sharing Test: {'✅ EFFECTIVE' if sharing_effective else '❌ INEFFECTIVE'}")
        print(f"   Volume Retrieved: {volume_shared}")
        print(f"   Prediction Retrieved: {prediction_shared}")
        
        # Test cross-component cache invalidation/updates
        original_stats = self.cache.get_stats()
        
        # Update cache from component 1
        self.cache.set_volume(shared_session_data, "15:45:00", 175.0)  # Update existing
        
        # Check if component 2 sees the update
        updated_volume = self.cache.get_volume(shared_session_data, "15:45:00")
        update_consistent = updated_volume == 175.0
        
        print(f"   Cache Update Consistency: {'✅ CONSISTENT' if update_consistent else '❌ INCONSISTENT'}")
        
        updated_stats = self.cache.get_stats()
        
        return {
            'sharing_effective': sharing_effective,
            'update_consistent': update_consistent,
            'stats_before': original_stats,
            'stats_after': updated_stats
        }
    
    def analyze_hit_miss_rates(self) -> Dict[str, Any]:
        """Analyze cache hit/miss rates by method."""
        print(f"\n📈 CACHE HIT/MISS ANALYSIS")
        print("=" * 40)
        
        hit_miss_stats = {}
        
        for method, accesses in self.access_patterns.items():
            if not accesses:
                continue
                
            get_accesses = [a for a in accesses if a['operation'] == 'GET']
            set_accesses = [a for a in accesses if a['operation'] == 'SET']
            
            if get_accesses:
                hits = sum(1 for a in get_accesses if a['success'] and not a.get('error'))
                total_gets = len(get_accesses)
                hit_rate = (hits / total_gets) * 100 if total_gets > 0 else 0
                
                avg_get_time = sum(a['duration_ms'] for a in get_accesses) / len(get_accesses)
                
                hit_miss_stats[method] = {
                    'total_gets': total_gets,
                    'hits': hits,
                    'misses': total_gets - hits,
                    'hit_rate_percent': hit_rate,
                    'avg_get_time_ms': avg_get_time,
                    'total_sets': len(set_accesses),
                    'set_success_rate': (sum(1 for a in set_accesses if a['success']) / len(set_accesses)) * 100 if set_accesses else 0
                }
                
                print(f"   📋 {method}:")
                print(f"      Gets: {total_gets}, Hits: {hits}, Hit Rate: {hit_rate:.1f}%")
                print(f"      Avg Get Time: {avg_get_time:.2f}ms")
                print(f"      Sets: {len(set_accesses)}, Set Success: {hit_miss_stats[method]['set_success_rate']:.1f}%")
        
        # Overall statistics
        total_gets = sum(stats['total_gets'] for stats in hit_miss_stats.values())
        total_hits = sum(stats['hits'] for stats in hit_miss_stats.values())
        overall_hit_rate = (total_hits / total_gets) * 100 if total_gets > 0 else 0
        
        print(f"\n🎯 OVERALL CACHE PERFORMANCE:")
        print(f"   Total Gets: {total_gets}")
        print(f"   Total Hits: {total_hits}")
        print(f"   Overall Hit Rate: {overall_hit_rate:.1f}%")
        
        return {
            'method_stats': hit_miss_stats,
            'overall': {
                'total_gets': total_gets,
                'total_hits': total_hits,
                'overall_hit_rate': overall_hit_rate
            }
        }
    
    def analyze_cache_thrashing(self) -> Dict[str, Any]:
        """Identify cache thrashing and redundant calculations."""
        print(f"\n💥 CACHE THRASHING ANALYSIS")
        print("=" * 40)
        
        # Analyze access patterns for thrashing indicators
        thrashing_indicators = {}
        
        for method, accesses in self.access_patterns.items():
            if len(accesses) < 10:  # Need sufficient data
                continue
            
            get_accesses = [a for a in accesses if a['operation'] == 'GET']
            if len(get_accesses) < 5:
                continue
            
            # Calculate thrashing indicators
            recent_accesses = get_accesses[-20:]  # Last 20 accesses
            recent_hit_rate = (sum(1 for a in recent_accesses if a['success']) / len(recent_accesses)) * 100
            
            # Detect rapid miss patterns (potential thrashing)
            miss_streaks = []
            current_streak = 0
            
            for access in recent_accesses:
                if not access['success']:
                    current_streak += 1
                else:
                    if current_streak > 0:
                        miss_streaks.append(current_streak)
                    current_streak = 0
            
            if current_streak > 0:
                miss_streaks.append(current_streak)
            
            max_miss_streak = max(miss_streaks) if miss_streaks else 0
            avg_miss_streak = sum(miss_streaks) / len(miss_streaks) if miss_streaks else 0
            
            # Detect redundant calculations (same args within short time)
            redundant_calculations = 0
            time_window = 5.0  # 5 seconds
            
            for i, access in enumerate(get_accesses):
                if not access['success']:  # Miss - potential redundant calculation
                    # Check if same arguments accessed recently
                    recent_window = [a for a in get_accesses[:i] 
                                   if access['timestamp'] - a['timestamp'] < time_window]
                    
                    # Simple redundancy check (same arg count)
                    similar_misses = [a for a in recent_window 
                                    if not a['success'] and a['args_count'] == access['args_count']]
                    
                    if similar_misses:
                        redundant_calculations += 1
            
            thrashing_indicators[method] = {
                'recent_hit_rate': recent_hit_rate,
                'max_miss_streak': max_miss_streak,
                'avg_miss_streak': avg_miss_streak,
                'redundant_calculations': redundant_calculations,
                'total_recent_accesses': len(recent_accesses),
                'thrashing_risk': 'HIGH' if recent_hit_rate < 50 and max_miss_streak > 5 else 
                                'MEDIUM' if recent_hit_rate < 70 or max_miss_streak > 3 else 'LOW'
            }
            
            print(f"   📋 {method}:")
            print(f"      Recent Hit Rate: {recent_hit_rate:.1f}%")
            print(f"      Max Miss Streak: {max_miss_streak}")
            print(f"      Redundant Calculations: {redundant_calculations}")
            print(f"      Thrashing Risk: {thrashing_indicators[method]['thrashing_risk']}")
        
        return thrashing_indicators
    
    def analyze_memory_patterns(self) -> Dict[str, Any]:
        """Analyze memory usage patterns."""
        print(f"\n💾 MEMORY USAGE PATTERN ANALYSIS")
        print("=" * 50)
        
        if len(self.memory_snapshots) < 2:
            print("   ❌ Insufficient memory snapshots for analysis")
            return {}
        
        # Calculate memory growth patterns
        initial_snapshot = self.memory_snapshots[0]
        final_snapshot = self.memory_snapshots[-1]
        
        cache_size_growth = final_snapshot['cache_size'] - initial_snapshot['cache_size']
        gc_objects_growth = final_snapshot['gc_objects'] - initial_snapshot['gc_objects']
        
        print(f"   📊 MEMORY GROWTH ANALYSIS:")
        print(f"      Cache Size Growth: {cache_size_growth:+d} entries")
        print(f"      GC Objects Growth: {gc_objects_growth:+d} objects")
        
        # Analyze memory efficiency per cache operation
        total_cache_ops = sum(len(accesses) for accesses in self.access_patterns.values())
        objects_per_op = gc_objects_growth / total_cache_ops if total_cache_ops > 0 else 0
        
        print(f"\n   📈 MEMORY EFFICIENCY:")
        print(f"      Total Cache Operations: {total_cache_ops}")
        print(f"      GC Objects per Operation: {objects_per_op:.2f} objects/op")
        
        # Find memory spikes (GC object increases)
        memory_spikes = []
        for i in range(1, len(self.memory_snapshots)):
            prev = self.memory_snapshots[i-1]
            curr = self.memory_snapshots[i]
            
            objects_increase = curr['gc_objects'] - prev['gc_objects']
            if objects_increase > 1000:  # Significant increase (>1000 objects)
                memory_spikes.append({
                    'from_label': prev['label'],
                    'to_label': curr['label'],
                    'objects_increase': objects_increase,
                    'cache_size_change': curr['cache_size'] - prev['cache_size']
                })
        
        if memory_spikes:
            print(f"\n   🚨 MEMORY SPIKES DETECTED:")
            for spike in memory_spikes:
                print(f"      {spike['from_label']} → {spike['to_label']}: +{spike['objects_increase']} objects")
        
        return {
            'growth_analysis': {
                'cache_size_growth': cache_size_growth,
                'gc_objects_growth': gc_objects_growth
            },
            'efficiency': {
                'total_operations': total_cache_ops,
                'objects_per_operation': objects_per_op
            },
            'memory_spikes': memory_spikes,
            'snapshots': self.memory_snapshots
        }
    
    def cleanup_instrumentation(self):
        """Restore original cache methods."""
        for method_name, original_method in self.original_methods.items():
            setattr(self.cache, method_name, original_method)
    
    def run_complete_cache_analysis(self) -> Dict[str, Any]:
        """Run complete cache efficiency analysis."""
        print("💾 UNIFIED HAWKES CACHE EFFICIENCY ANALYSIS")
        print("=" * 80)
        print("Analyzing cache hit/miss rates, state sharing, and memory patterns\n")
        
        try:
            # Phase 1: Simulate usage patterns
            self.simulate_cache_usage_patterns()
            
            # Phase 2: Analyze component sharing
            sharing_analysis = self.analyze_component_sharing()
            
            # Phase 3: Hit/miss rate analysis
            hit_miss_analysis = self.analyze_hit_miss_rates()
            
            # Phase 4: Cache thrashing analysis
            thrashing_analysis = self.analyze_cache_thrashing()
            
            # Phase 5: Memory pattern analysis
            memory_analysis = self.analyze_memory_patterns()
            
            # Generate comprehensive report
            cache_stats = self.cache.get_stats()
            
            report = {
                'analysis_duration_seconds': time.time() - self.start_time,
                'cache_configuration': {
                    'max_size': getattr(self.cache.cache, 'maxsize', 'Unknown'),
                    'ttl_seconds': getattr(self.cache.cache, 'ttl', 'Unknown'),
                    'current_size': len(self.cache.cache) if hasattr(self.cache, 'cache') else 0
                },
                'hit_miss_analysis': hit_miss_analysis,
                'component_sharing': sharing_analysis,
                'thrashing_analysis': thrashing_analysis,
                'memory_analysis': memory_analysis,
                'cache_stats': cache_stats,
                'access_patterns_summary': {
                    method: {
                        'total_accesses': len(accesses),
                        'get_operations': len([a for a in accesses if a['operation'] == 'GET']),
                        'set_operations': len([a for a in accesses if a['operation'] == 'SET'])
                    } for method, accesses in self.access_patterns.items()
                }
            }
            
            # Summary
            print(f"\n🏆 CACHE EFFICIENCY ANALYSIS SUMMARY")
            print("=" * 50)
            print(f"✅ Analysis Duration: {report['analysis_duration_seconds']:.1f} seconds")
            print(f"✅ Overall Hit Rate: {hit_miss_analysis['overall']['overall_hit_rate']:.1f}%")
            print(f"✅ Component Sharing: {'Effective' if sharing_analysis['sharing_effective'] else 'Ineffective'}")
            print(f"✅ GC Objects Growth: {memory_analysis['growth_analysis']['gc_objects_growth']:+d} objects")
            
            high_thrashing_methods = [method for method, stats in thrashing_analysis.items() 
                                    if stats['thrashing_risk'] == 'HIGH']
            print(f"✅ High Thrashing Risk: {len(high_thrashing_methods)} methods")
            
            return report
            
        finally:
            # Always cleanup instrumentation
            self.cleanup_instrumentation()

def main():
    """Run cache efficiency analysis."""
    analyzer = CacheEfficiencyAnalyzer()
    report = analyzer.run_complete_cache_analysis()
    
    # Save detailed report
    report_file = Path("/Users/<USER>/grok-claude-automation/cache_efficiency_report.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n💾 Detailed report saved: {report_file.name}")

if __name__ == "__main__":
    main()