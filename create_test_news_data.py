#!/usr/bin/env python3
"""
Create test news data for Level-1 files to validate news pipeline.
"""
import json
from pathlib import Path

def create_sample_news_context():
    """Create sample news_context structure with test data."""
    return {
        "news_impacted": True,
        "news_events": [
            {
                "event_time": 15,  # 15 minutes into session
                "event_type": "economic_data",
                "description": "Federal Reserve Interest Rate Decision - Hawkish Surprise",
                "impact_score": 4.5,  # High impact (>3 triggers news_factor boost)
                "source": "Federal Reserve",
                "sentiment": "bearish",
                "market_reaction": "immediate_selling_pressure"
            },
            {
                "event_time": 45,  # 45 minutes into session
                "event_type": "geopolitical",
                "description": "Trade War Escalation - New Tariffs Announced",
                "impact_score": 3.8,  # High impact
                "source": "Reuters",
                "sentiment": "bearish", 
                "market_reaction": "risk_off_sentiment"
            },
            {
                "event_time": 75,  # 75 minutes into session
                "event_type": "earnings",
                "description": "Major Tech Earnings Beat - Apple Revenue Surprise",
                "impact_score": 2.5,  # Medium impact (<3 no news_factor boost)
                "source": "Bloomberg",
                "sentiment": "bullish",
                "market_reaction": "sector_rotation_to_tech"
            }
        ],
        "cumulative_news_score": 3.6,  # Average of impact scores
        "dominant_sentiment": "bearish",
        "market_volatility_boost": 1.4  # Expected volatility multiplier
    }

def create_empty_news_context():
    """Create empty news_context structure (current state)."""
    return {
        "news_impacted": False,
        "news_events": []
    }

def update_session_file_with_news(file_path: Path, add_news: bool = True):
    """Update a Level-1 session file with news context."""
    try:
        with open(file_path, 'r') as f:
            session_data = json.load(f)
        
        # Update news_context
        if add_news:
            session_data["news_context"] = create_sample_news_context()
            print(f"✅ Added news data to {file_path.name}")
        else:
            session_data["news_context"] = create_empty_news_context()
            print(f"📋 Reset news data in {file_path.name}")
        
        # Write back to file
        with open(file_path, 'w') as f:
            json.dump(session_data, f, indent=2)
            
        return True
        
    except Exception as e:
        print(f"❌ Error updating {file_path.name}: {e}")
        return False

def trace_news_pipeline():
    """Trace news data through the complete pipeline."""
    print("🔍 NEWS DATA PIPELINE TRACE")
    print("=" * 50)
    
    # 1. Entry Points
    print("\n1️⃣ NEWS ENTRY POINTS:")
    print("   • Level-1 Files: news_context.news_events[]")
    print("   • Weekend Gap Analyzer: weekend_news parameter")
    print("   • HTF Intelligence Parser: Extracts from Level-1 data")
    
    # 2. Processing Flow
    print("\n2️⃣ NEWS PROCESSING FLOW:")
    print("   Level-1 news_context")
    print("   ↓")
    print("   HTF Adaptive Coupling._calculate_news_factor()")
    print("   ↓ (checks impact_score > 3 within 30min)")
    print("   news_factor = 0.0-0.2")
    print("   ↓")
    print("   CouplingContext.news_factor")
    print("   ↓")
    print("   γ(t) calculation with news weight")
    
    # 3. Current State Analysis
    print("\n3️⃣ CURRENT STATE ANALYSIS:")
    
    # Check existing Level-1 files
    sessions_dir = Path("/Users/<USER>/grok-claude-automation/data/sessions/level_1")
    level1_files = list(sessions_dir.glob("*_Lvl-1_*.json"))
    
    news_status = {}
    for file_path in level1_files[:3]:  # Check first 3 files
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            news_context = data.get('news_context', {})
            news_events = news_context.get('news_events', [])
            
            news_status[file_path.name] = {
                'has_news_context': 'news_context' in data,
                'news_impacted': news_context.get('news_impacted', False),
                'events_count': len(news_events),
                'total_impact': sum(event.get('impact_score', 0) for event in news_events)
            }
            
        except Exception as e:
            news_status[file_path.name] = {'error': str(e)}
    
    for filename, status in news_status.items():
        print(f"   📄 {filename}:")
        if 'error' in status:
            print(f"      ❌ Error: {status['error']}")
        else:
            print(f"      News Context: {'✅' if status['has_news_context'] else '❌'}")
            print(f"      News Impacted: {'✅' if status['news_impacted'] else '❌'}")
            print(f"      Events Count: {status['events_count']}")
            print(f"      Total Impact: {status['total_impact']}")
    
    # 4. Gap Analysis
    print("\n4️⃣ PIPELINE GAP ANALYSIS:")
    print("   ❌ All Level-1 files have empty news_events arrays")
    print("   ❌ No automated news ingestion system")
    print("   ❌ HTF Adaptive Coupling falls back to session_character proxy")
    print("   ❌ news_factor consistently returns 0.0")
    print("   ❌ γ(t) coupling never gets news boost")
    
    return news_status

def demonstrate_news_fix():
    """Demonstrate how news data should work."""
    print("\n5️⃣ NEWS PIPELINE FIX DEMONSTRATION:")
    
    # Create test session file
    test_file = Path("/Users/<USER>/grok-claude-automation/test_news_session.json")
    
    sample_session = {
        "session_metadata": {
            "session_id": "TEST_NEWS_2025_07_31",
            "session_type": "NY_PM",
            "date": "2025-07-31"
        },
        "price_data": {
            "session_character": "major_expansion_with_news_impact",
            "range": 89.5
        },
        "news_context": create_sample_news_context()
    }
    
    with open(test_file, 'w') as f:
        json.dump(sample_session, f, indent=2)
    
    print(f"   ✅ Created test file: {test_file.name}")
    
    # Simulate news_factor calculation
    news_context = sample_session["news_context"]
    news_events = news_context["news_events"]
    
    print(f"\n   🧮 NEWS_FACTOR CALCULATION SIMULATION:")
    t = 20  # 20 minutes into session
    max_impact = 0.0
    
    for event in news_events:
        impact_score = event.get('impact_score', 0)
        event_time = event.get('event_time', 0)
        
        print(f"      Event at {event_time}min: impact={impact_score}")
        
        if impact_score > 3 and abs(event_time - t) < 30:
            max_impact = max(max_impact, impact_score)
            print(f"         ✅ Within 30min window, impact={impact_score}")
    
    # Calculate news_factor
    if max_impact > 5:
        news_factor = 0.2
    elif max_impact > 3:
        news_factor = 0.15
    else:
        news_factor = 0.0
    
    print(f"   📊 RESULT:")
    print(f"      Max Impact Score: {max_impact}")
    print(f"      Calculated news_factor: {news_factor}")
    print(f"      Status: {'✅ NEWS BOOST APPLIED' if news_factor > 0 else '❌ NO NEWS BOOST'}")
    
    # Clean up
    test_file.unlink()
    
    return news_factor

if __name__ == "__main__":
    # Run complete trace
    news_status = trace_news_pipeline()
    
    # Demonstrate fix
    news_factor = demonstrate_news_fix()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Current State: All news_events arrays are empty")
    print(f"   Expected news_factor: 0.15-0.2 with proper news data")
    print(f"   Actual news_factor: 0.0 (falls back to session_character)")
    print(f"   Fix Required: Populate news_context.news_events with real data")
    print(f"   Test Result: {news_factor} news_factor achieved with sample data")