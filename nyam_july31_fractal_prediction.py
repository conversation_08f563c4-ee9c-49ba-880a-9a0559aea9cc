#!/usr/bin/env python3
"""
NYAM July 31st Fractal Hawkes Prediction
Complete prediction using the fixed fractal HTF-Session cascade system
"""

import sys
sys.path.append('/Users/<USER>/grok-claude-automation/src')

import json
import math
from datetime import datetime, timedelta
from pathlib import Path

# Import our fixed components
from htf_master_controller_enhanced import HTFMasterControllerEnhanced, ActivationSignal
from session_subordinate_executor import SessionHawkesExecutor, CascadePrediction
from fractal_cascade_integrator import FractalCascadeIntegrator

def run_nyam_july31_prediction():
    """Run complete prediction on NYAM July 31st data."""
    print("🚀 NYAM JULY 31ST FRACTAL HAWKES PREDICTION")
    print("=" * 80)
    print("Session: NY AM | Date: 2025-07-31 | Time: 09:30-10:43 ET")
    print("System: Fixed Fractal HTF-Session Cascade with Dual Hawkes Architecture\n")
    
    # Load session data
    data_file = Path("/Users/<USER>/grok-claude-automation/NYAM_PRELIMINARY_Lvl-1_2025_07_31.json")
    with open(data_file, 'r') as f:
        session_data = json.load(f)
    
    # Extract session metadata
    metadata = session_data["session_metadata"]
    price_data = session_data["price_data"]
    movements = session_data["price_movements"]
    
    print(f"📊 SESSION OVERVIEW:")
    print(f"   Session: {metadata['session_type']} on {metadata['date']}")
    print(f"   Duration: {metadata['start_time']} - {metadata['end_time']} ({metadata['duration_minutes']} min)")
    print(f"   Price Range: {price_data['high']} - {price_data['low']} ({price_data['range']} points)")
    print(f"   Session Character: {price_data['session_character']}")
    
    # Identify HTF events from price movements
    print(f"\n🎯 HTF EVENT IDENTIFICATION:")
    htf_events = []
    
    for movement in movements:
        context = movement["context"]
        # Look for HTF-significant events (our fixed pattern matching)
        if any(pattern in context.lower() for pattern in ["asia_session_low", "session_high", "session_low"]):
            htf_events.append({
                "timestamp": movement["timestamp"],
                "price": movement["price"],
                "context": context,
                "action": movement["action"]
            })
    
    print(f"   HTF Events Detected: {len(htf_events)}")
    for event in htf_events:
        print(f"      • {event['timestamp']}: {event['context']} at {event['price']}")
    
    # Focus on the key HTF event: Asia session low taken at open
    key_event = None
    for event in htf_events:
        if "asia_session_low" in event["context"].lower():
            key_event = event
            break
    
    if not key_event:
        print(f"\n❌ No Asia session low HTF event found!")
        return None
    
    print(f"\n🔥 KEY HTF EVENT SELECTED:")
    print(f"   Event: {key_event['context']}")
    print(f"   Time: {key_event['timestamp']}")
    print(f"   Price: {key_event['price']}")
    print(f"   Action: {key_event['action']}")
    
    # Initialize fractal system components
    print(f"\n🏗️ INITIALIZING FRACTAL HAWKES SYSTEM:")
    htf_controller = HTFMasterControllerEnhanced()
    session_executor = SessionHawkesExecutor()
    cascade_integrator = FractalCascadeIntegrator()
    
    print(f"   ✅ HTF Master Controller initialized")
    print(f"   ✅ Session Subordinate Executor initialized")
    print(f"   ✅ Fractal Cascade Integrator initialized")
    
    # Phase 1: HTF Master Controller Analysis
    print(f"\n⚡ PHASE 1: HTF MASTER CONTROLLER")
    print("-" * 60)
    
    # Calculate time from key event to prediction points
    event_time = datetime.strptime(f"2025-07-31 {key_event['timestamp']}", "%Y-%m-%d %H:%M:%S")
    
    # Run predictions at key session points
    prediction_times = [
        ("10:00:00", "Mid-session"),
        ("10:30:00", "Late session"),
        ("10:43:00", "Session end")
    ]
    
    results = []
    
    for time_str, label in prediction_times:
        print(f"\n📍 PREDICTION AT {time_str} ({label}):")
        
        prediction_time = datetime.strptime(f"2025-07-31 {time_str}", "%Y-%m-%d %H:%M:%S")
        hours_since_event = (prediction_time - event_time).total_seconds() / 3600
        
        # HTF Intensity Calculation (with fixes)
        mu_h = 0.02
        alpha_h = 35.51
        beta_h = 0.00442
        
        # Fixed multiplier for asia_session_low
        multiplier = 2.2  # 🔥 CRITICAL FIX
        confidence = 0.95
        magnitude = confidence * multiplier
        
        # Calculate HTF intensity
        exponential_decay = math.exp(-beta_h * hours_since_event)
        excitation = alpha_h * exponential_decay * magnitude
        htf_intensity = mu_h + excitation
        
        print(f"      HTF Analysis:")
        print(f"         Time since HTF event: {hours_since_event:.2f} hours")
        print(f"         Asia session low multiplier: {multiplier}x 🔥")
        print(f"         Event magnitude: {magnitude:.3f}")
        print(f"         HTF intensity: {htf_intensity:.4f}")
        print(f"         Activation threshold: 0.5")
        print(f"         Status: {'🟢 ACTIVATED' if htf_intensity > 0.5 else '🔴 DORMANT'}")
        
        if htf_intensity <= 0.5:
            print(f"         ❌ HTF not activated - no subordinate prediction")
            results.append({
                "time": time_str,
                "label": label,
                "htf_intensity": htf_intensity,
                "activated": False
            })
            continue
        
        # Phase 2: Session Subordinate Response
        print(f"      Session Subordinate Response:")
        
        baseline_boost = htf_intensity / 0.5
        session_baseline = 0.15  # NY_AM baseline
        enhanced_baseline = session_baseline * baseline_boost
        
        # Session factors based on current market state
        current_price = None
        for movement in movements:
            if movement["timestamp"] <= time_str:
                current_price = movement["price"]
        
        if current_price is None:
            current_price = price_data["close"]
        
        # Calculate session intensity
        session_volatility = min(price_data["range"] / 100, 1.0)  # Normalize
        session_momentum = 0.85  # Strong bearish based on character
        
        session_excitation = 0.6 * session_volatility * session_momentum
        session_intensity = enhanced_baseline + session_excitation
        
        print(f"         Enhanced baseline: {enhanced_baseline:.4f} ({baseline_boost:.1f}x boost)")
        print(f"         Session volatility: {session_volatility:.3f}")
        print(f"         Session momentum: {session_momentum:.3f}")
        print(f"         Session intensity: {session_intensity:.4f}")
        
        # Phase 3: Adaptive Coupling
        print(f"      Adaptive Coupling:")
        
        gamma_base = 0.0278  # NY_AM base gamma
        
        # Adaptive factors
        volatility_factor = 1 + session_volatility * 0.3
        momentum_factor = 1 + session_momentum * 0.2
        range_factor = min(price_data["range"] / 100, 2.0)
        htf_boost_factor = baseline_boost
        
        adaptive_multiplier = volatility_factor * momentum_factor * range_factor * htf_boost_factor
        gamma_enhanced = gamma_base * adaptive_multiplier
        gamma_final = min(gamma_enhanced, 1.5)  # Cap for stability
        
        print(f"         γ_base (NY_AM): {gamma_base}")
        print(f"         Adaptive multiplier: {adaptive_multiplier:.2f}x")
        print(f"         γ_enhanced: {gamma_enhanced:.6f}")
        print(f"         γ_final (capped): {gamma_final:.6f}")
        
        # Phase 4: Coupled Intensity Calculation
        print(f"      Coupled Intensity:")
        
        coupling_component = gamma_final * htf_intensity
        lambda_total = session_intensity + coupling_component
        
        print(f"         λ_session: {session_intensity:.4f}")
        print(f"         λ_HTF: {htf_intensity:.4f}")
        print(f"         γ·λ_HTF: {coupling_component:.4f}")
        print(f"         λ_total: {lambda_total:.4f}")
        
        # Cascade evaluation
        cascade_threshold = 0.5
        cascade_triggered = lambda_total > cascade_threshold
        
        if cascade_triggered:
            excess_intensity = lambda_total - cascade_threshold
            time_factor = 1 / (excess_intensity + 0.1)
            estimated_minutes = max(1, int(time_factor * 5))
            
            cascade_time = prediction_time + timedelta(minutes=estimated_minutes)
            
            print(f"         🎯 CASCADE PREDICTION:")
            print(f"            Threshold: {cascade_threshold}")
            print(f"            Ratio: {lambda_total / cascade_threshold:.1f}x")
            print(f"            Status: 🟢 TRIGGERED")
            print(f"            Estimated cascade: {cascade_time.strftime('%H:%M:%S')} ET")
            print(f"            Time to cascade: {estimated_minutes} minutes")
        else:
            print(f"         🎯 CASCADE EVALUATION:")
            print(f"            Status: 🔴 BELOW THRESHOLD")
            cascade_time = None
            estimated_minutes = None
        
        # Store results
        results.append({
            "time": time_str,
            "label": label,
            "hours_since_htf": hours_since_event,
            "htf_intensity": htf_intensity,
            "session_intensity": session_intensity,
            "gamma_coupling": gamma_final,
            "lambda_total": lambda_total,
            "cascade_triggered": cascade_triggered,
            "cascade_time": cascade_time.strftime('%H:%M:%S') if cascade_time else None,
            "minutes_to_cascade": estimated_minutes,
            "activated": True
        })
    
    # Summary and Analysis
    print(f"\n🏆 FRACTAL HAWKES PREDICTION SUMMARY")
    print("=" * 70)
    
    for result in results:
        print(f"\n📍 {result['time']} ({result['label']}):")
        if result['activated']:
            print(f"   HTF Intensity: {result['htf_intensity']:.4f}")
            print(f"   Coupled λ_total: {result['lambda_total']:.4f}")
            if result['cascade_triggered']:
                print(f"   🟢 CASCADE: {result['cascade_time']} ({result['minutes_to_cascade']}min)")
            else:
                print(f"   🔴 NO CASCADE")
        else:
            print(f"   🔴 HTF NOT ACTIVATED ({result['htf_intensity']:.4f} < 0.5)")
    
    # Validation against actual session
    print(f"\n📊 VALIDATION AGAINST ACTUAL SESSION:")
    print(f"   Session Low: {price_data['low']} at 10:43:00")
    print(f"   Range: {price_data['range']} points")
    print(f"   Character: {price_data['session_character']}")
    
    # Check if our predictions aligned with actual cascades
    actual_cascades = []
    for movement in movements:
        if movement["action"] in ["break", "sweep"] and movement["timestamp"] >= "10:00:00":
            actual_cascades.append(movement)
    
    print(f"   Actual cascades after 10:00: {len(actual_cascades)}")
    for cascade in actual_cascades:
        print(f"      • {cascade['timestamp']}: {cascade['context']} at {cascade['price']}")
    
    # Final assessment
    activated_predictions = [r for r in results if r['activated']]
    triggered_predictions = [r for r in activated_predictions if r['cascade_triggered']]
    
    print(f"\n✅ PREDICTION EFFECTIVENESS:")
    print(f"   HTF Activations: {len(activated_predictions)}/3")
    print(f"   Cascade Predictions: {len(triggered_predictions)}/{len(activated_predictions)}")
    print(f"   System Status: {'🟢 OPERATIONAL' if activated_predictions else '🔴 INACTIVE'}")
    
    return {
        "session_data": session_data,
        "htf_event": key_event,
        "predictions": results,
        "validation": {
            "actual_cascades": actual_cascades,
            "session_low": price_data['low'],
            "session_range": price_data['range']
        }
    }

if __name__ == "__main__":
    prediction_results = run_nyam_july31_prediction()