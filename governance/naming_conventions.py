#!/usr/bin/env python3
"""
JSON Governance System - Naming Conventions Module
Enforces strict SESSION_type_YYYY_MM_DD.json naming conventions
"""

import re
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union
from enum import Enum
from dataclasses import dataclass

class SessionType(Enum):
    """Supported trading session types"""
    ASIA = "ASIA"
    MIDNIGHT = "MIDNIGHT"
    LONDON = "LONDON"
    PREMARKET = "PREMARKET"
    NYAM = "NYAM"
    LUNCH = "LUNCH"
    NYPM = "NYPM"
    
class FileType(Enum):
    """Supported file processing types"""
    LVL_1 = "Lvl-1"
    LVL_3 = "Lvl-3"
    GROK_ENHANCED = "grokEnhanced"
    EVENT_TIMING_ENHANCED = "eventTimingEnhanced"
    PREDICTION = "prediction"
    MONTE_CARLO = "monteCarlo"
    
class TrackerType(Enum):
    """Supported tracker file types"""
    HTF_TRACKER = "HTF_Tracker"
    FVG_TRACKER = "FVG_Tracker"
    LIQ_TRACKER = "LIQ_Tracker"

@dataclass
class FileNameComponents:
    """Structured representation of file name components"""
    session: SessionType
    file_type: Union[FileType, TrackerType]
    date: str  # YYYY_MM_DD format
    suffix: Optional[str] = None  # For additional qualifiers like _timing
    
class NamingConventionValidator:
    """
    Validates and enforces SESSION_type_YYYY_MM_DD.json naming conventions
    
    Examples:
    - ASIA_grokEnhanced_2025_07_25.json
    - ASIA_Lvl-1_2025_07_25.json  
    - HTF_Tracker_ASIA_2025_07_25.json
    - FVG_Tracker_ASIA_2025_07_25.json
    - LIQ_Tracker_ASIA_2025_07_25.json
    """
    
    # Standard session file pattern: SESSION_type_YYYY_MM_DD.json
    SESSION_PATTERN = re.compile(
        r'^(ASIA|MIDNIGHT|LONDON|PREMARKET|NYAM|LUNCH|NYPM)_'
        r'(Lvl-1|Lvl-3|grokEnhanced|eventTimingEnhanced|prediction|monteCarlo)_'
        r'(\d{4}_\d{2}_\d{2})'
        r'(_\w+)?\.json$'
    )
    
    # Tracker file pattern: TYPE_Tracker_SESSION_YYYY_MM_DD.json
    TRACKER_PATTERN = re.compile(
        r'^(HTF|FVG|LIQ)_Tracker_'
        r'(ASIA|MIDNIGHT|LONDON|PREMARKET|NYAM|LUNCH|NYPM)_'
        r'(\d{4}_\d{2}_\d{2})'
        r'(_\w+)?\.json$'
    )
    
    def __init__(self):
        self.validation_cache = {}
        
    def validate_filename(self, filename: str) -> Tuple[bool, Optional[str], Optional[FileNameComponents]]:
        """
        Validate filename against governance conventions
        
        Args:
            filename: The filename to validate
            
        Returns:
            (is_valid, error_message, components)
        """
        if filename in self.validation_cache:
            return self.validation_cache[filename]
            
        # Check if it's a session file
        session_match = self.SESSION_PATTERN.match(filename)
        if session_match:
            try:
                session = SessionType(session_match.group(1))
                file_type = FileType(session_match.group(2))
                date = session_match.group(3)
                suffix = session_match.group(4)[1:] if session_match.group(4) else None
                
                # Validate date format
                if not self._validate_date_format(date):
                    result = (False, f"Invalid date format: {date}. Expected YYYY_MM_DD", None)
                else:
                    components = FileNameComponents(session, file_type, date, suffix)
                    result = (True, None, components)
                    
            except ValueError as e:
                result = (False, f"Invalid session or file type: {e}", None)
        
        # Check if it's a tracker file
        elif self.TRACKER_PATTERN.match(filename):
            tracker_match = self.TRACKER_PATTERN.match(filename)
            try:
                tracker_type = TrackerType(f"{tracker_match.group(1)}_Tracker")
                session = SessionType(tracker_match.group(2))
                date = tracker_match.group(3)
                suffix = tracker_match.group(4)[1:] if tracker_match.group(4) else None
                
                # Validate date format
                if not self._validate_date_format(date):
                    result = (False, f"Invalid date format: {date}. Expected YYYY_MM_DD", None)
                else:
                    components = FileNameComponents(session, tracker_type, date, suffix)
                    result = (True, None, components)
                    
            except ValueError as e:
                result = (False, f"Invalid tracker or session type: {e}", None)
        
        else:
            result = (False, f"Filename does not match governance conventions: {filename}", None)
            
        # Cache result
        self.validation_cache[filename] = result
        return result
    
    def _validate_date_format(self, date_str: str) -> bool:
        """Validate YYYY_MM_DD date format"""
        try:
            datetime.strptime(date_str, "%Y_%m_%d")
            return True
        except ValueError:
            return False
    
    def generate_filename(self, session: SessionType, file_type: Union[FileType, TrackerType], 
                         date: str, suffix: Optional[str] = None) -> str:
        """
        Generate a filename following governance conventions
        
        Args:
            session: Session type
            file_type: File or tracker type
            date: Date in YYYY_MM_DD format
            suffix: Optional suffix
            
        Returns:
            Generated filename
        """
        if not self._validate_date_format(date):
            raise ValueError(f"Invalid date format: {date}. Expected YYYY_MM_DD")
            
        suffix_str = f"_{suffix}" if suffix else ""
        
        if isinstance(file_type, TrackerType):
            # Tracker format: TYPE_Tracker_SESSION_YYYY_MM_DD.json
            tracker_prefix = file_type.value.replace("_Tracker", "")
            return f"{tracker_prefix}_Tracker_{session.value}_{date}{suffix_str}.json"
        else:
            # Session format: SESSION_type_YYYY_MM_DD.json
            return f"{session.value}_{file_type.value}_{date}{suffix_str}.json"
    
    def get_suggested_name(self, current_filename: str) -> Optional[str]:
        """
        Suggest a corrected filename for non-conforming files
        
        Args:
            current_filename: Current filename
            
        Returns:
            Suggested filename or None if cannot determine
        """
        # Remove extension
        base_name = current_filename.replace('.json', '')
        
        # Try to extract session type
        session = self._extract_session_type(base_name)
        if not session:
            return None
            
        # Try to extract file type
        file_type = self._extract_file_type(base_name)
        if not file_type:
            return None
            
        # Try to extract date
        date = self._extract_date(base_name)
        if not date:
            # Use current date if none found
            date = datetime.now().strftime("%Y_%m_%d")
            
        # Extract suffix
        suffix = self._extract_suffix(base_name)
        
        try:
            return self.generate_filename(session, file_type, date, suffix)
        except Exception:
            return None
    
    def _extract_session_type(self, filename: str) -> Optional[SessionType]:
        """Extract session type from filename"""
        for session in SessionType:
            if session.value.lower() in filename.lower():
                return session
        return None
    
    def _extract_file_type(self, filename: str) -> Optional[Union[FileType, TrackerType]]:
        """Extract file type from filename"""
        # Check tracker types first
        if 'htf' in filename.lower() and 'tracker' in filename.lower():
            return TrackerType.HTF_TRACKER
        elif 'fvg' in filename.lower() and 'tracker' in filename.lower():
            return TrackerType.FVG_TRACKER
        elif ('liq' in filename.lower() or 'liquidity' in filename.lower()) and 'tracker' in filename.lower():
            return TrackerType.LIQ_TRACKER
            
        # Check file types
        for file_type in FileType:
            if file_type.value.lower() in filename.lower():
                return file_type
                
        # Default mappings for common patterns
        if 'l1' in filename.lower() or 'level_1' in filename.lower():
            return FileType.LVL_1
        elif 'l3' in filename.lower() or 'level_3' in filename.lower():
            return FileType.LVL_3
        elif 'enhanced' in filename.lower():
            return FileType.GROK_ENHANCED
        elif 'prediction' in filename.lower():
            return FileType.PREDICTION
        elif 'monte' in filename.lower():
            return FileType.MONTE_CARLO
            
        return None
    
    def _extract_date(self, filename: str) -> Optional[str]:
        """Extract date from filename"""
        # Look for YYYY_MM_DD pattern
        date_patterns = [
            r'(\d{4}_\d{2}_\d{2})',  # 2025_07_25
            r'(\d{4}-\d{2}-\d{2})',  # 2025-07-25
            r'(\d{4}\d{2}\d{2})',    # 20250725
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, filename)
            if match:
                date_str = match.group(1)
                # Convert to standard format
                date_str = date_str.replace('-', '_')
                if len(date_str) == 8:  # YYYYMMDD
                    date_str = f"{date_str[:4]}_{date_str[4:6]}_{date_str[6:8]}"
                return date_str
                
        return None
    
    def _extract_suffix(self, filename: str) -> Optional[str]:
        """Extract suffix from filename"""
        common_suffixes = ['timing', 'enhanced', 'fixed', 'real', 'new', 'proper', 'improved']
        for suffix in common_suffixes:
            if suffix in filename.lower():
                return suffix
        return None
    
    def get_file_directory(self, components: FileNameComponents) -> str:
        """
        Determine the appropriate directory for a file based on its components
        
        Args:
            components: File name components
            
        Returns:
            Relative directory path
        """
        if isinstance(components.file_type, TrackerType):
            if components.file_type == TrackerType.HTF_TRACKER:
                return "data/trackers/htf"
            elif components.file_type == TrackerType.FVG_TRACKER:
                return "data/trackers/fvg"
            elif components.file_type == TrackerType.LIQ_TRACKER:
                return "data/trackers/liquidity"
        else:
            if components.file_type in [FileType.LVL_1]:
                return "data/preprocessing/level_1"
            elif components.file_type in [FileType.LVL_3]:
                return "data/preprocessing/level_3"
            elif components.file_type == FileType.GROK_ENHANCED:
                return "data/enhanced/grok_enhanced"
            elif components.file_type == FileType.EVENT_TIMING_ENHANCED:
                return "data/enhanced/event_timing"
            elif components.file_type in [FileType.PREDICTION, FileType.MONTE_CARLO]:
                return "data/enhanced/predictions"
                
        return "data/archive"  # Default for unclassified files

def validate_file_governance(filepath: str) -> Tuple[bool, str, Optional[FileNameComponents]]:
    """
    Convenience function to validate a file path against governance rules
    
    Args:
        filepath: Full or relative file path
        
    Returns:
        (is_valid, message, components)
    """
    validator = NamingConventionValidator()
    filename = os.path.basename(filepath)
    
    is_valid, error_msg, components = validator.validate_filename(filename)
    
    if is_valid:
        return True, f"✅ {filename} conforms to governance conventions", components
    else:
        suggested = validator.get_suggested_name(filename)
        suggestion_msg = f" → Suggested: {suggested}" if suggested else ""
        return False, f"❌ {filename}: {error_msg}{suggestion_msg}", None

if __name__ == "__main__":
    # Test examples from user's requirements
    test_files = [
        "ASIA_grokEnhanced_2025_07_25.json",
        "ASIA_Lvl-1_2025_07_25.json",
        "HTF_Tracker_ASIA_2025_07_25.json",
        "FVG_Tracker_ASIA_2025_07_25.json", 
        "LIQ_Tracker_ASIA_2025_07_25.json",
        "asia_l1_2025_07_25.json",  # Non-conforming example
    ]
    
    print("🏛️ JSON Governance System - Naming Convention Validation")
    print("=" * 60)
    
    for filename in test_files:
        is_valid, message, components = validate_file_governance(filename)
        print(message)
        if components:
            validator = NamingConventionValidator()
            directory = validator.get_file_directory(components)
            print(f"   📁 Target directory: {directory}")
        print()