#!/usr/bin/env python3
"""
JSON Governance System - Migration Engine
Migrates existing 183+ JSON files to new naming convention and directory structure
"""

import os
import shutil
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass

from .naming_conventions import NamingConventionValidator, SessionType, FileType, TrackerType
from .file_organizer import FileOrganizer
from .template_validator import TemplateValidator
from src.utils import get_logger, safe_json_load, safe_json_save

logger = get_logger(__name__)

@dataclass
class MigrationResult:
    """Result of file migration"""
    original_file: str
    new_file: Optional[str]
    migration_type: str
    success: bool
    errors: List[str]
    warnings: List[str]
    actions_taken: List[str]

@dataclass
class MigrationSummary:
    """Summary of migration operation"""
    total_files: int
    successful_migrations: int
    failed_migrations: int
    renamed_files: int
    moved_files: int
    archived_files: int
    created_trackers: int
    migration_time_seconds: float
    details: List[MigrationResult]

class MigrationEngine:
    """
    Migrates existing JSON files to governance conventions
    Handles naming standardization, directory organization, and tracker generation
    """
    
    def __init__(self, base_path: str = ".", dry_run: bool = False):
        self.base_path = Path(base_path)
        self.dry_run = dry_run
        self.naming_validator = NamingConventionValidator()
        self.file_organizer = FileOrganizer(base_path, dry_run)
        self.template_validator = TemplateValidator()
        
        # Migration statistics
        self.stats = {
            "files_analyzed": 0,
            "naming_corrections": 0,
            "directory_moves": 0,
            "tracker_generations": 0,
            "validation_fixes": 0
        }
        
    def migrate_all_files(self, source_directory: str = ".") -> MigrationSummary:
        """
        Migrate all JSON files in source directory
        
        Args:
            source_directory: Directory to scan for JSON files
            
        Returns:
            MigrationSummary with complete migration results
        """
        start_time = datetime.now()
        logger.info(f"🚀 Starting migration of all JSON files in {source_directory}")
        
        # Discover all JSON files
        json_files = self._discover_json_files(source_directory)
        logger.info(f"📊 Discovered {len(json_files)} JSON files to migrate")
        
        migration_results = []
        
        # Ensure directory structure exists
        self.file_organizer.ensure_directory_structure()
        
        # Migrate each file
        for json_file in json_files:
            result = self.migrate_single_file(str(json_file))
            migration_results.append(result)
            
        # Calculate summary
        end_time = datetime.now()
        migration_time = (end_time - start_time).total_seconds()
        
        summary = MigrationSummary(
            total_files=len(json_files),
            successful_migrations=sum(1 for r in migration_results if r.success),
            failed_migrations=sum(1 for r in migration_results if not r.success),
            renamed_files=sum(1 for r in migration_results if "renamed" in r.migration_type),
            moved_files=sum(1 for r in migration_results if "moved" in r.migration_type),
            archived_files=sum(1 for r in migration_results if "archived" in r.migration_type),
            created_trackers=sum(len([a for a in r.actions_taken if "tracker" in a.lower()]) for r in migration_results),
            migration_time_seconds=migration_time,
            details=migration_results
        )
        
        # Generate migration report
        self._generate_migration_report(summary)
        
        logger.info(f"✅ Migration complete: {summary.successful_migrations}/{summary.total_files} successful ({summary.migration_time_seconds:.1f}s)")
        
        return summary
    
    def migrate_single_file(self, file_path: str) -> MigrationResult:
        """
        Migrate a single JSON file
        
        Args:
            file_path: Path to JSON file to migrate
            
        Returns:
            MigrationResult with migration details
        """
        file_path = Path(file_path)
        logger.info(f"📄 Migrating: {file_path.name}")
        
        errors = []
        warnings = []
        actions_taken = []
        
        try:
            # Load file data
            file_data = safe_json_load(str(file_path))
            if not file_data:
                return self._create_error_result(str(file_path), "Failed to load file data")
                
            # Analyze file to determine type and target
            analysis = self._analyze_file(file_path, file_data)
            if not analysis["success"]:
                return self._create_error_result(str(file_path), analysis["error"])
                
            # Determine migration strategy
            strategy = self._determine_migration_strategy(file_path, file_data, analysis)
            actions_taken.append(f"Migration strategy: {strategy['type']}")
            
            # Execute migration based on strategy
            if strategy["type"] == "rename_and_move":
                new_file_path = self._execute_rename_and_move(file_path, file_data, strategy)
                migration_type = "renamed_and_moved"
            elif strategy["type"] == "move_only":
                new_file_path = self._execute_move_only(file_path, strategy)
                migration_type = "moved"
            elif strategy["type"] == "rename_only":
                new_file_path = self._execute_rename_only(file_path, strategy)
                migration_type = "renamed"
            elif strategy["type"] == "archive":
                new_file_path = self._execute_archive(file_path, strategy)
                migration_type = "archived"
            else:
                new_file_path = str(file_path)  # No changes needed
                migration_type = "no_change_needed"
                
            # Generate tracker files if applicable
            if strategy.get("generate_trackers", False):
                tracker_files = self._generate_tracker_files_for_migration(file_data, analysis)
                if tracker_files:
                    actions_taken.append(f"Generated {len(tracker_files)} tracker files")
                    
            # Validate migrated file
            if new_file_path != str(file_path):
                validation_result = self._validate_migrated_file(new_file_path, file_data)
                if not validation_result["valid"]:
                    warnings.extend(validation_result["issues"])
                else:
                    actions_taken.append("Migration validation passed")
                    
            return MigrationResult(
                original_file=str(file_path),
                new_file=new_file_path,
                migration_type=migration_type,
                success=True,
                errors=errors,
                warnings=warnings,
                actions_taken=actions_taken
            )
            
        except Exception as e:
            logger.error(f"❌ Migration failed for {file_path.name}: {e}")
            return self._create_error_result(str(file_path), str(e))
    
    def _discover_json_files(self, source_directory: str) -> List[Path]:
        """Discover all JSON files in source directory"""
        source_path = Path(source_directory)
        json_files = []
        
        # Recursively find JSON files
        for json_file in source_path.rglob("*.json"):
            # Skip files already in governance structure
            if self._is_already_governed(json_file):
                continue
                
            # Skip system/temp files
            if self._is_system_file(json_file):
                continue
                
            json_files.append(json_file)
            
        return sorted(json_files)
    
    def _is_already_governed(self, file_path: Path) -> bool:
        """Check if file is already in governance structure"""
        # Check if file is in governance directories
        governance_dirs = [
            "data/preprocessing", "data/enhanced", "data/trackers", "data/archive"
        ]
        
        for gov_dir in governance_dirs:
            if str(file_path).startswith(str(self.base_path / gov_dir)):
                return True
                
        # Check if filename follows governance convention
        is_valid, _, _ = self.naming_validator.validate_filename(file_path.name)
        return is_valid
    
    def _is_system_file(self, file_path: Path) -> bool:
        """Check if file is a system/temp file to skip"""
        skip_patterns = [
            "node_modules", ".git", "__pycache__", "temp", "tmp",
            "package.json", "package-lock.json", "requirements.txt"
        ]
        
        file_str = str(file_path)
        return any(pattern in file_str for pattern in skip_patterns)
    
    def _analyze_file(self, file_path: Path, file_data: Dict) -> Dict[str, Any]:
        """Analyze file to determine type and characteristics"""
        analysis = {
            "success": True,
            "error": None,
            "session_type": None,
            "file_type": None,
            "date": None,
            "is_tracker": False,
            "is_session": False,
            "is_prediction": False,
            "needs_renaming": False,
            "needs_moving": False,
            "data_quality": "unknown"
        }
        
        try:
            # Detect session type
            session_type = self._detect_session_type_from_data(file_data, file_path.name)
            analysis["session_type"] = session_type
            
            # Detect file type
            file_type = self._detect_file_type_from_data(file_data, file_path.name)
            analysis["file_type"] = file_type
            
            # Extract date
            date = self._extract_date_from_data(file_data, file_path.name)
            analysis["date"] = date
            
            # Classify file
            analysis["is_tracker"] = self._is_tracker_file(file_data, file_path.name)
            analysis["is_session"] = self._is_session_file(file_data, file_path.name)
            analysis["is_prediction"] = self._is_prediction_file(file_data, file_path.name)
            
            # Check if renaming needed
            if session_type and file_type and date:
                expected_name = self.naming_validator.generate_filename(session_type, file_type, date)
                analysis["needs_renaming"] = file_path.name != expected_name
                analysis["expected_name"] = expected_name
            else:
                analysis["needs_renaming"] = True  # Cannot determine proper name
                
            # Check if moving needed
            if file_type:
                expected_dir = self._get_expected_directory(file_type)
                current_dir = str(file_path.parent)
                analysis["needs_moving"] = not current_dir.endswith(expected_dir)
                analysis["expected_directory"] = expected_dir
                
            # Assess data quality
            analysis["data_quality"] = self._assess_data_quality(file_data)
            
        except Exception as e:
            analysis["success"] = False
            analysis["error"] = str(e)
            
        return analysis
    
    def _detect_session_type_from_data(self, file_data: Dict, filename: str) -> Optional[SessionType]:
        """Detect session type from file data and filename"""
        # Try session metadata first
        session_metadata = file_data.get("session_metadata", {})
        session_type_str = session_metadata.get("session_type", "").upper()
        
        if session_type_str:
            try:
                return SessionType(session_type_str)
            except ValueError:
                pass
                
        # Try filename detection
        filename_lower = filename.lower()
        for session_type in SessionType:
            if session_type.value.lower() in filename_lower:
                return session_type
                
        # Try common aliases
        aliases = {
            "ny_pm": SessionType.NYPM,
            "nypm": SessionType.NYPM,
            "ny_am": SessionType.NYAM,
            "nyam": SessionType.NYAM,
            "premarket": SessionType.PREMARKET,
            "pre_market": SessionType.PREMARKET
        }
        
        for alias, session_type in aliases.items():
            if alias in filename_lower:
                return session_type
                
        return None
    
    def _detect_file_type_from_data(self, file_data: Dict, filename: str) -> Optional[Any]:
        """Detect file type from file data and filename"""
        filename_lower = filename.lower()
        
        # Check for tracker types
        if "htf" in filename_lower and "tracker" in filename_lower:
            return TrackerType.HTF_TRACKER
        elif "fvg" in filename_lower and "tracker" in filename_lower:
            return TrackerType.FVG_TRACKER
        elif ("liq" in filename_lower or "liquidity" in filename_lower) and "tracker" in filename_lower:
            return TrackerType.LIQ_TRACKER
            
        # Check for session types based on data structure
        if "grok_enhanced_data" in file_data or "enhanced" in filename_lower:
            return FileType.GROK_ENHANCED
        elif "event_timing" in filename_lower or "timing" in filename_lower:
            return FileType.EVENT_TIMING_ENHANCED
        elif "prediction" in filename_lower or "monte_carlo" in filename_lower:
            return FileType.PREDICTION
        elif "l1" in filename_lower or "level_1" in filename_lower:
            return FileType.LVL_1
        elif "l3" in filename_lower or "level_3" in filename_lower:
            return FileType.LVL_3
        else:
            # Default to Level 1 for session files
            if self._is_session_file(file_data, filename):
                return FileType.LVL_1
                
        return None
    
    def _extract_date_from_data(self, file_data: Dict, filename: str) -> Optional[str]:
        """Extract date from file data or filename"""
        # Try session metadata
        session_metadata = file_data.get("session_metadata", {})
        date_str = session_metadata.get("date", "")
        
        if date_str:
            # Convert to YYYY_MM_DD format
            date_str = date_str.replace("-", "_")
            if len(date_str) == 10:
                return date_str
                
        # Try filename extraction
        return self.naming_validator._extract_date(filename)
    
    def _is_tracker_file(self, file_data: Dict, filename: str) -> bool:
        """Check if file is a tracker file"""
        filename_lower = filename.lower()
        
        # Check filename patterns
        if "tracker" in filename_lower:
            return True
            
        # Check data structure
        tracker_metadata = file_data.get("tracker_metadata", {})
        if tracker_metadata and "tracker_type" in tracker_metadata:
            return True
            
        # Check for tracker-specific fields
        tracker_fields = ["t_memory", "energy_rate", "active_structures", "untaken_liquidity_registry"]
        return any(field in file_data for field in tracker_fields)
    
    def _is_session_file(self, file_data: Dict, filename: str) -> bool:
        """Check if file is a session file"""
        # Check for session-specific fields
        session_fields = ["session_metadata", "price_data", "price_movements"]
        return any(field in file_data for field in session_fields)
    
    def _is_prediction_file(self, file_data: Dict, filename: str) -> bool:
        """Check if file is a prediction file"""
        filename_lower = filename.lower()
        
        # Check filename patterns
        prediction_patterns = ["prediction", "monte_carlo", "forecast"]
        if any(pattern in filename_lower for pattern in prediction_patterns):
            return True
            
        # Check data structure
        prediction_fields = ["prediction_results", "monte_carlo_predictions", "simulation_results"]
        return any(field in file_data for field in prediction_fields)
    
    def _get_expected_directory(self, file_type: Any) -> str:
        """Get expected directory for file type"""
        if isinstance(file_type, TrackerType):
            if file_type == TrackerType.HTF_TRACKER:
                return "data/trackers/htf"
            elif file_type == TrackerType.FVG_TRACKER:
                return "data/trackers/fvg"
            elif file_type == TrackerType.LIQ_TRACKER:
                return "data/trackers/liquidity"
        else:
            if file_type == FileType.LVL_1:
                return "data/preprocessing/level_1"
            elif file_type == FileType.LVL_3:
                return "data/preprocessing/level_3"
            elif file_type == FileType.GROK_ENHANCED:
                return "data/enhanced/grok_enhanced"
            elif file_type == FileType.EVENT_TIMING_ENHANCED:
                return "data/enhanced/event_timing"
            elif file_type in [FileType.PREDICTION, FileType.MONTE_CARLO]:
                return "data/enhanced/predictions"
                
        return "data/archive"
    
    def _assess_data_quality(self, file_data: Dict) -> str:
        """Assess data quality of file"""
        required_fields = ["session_metadata", "price_data"]
        present_fields = sum(1 for field in required_fields if field in file_data)
        quality_ratio = present_fields / len(required_fields)
        
        if quality_ratio >= 0.8:
            return "good"
        elif quality_ratio >= 0.5:
            return "fair"
        else:
            return "poor"
    
    def _determine_migration_strategy(self, file_path: Path, file_data: Dict, analysis: Dict) -> Dict[str, Any]:
        """Determine migration strategy for file"""
        strategy = {
            "type": "no_change_needed",
            "target_directory": None,
            "target_filename": None,
            "generate_trackers": False,
            "archive_reason": None
        }
        
        # If analysis failed, archive the file
        if not analysis["success"]:
            strategy["type"] = "archive"
            strategy["archive_reason"] = analysis["error"]
            strategy["target_directory"] = "data/archive/non_conforming"
            return strategy
            
        # If file cannot be properly classified, archive it
        if not analysis["session_type"] or not analysis["file_type"] or not analysis["date"]:
            strategy["type"] = "archive"
            strategy["archive_reason"] = "Cannot determine session type, file type, or date"
            strategy["target_directory"] = "data/archive/non_conforming"
            return strategy
            
        # Determine target location
        target_directory = self._get_expected_directory(analysis["file_type"])
        target_filename = analysis.get("expected_name", file_path.name)
        
        # Determine strategy type
        needs_rename = analysis["needs_renaming"]
        needs_move = analysis["needs_moving"]
        
        if needs_rename and needs_move:
            strategy["type"] = "rename_and_move"
        elif needs_move:
            strategy["type"] = "move_only"
        elif needs_rename:
            strategy["type"] = "rename_only"
        else:
            strategy["type"] = "no_change_needed"
            
        strategy["target_directory"] = target_directory
        strategy["target_filename"] = target_filename
        
        # Check if tracker generation is needed
        if analysis["is_session"] and not analysis["is_tracker"]:
            strategy["generate_trackers"] = True
            
        return strategy
    
    def _execute_rename_and_move(self, file_path: Path, file_data: Dict, strategy: Dict) -> str:
        """Execute rename and move strategy"""
        target_dir = self.base_path / strategy["target_directory"]
        target_path = target_dir / strategy["target_filename"]
        
        if not self.dry_run:
            # Ensure target directory exists
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Handle conflicts
            if target_path.exists():
                target_path = self._resolve_filename_conflict(target_path)
                
            # Move and rename file
            shutil.move(str(file_path), str(target_path))
            logger.info(f"✅ Renamed and moved: {file_path.name} → {target_path}")
        else:
            logger.info(f"🔍 Would rename and move: {file_path.name} → {target_path}")
            
        return str(target_path)
    
    def _execute_move_only(self, file_path: Path, strategy: Dict) -> str:
        """Execute move only strategy"""
        target_dir = self.base_path / strategy["target_directory"]
        target_path = target_dir / file_path.name
        
        if not self.dry_run:
            target_dir.mkdir(parents=True, exist_ok=True)
            
            if target_path.exists():
                target_path = self._resolve_filename_conflict(target_path)
                
            shutil.move(str(file_path), str(target_path))
            logger.info(f"✅ Moved: {file_path.name} → {target_path}")
        else:
            logger.info(f"🔍 Would move: {file_path.name} → {target_path}")
            
        return str(target_path)
    
    def _execute_rename_only(self, file_path: Path, strategy: Dict) -> str:
        """Execute rename only strategy"""
        target_path = file_path.parent / strategy["target_filename"]
        
        if not self.dry_run:
            if target_path.exists():
                target_path = self._resolve_filename_conflict(target_path)
                
            file_path.rename(target_path)
            logger.info(f"✅ Renamed: {file_path.name} → {target_path.name}")
        else:
            logger.info(f"🔍 Would rename: {file_path.name} → {target_path.name}")
            
        return str(target_path)
    
    def _execute_archive(self, file_path: Path, strategy: Dict) -> str:
        """Execute archive strategy"""
        target_dir = self.base_path / strategy["target_directory"]
        target_path = target_dir / file_path.name
        
        if not self.dry_run:
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Create metadata file explaining why archived
            metadata_path = target_dir / f"{file_path.stem}_archive_reason.json"
            metadata = {
                "original_file": str(file_path),
                "archive_reason": strategy["archive_reason"],
                "archived_timestamp": datetime.now().isoformat()
            }
            safe_json_save(metadata, str(metadata_path))
            
            if target_path.exists():
                target_path = self._resolve_filename_conflict(target_path)
                
            shutil.move(str(file_path), str(target_path))
            logger.info(f"📦 Archived: {file_path.name} → {target_path} (Reason: {strategy['archive_reason']})")
        else:
            logger.info(f"🔍 Would archive: {file_path.name} (Reason: {strategy['archive_reason']})")
            
        return str(target_path)
    
    def _resolve_filename_conflict(self, target_path: Path) -> Path:
        """Resolve filename conflicts by adding counter"""
        counter = 1
        base_stem = target_path.stem
        suffix = target_path.suffix
        
        while target_path.exists():
            new_name = f"{base_stem}_conflict_{counter}{suffix}"
            target_path = target_path.parent / new_name
            counter += 1
            
        return target_path
    
    def _generate_tracker_files_for_migration(self, file_data: Dict, analysis: Dict) -> List[str]:
        """Generate tracker files during migration"""
        tracker_files = []
        
        if not analysis["session_type"] or not analysis["date"]:
            return tracker_files
            
        try:
            session_type = analysis["session_type"]
            date = analysis["date"]
            
            # Generate HTF Tracker
            htf_data = self._create_htf_tracker_from_session(file_data, session_type, date)
            htf_filename = self.naming_validator.generate_filename(session_type, TrackerType.HTF_TRACKER, date)
            htf_path = self.base_path / "data/trackers/htf" / htf_filename
            
            if not self.dry_run:
                htf_path.parent.mkdir(parents=True, exist_ok=True)
                if safe_json_save(htf_data, str(htf_path)):
                    tracker_files.append(str(htf_path))
            else:
                tracker_files.append(f"Would create: {htf_path}")
                
            # Generate FVG Tracker
            fvg_data = self._create_fvg_tracker_from_session(file_data, session_type, date)
            fvg_filename = self.naming_validator.generate_filename(session_type, TrackerType.FVG_TRACKER, date)
            fvg_path = self.base_path / "data/trackers/fvg" / fvg_filename
            
            if not self.dry_run:
                fvg_path.parent.mkdir(parents=True, exist_ok=True)
                if safe_json_save(fvg_data, str(fvg_path)):
                    tracker_files.append(str(fvg_path))
            else:
                tracker_files.append(f"Would create: {fvg_path}")
                
            # Generate Liquidity Tracker
            liq_data = self._create_liquidity_tracker_from_session(file_data, session_type, date)
            liq_filename = self.naming_validator.generate_filename(session_type, TrackerType.LIQ_TRACKER, date)
            liq_path = self.base_path / "data/trackers/liquidity" / liq_filename
            
            if not self.dry_run:
                liq_path.parent.mkdir(parents=True, exist_ok=True)
                if safe_json_save(liq_data, str(liq_path)):
                    tracker_files.append(str(liq_path))
            else:
                tracker_files.append(f"Would create: {liq_path}")
                
        except Exception as e:
            logger.error(f"❌ Failed to generate tracker files: {e}")
            
        return tracker_files
    
    def _create_htf_tracker_from_session(self, file_data: Dict, session_type: SessionType, date: str) -> Dict:
        """Create HTF tracker from session data"""
        structures = file_data.get("structures_identified", {})
        session_levels = structures.get("session_levels", [])
        
        return {
            "tracker_metadata": {
                "tracker_id": f"htf_{session_type.value.lower()}_{date}",
                "session_reference": f"{session_type.value}_{date}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "HTF_Context"
            },
            "active_structures": [
                {
                    "level": level.get("level", 0.0),
                    "structure_type": level.get("type", "session_level"),
                    "strength": 0.8,
                    "age_minutes": 0,
                    "interaction_count": len(level.get("touches", []))
                }
                for level in session_levels
            ],
            "htf_distance_influence": 0.5,
            "structure_registry": {
                "total_structures": len(session_levels),
                "last_update": datetime.now().isoformat()
            }
        }
    
    def _create_fvg_tracker_from_session(self, file_data: Dict, session_type: SessionType, date: str) -> Dict:
        """Create FVG tracker from session data"""
        session_character = file_data.get("session_metadata", {}).get("session_character", "neutral")
        
        energy_rate = 1.0
        if "expansion" in session_character.lower():
            energy_rate = 1.3
        elif "consolidation" in session_character.lower():
            energy_rate = 0.8
            
        return {
            "tracker_metadata": {
                "tracker_id": f"fvg_{session_type.value.lower()}_{date}",
                "session_reference": f"{session_type.value}_{date}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "FVG_State"
            },
            "t_memory": 22.5,
            "energy_rate": energy_rate,
            "carryover_fvgs": file_data.get("fvg_analysis", {}).get("fair_value_gaps", []),
            "session_character": session_character
        }
    
    def _create_liquidity_tracker_from_session(self, file_data: Dict, session_type: SessionType, date: str) -> Dict:
        """Create liquidity tracker from session data"""
        liquidity_analysis = file_data.get("liquidity_analysis", {})
        
        return {
            "tracker_metadata": {
                "tracker_id": f"liq_{session_type.value.lower()}_{date}",
                "session_reference": f"{session_type.value}_{date}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "Liquidity_State"
            },
            "untaken_liquidity_registry": liquidity_analysis.get("untaken_liquidity", []),
            "liquidity_gradient": 0.3,
            "session_bias": "neutral"
        }
    
    def _validate_migrated_file(self, file_path: str, file_data: Dict) -> Dict[str, Any]:
        """Validate migrated file"""
        validation = {
            "valid": True,
            "issues": []
        }
        
        try:
            # Check naming convention
            filename = Path(file_path).name
            is_valid, error_msg, _ = self.naming_validator.validate_filename(filename)
            
            if not is_valid:
                validation["valid"] = False
                validation["issues"].append(f"Naming convention violation: {error_msg}")
                
            # Check file exists and is readable
            if not Path(file_path).exists():
                validation["valid"] = False
                validation["issues"].append("Migrated file does not exist")
            else:
                # Try to read the file
                test_data = safe_json_load(file_path)
                if not test_data:
                    validation["valid"] = False
                    validation["issues"].append("Migrated file is not readable JSON")
                    
        except Exception as e:
            validation["valid"] = False
            validation["issues"].append(f"Validation error: {e}")
            
        return validation
    
    def _generate_migration_report(self, summary: MigrationSummary):
        """Generate comprehensive migration report"""
        report_lines = [
            "🚀 JSON Governance System - Migration Report",
            "=" * 60,
            "",
            f"📊 Migration Summary:",
            f"   Total files processed: {summary.total_files}",
            f"   Successful migrations: {summary.successful_migrations}",
            f"   Failed migrations: {summary.failed_migrations}",
            f"   Files renamed: {summary.renamed_files}",
            f"   Files moved: {summary.moved_files}",
            f"   Files archived: {summary.archived_files}",
            f"   Tracker files created: {summary.created_trackers}",
            f"   Migration time: {summary.migration_time_seconds:.1f} seconds",
            ""
        ]
        
        # Success rate
        if summary.total_files > 0:
            success_rate = summary.successful_migrations / summary.total_files * 100
            report_lines.append(f"✅ Success rate: {success_rate:.1f}%")
            report_lines.append("")
        
        # Migration type breakdown
        type_counts = {}
        for result in summary.details:
            migration_type = result.migration_type
            type_counts[migration_type] = type_counts.get(migration_type, 0) + 1
            
        report_lines.append("📋 Migration Types:")
        for migration_type, count in type_counts.items():
            report_lines.append(f"   {migration_type}: {count}")
        report_lines.append("")
        
        # Detailed results (first 20)
        report_lines.append("📄 Detailed Results (first 20):")
        for i, result in enumerate(summary.details[:20]):
            filename = Path(result.original_file).name
            status = "✅" if result.success else "❌"
            
            report_lines.append(f"{status} {filename}: {result.migration_type}")
            
            if result.new_file and result.new_file != result.original_file:
                new_filename = Path(result.new_file).name
                report_lines.append(f"   → {new_filename}")
                
            if result.errors:
                for error in result.errors[:2]:  # Show first 2 errors
                    report_lines.append(f"   ❌ {error}")
                    
        if len(summary.details) > 20:
            report_lines.append(f"   ... and {len(summary.details) - 20} more files")
            
        # Save report
        report_content = "\n".join(report_lines)
        report_file = self.base_path / "migration_report.txt"
        
        try:
            with open(report_file, 'w') as f:
                f.write(report_content)
            logger.info(f"📄 Migration report saved: {report_file}")
        except Exception as e:
            logger.error(f"❌ Failed to save migration report: {e}")
            
        # Also log summary to console
        logger.info(f"\n{report_content}")
    
    def _create_error_result(self, original_file: str, error_message: str) -> MigrationResult:
        """Create error migration result"""
        return MigrationResult(
            original_file=original_file,
            new_file=None,
            migration_type="failed",
            success=False,
            errors=[error_message],
            warnings=[],
            actions_taken=[]
        )

def migrate_project_files(base_path: str = ".", dry_run: bool = False) -> MigrationSummary:
    """
    Convenience function to migrate all project files
    
    Args:
        base_path: Base project path
        dry_run: If True, don't actually move/rename files
        
    Returns:
        MigrationSummary
    """
    engine = MigrationEngine(base_path, dry_run)
    return engine.migrate_all_files(base_path)

if __name__ == "__main__":
    # Test migration engine
    print("🚀 JSON Governance System - Migration Engine")
    print("=" * 60)
    
    # Run in dry-run mode first
    print("Running in dry-run mode to preview migration...")
    summary = migrate_project_files(dry_run=True)
    
    print(f"\nMigration preview complete:")
    print(f"  📊 {summary.total_files} files analyzed")
    print(f"  ✅ {summary.successful_migrations} would be migrated successfully")
    print(f"  ❌ {summary.failed_migrations} would fail migration")
    print(f"  📝 {summary.renamed_files} would be renamed")
    print(f"  📁 {summary.moved_files} would be moved")
    print(f"  📦 {summary.archived_files} would be archived")
    print(f"  🎯 {summary.created_trackers} tracker files would be created")