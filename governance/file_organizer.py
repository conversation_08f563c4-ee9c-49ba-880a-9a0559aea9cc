#!/usr/bin/env python3
"""
JSON Governance System - File Organizer Module
Automatic filing system for JSON files based on governance conventions
"""

import os
import shutil
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import json
from datetime import datetime

from .naming_conventions import NamingConventionValidator, SessionType, FileType, TrackerType, FileNameComponents
from .template_validator import TemplateValidator, TemplateType
from src.utils import get_logger, safe_json_load

logger = get_logger(__name__)

class FileOrganizer:
    """
    Organizes JSON files according to governance directory structure
    Handles automatic filing, directory creation, and file movement
    """
    
    def __init__(self, base_path: str = ".", dry_run: bool = False):
        self.base_path = Path(base_path)
        self.dry_run = dry_run
        self.validator = NamingConventionValidator()
        self.template_validator = TemplateValidator()
        
        # Initialize directory structure
        self.directories = {
            # Preprocessing directories
            "level_1": self.base_path / "data/preprocessing/level_1",
            "level_3": self.base_path / "data/preprocessing/level_3", 
            "extraction": self.base_path / "data/preprocessing/extraction",
            
            # Enhanced directories
            "grok_enhanced": self.base_path / "data/enhanced/grok_enhanced",
            "event_timing": self.base_path / "data/enhanced/event_timing",
            "predictions": self.base_path / "data/enhanced/predictions",
            
            # Tracker directories
            "htf_trackers": self.base_path / "data/trackers/htf",
            "fvg_trackers": self.base_path / "data/trackers/fvg",
            "liquidity_trackers": self.base_path / "data/trackers/liquidity",
            
            # Archive directory
            "archive": self.base_path / "data/archive",
            "non_conforming": self.base_path / "data/archive/non_conforming"
        }
        
    def ensure_directory_structure(self):
        """Create the governance directory structure"""
        for name, path in self.directories.items():
            if not self.dry_run:
                path.mkdir(parents=True, exist_ok=True)
                logger.info(f"✅ Created directory: {path}")
            else:
                logger.info(f"🔍 Would create directory: {path}")
                
    def organize_file(self, filepath: str) -> Tuple[bool, str, Optional[str]]:
        """
        Organize a single file according to governance conventions
        
        Args:
            filepath: Path to the file to organize
            
        Returns:
            (success, message, new_filepath)
        """
        filepath = Path(filepath)
        
        if not filepath.exists():
            return False, f"File not found: {filepath}", None
            
        if not filepath.suffix == '.json':
            return False, f"Not a JSON file: {filepath}", None
            
        # Validate current filename
        is_valid, error_msg, components = self.validator.validate_filename(filepath.name)
        
        if not is_valid:
            # Try to suggest a corrected name
            suggested_name = self.validator.get_suggested_name(filepath.name)
            if suggested_name:
                return self._handle_naming_correction(filepath, suggested_name)
            else:
                return self._handle_non_conforming_file(filepath, error_msg)
        
        # File is valid, determine target directory
        target_dir = self._get_target_directory(components)
        target_path = target_dir / filepath.name
        
        # Handle file movement
        return self._move_file(filepath, target_path, "Organized conforming file")
    
    def _handle_naming_correction(self, filepath: Path, suggested_name: str) -> Tuple[bool, str, Optional[str]]:
        """Handle files that need naming correction"""
        # Parse suggested name to get components
        is_valid, _, components = self.validator.validate_filename(suggested_name)
        
        if not is_valid or not components:
            return self._handle_non_conforming_file(filepath, "Could not generate valid name")
            
        # Determine target directory
        target_dir = self._get_target_directory(components)
        target_path = target_dir / suggested_name
        
        message = f"Renamed {filepath.name} → {suggested_name}"
        return self._move_file(filepath, target_path, message)
    
    def _handle_non_conforming_file(self, filepath: Path, reason: str) -> Tuple[bool, str, Optional[str]]:
        """Handle files that cannot be made conforming"""
        target_dir = self.directories["non_conforming"]
        target_path = target_dir / filepath.name
        
        # Add metadata file for non-conforming files
        metadata_path = target_dir / f"{filepath.stem}_metadata.json"
        metadata = {
            "original_path": str(filepath),
            "reason": reason,
            "archived_date": datetime.now().isoformat(),
            "file_size": filepath.stat().st_size if filepath.exists() else 0
        }
        
        if not self.dry_run:
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
                
        message = f"Archived non-conforming file: {reason}"
        return self._move_file(filepath, target_path, message)
    
    def _get_target_directory(self, components: FileNameComponents) -> Path:
        """Determine target directory for file components"""
        if isinstance(components.file_type, TrackerType):
            if components.file_type == TrackerType.HTF_TRACKER:
                return self.directories["htf_trackers"]
            elif components.file_type == TrackerType.FVG_TRACKER:
                return self.directories["fvg_trackers"] 
            elif components.file_type == TrackerType.LIQ_TRACKER:
                return self.directories["liquidity_trackers"]
        else:
            if components.file_type == FileType.LVL_1:
                return self.directories["level_1"]
            elif components.file_type == FileType.LVL_3:
                return self.directories["level_3"]
            elif components.file_type == FileType.GROK_ENHANCED:
                return self.directories["grok_enhanced"]
            elif components.file_type == FileType.EVENT_TIMING_ENHANCED:
                return self.directories["event_timing"]
            elif components.file_type in [FileType.PREDICTION, FileType.MONTE_CARLO]:
                return self.directories["predictions"]
                
        # Default to archive
        return self.directories["archive"]
    
    def _move_file(self, source: Path, target: Path, message: str) -> Tuple[bool, str, Optional[str]]:
        """Move file from source to target location"""
        try:
            if not self.dry_run:
                # Ensure target directory exists
                target.parent.mkdir(parents=True, exist_ok=True)
                
                # Handle file conflicts
                if target.exists():
                    # Create backup name
                    counter = 1
                    while target.exists():
                        stem = target.stem
                        suffix = target.suffix
                        target = target.parent / f"{stem}_backup_{counter}{suffix}"
                        counter += 1
                        
                # Move file
                shutil.move(str(source), str(target))
                logger.info(f"✅ {message}: {source} → {target}")
            else:
                logger.info(f"🔍 Would move: {source} → {target} ({message})")
                
            return True, message, str(target)
            
        except Exception as e:
            error_msg = f"Failed to move {source}: {e}"
            logger.error(f"❌ {error_msg}")
            return False, error_msg, None
    
    def batch_organize(self, directory: str = ".") -> Dict[str, any]:
        """
        Organize all JSON files in a directory
        
        Args:
            directory: Directory to scan for JSON files
            
        Returns:
            Organization results summary
        """
        directory = Path(directory)
        json_files = list(directory.glob("*.json"))
        
        results = {
            "total_files": len(json_files),
            "successful": 0,
            "failed": 0,
            "renamed": 0,
            "archived": 0,
            "details": []
        }
        
        logger.info(f"🔍 Found {len(json_files)} JSON files to organize")
        
        for filepath in json_files:
            success, message, new_path = self.organize_file(filepath)
            
            result_detail = {
                "original_file": str(filepath),
                "success": success,
                "message": message,
                "new_path": new_path
            }
            results["details"].append(result_detail)
            
            if success:
                results["successful"] += 1
                if "Renamed" in message:
                    results["renamed"] += 1
                elif "Archived" in message:
                    results["archived"] += 1
            else:
                results["failed"] += 1
                
        return results
    
    def generate_organization_report(self, results: Dict[str, any]) -> str:
        """Generate a comprehensive organization report"""
        report_lines = [
            "🗂️ JSON File Organization Report",
            "=" * 50,
            "",
            f"📊 Summary:",
            f"   Total files processed: {results['total_files']}",
            f"   Successfully organized: {results['successful']}",
            f"   Failed: {results['failed']}",
            f"   Files renamed: {results['renamed']}",
            f"   Files archived: {results['archived']}",
            ""
        ]
        
        # Success rate
        if results["total_files"] > 0:
            success_rate = results["successful"] / results["total_files"] * 100
            report_lines.append(f"✅ Success rate: {success_rate:.1f}%")
            report_lines.append("")
        
        # Detailed results
        report_lines.append("📋 Detailed Results:")
        for detail in results["details"]:
            filename = Path(detail["original_file"]).name
            status = "✅" if detail["success"] else "❌"
            message = detail["message"]
            
            report_lines.append(f"{status} {filename}: {message}")
            
            if detail["new_path"] and detail["new_path"] != detail["original_file"]:
                report_lines.append(f"   📁 New location: {detail['new_path']}")
                
        return "\n".join(report_lines)
    
    def validate_organization(self) -> Dict[str, any]:
        """
        Validate the current organization state
        
        Returns:
            Validation results
        """
        validation_results = {
            "directories_exist": True,
            "missing_directories": [],
            "file_counts": {},
            "compliance_issues": []
        }
        
        # Check directory structure
        for name, path in self.directories.items():
            if not path.exists():
                validation_results["directories_exist"] = False
                validation_results["missing_directories"].append(str(path))
            else:
                # Count files in each directory
                json_files = list(path.glob("*.json"))
                validation_results["file_counts"][name] = len(json_files)
                
                # Check file compliance in each directory
                for json_file in json_files:
                    is_valid, error_msg, _ = self.validator.validate_filename(json_file.name)
                    if not is_valid:
                        validation_results["compliance_issues"].append({
                            "file": str(json_file),
                            "issue": error_msg
                        })
                        
        return validation_results
    
    def create_directory_index(self) -> Dict[str, any]:
        """
        Create an index of all organized files
        
        Returns:
            Directory index
        """
        index = {
            "created_date": datetime.now().isoformat(),
            "total_files": 0,
            "directories": {}
        }
        
        for name, path in self.directories.items():
            if path.exists():
                json_files = list(path.glob("*.json"))
                file_info = []
                
                for json_file in json_files:
                    stat = json_file.stat()
                    file_info.append({
                        "filename": json_file.name,
                        "size_bytes": stat.st_size,
                        "modified_date": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "compliant": self.validator.validate_filename(json_file.name)[0]
                    })
                    
                index["directories"][name] = {
                    "path": str(path),
                    "file_count": len(json_files),
                    "files": file_info
                }
                index["total_files"] += len(json_files)
                
        return index

def organize_project_files(base_path: str = ".", dry_run: bool = False) -> Dict[str, any]:
    """
    Convenience function to organize all project JSON files
    
    Args:
        base_path: Base project path
        dry_run: If True, don't actually move files
        
    Returns:
        Organization results
    """
    organizer = FileOrganizer(base_path, dry_run)
    
    # Ensure directory structure exists
    organizer.ensure_directory_structure()
    
    # Organize files
    results = organizer.batch_organize(base_path)
    
    # Generate report
    report = organizer.generate_organization_report(results)
    logger.info(f"\n{report}")
    
    return results

if __name__ == "__main__":
    # Test organization system
    print("🗂️ JSON Governance System - File Organization")
    print("=" * 60)
    
    # Run in dry-run mode first
    print("Running in dry-run mode to preview changes...")
    results = organize_project_files(dry_run=True)
    
    print(f"\nPreview complete. Found {results['total_files']} JSON files.")
    print(f"Would organize {results['successful']} files successfully.")
    
    if results['failed'] > 0:
        print(f"⚠️ {results['failed']} files would require manual attention.")