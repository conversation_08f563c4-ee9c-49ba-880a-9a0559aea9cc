#!/usr/bin/env python3
"""
JSON Governance System - Template Validator Module
Validates JSON files against standardized templates for schema compliance
"""

import json
import os
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from src.utils import get_logger

logger = get_logger(__name__)

@dataclass
class ValidationResult:
    """Result of template validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    compliance_score: float  # 0.0 to 1.0
    missing_fields: List[str]
    extra_fields: List[str]
    
class TemplateType(Enum):
    """Supported template types"""
    ASIA_LVL_1 = "ASIA_Lvl-1"
    ASIA_GROK_ENHANCED = "ASIA_grokEnhanced"
    HTF_TRACKER = "HTF_Tracker"
    FVG_TRACKER = "FVG_Tracker"
    LIQ_TRACKER = "LIQ_Tracker"
    PREDICTION = "prediction"
    
class TemplateValidator:
    """
    Validates JSON files against governance templates
    Ensures schema compliance and data quality standards
    """
    
    def __init__(self, templates_dir: str = "templates"):
        self.templates_dir = templates_dir
        self.templates = {}
        self.schemas = {}
        self._load_templates()
        
    def _load_templates(self):
        """Load all template files and schemas"""
        try:
            # Load session templates
            self._load_session_templates()
            # Load tracker templates  
            self._load_tracker_templates()
            # Load prediction templates
            self._load_prediction_templates()
            
            logger.info(f"✅ Loaded {len(self.templates)} governance templates")
            
        except Exception as e:
            logger.error(f"❌ Failed to load templates: {e}")
            
    def _load_session_templates(self):
        """Load session-specific templates"""
        # Asia Level 1 Template
        self.templates[TemplateType.ASIA_LVL_1] = {
            "session_metadata": {
                "session_id": {"type": "string", "required": True},
                "session_type": {"type": "string", "required": True, "enum": ["Asia"]},
                "date": {"type": "string", "required": True, "pattern": r"\d{4}-\d{2}-\d{2}"},
                "start_time": {"type": "string", "required": True},
                "end_time": {"type": "string", "required": True},
                "duration_minutes": {"type": "number", "required": True},
                "sequence_position": {"type": "number", "required": True},
                "timezone": {"type": "string", "required": True}
            },
            "price_data": {
                "open": {"type": "number", "required": True},
                "high": {"type": "number", "required": True},
                "low": {"type": "number", "required": True},
                "close": {"type": "number", "required": True},
                "range": {"type": "number", "required": True}
            },
            "price_movements": {"type": "array", "required": True},
            "phase_transitions": {"type": "array", "required": True},
            "structures_identified": {"type": "object", "required": True}
        }
        
        # Asia Grok Enhanced Template
        self.templates[TemplateType.ASIA_GROK_ENHANCED] = {
            **self.templates[TemplateType.ASIA_LVL_1],
            "grok_enhanced_data": {
                "unit_a_results": {"type": "object", "required": True},
                "unit_b_results": {"type": "object", "required": True},
                "unit_c_results": {"type": "object", "required": True},
                "unit_d_results": {"type": "object", "required": True},
                "extracted_values": {
                    "energy_rate": {"type": "number", "required": True, "min": 0.1, "max": 3.0},
                    "momentum_strength": {"type": "number", "required": True, "min": 0.1},
                    "alpha_t": {"type": "number", "required": True, "min": 0.0, "max": 1.0},
                    "structural_integrity": {"type": "number", "required": True}
                }
            },
            "processing_metadata": {
                "processing_time_ms": {"type": "number", "required": True},
                "pipeline_version": {"type": "string", "required": True},
                "api_calls_made": {"type": "number", "required": True}
            }
        }
        
    def _load_tracker_templates(self):
        """Load tracker-specific templates"""
        # HTF Tracker Template
        self.templates[TemplateType.HTF_TRACKER] = {
            "tracker_metadata": {
                "tracker_id": {"type": "string", "required": True},
                "session_reference": {"type": "string", "required": True},
                "creation_time": {"type": "string", "required": True},
                "tracker_type": {"type": "string", "required": True, "enum": ["HTF_Context"]}
            },
            "active_structures": {
                "type": "array",
                "required": True,
                "min_items": 1,
                "items": {
                    "level": {"type": "number", "required": True},
                    "structure_type": {"type": "string", "required": True},
                    "strength": {"type": "number", "required": True, "min": 0.0, "max": 1.0},
                    "age_minutes": {"type": "number", "required": True},
                    "interaction_count": {"type": "number", "required": True}
                }
            },
            "htf_distance_influence": {"type": "number", "required": True},
            "structure_registry": {"type": "object", "required": True}
        }
        
        # FVG Tracker Template
        self.templates[TemplateType.FVG_TRACKER] = {
            "tracker_metadata": {
                "tracker_id": {"type": "string", "required": True},
                "session_reference": {"type": "string", "required": True},
                "creation_time": {"type": "string", "required": True},
                "tracker_type": {"type": "string", "required": True, "enum": ["FVG_State"]}
            },
            "t_memory": {"type": "number", "required": True, "min": 0.1},
            "energy_rate": {"type": "number", "required": True, "min": 0.1, "max": 3.0},
            "carryover_fvgs": {"type": "array", "required": True},
            "session_character": {"type": "string", "required": True}
        }
        
        # Liquidity Tracker Template
        self.templates[TemplateType.LIQ_TRACKER] = {
            "tracker_metadata": {
                "tracker_id": {"type": "string", "required": True},
                "session_reference": {"type": "string", "required": True},
                "creation_time": {"type": "string", "required": True},
                "tracker_type": {"type": "string", "required": True, "enum": ["Liquidity_State"]}
            },
            "untaken_liquidity_registry": {
                "type": "array",
                "required": True,
                "items": {
                    "level": {"type": "number", "required": True},
                    "weight": {"type": "number", "required": True, "min": 0.1},
                    "side": {"type": "string", "required": True, "enum": ["buy", "sell"]},
                    "age_minutes": {"type": "number", "required": True}
                }
            },
            "liquidity_gradient": {"type": "number", "required": True},
            "session_bias": {"type": "string", "required": True}
        }
        
    def _load_prediction_templates(self):
        """Load prediction-specific templates"""
        self.templates[TemplateType.PREDICTION] = {
            "prediction_metadata": {
                "prediction_id": {"type": "string", "required": True},
                "target_session": {"type": "string", "required": True},
                "prediction_type": {"type": "string", "required": True},
                "creation_time": {"type": "string", "required": True},
                "model_version": {"type": "string", "required": True}
            },
            "prediction_results": {
                "predicted_close": {"type": "number", "required": True},
                "confidence_interval": {
                    "lower": {"type": "number", "required": True},
                    "upper": {"type": "number", "required": True}
                },
                "probability_bands": {"type": "object", "required": True},
                "event_timing_predictions": {"type": "array", "required": True}
            },
            "model_inputs": {"type": "object", "required": True},
            "validation_metrics": {"type": "object", "required": False}
        }
        
    def validate_file(self, filepath: str, template_type: TemplateType) -> ValidationResult:
        """
        Validate a JSON file against its template
        
        Args:
            filepath: Path to JSON file
            template_type: Template to validate against
            
        Returns:
            ValidationResult with detailed compliance information
        """
        try:
            # Load file
            with open(filepath, 'r') as f:
                data = json.load(f)
                
            # Get template
            template = self.templates.get(template_type)
            if not template:
                return ValidationResult(
                    is_valid=False,
                    errors=[f"Template not found: {template_type}"],
                    warnings=[],
                    compliance_score=0.0,
                    missing_fields=[],
                    extra_fields=[]
                )
                
            # Perform validation
            return self._validate_against_template(data, template, template_type)
            
        except FileNotFoundError:
            return ValidationResult(
                is_valid=False,
                errors=[f"File not found: {filepath}"],
                warnings=[],
                compliance_score=0.0,
                missing_fields=[],
                extra_fields=[]
            )
        except json.JSONDecodeError as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Invalid JSON: {e}"],
                warnings=[],
                compliance_score=0.0,
                missing_fields=[],
                extra_fields=[]
            )
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation error: {e}"],
                warnings=[],
                compliance_score=0.0,
                missing_fields=[],
                extra_fields=[]
            )
    
    def _validate_against_template(self, data: Dict, template: Dict, template_type: TemplateType) -> ValidationResult:
        """Validate data against template structure"""
        errors = []
        warnings = []
        missing_fields = []
        extra_fields = []
        
        # Check required fields
        required_score = self._check_required_fields(data, template, "", missing_fields, errors)
        
        # Check data types and constraints
        type_score = self._check_data_types(data, template, "", errors, warnings)
        
        # Check for extra fields
        extra_score = self._check_extra_fields(data, template, "", extra_fields, warnings)
        
        # Special validations
        special_score = self._perform_special_validations(data, template_type, errors, warnings)
        
        # Calculate overall compliance score
        compliance_score = (required_score + type_score + extra_score + special_score) / 4.0
        
        is_valid = len(errors) == 0 and compliance_score >= 0.8
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            compliance_score=compliance_score,
            missing_fields=missing_fields,
            extra_fields=extra_fields
        )
        
    def _check_required_fields(self, data: Dict, template: Dict, path: str, missing_fields: List[str], errors: List[str]) -> float:
        """Check for required fields"""
        total_required = 0
        found_required = 0
        
        for key, spec in template.items():
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(spec, dict):
                if spec.get("required", False):
                    total_required += 1
                    if key in data:
                        found_required += 1
                    else:
                        missing_fields.append(current_path)
                        errors.append(f"Missing required field: {current_path}")
                        
                elif spec.get("type") == "object" and key in data:
                    # Recursively check nested objects
                    nested_score = self._check_required_fields(
                        data[key], spec, current_path, missing_fields, errors
                    )
                    
        return found_required / total_required if total_required > 0 else 1.0
        
    def _check_data_types(self, data: Dict, template: Dict, path: str, errors: List[str], warnings: List[str]) -> float:
        """Check data types and constraints"""
        total_checks = 0
        passed_checks = 0
        
        for key, spec in template.items():
            if key not in data:
                continue
                
            current_path = f"{path}.{key}" if path else key
            value = data[key]
            
            if isinstance(spec, dict) and "type" in spec:
                total_checks += 1
                expected_type = spec["type"]
                
                # Type checking
                if expected_type == "string" and isinstance(value, str):
                    passed_checks += 1
                elif expected_type == "number" and isinstance(value, (int, float)):
                    passed_checks += 1
                    # Check constraints
                    if "min" in spec and value < spec["min"]:
                        errors.append(f"{current_path}: value {value} below minimum {spec['min']}")
                    elif "max" in spec and value > spec["max"]:
                        errors.append(f"{current_path}: value {value} above maximum {spec['max']}")
                elif expected_type == "array" and isinstance(value, list):
                    passed_checks += 1
                    # Check array constraints
                    if "min_items" in spec and len(value) < spec["min_items"]:
                        errors.append(f"{current_path}: array has {len(value)} items, minimum {spec['min_items']}")
                elif expected_type == "object" and isinstance(value, dict):
                    passed_checks += 1
                else:
                    errors.append(f"{current_path}: expected {expected_type}, got {type(value).__name__}")
                    
        return passed_checks / total_checks if total_checks > 0 else 1.0
        
    def _check_extra_fields(self, data: Dict, template: Dict, path: str, extra_fields: List[str], warnings: List[str]) -> float:
        """Check for unexpected extra fields"""
        extra_count = 0
        total_fields = len(data)
        
        for key in data:
            current_path = f"{path}.{key}" if path else key
            if key not in template:
                extra_fields.append(current_path)
                warnings.append(f"Extra field found: {current_path}")
                extra_count += 1
                
        # Penalize heavily for too many extra fields
        if total_fields > 0:
            extra_ratio = extra_count / total_fields
            return max(0.0, 1.0 - extra_ratio)
        return 1.0
        
    def _perform_special_validations(self, data: Dict, template_type: TemplateType, errors: List[str], warnings: List[str]) -> float:
        """Perform template-specific validations"""
        score = 1.0
        
        if template_type == TemplateType.ASIA_GROK_ENHANCED:
            # Validate enhanced data quality
            extracted_values = data.get("grok_enhanced_data", {}).get("extracted_values", {})
            
            # Check for fallback values (energy_rate = 1.0 indicates fallback)
            energy_rate = extracted_values.get("energy_rate", 1.0)
            if energy_rate == 1.0:
                errors.append("energy_rate appears to be fallback value (1.0) - real enhanced data required")
                score -= 0.3
                
            # Check momentum_strength presence
            if "momentum_strength" not in extracted_values:
                errors.append("momentum_strength missing - required for enhanced data validation")
                score -= 0.2
                
        elif template_type in [TemplateType.HTF_TRACKER, TemplateType.FVG_TRACKER, TemplateType.LIQ_TRACKER]:
            # Validate tracker data quality
            tracker_metadata = data.get("tracker_metadata", {})
            
            # Check tracker ID format
            tracker_id = tracker_metadata.get("tracker_id", "")
            if not tracker_id or "_" not in tracker_id:
                warnings.append("tracker_id should follow standard format")
                score -= 0.1
                
        return max(0.0, score)
    
    def auto_detect_template_type(self, filepath: str) -> Optional[TemplateType]:
        """
        Auto-detect template type from filename
        
        Args:
            filepath: Path to JSON file
            
        Returns:
            Detected template type or None
        """
        filename = os.path.basename(filepath).lower()
        
        # Check tracker types first
        if "htf_tracker" in filename:
            return TemplateType.HTF_TRACKER
        elif "fvg_tracker" in filename:
            return TemplateType.FVG_TRACKER
        elif "liq_tracker" in filename:
            return TemplateType.LIQ_TRACKER
            
        # Check session types
        elif "grokenhanced" in filename:
            return TemplateType.ASIA_GROK_ENHANCED
        elif "lvl-1" in filename or "l1" in filename:
            return TemplateType.ASIA_LVL_1
        elif "prediction" in filename:
            return TemplateType.PREDICTION
            
        return None
    
    def generate_compliance_report(self, results: Dict[str, ValidationResult]) -> str:
        """Generate a comprehensive compliance report"""
        report_lines = [
            "🏛️ JSON Governance Compliance Report",
            "=" * 50,
            ""
        ]
        
        total_files = len(results)
        valid_files = sum(1 for r in results.values() if r.is_valid)
        avg_score = sum(r.compliance_score for r in results.values()) / total_files if total_files > 0 else 0
        
        report_lines.extend([
            f"📊 Summary:",
            f"   Total files validated: {total_files}",
            f"   Valid files: {valid_files} ({valid_files/total_files*100:.1f}%)",
            f"   Average compliance score: {avg_score:.2f}",
            ""
        ])
        
        # Detailed results
        for filepath, result in results.items():
            filename = os.path.basename(filepath)
            status = "✅" if result.is_valid else "❌"
            
            report_lines.extend([
                f"{status} {filename} (Score: {result.compliance_score:.2f})",
            ])
            
            if result.errors:
                for error in result.errors:
                    report_lines.append(f"   ❌ {error}")
                    
            if result.warnings:
                for warning in result.warnings:
                    report_lines.append(f"   ⚠️ {warning}")
                    
            report_lines.append("")
            
        return "\n".join(report_lines)

def validate_file_compliance(filepath: str, template_type: Optional[TemplateType] = None) -> ValidationResult:
    """
    Convenience function to validate file compliance
    
    Args:
        filepath: Path to JSON file
        template_type: Template type (auto-detected if None)
        
    Returns:
        ValidationResult
    """
    validator = TemplateValidator()
    
    if template_type is None:
        template_type = validator.auto_detect_template_type(filepath)
        if template_type is None:
            return ValidationResult(
                is_valid=False,
                errors=["Cannot determine template type from filename"],
                warnings=[],
                compliance_score=0.0,
                missing_fields=[],
                extra_fields=[]
            )
    
    return validator.validate_file(filepath, template_type)

if __name__ == "__main__":
    # Test with the Asia L1 file we examined
    test_file = "/Users/<USER>/grok-claude-automation/asia_l1_2025_07_25.json"
    
    print("🏛️ JSON Governance System - Template Validation")
    print("=" * 60)
    
    result = validate_file_compliance(test_file, TemplateType.ASIA_LVL_1)
    
    print(f"File: {os.path.basename(test_file)}")
    print(f"Valid: {'✅' if result.is_valid else '❌'}")
    print(f"Compliance Score: {result.compliance_score:.2f}")
    
    if result.errors:
        print("\nErrors:")
        for error in result.errors:
            print(f"  ❌ {error}")
            
    if result.warnings:
        print("\nWarnings:")
        for warning in result.warnings:
            print(f"  ⚠️ {warning}")