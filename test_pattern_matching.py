#!/usr/bin/env python3
"""
Test HTF Session Intelligence Parser patterns against underscore-separated text
"""
import re

# Current patterns from htf_session_intelligence_parser.py
session_patterns = {
    "session_references": [
        r"(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]am|nyam|ny[_\s]pm|nypm|pm)\s+session\s+(?P<structure>high|low)",
        r"(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]am|nyam|ny[_\s]pm|nypm|pm)\s+(?P<structure>high|low)",
        r"previous\s+day'?s?\s+(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]am|nyam|ny[_\s]pm|nypm|pm)?\s*(?P<structure>high|low)",
        r"yesterday'?s?\s+(?P<session>premarket|pre-market|midnight|asia|london|lunch|ny[_\s]am|nyam|ny[_\s]pm|nypm|pm)?\s*(?P<structure>high|low)",
        r"(?P<session>asia|london|lunch|ny[_\s]am|ny[_\s]pm)\s+session\s+(?P<structure>low|high)\s+(?:at\s+)?(?P<level>[0-9,]+\.?[0-9]*)",
        r"(?P<structure>high|low)\s+(?:of\s+)?(?:the\s+)?(?P<session>asia|london|lunch|ny[_\s]am|ny[_\s]pm)\s+session",
    ],
    "takeout_actions": [
        r"(?P<level>[\d,.]+)\s+(?:gets?|was|is)?\s*taken\s+out",
        r"took\s+(?:out\s+)?(?P<reference>.*?)\s+(?:at\s+)?(?P<level>[\d,.]+)?",
        r"sweep\s+(?:of\s+)?(?P<reference>.*?)(?:\s+at\s+(?P<level>[\d,.]+))?",
        r"(?P<reference>.*?)\s+taken\s+(?:out\s+)?(?:at\s+(?P<level>[\d,.]+))?",
    ]
}

# Test cases from actual HTF event descriptions
test_cases = [
    "Asia_session_low_taken_at_open",
    "Asia session low taken at open", 
    "london_session_high_violated",
    "London session high violated",
    "ny_am_session_low_swept",
    "NY AM session low swept",
    "took out Asia session low at 23450",
    "Asia session low gets taken out",
    "previous day's Asia low",
    "yesterday's London high"
]

def test_patterns():
    print("🔍 HTF Pattern Matching Diagnostic")
    print("=" * 50)
    
    for test_text in test_cases:
        print(f"\n📝 Testing: '{test_text}'")
        matches_found = 0
        
        # Test session reference patterns
        for i, pattern in enumerate(session_patterns["session_references"]):
            match = re.search(pattern, test_text.lower(), re.IGNORECASE)
            if match:
                matches_found += 1
                print(f"   ✅ Pattern {i+1} (session_references): {match.groupdict()}")
        
        # Test takeout action patterns  
        for i, pattern in enumerate(session_patterns["takeout_actions"]):
            match = re.search(pattern, test_text.lower(), re.IGNORECASE)
            if match:
                matches_found += 1
                print(f"   ✅ Pattern {i+1} (takeout_actions): {match.groupdict()}")
        
        if matches_found == 0:
            print("   ❌ No patterns matched!")

if __name__ == "__main__":
    test_patterns()