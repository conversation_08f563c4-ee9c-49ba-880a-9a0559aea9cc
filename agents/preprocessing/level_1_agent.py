#!/usr/bin/env python3
"""
JSON Governance System - Level 1 Processing Agent
Specialized agent for Level 1 raw data processing and standardization
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

from src.utils import get_logger
from governance.naming_conventions import SessionType

logger = get_logger(__name__)

@dataclass
class Level1ProcessingConfig:
    """Configuration for Level 1 processing"""
    validate_price_data: bool = True
    require_timing_analysis: bool = True
    standardize_timestamps: bool = True
    extract_key_levels: bool = True
    
class Level1Agent:
    """
    Specialized agent for Level 1 raw session data processing
    Handles standardization, validation, and core data extraction
    """
    
    def __init__(self, config: Optional[Level1ProcessingConfig] = None):
        self.config = config or Level1ProcessingConfig()
        
    def process(self, session_data: Dict, session_type: SessionType) -> Dict:
        """
        Process session data to Level 1 standard
        
        Args:
            session_data: Raw session data
            session_type: Detected session type
            
        Returns:
            Level 1 processed data
        """
        logger.info(f"📊 Level 1 Agent processing {session_type.value} session")
        
        processed_data = {
            "session_metadata": self._standardize_metadata(session_data, session_type),
            "price_data": self._standardize_price_data(session_data),
            "market_structure": self._extract_market_structure(session_data),
            "timing_analysis": self._extract_timing_analysis(session_data),
            "level_interactions": self._extract_level_interactions(session_data),
            "behavioral_profile": self._extract_behavioral_profile(session_data),
            "data_quality": self._assess_data_quality(session_data)
        }
        
        # Add Level 1 specific metadata
        processed_data["processing_metadata"] = {
            "processing_level": "level_1",
            "agent_version": "1.0.0",
            "processed_timestamp": datetime.now().isoformat(),
            "data_completeness": self._calculate_completeness(session_data),
            "standardization_applied": True
        }
        
        logger.info("✅ Level 1 processing complete")
        return processed_data
    
    def _standardize_metadata(self, session_data: Dict, session_type: SessionType) -> Dict:
        """Standardize session metadata"""
        original = session_data.get("session_metadata", {})
        
        # Extract and validate core metadata
        metadata = {
            "session_id": original.get("session_id") or f"{session_type.value.lower()}_session_{datetime.now().strftime('%Y_%m_%d')}",
            "session_type": session_type.value,
            "date": self._standardize_date(original.get("date", "")),
            "trading_hours": self._extract_trading_hours(original),
            "timezone": original.get("timezone", "ET"),
            "sequence_position": self._determine_sequence_position(session_type),
            "session_character": self._analyze_session_character(session_data)
        }
        
        # Add duration calculation
        if metadata["trading_hours"]["start_time"] and metadata["trading_hours"]["end_time"]:
            metadata["duration_minutes"] = self._calculate_duration(
                metadata["trading_hours"]["start_time"],
                metadata["trading_hours"]["end_time"]
            )
        
        return metadata
    
    def _standardize_price_data(self, session_data: Dict) -> Dict:
        """Standardize and validate price data"""
        price_data = session_data.get("price_data", {})
        
        # Extract OHLC data
        standardized = {
            "open": float(price_data.get("open", 0.0)),
            "high": float(price_data.get("high", 0.0)),
            "low": float(price_data.get("low", 0.0)),
            "close": float(price_data.get("close", 0.0))
        }
        
        # Calculate derived metrics
        standardized["range"] = standardized["high"] - standardized["low"]
        standardized["session_character"] = self._classify_price_character(standardized)
        
        # Validate price data
        if self.config.validate_price_data:
            standardized["validation"] = self._validate_ohlc(standardized)
        
        return standardized
    
    def _extract_market_structure(self, session_data: Dict) -> Dict:
        """Extract and standardize market structure data"""
        structures = session_data.get("structures_identified", {})
        
        structure_data = {
            "fair_value_gaps": self._standardize_fvgs(structures.get("fair_value_gaps", [])),
            "session_levels": self._standardize_session_levels(structures.get("session_levels", [])),
            "key_levels": self._extract_key_levels(session_data),
            "structure_count": 0
        }
        
        # Calculate structure count
        structure_data["structure_count"] = (
            len(structure_data["fair_value_gaps"]) +
            len(structure_data["session_levels"]) +
            len(structure_data["key_levels"])
        )
        
        return structure_data
    
    def _extract_timing_analysis(self, session_data: Dict) -> Dict:
        """Extract and standardize timing analysis"""
        micro_timing = session_data.get("micro_timing_analysis", {})
        
        timing_data = {
            "phase_transitions": self._standardize_phase_transitions(session_data.get("phase_transitions", [])),
            "cascade_events": micro_timing.get("cascade_events", []),
            "timing_metrics": micro_timing.get("timing_metrics", {}),
            "expansion_consolidation_cycles": self._analyze_expansion_cycles(session_data)
        }
        
        # Add timing quality assessment
        timing_data["timing_quality"] = self._assess_timing_quality(timing_data)
        
        return timing_data
    
    def _extract_level_interactions(self, session_data: Dict) -> List[Dict]:
        """Extract and standardize level interactions"""
        interactions = session_data.get("level_interactions", [])
        
        standardized_interactions = []
        for interaction in interactions:
            standardized_interactions.append({
                "timestamp": interaction.get("timestamp", ""),
                "price_level": float(interaction.get("level", 0.0)),
                "interaction_type": interaction.get("interaction_type", "unknown"),
                "level_origin": interaction.get("level_origin", "unknown"),
                "result": interaction.get("result", "unknown"),
                "significance": self._assess_interaction_significance(interaction)
            })
        
        return standardized_interactions
    
    def _extract_behavioral_profile(self, session_data: Dict) -> Dict:
        """Extract behavioral observations and patterns"""
        behavioral = session_data.get("behavioral_observations", {})
        
        profile = {
            "session_type_observed": behavioral.get("session_type_observed", "neutral"),
            "directional_bias": self._determine_directional_bias(session_data),
            "volatility_profile": self._assess_volatility_profile(session_data),
            "institutional_activity": behavioral.get("institutional_activity", "moderate"),
            "market_efficiency": self._assess_market_efficiency(session_data)
        }
        
        return profile
    
    def _assess_data_quality(self, session_data: Dict) -> Dict:
        """Assess overall data quality"""
        quality_metrics = {
            "completeness_score": self._calculate_completeness(session_data),
            "consistency_score": self._check_data_consistency(session_data),
            "accuracy_indicators": self._check_accuracy_indicators(session_data),
            "missing_fields": self._identify_missing_fields(session_data),
            "data_anomalies": self._detect_anomalies(session_data)
        }
        
        # Calculate overall quality score
        quality_metrics["overall_quality_score"] = (
            quality_metrics["completeness_score"] +
            quality_metrics["consistency_score"]
        ) / 2.0
        
        return quality_metrics
    
    def _standardize_date(self, date_str: str) -> str:
        """Standardize date format to YYYY-MM-DD"""
        if not date_str:
            return datetime.now().strftime("%Y-%m-%d")
            
        # Handle various date formats
        date_str = date_str.replace("_", "-")
        if len(date_str) == 8:  # YYYYMMDD
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        elif len(date_str) == 10:  # YYYY-MM-DD or YYYY_MM_DD
            return date_str
        else:
            return datetime.now().strftime("%Y-%m-%d")
    
    def _extract_trading_hours(self, metadata: Dict) -> Dict:
        """Extract and standardize trading hours"""
        return {
            "start_time": metadata.get("start_time", ""),
            "end_time": metadata.get("end_time", ""),
            "timezone": metadata.get("timezone", "ET")
        }
    
    def _determine_sequence_position(self, session_type: SessionType) -> int:
        """Determine sequence position for session type"""
        sequence_map = {
            SessionType.MIDNIGHT: 0,
            SessionType.ASIA: 1,
            SessionType.LONDON: 2,
            SessionType.PREMARKET: 3,
            SessionType.NYAM: 4,
            SessionType.LUNCH: 5,
            SessionType.NYPM: 6
        }
        return sequence_map.get(session_type, 0)
    
    def _analyze_session_character(self, session_data: Dict) -> str:
        """Analyze overall session character"""
        price_data = session_data.get("price_data", {})
        
        # Check existing character
        existing_character = price_data.get("session_character", "")
        if existing_character:
            return existing_character
            
        # Analyze price range and movements
        price_range = price_data.get("range", 0)
        if price_range > 100:
            return "high_volatility_expansion"
        elif price_range > 50:
            return "moderate_expansion"
        else:
            return "consolidation_dominant"
    
    def _calculate_duration(self, start_time: str, end_time: str) -> int:
        """Calculate session duration in minutes"""
        try:
            start = datetime.strptime(start_time.split()[0], "%H:%M:%S")
            end = datetime.strptime(end_time.split()[0], "%H:%M:%S")
            
            # Handle day rollover
            if end < start:
                end = end.replace(day=end.day + 1)
                
            duration = (end - start).total_seconds() / 60
            return int(duration)
        except:
            return 300  # Default 5 hours
    
    def _classify_price_character(self, price_data: Dict) -> str:
        """Classify price movement character"""
        price_range = price_data["range"]
        
        if price_range > 100:
            return "high_range_expansion"
        elif price_range > 50:
            return "moderate_range_movement"
        else:
            return "low_range_consolidation"
    
    def _validate_ohlc(self, price_data: Dict) -> Dict:
        """Validate OHLC price data"""
        validation = {
            "valid_ohlc": True,
            "issues": []
        }
        
        # Check basic OHLC relationships
        if price_data["high"] < price_data["low"]:
            validation["valid_ohlc"] = False
            validation["issues"].append("High below low")
            
        if price_data["open"] > price_data["high"] or price_data["open"] < price_data["low"]:
            validation["valid_ohlc"] = False
            validation["issues"].append("Open outside high-low range")
            
        if price_data["close"] > price_data["high"] or price_data["close"] < price_data["low"]:
            validation["valid_ohlc"] = False
            validation["issues"].append("Close outside high-low range")
        
        return validation
    
    def _standardize_fvgs(self, fvgs: List[Dict]) -> List[Dict]:
        """Standardize FVG data"""
        standardized = []
        for fvg in fvgs:
            standardized.append({
                "id": fvg.get("id", ""),
                "formation_time": fvg.get("formation_time", ""),
                "type": fvg.get("type", "unknown"),
                "premium_high": float(fvg.get("premium_high", 0.0)),
                "discount_low": float(fvg.get("discount_low", 0.0)),
                "size_points": float(fvg.get("size_points", 0.0)),
                "delivery_status": fvg.get("delivery_status", "pending")
            })
        return standardized
    
    def _standardize_session_levels(self, levels: List[Dict]) -> List[Dict]:
        """Standardize session level data"""
        standardized = []
        for level in levels:
            standardized.append({
                "type": level.get("type", "unknown"),
                "level": float(level.get("level", 0.0)),
                "formation_time": level.get("formation_time", ""),
                "touches": level.get("touches", []),
                "holds": level.get("holds", True),
                "strength": self._calculate_level_strength(level)
            })
        return standardized
    
    def _extract_key_levels(self, session_data: Dict) -> List[Dict]:
        """Extract key price levels from session data"""
        price_data = session_data.get("price_data", {})
        
        key_levels = [
            {"type": "session_high", "level": price_data.get("high", 0.0), "strength": 1.0},
            {"type": "session_low", "level": price_data.get("low", 0.0), "strength": 1.0},
            {"type": "session_open", "level": price_data.get("open", 0.0), "strength": 0.8},
            {"type": "session_close", "level": price_data.get("close", 0.0), "strength": 0.8}
        ]
        
        return key_levels
    
    def _standardize_phase_transitions(self, transitions: List[Dict]) -> List[Dict]:
        """Standardize phase transition data"""
        standardized = []
        for transition in transitions:
            standardized.append({
                "phase_type": transition.get("phase_type", "unknown"),
                "start_time": transition.get("start_time", ""),
                "end_time": transition.get("end_time", ""),
                "duration_minutes": self._calculate_phase_duration(transition),
                "price_movement": self._calculate_phase_movement(transition),
                "description": transition.get("description", "")
            })
        return standardized
    
    def _analyze_expansion_cycles(self, session_data: Dict) -> Dict:
        """Analyze expansion/consolidation cycles"""
        consolidation_raw = session_data.get("consolidation_expansion_raw", {})
        
        expansion_periods = consolidation_raw.get("expansion_periods", [])
        consolidation_periods = consolidation_raw.get("consolidation_periods", [])
        
        return {
            "total_expansions": len(expansion_periods),
            "total_consolidations": len(consolidation_periods),
            "expansion_ratio": len(expansion_periods) / (len(expansion_periods) + len(consolidation_periods)) 
                             if (len(expansion_periods) + len(consolidation_periods)) > 0 else 0.5,
            "average_expansion_distance": self._calculate_average_expansion(expansion_periods),
            "average_consolidation_duration": self._calculate_average_consolidation_duration(consolidation_periods)
        }
    
    def _assess_timing_quality(self, timing_data: Dict) -> Dict:
        """Assess quality of timing data"""
        return {
            "phase_transition_completeness": len(timing_data["phase_transitions"]) > 0,
            "cascade_events_present": len(timing_data["cascade_events"]) > 0,
            "timing_metrics_available": bool(timing_data["timing_metrics"]),
            "overall_timing_score": 0.8  # Default score
        }
    
    def _assess_interaction_significance(self, interaction: Dict) -> str:
        """Assess significance of level interaction"""
        interaction_type = interaction.get("interaction_type", "")
        result = interaction.get("result", "")
        
        if interaction_type == "break" and result in ["broken", "delivered"]:
            return "high"
        elif interaction_type == "touch" and result == "respected":
            return "medium"
        else:
            return "low"
    
    def _determine_directional_bias(self, session_data: Dict) -> str:
        """Determine overall directional bias"""
        price_data = session_data.get("price_data", {})
        
        open_price = price_data.get("open", 0.0)
        close_price = price_data.get("close", 0.0)
        
        if close_price > open_price:
            return "bullish"
        elif close_price < open_price:
            return "bearish"
        else:
            return "neutral"
    
    def _assess_volatility_profile(self, session_data: Dict) -> str:
        """Assess volatility profile"""
        price_data = session_data.get("price_data", {})
        price_range = price_data.get("range", 0)
        
        if price_range > 100:
            return "high_volatility"
        elif price_range > 50:
            return "moderate_volatility"
        else:
            return "low_volatility"
    
    def _assess_market_efficiency(self, session_data: Dict) -> str:
        """Assess market efficiency based on price movements"""
        price_movements = session_data.get("price_movements", [])
        
        if len(price_movements) > 10:
            return "high_activity"
        elif len(price_movements) > 5:
            return "moderate_activity"
        else:
            return "low_activity"
    
    def _calculate_completeness(self, session_data: Dict) -> float:
        """Calculate data completeness score"""
        required_fields = [
            "session_metadata", "price_data", "price_movements",
            "phase_transitions", "structures_identified"
        ]
        
        present_fields = sum(1 for field in required_fields if field in session_data)
        return present_fields / len(required_fields)
    
    def _check_data_consistency(self, session_data: Dict) -> float:
        """Check data consistency"""
        # Simple consistency checks
        consistency_score = 1.0
        
        # Check price data consistency
        price_data = session_data.get("price_data", {})
        if price_data:
            if price_data.get("high", 0) < price_data.get("low", 0):
                consistency_score -= 0.3
                
        return max(0.0, consistency_score)
    
    def _check_accuracy_indicators(self, session_data: Dict) -> List[str]:
        """Check for accuracy indicators"""
        indicators = []
        
        # Check for realistic price ranges
        price_data = session_data.get("price_data", {})
        if price_data.get("range", 0) > 1000:
            indicators.append("unusually_large_range")
        elif price_data.get("range", 0) < 1:
            indicators.append("unusually_small_range")
            
        return indicators
    
    def _identify_missing_fields(self, session_data: Dict) -> List[str]:
        """Identify missing critical fields"""
        critical_fields = [
            "session_metadata.session_id",
            "session_metadata.date",
            "price_data.open",
            "price_data.high",
            "price_data.low",
            "price_data.close"
        ]
        
        missing = []
        for field in critical_fields:
            keys = field.split(".")
            current = session_data
            
            try:
                for key in keys:
                    current = current[key]
            except (KeyError, TypeError):
                missing.append(field)
                
        return missing
    
    def _detect_anomalies(self, session_data: Dict) -> List[str]:
        """Detect data anomalies"""
        anomalies = []
        
        # Check for zero prices
        price_data = session_data.get("price_data", {})
        for price_type in ["open", "high", "low", "close"]:
            if price_data.get(price_type, 0) == 0:
                anomalies.append(f"zero_{price_type}_price")
                
        return anomalies
    
    def _calculate_level_strength(self, level: Dict) -> float:
        """Calculate level strength based on touches and holds"""
        touches = len(level.get("touches", []))
        holds = level.get("holds", True)
        
        strength = min(1.0, touches * 0.2)  # Base strength from touches
        if holds:
            strength += 0.3  # Bonus for holding
            
        return min(1.0, strength)
    
    def _calculate_phase_duration(self, transition: Dict) -> int:
        """Calculate phase duration in minutes"""
        try:
            start_time = transition.get("start_time", "")
            end_time = transition.get("end_time", "")
            
            if start_time and end_time:
                start = datetime.strptime(start_time, "%H:%M:%S")
                end = datetime.strptime(end_time, "%H:%M:%S")
                
                if end < start:
                    end = end.replace(day=end.day + 1)
                    
                return int((end - start).total_seconds() / 60)
        except:
            pass
            
        return 0
    
    def _calculate_phase_movement(self, transition: Dict) -> float:
        """Calculate price movement during phase"""
        high = transition.get("high", 0.0)
        low = transition.get("low", 0.0)
        return abs(high - low)
    
    def _calculate_average_expansion(self, expansions: List[Dict]) -> float:
        """Calculate average expansion distance"""
        if not expansions:
            return 0.0
            
        total_distance = sum(exp.get("total_distance", 0.0) for exp in expansions)
        return total_distance / len(expansions)
    
    def _calculate_average_consolidation_duration(self, consolidations: List[Dict]) -> float:
        """Calculate average consolidation duration"""
        if not consolidations:
            return 0.0
            
        durations = []
        for cons in consolidations:
            try:
                start = datetime.strptime(cons.get("start", ""), "%H:%M:%S")
                end = datetime.strptime(cons.get("end", ""), "%H:%M:%S")
                duration = (end - start).total_seconds() / 60
                durations.append(duration)
            except:
                continue
                
        return sum(durations) / len(durations) if durations else 0.0

def create_level_1_agent(config: Optional[Level1ProcessingConfig] = None) -> Level1Agent:
    """Factory function to create Level 1 agent"""
    return Level1Agent(config)

if __name__ == "__main__":
    # Test Level 1 agent
    print("🔬 Level 1 Processing Agent Test")
    print("=" * 40)
    
    agent = create_level_1_agent()
    
    # Test with sample data
    sample_data = {
        "session_metadata": {
            "session_id": "test_session",
            "date": "2025-07-25"
        },
        "price_data": {
            "open": 23400.0,
            "high": 23450.0,
            "low": 23380.0,
            "close": 23420.0,
            "range": 70.0
        },
        "structures_identified": {
            "fair_value_gaps": [],
            "session_levels": []
        }
    }
    
    result = agent.process(sample_data, SessionType.ASIA)
    print(f"✅ Level 1 processing complete")
    print(f"Data quality score: {result['data_quality']['overall_quality_score']:.2f}")
    print(f"Structure count: {result['market_structure']['structure_count']}")