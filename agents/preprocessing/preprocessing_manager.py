#!/usr/bin/env python3
"""
JSON Governance System - Preprocessing Manager Agent
Master coordination agent for all preprocessing/extraction/parsing operations
"""

import os
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass

from src.utils import get_logger, safe_json_load, safe_json_save
from governance.naming_conventions import NamingConventionValidator, SessionType, FileType
from governance.template_validator import TemplateValidator, TemplateType, ValidationResult

logger = get_logger(__name__)

@dataclass 
class PreprocessingResult:
    """Result of preprocessing operation"""
    success: bool
    session_file: str
    output_files: List[str]
    processing_time_ms: float
    validation_results: ValidationResult
    tracker_files: List[str]
    errors: List[str]
    warnings: List[str]

class PreprocessingManager:
    """
    Master coordination agent for preprocessing operations
    Orchestrates level_1, level_3, extraction, and validation agents
    """
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.naming_validator = NamingConventionValidator()
        self.template_validator = TemplateValidator()
        
        # Agent configuration
        self.agents = {
            "level_1": None,      # Will be loaded dynamically
            "level_3": None,
            "extraction": None,
            "validation": None,
            "tracker_generator": None
        }
        
        # Processing statistics
        self.stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "processing_time_total_ms": 0,
            "average_processing_time_ms": 0
        }
        
    def process_session(self, session_file: str, target_level: str = "level_1", 
                       output_dir: Optional[str] = None) -> PreprocessingResult:
        """
        Process a session file through the preprocessing pipeline
        
        Args:
            session_file: Path to session file
            target_level: Target processing level (level_1, level_3)
            output_dir: Output directory (auto-determined if None)
            
        Returns:
            PreprocessingResult with processing details
        """
        start_time = datetime.now()
        session_path = Path(session_file)
        
        logger.info(f"🚀 Starting preprocessing: {session_path.name}")
        
        try:
            # Load and validate input session
            session_data = safe_json_load(str(session_path))
            if not session_data:
                return self._create_error_result(session_file, "Failed to load session file")
            
            # Determine session type and validate naming
            session_type = self._detect_session_type(session_data, session_path.name)
            if not session_type:
                return self._create_error_result(session_file, "Cannot determine session type")
                
            # Generate output filename with governance conventions
            date_str = self._extract_date_from_session(session_data, session_path.name)
            output_filename = self.naming_validator.generate_filename(
                session_type, FileType.LVL_1 if target_level == "level_1" else FileType.LVL_3, 
                date_str
            )
            
            # Determine output directory
            if output_dir is None:
                output_dir = f"data/preprocessing/{target_level}"
            output_path = self.base_path / output_dir / output_filename
            
            # Process based on target level
            if target_level == "level_1":
                processed_data = self._process_level_1(session_data, session_type)
            elif target_level == "level_3":
                processed_data = self._process_level_3(session_data, session_type)
            else:
                return self._create_error_result(session_file, f"Unknown target level: {target_level}")
            
            # Validate processed data
            template_type = TemplateType.ASIA_LVL_1 if target_level == "level_1" else TemplateType.ASIA_GROK_ENHANCED
            validation_result = self._validate_processed_data(processed_data, template_type)
            
            # Generate tracker files
            tracker_files = self._generate_tracker_files(processed_data, session_type, date_str)
            
            # Save output file
            output_files = []
            if safe_json_save(processed_data, str(output_path)):
                output_files.append(str(output_path))
                logger.info(f"✅ Saved processed file: {output_path}")
            else:
                return self._create_error_result(session_file, "Failed to save output file")
                
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Update statistics
            self._update_stats(processing_time, True)
            
            return PreprocessingResult(
                success=True,
                session_file=session_file,
                output_files=output_files,
                processing_time_ms=processing_time,
                validation_results=validation_result,
                tracker_files=tracker_files,
                errors=[],
                warnings=validation_result.warnings if validation_result else []
            )
            
        except Exception as e:
            logger.error(f"❌ Preprocessing failed: {e}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self._update_stats(processing_time, False)
            return self._create_error_result(session_file, str(e))
    
    def _process_level_1(self, session_data: Dict, session_type: SessionType) -> Dict:
        """Process session to Level 1 standard"""
        logger.info("📊 Processing to Level 1 standard...")
        
        # Extract core session data
        processed_data = {
            "session_metadata": self._extract_session_metadata(session_data, session_type),
            "price_data": self._extract_price_data(session_data),
            "price_movements": self._extract_price_movements(session_data),
            "phase_transitions": self._extract_phase_transitions(session_data),
            "structures_identified": self._extract_structures(session_data),
            "level_interactions": session_data.get("level_interactions", []),
            "micro_timing_analysis": session_data.get("micro_timing_analysis", {}),
            "consolidation_expansion_raw": session_data.get("consolidation_expansion_raw", {}),
            "fpfvg_observations": session_data.get("fpfvg_observations", {}),
            "news_context": session_data.get("news_context", {"news_impacted": False, "news_events": []}),
            "liquidity_analysis": session_data.get("liquidity_analysis", {}),
            "fvg_analysis": session_data.get("fvg_analysis", {}),
            "behavioral_observations": session_data.get("behavioral_observations", {})
        }
        
        # Add preprocessing metadata
        processed_data["preprocessing_metadata"] = {
            "processing_level": "level_1",
            "processed_date": datetime.now().isoformat(),
            "source_file": session_data.get("session_metadata", {}).get("session_id", "unknown"),
            "governance_compliant": True,
            "template_validated": True
        }
        
        return processed_data
    
    def _process_level_3(self, session_data: Dict, session_type: SessionType) -> Dict:
        """Process session to Level 3 enhanced standard"""
        logger.info("🔬 Processing to Level 3 enhanced standard...")
        
        # Start with Level 1 data
        processed_data = self._process_level_1(session_data, session_type)
        
        # Add Level 3 enhancements
        processed_data["enhanced_analytics"] = {
            "advanced_timing_analysis": self._generate_advanced_timing(session_data),
            "pattern_recognition": self._generate_pattern_analysis(session_data),
            "mathematical_relationships": self._extract_mathematical_relationships(session_data),
            "cross_session_context": self._build_cross_session_context(session_data),
            "predictive_indicators": self._generate_predictive_indicators(session_data)
        }
        
        # Update metadata
        processed_data["preprocessing_metadata"]["processing_level"] = "level_3"
        processed_data["preprocessing_metadata"]["enhanced_features"] = [
            "advanced_timing_analysis",
            "pattern_recognition", 
            "mathematical_relationships",
            "cross_session_context",
            "predictive_indicators"
        ]
        
        return processed_data
    
    def _detect_session_type(self, session_data: Dict, filename: str) -> Optional[SessionType]:
        """Detect session type from data or filename"""
        # Try session metadata first
        session_metadata = session_data.get("session_metadata", {})
        session_type_str = session_metadata.get("session_type", "").upper()
        
        if session_type_str:
            try:
                return SessionType(session_type_str)
            except ValueError:
                pass
                
        # Try filename detection
        filename_lower = filename.lower()
        for session_type in SessionType:
            if session_type.value.lower() in filename_lower:
                return session_type
                
        return None
    
    def _extract_date_from_session(self, session_data: Dict, filename: str) -> str:
        """Extract date in YYYY_MM_DD format"""
        # Try session metadata first
        session_metadata = session_data.get("session_metadata", {})
        date_str = session_metadata.get("date", "")
        
        if date_str:
            # Convert various date formats to YYYY_MM_DD
            date_str = date_str.replace("-", "_")
            if len(date_str) == 10:  # YYYY_MM_DD or YYYY-MM-DD
                return date_str
                
        # Try filename extraction
        date_from_filename = self.naming_validator._extract_date(filename)
        if date_from_filename:
            return date_from_filename
            
        # Default to current date
        return datetime.now().strftime("%Y_%m_%d")
    
    def _extract_session_metadata(self, session_data: Dict, session_type: SessionType) -> Dict:
        """Extract and standardize session metadata"""
        original_metadata = session_data.get("session_metadata", {})
        
        return {
            "session_id": original_metadata.get("session_id", f"{session_type.value.lower()}_session"),
            "session_type": session_type.value,
            "date": original_metadata.get("date", datetime.now().strftime("%Y-%m-%d")),
            "start_time": original_metadata.get("start_time", "00:00:00 ET"),
            "end_time": original_metadata.get("end_time", "23:59:00 ET"),
            "duration_minutes": original_metadata.get("duration_minutes", 300),
            "sequence_position": original_metadata.get("sequence_position", 1),
            "timezone": original_metadata.get("timezone", "ET"),
            "session_character": original_metadata.get("session_character", "neutral")
        }
    
    def _extract_price_data(self, session_data: Dict) -> Dict:
        """Extract and validate price data"""
        price_data = session_data.get("price_data", {})
        
        return {
            "open": price_data.get("open", 0.0),
            "high": price_data.get("high", 0.0),
            "low": price_data.get("low", 0.0),
            "close": price_data.get("close", 0.0),
            "range": price_data.get("range", 0.0),
            "session_character": price_data.get("session_character", "neutral")
        }
    
    def _extract_price_movements(self, session_data: Dict) -> List[Dict]:
        """Extract significant price movements"""
        return session_data.get("price_movements", [])
    
    def _extract_phase_transitions(self, session_data: Dict) -> List[Dict]:
        """Extract phase transitions"""
        return session_data.get("phase_transitions", [])
    
    def _extract_structures(self, session_data: Dict) -> Dict:
        """Extract market structures"""
        return session_data.get("structures_identified", {
            "fair_value_gaps": [],
            "session_levels": []
        })
    
    def _generate_advanced_timing(self, session_data: Dict) -> Dict:
        """Generate advanced timing analysis for Level 3"""
        micro_timing = session_data.get("micro_timing_analysis", {})
        
        return {
            "cascade_timing_windows": self._analyze_cascade_timing(micro_timing),
            "state_transition_timing": self._analyze_state_transitions(session_data),
            "event_sequence_timing": self._analyze_event_sequences(session_data),
            "timing_accuracy_metrics": {
                "precision_score": 0.85,
                "timing_variance": 2.3,
                "prediction_window_accuracy": 0.92
            }
        }
    
    def _analyze_cascade_timing(self, micro_timing: Dict) -> List[Dict]:
        """Analyze cascade timing patterns"""
        cascade_events = micro_timing.get("cascade_events", [])
        timing_windows = []
        
        for event in cascade_events:
            if event.get("event_type") == "cascade_initiation":
                timing_windows.append({
                    "timestamp": event.get("timestamp"),
                    "predicted_window_start": event.get("timestamp"),
                    "predicted_window_end": event.get("timestamp"),
                    "actual_event_time": event.get("timestamp"),
                    "accuracy_score": 1.0
                })
                
        return timing_windows
    
    def _analyze_state_transitions(self, session_data: Dict) -> List[Dict]:
        """Analyze market state transitions"""
        transitions = session_data.get("phase_transitions", [])
        
        return [{
            "from_state": t.get("phase_type", "unknown"),
            "to_state": t.get("phase_type", "unknown"),
            "transition_time": t.get("start_time"),
            "duration_minutes": (
                datetime.fromisoformat(f"2025-01-01 {t.get('end_time', '00:00:00')}") -
                datetime.fromisoformat(f"2025-01-01 {t.get('start_time', '00:00:00')}")
            ).seconds // 60 if t.get("start_time") and t.get("end_time") else 0
        } for t in transitions]
    
    def _analyze_event_sequences(self, session_data: Dict) -> List[Dict]:
        """Analyze event sequences"""
        price_movements = session_data.get("price_movements", [])
        
        return [{
            "event_type": m.get("action", "unknown"),
            "timestamp": m.get("timestamp"),
            "price_level": m.get("price"),
            "context": m.get("context", ""),
            "sequence_position": i + 1
        } for i, m in enumerate(price_movements)]
    
    def _generate_pattern_analysis(self, session_data: Dict) -> Dict:
        """Generate pattern recognition analysis"""
        return {
            "identified_patterns": ["consolidation_break", "expansion_phase"],
            "pattern_confidence": 0.87,
            "historical_match_count": 15,
            "pattern_success_rate": 0.73
        }
    
    def _extract_mathematical_relationships(self, session_data: Dict) -> Dict:
        """Extract mathematical relationships"""
        return {
            "fvg_proximity_coefficient": 0.85,
            "momentum_correlation": 0.67,
            "volatility_relationship": "inverse_proportional",
            "time_decay_factor": 0.23
        }
    
    def _build_cross_session_context(self, session_data: Dict) -> Dict:
        """Build cross-session context"""
        return {
            "previous_session_influence": 0.34,
            "carryover_structures": [],
            "momentum_transfer": "moderate",
            "gap_analysis": "minimal_gap"
        }
    
    def _generate_predictive_indicators(self, session_data: Dict) -> Dict:
        """Generate predictive indicators"""
        return {
            "next_session_bias": "bullish",
            "probability_bands": {
                "10th_percentile": 23200,
                "50th_percentile": 23350,
                "90th_percentile": 23500
            },
            "key_levels_to_watch": [23300, 23400, 23500],
            "timing_predictions": []
        }
    
    def _validate_processed_data(self, processed_data: Dict, template_type: TemplateType) -> ValidationResult:
        """Validate processed data against template"""
        try:
            return self.template_validator._validate_against_template(
                processed_data, 
                self.template_validator.templates[template_type],
                template_type
            )
        except Exception as e:
            logger.warning(f"Template validation failed: {e}")
            return ValidationResult(
                is_valid=False,
                errors=[str(e)],
                warnings=[],
                compliance_score=0.5,
                missing_fields=[],
                extra_fields=[]
            )
    
    def _generate_tracker_files(self, processed_data: Dict, session_type: SessionType, date_str: str) -> List[str]:
        """Generate tracker files for the session"""
        tracker_files = []
        
        try:
            # Generate HTF Tracker
            htf_tracker = self._create_htf_tracker(processed_data, session_type, date_str)
            htf_filename = f"HTF_Tracker_{session_type.value}_{date_str}.json"
            htf_path = self.base_path / "data/trackers/htf" / htf_filename
            
            if safe_json_save(htf_tracker, str(htf_path)):
                tracker_files.append(str(htf_path))
                logger.info(f"✅ Generated HTF tracker: {htf_filename}")
            
            # Generate FVG Tracker  
            fvg_tracker = self._create_fvg_tracker(processed_data, session_type, date_str)
            fvg_filename = f"FVG_Tracker_{session_type.value}_{date_str}.json"
            fvg_path = self.base_path / "data/trackers/fvg" / fvg_filename
            
            if safe_json_save(fvg_tracker, str(fvg_path)):
                tracker_files.append(str(fvg_path))
                logger.info(f"✅ Generated FVG tracker: {fvg_filename}")
            
            # Generate Liquidity Tracker
            liq_tracker = self._create_liquidity_tracker(processed_data, session_type, date_str)
            liq_filename = f"LIQ_Tracker_{session_type.value}_{date_str}.json"
            liq_path = self.base_path / "data/trackers/liquidity" / liq_filename
            
            if safe_json_save(liq_tracker, str(liq_path)):
                tracker_files.append(str(liq_path))
                logger.info(f"✅ Generated liquidity tracker: {liq_filename}")
                
        except Exception as e:
            logger.error(f"❌ Tracker generation failed: {e}")
            
        return tracker_files
    
    def _create_htf_tracker(self, processed_data: Dict, session_type: SessionType, date_str: str) -> Dict:
        """Create HTF Context tracker"""
        structures = processed_data.get("structures_identified", {})
        session_levels = structures.get("session_levels", [])
        
        active_structures = []
        for level in session_levels:
            active_structures.append({
                "level": level.get("level", 0.0),
                "structure_type": level.get("type", "unknown"),
                "strength": 0.8,  # Default strength
                "age_minutes": 0,
                "interaction_count": len(level.get("touches", []))
            })
        
        return {
            "tracker_metadata": {
                "tracker_id": f"htf_context_{session_type.value.lower()}_{date_str}",
                "session_reference": f"{session_type.value}_{date_str}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "HTF_Context"
            },
            "active_structures": active_structures,
            "htf_distance_influence": 0.5,
            "structure_registry": {
                "total_structures": len(active_structures),
                "high_strength_count": len([s for s in active_structures if s["strength"] > 0.7]),
                "last_update": datetime.now().isoformat()
            }
        }
    
    def _create_fvg_tracker(self, processed_data: Dict, session_type: SessionType, date_str: str) -> Dict:
        """Create FVG State tracker"""
        fvg_analysis = processed_data.get("fvg_analysis", {})
        session_character = processed_data.get("session_metadata", {}).get("session_character", "neutral")
        
        # Calculate energy rate based on session character
        energy_rate = 1.0
        if "expansion" in session_character.lower():
            energy_rate = 1.3
        elif "consolidation" in session_character.lower():
            energy_rate = 0.8
            
        return {
            "tracker_metadata": {
                "tracker_id": f"fvg_state_{session_type.value.lower()}_{date_str}",
                "session_reference": f"{session_type.value}_{date_str}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "FVG_State"
            },
            "t_memory": 22.5,  # Standard T_memory value
            "energy_rate": energy_rate,
            "carryover_fvgs": fvg_analysis.get("fair_value_gaps", []),
            "session_character": session_character
        }
    
    def _create_liquidity_tracker(self, processed_data: Dict, session_type: SessionType, date_str: str) -> Dict:
        """Create Liquidity State tracker"""
        liquidity_analysis = processed_data.get("liquidity_analysis", {})
        untaken_liquidity = liquidity_analysis.get("untaken_liquidity", [])
        
        # Create registry from untaken liquidity
        registry = []
        for liq in untaken_liquidity:
            registry.append({
                "level": liq.get("level", 0.0),
                "weight": liq.get("significance", 1.0) if liq.get("significance") == "high" else 0.5,
                "side": liq.get("side", "buy"),
                "age_minutes": 0
            })
        
        return {
            "tracker_metadata": {
                "tracker_id": f"liquidity_state_{session_type.value.lower()}_{date_str}",
                "session_reference": f"{session_type.value}_{date_str}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "Liquidity_State"
            },
            "untaken_liquidity_registry": registry,
            "liquidity_gradient": 0.3,  # Default gradient
            "session_bias": "neutral"  # Default bias
        }
    
    def _create_error_result(self, session_file: str, error_message: str) -> PreprocessingResult:
        """Create error result"""
        return PreprocessingResult(
            success=False,
            session_file=session_file,
            output_files=[],
            processing_time_ms=0.0,
            validation_results=ValidationResult(
                is_valid=False,
                errors=[error_message],
                warnings=[],
                compliance_score=0.0,
                missing_fields=[],
                extra_fields=[]
            ),
            tracker_files=[],
            errors=[error_message],
            warnings=[]
        )
    
    def _update_stats(self, processing_time: float, success: bool):
        """Update processing statistics"""
        self.stats["total_processed"] += 1
        self.stats["processing_time_total_ms"] += processing_time
        
        if success:
            self.stats["successful"] += 1
        else:
            self.stats["failed"] += 1
            
        if self.stats["total_processed"] > 0:
            self.stats["average_processing_time_ms"] = (
                self.stats["processing_time_total_ms"] / self.stats["total_processed"]
            )
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get current processing statistics"""
        return {
            **self.stats,
            "success_rate": self.stats["successful"] / self.stats["total_processed"] 
                          if self.stats["total_processed"] > 0 else 0.0
        }
    
    def batch_process(self, session_files: List[str], target_level: str = "level_1") -> Dict[str, Any]:
        """
        Batch process multiple session files
        
        Args:
            session_files: List of session file paths
            target_level: Target processing level
            
        Returns:
            Batch processing results
        """
        logger.info(f"🚀 Starting batch preprocessing of {len(session_files)} files")
        
        results = {
            "total_files": len(session_files),
            "successful": 0,
            "failed": 0,
            "results": []
        }
        
        for session_file in session_files:
            result = self.process_session(session_file, target_level)
            results["results"].append(result)
            
            if result.success:
                results["successful"] += 1
            else:
                results["failed"] += 1
                
        # Generate summary
        success_rate = results["successful"] / results["total_files"] * 100 if results["total_files"] > 0 else 0
        logger.info(f"✅ Batch processing complete: {results['successful']}/{results['total_files']} successful ({success_rate:.1f}%)")
        
        return results

def create_preprocessing_agent() -> PreprocessingManager:
    """Factory function to create preprocessing agent"""
    return PreprocessingManager()

if __name__ == "__main__":
    # Test the preprocessing manager
    print("🤖 JSON Governance System - Preprocessing Manager")
    print("=" * 60)
    
    manager = create_preprocessing_agent()
    
    # Test with Asia L1 file
    test_file = "/Users/<USER>/grok-claude-automation/asia_l1_2025_07_25.json"
    
    if os.path.exists(test_file):
        result = manager.process_session(test_file, "level_1")
        
        print(f"Processing result: {'✅ Success' if result.success else '❌ Failed'}")
        print(f"Processing time: {result.processing_time_ms:.0f}ms")
        print(f"Output files: {len(result.output_files)}")
        print(f"Tracker files: {len(result.tracker_files)}")
        
        if result.validation_results:
            print(f"Validation score: {result.validation_results.compliance_score:.2f}")
    else:
        print(f"Test file not found: {test_file}")