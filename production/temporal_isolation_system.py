#!/usr/bin/env python3
"""
Temporal Isolation System
Creates bias-free snapshots for hindsight predictions by ensuring only
information available at prediction time is used. Critical for temporal
safety in backtesting scenarios.
"""

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
from src.hawkes_cascade_predictor import HawkesCascadePredictor
from src.micro_timing_analysis import MicroTimingAnalyzer
from src.shadow_validation_system import <PERSON>Valida<PERSON>
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, time
import json

@dataclass
class TemporalSnapshot:
    """Bias-free temporal snapshot at specific time point"""
    snapshot_time: str
    session_data_up_to_time: Dict[str, Any]
    available_events: List[Dict[str, Any]]
    market_state: Dict[str, Any]
    temporal_safety_validated: bool
    excluded_future_events: int

@dataclass
class BiasFreePrediction:
    """Prediction result with temporal safety validation"""
    prediction_time: str
    predicted_cascade_minutes: float
    prediction_confidence: float
    temporal_snapshot_used: TemporalSnapshot
    hawkes_parameters: Dict[str, float]
    temporal_safety_checks: Dict[str, bool]
    prediction_method: str

class TemporalIsolationSystem:
    """
    Creates bias-free temporal snapshots for hindsight prediction testing.
    Ensures no future information contamination in predictions.
    """
    
    def __init__(self):
        self.hawkes_predictor = HawkesCascadePredictor()
        self.micro_timing_analyzer = MicroTimingAnalyzer()
        
    def _timestamp_to_minutes(self, timestamp_str: str, session_start: str = "12:00:00") -> float:
        """Convert timestamp to minutes from session start."""
        
        # Parse timestamps
        try:
            # Handle timezone suffixes
            clean_timestamp = timestamp_str.split(' ')[0]
            clean_session_start = session_start.split(' ')[0]
            
            event_time = datetime.strptime(clean_timestamp, "%H:%M:%S").time()
            start_time = datetime.strptime(clean_session_start, "%H:%M:%S").time()
            
            # Convert to minutes
            event_minutes = event_time.hour * 60 + event_time.minute + event_time.second / 60.0
            start_minutes = start_time.hour * 60 + start_time.minute + start_time.second / 60.0
            
            return event_minutes - start_minutes
            
        except Exception as e:
            print(f"⚠️ Timestamp conversion error: {e}")
            return 0.0
    
    def create_temporal_snapshot(self, session_data: Dict[str, Any], 
                               cutoff_time: str) -> TemporalSnapshot:
        """
        Create bias-free temporal snapshot up to cutoff time only.
        
        Args:
            session_data: Complete session data
            cutoff_time: Time cutoff (e.g., "12:59:00" for lunch close)
            
        Returns:
            Temporal snapshot with only information available at cutoff time
        """
        
        print(f"🔍 CREATING TEMPORAL SNAPSHOT - CUTOFF: {cutoff_time}")
        print("=" * 50)
        
        # Get session start time
        session_metadata = session_data.get("session_metadata", {})
        session_start = session_metadata.get("start_time", "12:00:00")
        
        # Convert cutoff to minutes
        cutoff_minutes = self._timestamp_to_minutes(cutoff_time, session_start)
        
        print(f"📊 Session: {session_start} → Cutoff: {cutoff_time} ({cutoff_minutes:.1f} min)")
        
        # Filter price movements to cutoff time only
        all_price_movements = session_data.get("price_movements", [])
        available_events = []
        excluded_events = 0
        
        for movement in all_price_movements:
            event_minutes = self._timestamp_to_minutes(movement["timestamp"], session_start)
            
            if event_minutes <= cutoff_minutes:
                available_events.append(movement)
            else:
                excluded_events += 1
        
        print(f"✅ Available events: {len(available_events)}")
        print(f"⚠️ Excluded future events: {excluded_events}")
        
        # Calculate market state up to cutoff time only
        if available_events:
            available_prices = [float(str(event["price"]).replace(',', '')) for event in available_events]
            
            market_state = {
                "current_price": available_prices[-1],
                "high_so_far": max(available_prices),
                "low_so_far": min(available_prices),
                "range_so_far": max(available_prices) - min(available_prices),
                "session_open": available_prices[0] if available_prices else session_data.get("price_data", {}).get("open", 0),
                "events_count": len(available_events)
            }
        else:
            # Fallback to session open data
            price_data = session_data.get("price_data", {})
            market_state = {
                "current_price": price_data.get("open", 0),
                "high_so_far": price_data.get("open", 0),
                "low_so_far": price_data.get("open", 0),
                "range_so_far": 0,
                "session_open": price_data.get("open", 0),
                "events_count": 0
            }
        
        # Create bias-free session data
        session_data_snapshot = {
            "session_metadata": session_metadata,
            "price_data": {
                "open": market_state["session_open"],
                "high": market_state["high_so_far"],  # Only known high
                "low": market_state["low_so_far"],    # Only known low
                "close": market_state["current_price"], # Current price as "close"
                "range": market_state["range_so_far"],
                "session_character": "temporal_snapshot_inferred"  # Don't use final character
            },
            "price_movements": available_events,
            "temporal_isolation": {
                "snapshot_time": cutoff_time,
                "cutoff_minutes": cutoff_minutes,
                "future_events_excluded": excluded_events,
                "temporal_safety_active": True
            }
        }
        
        print(f"📈 Market State at {cutoff_time}:")
        print(f"   Current: {market_state['current_price']:.2f}")
        print(f"   Range: {market_state['range_so_far']:.2f}")
        print(f"   High: {market_state['high_so_far']:.2f}")
        print(f"   Low: {market_state['low_so_far']:.2f}")
        
        return TemporalSnapshot(
            snapshot_time=cutoff_time,
            session_data_up_to_time=session_data_snapshot,
            available_events=available_events,
            market_state=market_state,
            temporal_safety_validated=True,
            excluded_future_events=excluded_events
        )
    
    def validate_temporal_safety(self, snapshot: TemporalSnapshot, 
                                target_prediction_time: Optional[float] = None) -> Dict[str, bool]:
        """Validate temporal safety of snapshot."""
        
        checks = {
            "no_future_events": snapshot.excluded_future_events >= 0,
            "snapshot_time_valid": snapshot.snapshot_time is not None,
            "temporal_isolation_active": snapshot.session_data_up_to_time.get("temporal_isolation", {}).get("temporal_safety_active", False),
            "market_state_consistent": len(snapshot.available_events) == snapshot.market_state["events_count"]
        }
        
        # Check prediction time is before target if provided
        if target_prediction_time is not None:
            cutoff_minutes = snapshot.session_data_up_to_time.get("temporal_isolation", {}).get("cutoff_minutes", 0)
            checks["prediction_before_target"] = cutoff_minutes < target_prediction_time
        
        all_safe = all(checks.values())
        checks["overall_temporal_safety"] = all_safe
        
        print(f"🛡️ TEMPORAL SAFETY VALIDATION:")
        for check, result in checks.items():
            print(f"   {check}: {'✅' if result else '❌'}")
        
        return checks
    
    def generate_bias_free_prediction(self, session_data: Dict[str, Any],
                                    cutoff_time: str,
                                    target_cascade_time: Optional[float] = None) -> BiasFreePrediction:
        """
        Generate bias-free prediction using temporal isolation.
        
        Args:
            session_data: Complete session data
            cutoff_time: Prediction cutoff time
            target_cascade_time: Optional target time for validation
            
        Returns:
            Bias-free prediction result
        """
        
        print(f"🎯 GENERATING BIAS-FREE PREDICTION")
        print("=" * 50)
        
        # Create temporal snapshot
        snapshot = self.create_temporal_snapshot(session_data, cutoff_time)
        
        # Validate temporal safety
        safety_checks = self.validate_temporal_safety(snapshot, target_cascade_time)
        
        if not safety_checks["overall_temporal_safety"]:
            raise ValueError("Temporal safety validation failed! Prediction would be biased.")
        
        # Generate Hawkes prediction using isolated data only
        print(f"\\n🤖 Running Hawkes prediction on isolated data...")
        prediction_result = self.hawkes_predictor.predict_cascade_timing(
            snapshot.session_data_up_to_time
        )
        
        # Extract Hawkes parameters
        hawkes_params = {
            "mu": prediction_result.parameters_used.mu,
            "alpha": prediction_result.parameters_used.alpha,
            "beta": prediction_result.parameters_used.beta,
            "threshold": prediction_result.parameters_used.threshold
        }
        
        print(f"✅ BIAS-FREE PREDICTION GENERATED:")
        print(f"   Predicted cascade: {prediction_result.predicted_cascade_time:.1f} minutes")
        print(f"   Confidence: {prediction_result.prediction_confidence:.2f}")
        print(f"   Temporal safety: ✅")
        
        return BiasFreePrediction(
            prediction_time=cutoff_time,
            predicted_cascade_minutes=prediction_result.predicted_cascade_time,
            prediction_confidence=prediction_result.prediction_confidence,
            temporal_snapshot_used=snapshot,
            hawkes_parameters=hawkes_params,
            temporal_safety_checks=safety_checks,
            prediction_method="hawkes_process_temporal_isolation"
        )

def test_temporal_isolation_on_lunch():
    """Test temporal isolation system on Friday lunch data."""
    
    print("🧪 TESTING TEMPORAL ISOLATION SYSTEM")
    print("=" * 60)
    
    # Load Friday lunch session
    lunch_data = load_json_data('Lunch_Lvl-1_2025_07_25.json')
    
    # Initialize temporal isolation system
    temporal_system = TemporalIsolationSystem()
    
    # Generate PM prediction from lunch close (12:59:00)
    pm_prediction = temporal_system.generate_bias_free_prediction(
        session_data=lunch_data,
        cutoff_time="12:59:00"  # Lunch session close
    )
    
    # Save prediction results
    prediction_results = {
        "temporal_isolation_prediction": {
            "session_analyzed": "Lunch_Lvl-1_2025_07_25.json",
            "prediction_time": pm_prediction.prediction_time,
            "predicted_pm_cascade_minutes": pm_prediction.predicted_cascade_minutes,
            "prediction_confidence": pm_prediction.prediction_confidence,
            "hawkes_parameters": pm_prediction.hawkes_parameters,
            "temporal_safety_validated": pm_prediction.temporal_safety_checks["overall_temporal_safety"],
            "excluded_future_events": pm_prediction.temporal_snapshot_used.excluded_future_events,
            "method": pm_prediction.prediction_method,
            "generation_timestamp": datetime.now().isoformat()
        },
        "temporal_snapshot_summary": {
            "snapshot_time": pm_prediction.temporal_snapshot_used.snapshot_time,
            "available_events": len(pm_prediction.temporal_snapshot_used.available_events),
            "market_state": pm_prediction.temporal_snapshot_used.market_state,
            "temporal_safety_checks": pm_prediction.temporal_safety_checks
        }
    }
    
    save_json_data(prediction_results, 'PM_Prediction_from_Lunch_Temporal_Isolation.json')
    
    print(f"\\n✅ TEMPORAL ISOLATION TEST COMPLETE:")
    print(f"   PM Cascade Prediction: {pm_prediction.predicted_cascade_minutes:.1f} minutes")
    print(f"   Prediction Confidence: {pm_prediction.prediction_confidence:.2f}")
    print(f"   Temporal Safety: {'✅' if pm_prediction.temporal_safety_checks['overall_temporal_safety'] else '❌'}")
    print(f"   Results saved: PM_Prediction_from_Lunch_Temporal_Isolation.json")
    
    return pm_prediction

if __name__ == "__main__":
    test_temporal_isolation_on_lunch()