#!/usr/bin/env python3
"""
Monte Carlo Validation Package for Grok 4
========================================

Complete package for Grok 4's in-house Python execution to validate and 
fine-tune our production timing system. Based on Opus 4's three-phase approach.

INSTRUCTIONS FOR GROK 4:
Run this code to validate our market timing system's robustness through 
Monte Carlo simulation. Focus on maintaining 0.58-minute accuracy while 
testing parameter variations.
"""

import numpy as np
import json
from datetime import datetime
from typing import Dict, List, Tuple

# VALIDATED SESSION DATA (Ground Truth) - Updated with Forex Factory News Analysis
VALIDATED_SESSIONS = {
    'asia_nyam': {
        'volatility': 0.087, 
        'gamma': 2.261, 
        'distance': 4.0, 
        'actual_minutes': 0,
        'session_type': 'inter_regional',
        'news_impacted': False,
        'session_character': 'expansion_consolidation_retracement'
    },
    'nyam_nypm_july23': {
        'volatility': 0.14675, 
        'gamma': 2.44025, 
        'distance': 1.0, 
        'actual_minutes': 0,
        'session_type': 'contiguous',
        'news_impacted': False,  # July 23 - only medium impact events (home sales 10:00)
        'session_character': 'expansion_consolidation_recovery'
    },
    'asia_london': {
        'volatility': 0.04875, 
        'gamma': 2.14625, 
        'distance': 3.0, 
        'actual_minutes': 0,
        'session_type': 'inter_regional',
        'news_impacted': False,
        'session_character': 'consolidation_expansion_retracement'
    },
    'london_premarket': {
        'volatility': 0.04475, 
        'gamma': 2.13425, 
        'distance': 2.5, 
        'actual_minutes': 1,
        'session_type': 'semi_contiguous',
        'news_impacted': False,
        'session_character': 'expansion_consolidation_recovery'
    },
    'premarket_nyam': {
        'volatility': 0.053, 
        'gamma': 2.0, 
        'distance': 1.0, 
        'actual_minutes': 0,
        'session_type': 'contiguous',
        'news_impacted': False,
        'session_character': 'accumulative_with_gap_redeliveries'
    },
    'nyam_nypm_july22': {
        'volatility': 0.24175, 
        'gamma': 2.0, 
        'distance': 1.0, 
        'actual_minutes': 0,
        'session_type': 'contiguous',
        'news_impacted': True,  # July 22 - Fed Chair Powell Speaks 08:30
        'news_type': 'fed_chair_speech',
        'news_timing_offset': 60,  # 60 minutes before session
        'session_character': 'explosive_with_major_liquidity_sweeps'
    },
    
    # NEW HIGH-IMPACT NEWS SESSIONS FROM FOREX FACTORY ANALYSIS
    'july_15_cpi_nyam': {
        'volatility': 0.126,  # From July 15 CPI session analysis
        'gamma': 2.3,  # Estimated from institutional distribution pattern
        'distance': 1.0,
        'actual_minutes': 0,  # Immediate session open expansion
        'session_type': 'contiguous',
        'news_impacted': True,
        'news_type': 'cpi_release',
        'news_timing_offset': 60,  # CPI 08:30, NY open 09:30
        'news_impact_level': 'HIGH',
        'volatility_multiplier': 1.3,
        'session_character': 'institutional_distribution_with_multiple_liquidity_sweeps',
        'session_range': 126.75,
        'source_file': '2025_07_15_nyam_l1.json'
    },
    'july_16_ppi_nyam': {
        'volatility': 0.254,  # EXTREME volatility from PPI release
        'gamma': 2.8,  # High gamma from extreme range expansion  
        'distance': 1.0,
        'actual_minutes': 0,  # Immediate extreme volatility
        'session_type': 'contiguous',
        'news_impacted': True,
        'news_type': 'ppi_release',
        'news_timing_offset': 60,  # PPI 08:30, NY open 09:30
        'news_impact_level': 'EXTREME',
        'volatility_multiplier': 1.8,
        'session_character': 'extreme_volatility_with_deep_liquidation',
        'session_range': 254.00,
        'source_file': '2025_07_16_nyam_l1.json'
    },
    'july_24_unemployment_pmi_nyam': {
        'volatility': 0.103,
        'gamma': 2.4,
        'distance': 1.0,
        'actual_minutes': 0,  # Session open expansion
        'session_type': 'contiguous',
        'news_impacted': True,
        'news_type': 'unemployment_claims_flash_pmi',
        'news_timing_offset': 60,  # Unemployment 08:30, NY open 09:30
        'intra_session_news': True,  # Flash PMI at 09:45 (mid-session)
        'intra_session_timing': 15,  # 15 minutes after session start
        'news_impact_level': 'HIGH',
        'volatility_multiplier': 1.2,
        'session_character': 'expansion_consolidation_recovery',
        'session_range': 102.75,
        'source_file': 'nyam_session_l1_2025_07_24.json'
    }
}

# CURRENT PROVEN FORMULA
def universal_timing_formula(volatility: float, distance: float, 
                           news_impacted: bool = False, news_data: Dict = None) -> Dict:
    """
    Enhanced proven formula with Forex Factory news integration.
    Achieves 0.39-minute average error with 100% coverage (Grok 4 validated).
    Note: Gamma parameter removed as unused in current formula.
    """
    # Universal base (proven)
    base = 0.5
    
    # Volatility factor
    volatility_factor = 1 + volatility * 0.1
    
    # Distance factor (minimal - avoid session distance scaling issues)
    distance_factor = 1.0  # Keep simple initially
    
    # Enhanced news impact handling
    news_multiplier = 1.0
    if news_impacted and news_data:
        # Apply news-specific multipliers based on Forex Factory analysis
        if news_data.get('news_impact_level') == 'EXTREME':
            news_multiplier = 0.3  # Extreme volatility = immediate events
        elif news_data.get('news_impact_level') == 'HIGH':
            news_multiplier = 0.5  # High impact = faster events
        elif news_data.get('news_type') == 'fed_chair_speech':
            news_multiplier = 0.6  # Fed speeches = quicker reactions
        else:
            news_multiplier = 0.67  # Default news impact
    elif news_impacted:
        news_multiplier = 0.67  # Default if no detailed news data
    
    # Intra-session news adjustment (optimized based on Grok 4 validation)
    intra_session_factor = 1.0
    if news_data and news_data.get('intra_session_news'):
        intra_session_factor = 0.75  # Further acceleration for dual-news events (July 24 type)
    
    # Prediction
    prediction = base * volatility_factor * distance_factor * news_multiplier * intra_session_factor
    
    # Confidence interval (empirically calibrated for 100% coverage)
    confidence_width = prediction * 2.5
    
    return {
        'predicted_minutes': prediction,
        'confidence_lower': max(0, prediction - confidence_width),
        'confidence_upper': prediction + confidence_width,
        'components': {
            'base': base,
            'volatility_factor': volatility_factor,
            'distance_factor': distance_factor,
            'news_multiplier': news_multiplier,
            'intra_session_factor': intra_session_factor
        },
        'news_analysis': news_data if news_data else {}
    }

# PROPOSED SESSION-SPECIFIC BASES (for testing only)
SESSION_SPECIFIC_BASES = {
    'asia': 0.75,      # Hypothesis: slower development
    'london': 0.4,     # Hypothesis: quick liquidity influx  
    'ny': 0.3,         # Hypothesis: fastest expansion
    'premarket': 0.6   # Hypothesis: moderate development
}

def session_specific_formula(session_region: str, volatility: float, gamma: float, 
                           distance: float, news_impacted: bool = False) -> Dict:
    """
    Alternative formula with session-specific bases (requires validation).
    """
    # Session-specific base
    base = SESSION_SPECIFIC_BASES.get(session_region, 0.5)
    
    # Same factors as universal
    volatility_factor = 1 + volatility * 0.1
    distance_factor = 1.0
    news_multiplier = 0.67 if news_impacted else 1.0
    
    prediction = base * volatility_factor * distance_factor * news_multiplier
    confidence_width = prediction * 2.5
    
    return {
        'predicted_minutes': prediction,
        'confidence_lower': max(0, prediction - confidence_width),
        'confidence_upper': prediction + confidence_width,
        'session_base_used': base,
        'session_region': session_region
    }

# MONTE CARLO VALIDATION FUNCTIONS

def phase1_universal_base_robustness(n_simulations: int = 1000) -> Dict:
    """
    Phase 1: Validate universal 0.5 base robustness with conservative noise.
    Target: >90% within ±1.5 minutes to maintain current standards.
    """
    
    results = {}
    overall_successes = 0
    total_tests = 0
    
    print("=== PHASE 1: Universal Base Robustness Testing ===")
    
    for session_name, session_data in VALIDATED_SESSIONS.items():
        successes = 0
        errors = []
        
        for _ in range(n_simulations):
            # Conservative noise (10% volatility only - gamma removed as unused)
            vol_noise = np.random.normal(0, 0.1 * session_data['volatility'])
            
            # Bounded perturbations
            vol_perturbed = np.clip(session_data['volatility'] + vol_noise, 0.01, 0.30)
            
            # Prepare news data for enhanced formula
            news_data = {}
            if session_data['news_impacted']:
                news_data = {
                    'news_type': session_data.get('news_type', 'unknown'),
                    'news_impact_level': session_data.get('news_impact_level', 'MEDIUM'),
                    'news_timing_offset': session_data.get('news_timing_offset', 60),
                    'intra_session_news': session_data.get('intra_session_news', False),
                    'volatility_multiplier': session_data.get('volatility_multiplier', 1.0)
                }
            
            # Make prediction with enhanced news handling
            prediction = universal_timing_formula(
                vol_perturbed, session_data['distance'],
                session_data['news_impacted'], news_data
            )
            
            # Calculate error
            error = abs(prediction['predicted_minutes'] - session_data['actual_minutes'])
            errors.append(error)
            
            # Success if within ±1.5 minutes
            if error <= 1.5:
                successes += 1
        
        # Session results
        success_rate = successes / n_simulations
        avg_error = np.mean(errors)
        
        results[session_name] = {
            'success_rate': success_rate,
            'average_error': avg_error,
            'passes_threshold': success_rate >= 0.90
        }
        
        overall_successes += successes
        total_tests += n_simulations
        
        print(f"{session_name}: {success_rate:.1%} success, {avg_error:.3f} avg error")
    
    overall_success_rate = overall_successes / total_tests
    results['overall'] = {
        'success_rate': overall_success_rate,
        'target_met': overall_success_rate >= 0.90,
        'baseline_accuracy': 0.58  # Current proven performance
    }
    
    print(f"\nOverall Success Rate: {overall_success_rate:.1%} (Target: ≥90%)")
    
    return results

def phase2_distance_sensitivity(n_simulations: int = 1000) -> Dict:
    """
    Phase 2: Test if distance factors require different handling.
    Only run if Phase 1 shows distance-correlated patterns.
    """
    
    print("\n=== PHASE 2: Distance Factor Sensitivity ===")
    
    # Group sessions by distance
    distance_groups = {
        'contiguous': [],      # distance = 1.0
        'semi_contiguous': [], # distance = 1.5-2.5  
        'inter_regional': []   # distance >= 3.0
    }
    
    for session_name, session_data in VALIDATED_SESSIONS.items():
        if session_data['distance'] == 1.0:
            distance_groups['contiguous'].append((session_name, session_data))
        elif 1.5 <= session_data['distance'] <= 2.5:
            distance_groups['semi_contiguous'].append((session_name, session_data))
        else:
            distance_groups['inter_regional'].append((session_name, session_data))
    
    results = {}
    
    for group_name, sessions in distance_groups.items():
        if not sessions:
            continue
            
        group_errors = []
        
        for session_name, session_data in sessions:
            for _ in range(n_simulations // len(sessions)):
                # Same noise as Phase 1
                vol_noise = np.random.normal(0, 0.1 * session_data['volatility'])
                vol_perturbed = np.clip(session_data['volatility'] + vol_noise, 0.01, 0.30)
                
                prediction = universal_timing_formula(
                    vol_perturbed, session_data['distance'],
                    session_data['news_impacted']
                )
                
                error = abs(prediction['predicted_minutes'] - session_data['actual_minutes'])
                group_errors.append(error)
        
        results[group_name] = {
            'average_error': np.mean(group_errors),
            'error_std': np.std(group_errors),
            'sample_size': len(group_errors)
        }
        
        print(f"{group_name}: {np.mean(group_errors):.3f} ± {np.std(group_errors):.3f} minutes")
    
    return results

def phase3_session_specific_testing(baseline_accuracy: float = 0.58) -> Dict:
    """
    Phase 3: Test session-specific bases vs universal base.
    Only adopt if >20% improvement over baseline.
    """
    
    print("\n=== PHASE 3: Session-Specific Base Testing ===")
    
    # Map sessions to regions (including new news sessions)
    session_to_region = {
        'asia_nyam': 'asia',
        'asia_london': 'asia', 
        'london_premarket': 'london',
        'premarket_nyam': 'premarket',
        'nyam_nypm_july23': 'ny',
        'nyam_nypm_july22': 'ny',
        # New news-impacted sessions (all NY sessions based on timing)
        'july_15_cpi_nyam': 'ny',
        'july_16_ppi_nyam': 'ny', 
        'july_24_unemployment_pmi_nyam': 'ny'
    }
    
    results = {
        'universal_baseline': baseline_accuracy,
        'session_specific_results': {},
        'recommendation': 'universal'  # Default
    }
    
    universal_errors = []
    session_specific_errors = []
    
    for session_name, session_data in VALIDATED_SESSIONS.items():
        region = session_to_region.get(session_name, 'unknown')
        
        # Universal prediction
        universal_pred = universal_timing_formula(
            session_data['volatility'], session_data['distance'], 
            session_data['news_impacted']
        )
        universal_error = abs(universal_pred['predicted_minutes'] - session_data['actual_minutes'])
        universal_errors.append(universal_error)
        
        # Session-specific prediction (still uses gamma in legacy formula)
        session_pred = session_specific_formula(
            region, session_data['volatility'], session_data['gamma'],
            session_data['distance'], session_data['news_impacted']
        )
        session_error = abs(session_pred['predicted_minutes'] - session_data['actual_minutes'])
        session_specific_errors.append(session_error)
        
        print(f"{session_name} ({region}): Universal={universal_error:.3f}, Specific={session_error:.3f}")
    
    universal_avg = np.mean(universal_errors)
    session_avg = np.mean(session_specific_errors)
    improvement = (universal_avg - session_avg) / universal_avg
    
    results['session_specific_results'] = {
        'universal_average_error': universal_avg,
        'session_specific_average_error': session_avg,
        'improvement_percentage': improvement * 100,
        'meets_threshold': improvement > 0.20  # >20% improvement required
    }
    
    if improvement > 0.20:
        results['recommendation'] = 'session_specific'
        print(f"\n✅ Session-specific bases show {improvement:.1%} improvement - RECOMMEND ADOPTION")
    else:
        print(f"\n❌ Session-specific improvement only {improvement:.1%} - KEEP UNIVERSAL BASE")
    
    return results

def comprehensive_validation() -> Dict:
    """
    Run all three phases and provide final recommendations.
    """
    
    print("🎯 Monte Carlo Validation of Market Timing System")
    print("=" * 60)
    
    # Phase 1: Universal base robustness
    phase1_results = phase1_universal_base_robustness()
    
    if not phase1_results['overall']['target_met']:
        print("\n❌ Phase 1 FAILED - Universal base needs recalibration before proceeding")
        return {'status': 'failed', 'phase1': phase1_results}
    
    print("\n✅ Phase 1 PASSED - Universal base is robust")
    
    # Phase 2: Distance sensitivity
    phase2_results = phase2_distance_sensitivity()
    
    # Phase 3: Session-specific testing  
    phase3_results = phase3_session_specific_testing()
    
    # Final recommendations
    final_recommendation = {
        'timestamp': datetime.now().isoformat(),
        'status': 'completed',
        'phases': {
            'phase1_robustness': phase1_results,
            'phase2_distance': phase2_results, 
            'phase3_session_specific': phase3_results
        },
        'final_recommendations': {
            'base_formula': phase3_results['recommendation'],
            'news_integration': 'implement' if any(s['news_impacted'] for s in VALIDATED_SESSIONS.values()) else 'skip',
            'confidence_interval_multiplier': 2.5,  # Maintains 100% coverage
            'production_ready': phase1_results['overall']['target_met']
        }
    }
    
    print(f"\n🏆 FINAL RECOMMENDATION: Use {phase3_results['recommendation']} base formula")
    
    return final_recommendation

# MAIN EXECUTION FOR GROK 4
if __name__ == "__main__":
    # Run comprehensive validation
    results = comprehensive_validation()
    
    # Display key metrics
    print(f"\n📊 KEY METRICS:")
    print(f"Phase 1 Success Rate: {results['phases']['phase1_robustness']['overall']['success_rate']:.1%}")
    print(f"Recommended Formula: {results['final_recommendations']['base_formula']}")
    print(f"Production Ready: {results['final_recommendations']['production_ready']}")
    
    # Save results for analysis
    print(f"\n💾 Results saved in 'results' variable for further analysis")