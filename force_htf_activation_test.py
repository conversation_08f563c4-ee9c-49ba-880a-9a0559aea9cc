#!/usr/bin/env python3
"""
Force HTF Activation Test - Manually set HTF intensity > 0.5 threshold
Verify complete data flow from HTF Master → Session Subordinate → Coupled Intensity
"""

import sys
sys.path.append('/Users/<USER>/grok-claude-automation/src')

import json
import math
from datetime import datetime, timedelta
from pathlib import Path

# Import our fixed components
from htf_master_controller_enhanced import HTFMasterControllerEnhanced, ActivationSignal
from session_subordinate_executor import SessionHawkesExecutor, CascadePrediction
from fractal_cascade_integrator import FractalCascadeIntegrator

def create_high_intensity_htf_context():
    """Create HTF context with manually high intensity."""
    return {
        "htf_events": [
            {
                "event_type": "level_takeout",
                "level": 23712.0,
                "htf_significance": "asia_session_low_violated",  # Uses 2.2x multiplier
                "reference_date": "2025-07-31", 
                "violated_on": "2025-07-31",
                "origin_session": "Asia_2025-07-31",
                "taken_by_session": "London_2025-07-31",
                "timestamp": datetime.now().isoformat(),
                "confidence": 0.95,
                "processed_by": "Force_Test"
            },
            {
                "event_type": "level_takeout", 
                "level": 23698.5,
                "htf_significance": "london_session_high_violated",  # Uses 2.3x multiplier
                "reference_date": "2025-07-30",
                "violated_on": "2025-07-31", 
                "origin_session": "London_2025-07-30",
                "taken_by_session": "London_2025-07-31",
                "timestamp": (datetime.now() - timedelta(hours=2)).isoformat(),
                "confidence": 0.92,
                "processed_by": "Force_Test"
            }
        ],
        "last_update": datetime.now().isoformat(),
        "intelligence_processed": True
    }

def test_htf_master_activation():
    """Test HTF Master Controller activation signal generation."""
    print("🔥 HTF MASTER CONTROLLER ACTIVATION TEST")
    print("=" * 60)
    
    # Initialize enhanced controller
    controller = HTFMasterControllerEnhanced()
    
    # Create high-intensity HTF context
    htf_context = create_high_intensity_htf_context()
    
    # Save test HTF context file
    htf_file = Path("/Users/<USER>/grok-claude-automation/data/htf/HTF_Context_TEST_2025_07_31.json")
    htf_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(htf_file, 'w') as f:
        json.dump(htf_context, f, indent=2)
    
    print(f"✅ Created test HTF context: {htf_file.name}")
    
    # Load intelligence HTF events
    controller.load_intelligence_htf_events(force_reload=True)
    
    # Calculate current HTF intensity (will be artificially high due to our test events)
    current_time = datetime.now()
    
    # Manually calculate high HTF intensity for test
    # Use our test events to simulate high intensity
    mu_h = 0.02
    alpha_h = 35.51
    beta_h = 0.00442
    
    # Calculate intensity from our test events
    total_intensity = mu_h
    for event in htf_context["htf_events"]:
        # Time since event (simulate recent events)
        hours_ago = 2.0  # 2 hours ago
        magnitude = 2.2 if "asia_session_low" in event["htf_significance"] else 2.3
        excitation = alpha_h * math.exp(-beta_h * hours_ago) * magnitude
        total_intensity += excitation
    
    htf_intensity = total_intensity
    
    print(f"\n📊 HTF INTENSITY CALCULATION:")
    print(f"   Current HTF Intensity: {htf_intensity:.4f}")
    print(f"   Activation Threshold: {controller.threshold_h}")
    print(f"   Threshold Ratio: {htf_intensity / controller.threshold_h:.1f}x")
    print(f"   Status: {'🟢 ACTIVE' if htf_intensity > controller.threshold_h else '🔴 DORMANT'}")
    
    # Generate activation signal
    activation_signal = controller.generate_intelligence_activation_signal(current_time)
    
    if activation_signal:
        print(f"\n🚀 ACTIVATION SIGNAL GENERATED:")
        print(f"   Target Sessions: {activation_signal.target_sessions}")
        print(f"   Cascade Type: {activation_signal.cascade_type}")
        print(f"   HTF Intensity: {activation_signal.htf_intensity:.4f}")
        print(f"   Confidence Boost: {activation_signal.confidence_boost:.2f}")
        print(f"   Parameter Adjustments:")
        for param, value in activation_signal.param_adjustments.items():
            print(f"      • {param}: {value}")
        print(f"   Intelligence Events: {len(activation_signal.intelligence_events)}")
    else:
        print(f"\n❌ NO ACTIVATION SIGNAL GENERATED")
        return None, htf_intensity
    
    # Clean up test file
    htf_file.unlink()
    
    return activation_signal, htf_intensity

def test_session_subordinate_response(activation_signal):
    """Test Session Subordinate Executor response to activation signal."""
    print(f"\n🎯 SESSION SUBORDINATE EXECUTOR TEST")
    print("=" * 60)
    
    if not activation_signal:
        print("❌ Cannot test subordinate - no activation signal")
        return None
    
    # Initialize subordinate executor
    executor = SessionHawkesExecutor()
    
    print(f"📋 INITIAL STATE:")
    status = executor.get_prediction_status()
    print(f"   Activation Status: {status['activation_status']}")
    print(f"   Current Intensity: {status['current_intensity']:.4f}")
    print(f"   Threshold: {status['threshold']:.4f}")
    
    # Convert activation signal to dict for executor
    htf_signal = {
        "target_sessions": activation_signal.target_sessions,
        "activation_window": "current_test",
        "cascade_type": activation_signal.cascade_type,
        "param_adjustments": activation_signal.param_adjustments,
        "confidence_boost": activation_signal.confidence_boost,
        "htf_intensity": activation_signal.htf_intensity,
        "activation_time": datetime.now().isoformat()
    }
    
    # Send activation signal to subordinate
    print(f"\n📡 SENDING ACTIVATION SIGNAL...")
    cascade_prediction = executor.await_activation(htf_signal)
    
    if cascade_prediction:
        print(f"\n✅ CASCADE PREDICTION GENERATED:")
        print(f"   Estimated Time: {cascade_prediction.estimated_time.strftime('%H:%M:%S')} ET")
        print(f"   Cascade Type: {cascade_prediction.cascade_type}")
        print(f"   Time to Cascade: {cascade_prediction.time_to_cascade_minutes:.1f} minutes")
        print(f"   Confidence: {cascade_prediction.confidence:.1%}")
        print(f"   Intensity at Prediction: {cascade_prediction.intensity_at_prediction:.4f}")
        print(f"   Method: {cascade_prediction.method}")
        print(f"   Target Zones: {cascade_prediction.target_zones[:2]}")
    else:
        print(f"\n❌ NO CASCADE PREDICTION GENERATED")
        return None
    
    # Check parameter adjustments applied
    print(f"\n🔧 PARAMETER ADJUSTMENTS APPLIED:")
    status_after = executor.get_prediction_status()
    print(f"   New Baseline Intensity: {status_after['parameters']['baseline_intensity']:.6f}")
    print(f"   New Decay Rate: {status_after['parameters']['decay_rate']:.6f}")
    print(f"   New Threshold: {status_after['parameters']['threshold']:.6f}")
    print(f"   Enhancement Factor: {status_after['parameters']['baseline_intensity'] / executor.default_params['baseline_intensity']:.2f}x")
    
    return cascade_prediction

def test_coupled_intensity_calculation(activation_signal, htf_intensity):
    """Test coupled intensity calculation λ_total(t) = λ_session(t) + γ(t)·λ_HTF(t)."""
    print(f"\n⚡ COUPLED INTENSITY CALCULATION TEST")
    print("=" * 60)
    
    if not activation_signal:
        print("❌ Cannot test coupling - no activation signal")
        return
    
    # Simulate session-level intensity
    lambda_session = 0.245  # Typical session intensity near threshold
    
    # HTF intensity from our calculation
    lambda_htf = htf_intensity
    
    # Coupling factor γ(t) - enhanced due to activation
    gamma_base = 0.3  # Base coupling strength
    
    # Enhanced gamma due to HTF activation
    htf_boost = activation_signal.param_adjustments.get("baseline_boost", 1.0)
    gamma_enhanced = gamma_base * htf_boost
    
    # Calculate coupled intensity
    lambda_total = lambda_session + (gamma_enhanced * lambda_htf)
    
    print(f"📊 COUPLING CALCULATION:")
    print(f"   λ_session(t): {lambda_session:.4f}")
    print(f"   λ_HTF(t): {lambda_htf:.4f}") 
    print(f"   γ_base: {gamma_base:.3f}")
    print(f"   HTF boost factor: {htf_boost:.2f}")
    print(f"   γ_enhanced: {gamma_enhanced:.3f}")
    print(f"   λ_total(t) = λ_session + γ·λ_HTF")
    print(f"   λ_total(t) = {lambda_session:.4f} + ({gamma_enhanced:.3f} × {lambda_htf:.4f})")
    print(f"   λ_total(t) = {lambda_total:.4f}")
    
    # Check if this would trigger cascade
    cascade_threshold = 0.5
    print(f"\n🎯 CASCADE EVALUATION:")
    print(f"   Cascade Threshold: {cascade_threshold}")
    print(f"   Coupled Intensity: {lambda_total:.4f}")
    print(f"   Threshold Ratio: {lambda_total / cascade_threshold:.1f}x")
    print(f"   Cascade Status: {'🟢 TRIGGERED' if lambda_total > cascade_threshold else '🔴 BELOW THRESHOLD'}")
    
    return lambda_total

def run_complete_activation_test():
    """Run complete HTF activation test."""
    print("🧪 COMPLETE HTF ACTIVATION PIPELINE TEST")
    print("=" * 80)
    print("Testing: HTF Master → Session Subordinate → Coupled Intensity")
    print("Goal: Verify data flow when HTF intensity > 0.5 threshold\n")
    
    # Phase 1: HTF Master Controller
    activation_signal, htf_intensity = test_htf_master_activation()
    
    if not activation_signal:
        print("\n❌ TEST FAILED: HTF Master did not generate activation signal")
        return
    
    # Phase 2: Session Subordinate Executor  
    cascade_prediction = test_session_subordinate_response(activation_signal)
    
    if not cascade_prediction:
        print("\n❌ TEST FAILED: Session Subordinate did not generate prediction")
        return
    
    # Phase 3: Coupled Intensity Calculation
    lambda_total = test_coupled_intensity_calculation(activation_signal, htf_intensity)
    
    # Summary
    print(f"\n🏆 TEST RESULTS SUMMARY")
    print("=" * 40)
    print(f"✅ HTF Intensity: {htf_intensity:.4f} >> 0.5 threshold")
    print(f"✅ Activation Signal: Generated with {len(activation_signal.target_sessions)} target sessions")
    print(f"✅ Session Response: {cascade_prediction.time_to_cascade_minutes:.1f}min cascade prediction") 
    print(f"✅ Parameter Boost: {activation_signal.param_adjustments.get('baseline_boost', 1.0):.2f}x enhancement")
    print(f"✅ Coupled Intensity: {lambda_total:.4f} ({'TRIGGERED' if lambda_total > 0.5 else 'BELOW THRESHOLD'})")
    
    expected_vs_actual = {
        "expected_htf_intensity": "> 0.5",
        "actual_htf_intensity": f"{htf_intensity:.4f}",
        "expected_activation": "Yes",
        "actual_activation": "Yes" if activation_signal else "No",
        "expected_subordinate_response": "Cascade prediction",
        "actual_subordinate_response": f"{cascade_prediction.time_to_cascade_minutes:.1f}min prediction" if cascade_prediction else "None",
        "expected_coupling": "Enhanced λ_total",
        "actual_coupling": f"{lambda_total:.4f}"
    }
    
    print(f"\n📋 EXPECTED VS ACTUAL:")
    for key, value in expected_vs_actual.items():
        expected = key.startswith("expected_")
        if expected:
            actual_key = key.replace("expected_", "actual_")
            actual_value = expected_vs_actual[actual_key]
            category = key.replace("expected_", "").replace("_", " ").title()
            print(f"   {category}:")
            print(f"      Expected: {value}")
            print(f"      Actual: {actual_value}")
            # Simple status check
            is_success = ('Yes' in str(actual_value) or 
                         any(c.isdigit() for c in str(actual_value)) and 
                         any(word in str(actual_value) for word in ['prediction', 'TRIGGERED']) or
                         'min' in str(actual_value))
            print(f"      Status: {'✅' if is_success else '❌'}")

if __name__ == "__main__":
    run_complete_activation_test()