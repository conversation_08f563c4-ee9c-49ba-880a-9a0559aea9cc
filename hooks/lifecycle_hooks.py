#!/usr/bin/env python3
"""
JSON Governance System - File Lifecycle Hooks
Handles pre-creation validation, post-creation processing, and dependency tracking
"""

import json
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass

from src.utils import get_logger, safe_json_load, safe_json_save
from governance.naming_conventions import NamingConventionValidator, SessionType, FileType, TrackerType
from governance.template_validator import TemplateValidator, TemplateType, ValidationResult

logger = get_logger(__name__)

@dataclass
class LifecycleHookResult:
    """Result of lifecycle hook execution"""
    success: bool
    hook_type: str
    actions_taken: List[str]
    files_created: List[str]
    files_modified: List[str]
    errors: List[str]
    warnings: List[str]

class FileLifecycleManager:
    """
    Manages file lifecycle hooks for JSON governance system
    Handles creation, validation, and dependency management
    """
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.naming_validator = NamingConventionValidator()
        self.template_validator = TemplateValidator()
        
        # Dependency tracking
        self.dependency_map = {
            # Session files depend on tracker files for enhanced processing
            FileType.GROK_ENHANCED: [TrackerType.HTF_TRACKER, TrackerType.FVG_TRACKER, TrackerType.LIQ_TRACKER],
            FileType.EVENT_TIMING_ENHANCED: [TrackerType.HTF_TRACKER, TrackerType.FVG_TRACKER],
            FileType.MONTE_CARLO: [TrackerType.FVG_TRACKER, TrackerType.LIQ_TRACKER],
            
            # Level 3 depends on Level 1
            FileType.LVL_3: [FileType.LVL_1],
            
            # Predictions depend on enhanced data
            FileType.PREDICTION: [FileType.GROK_ENHANCED]
        }
        
    def pre_creation_validator_hook(self, file_path: str, file_data: Dict) -> LifecycleHookResult:
        """
        Validate file before creation
        Ensures template compliance and naming conventions
        
        Args:
            file_path: Intended file path
            file_data: File data to validate
            
        Returns:
            LifecycleHookResult with validation results
        """
        logger.info(f"🔍 Pre-creation validation: {Path(file_path).name}")
        
        actions_taken = []
        errors = []
        warnings = []
        
        try:
            filename = Path(file_path).name
            
            # Validate naming convention
            is_valid_name, name_error, components = self.naming_validator.validate_filename(filename)
            if not is_valid_name:
                errors.append(f"Naming convention violation: {name_error}")
                
                # Try to suggest correction
                suggested_name = self.naming_validator.get_suggested_name(filename)
                if suggested_name:
                    warnings.append(f"Suggested filename: {suggested_name}")
                    actions_taken.append("Generated naming suggestion")
            else:
                actions_taken.append("Naming convention validated")
                
            # Validate template compliance
            if components:
                template_type = self._determine_template_type(components)
                if template_type:
                    validation_result = self.template_validator._validate_against_template(
                        file_data, 
                        self.template_validator.templates.get(template_type, {}),
                        template_type
                    )
                    
                    if not validation_result.is_valid:
                        errors.extend(validation_result.errors)
                        warnings.extend(validation_result.warnings)
                    else:
                        actions_taken.append("Template compliance validated")
                        
            # Check dependencies
            dependency_issues = self._check_dependencies(file_path, file_data, components)
            if dependency_issues:
                warnings.extend(dependency_issues)
                actions_taken.append("Dependency check performed")
                
            # Validate target directory
            target_dir = Path(file_path).parent
            if not target_dir.exists():
                warnings.append(f"Target directory does not exist: {target_dir}")
                actions_taken.append("Directory existence check")
                
            success = len(errors) == 0
            
            return LifecycleHookResult(
                success=success,
                hook_type="pre_creation_validation",
                actions_taken=actions_taken,
                files_created=[],
                files_modified=[],
                errors=errors,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"❌ Pre-creation validation failed: {e}")
            return LifecycleHookResult(
                success=False,
                hook_type="pre_creation_validation",
                actions_taken=["Exception occurred"],
                files_created=[],
                files_modified=[],
                errors=[str(e)],
                warnings=[]
            )
    
    def post_creation_processor_hook(self, file_path: str, file_data: Dict) -> LifecycleHookResult:
        """
        Process file after creation
        Generates tracker files, updates indexes, and manages dependencies
        
        Args:
            file_path: Created file path
            file_data: File data
            
        Returns:
            LifecycleHookResult with processing results
        """
        logger.info(f"⚙️ Post-creation processing: {Path(file_path).name}")
        
        actions_taken = []
        files_created = []
        files_modified = []
        errors = []
        warnings = []
        
        try:
            filename = Path(file_path).name
            is_valid, _, components = self.naming_validator.validate_filename(filename)
            
            if not is_valid or not components:
                errors.append("Invalid filename - cannot process")
                return self._create_error_result("post_creation_processing", errors)
                
            # Auto-generate tracker files if this is a session file
            if isinstance(components.file_type, FileType) and components.file_type in [FileType.LVL_1, FileType.GROK_ENHANCED]:
                tracker_files = self._auto_generate_tracker_files(file_data, components)
                files_created.extend(tracker_files)
                actions_taken.append(f"Generated {len(tracker_files)} tracker files")
                
            # Update dependency tracking
            self._update_dependency_tracking(file_path, components)
            actions_taken.append("Updated dependency tracking")
            
            # Update file index
            index_updated = self._update_file_index(file_path, components)
            if index_updated:
                files_modified.append("file_index.json")
                actions_taken.append("Updated file index")
                
            # Generate metadata file
            metadata_file = self._generate_metadata_file(file_path, file_data, components)
            if metadata_file:
                files_created.append(metadata_file)
                actions_taken.append("Generated metadata file")
                
            # Check for follow-up processing needs
            followup_needed = self._check_followup_processing(file_path, components)
            if followup_needed:
                warnings.extend(followup_needed)
                actions_taken.append("Identified follow-up processing needs")
                
            return LifecycleHookResult(
                success=True,
                hook_type="post_creation_processing",
                actions_taken=actions_taken,
                files_created=files_created,
                files_modified=files_modified,
                errors=errors,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"❌ Post-creation processing failed: {e}")
            return self._create_error_result("post_creation_processing", [str(e)])
    
    def naming_convention_enforcer_hook(self, file_path: str) -> LifecycleHookResult:
        """
        Enforce naming convention in real-time
        Suggests corrections and handles renaming
        
        Args:
            file_path: File path to check
            
        Returns:
            LifecycleHookResult with enforcement results
        """
        logger.info(f"📝 Naming convention enforcement: {Path(file_path).name}")
        
        actions_taken = []
        files_modified = []
        errors = []
        warnings = []
        
        try:
            filename = Path(file_path).name
            is_valid, error_msg, components = self.naming_validator.validate_filename(filename)
            
            if is_valid:
                actions_taken.append("Naming convention compliant")
                return LifecycleHookResult(
                    success=True,
                    hook_type="naming_convention_enforcement",
                    actions_taken=actions_taken,
                    files_created=[],
                    files_modified=[],
                    errors=[],
                    warnings=[]
                )
            
            # Try to generate correction
            suggested_name = self.naming_validator.get_suggested_name(filename)
            if suggested_name:
                warnings.append(f"Non-compliant filename: {filename}")
                warnings.append(f"Suggested correction: {suggested_name}")
                actions_taken.append("Generated naming correction suggestion")
                
                # Optionally auto-rename (if enabled)
                if self._should_auto_rename(file_path):
                    new_path = Path(file_path).parent / suggested_name
                    try:
                        Path(file_path).rename(new_path)
                        files_modified.append(str(new_path))
                        actions_taken.append(f"Auto-renamed to: {suggested_name}")
                    except Exception as e:
                        errors.append(f"Auto-rename failed: {e}")
            else:
                errors.append(f"Cannot generate naming correction for: {filename}")
                
            return LifecycleHookResult(
                success=len(errors) == 0,
                hook_type="naming_convention_enforcement",
                actions_taken=actions_taken,
                files_created=[],
                files_modified=files_modified,
                errors=errors,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"❌ Naming convention enforcement failed: {e}")
            return self._create_error_result("naming_convention_enforcement", [str(e)])
    
    def dependency_tracker_hook(self, file_path: str, file_data: Dict) -> LifecycleHookResult:
        """
        Track file dependencies and relationships
        Maintains dependency graph and validates relationships
        
        Args:
            file_path: File path
            file_data: File data
            
        Returns:
            LifecycleHookResult with dependency tracking results
        """
        logger.info(f"🔗 Dependency tracking: {Path(file_path).name}")
        
        actions_taken = []
        files_created = []
        files_modified = []
        errors = []
        warnings = []
        
        try:
            filename = Path(file_path).name
            is_valid, _, components = self.naming_validator.validate_filename(filename)
            
            if not is_valid or not components:
                errors.append("Invalid filename - cannot track dependencies")
                return self._create_error_result("dependency_tracking", errors)
                
            # Update dependency graph
            dependency_graph = self._load_dependency_graph()
            self._update_dependency_graph(dependency_graph, file_path, components, file_data)
            
            # Save updated graph
            graph_file = self.base_path / "data" / "dependency_graph.json"
            if safe_json_save(dependency_graph, str(graph_file)):
                files_modified.append(str(graph_file))
                actions_taken.append("Updated dependency graph")
                
            # Check for missing dependencies
            missing_deps = self._check_missing_dependencies(file_path, components)
            if missing_deps:
                warnings.extend([f"Missing dependency: {dep}" for dep in missing_deps])
                actions_taken.append("Identified missing dependencies")
                
            # Check for circular dependencies
            circular_deps = self._check_circular_dependencies(dependency_graph, file_path)
            if circular_deps:
                errors.extend([f"Circular dependency: {dep}" for dep in circular_deps])
                
            # Generate dependency report
            report_file = self._generate_dependency_report(file_path, components, dependency_graph)
            if report_file:
                files_created.append(report_file)
                actions_taken.append("Generated dependency report")
                
            return LifecycleHookResult(
                success=len(errors) == 0,
                hook_type="dependency_tracking",
                actions_taken=actions_taken,
                files_created=files_created,
                files_modified=files_modified,
                errors=errors,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"❌ Dependency tracking failed: {e}")
            return self._create_error_result("dependency_tracking", [str(e)])
    
    def _determine_template_type(self, components) -> Optional[TemplateType]:
        """Determine template type from file components"""
        if isinstance(components.file_type, TrackerType):
            if components.file_type == TrackerType.HTF_TRACKER:
                return TemplateType.HTF_TRACKER
            elif components.file_type == TrackerType.FVG_TRACKER:
                return TemplateType.FVG_TRACKER
            elif components.file_type == TrackerType.LIQ_TRACKER:
                return TemplateType.LIQ_TRACKER
        else:
            if components.file_type == FileType.LVL_1:
                return TemplateType.ASIA_LVL_1
            elif components.file_type == FileType.GROK_ENHANCED:
                return TemplateType.ASIA_GROK_ENHANCED
            elif components.file_type in [FileType.PREDICTION, FileType.MONTE_CARLO]:
                return TemplateType.PREDICTION
                
        return None
    
    def _check_dependencies(self, file_path: str, file_data: Dict, components) -> List[str]:
        """Check file dependencies"""
        dependency_issues = []
        
        if not components:
            return dependency_issues
            
        # Check if this file type has dependencies
        required_deps = self.dependency_map.get(components.file_type, [])
        
        for dep_type in required_deps:
            # Check if dependency file exists
            dep_filename = self.naming_validator.generate_filename(
                components.session, dep_type, components.date
            )
            
            # Determine dependency directory
            if isinstance(dep_type, TrackerType):
                if dep_type == TrackerType.HTF_TRACKER:
                    dep_dir = "data/trackers/htf"
                elif dep_type == TrackerType.FVG_TRACKER:
                    dep_dir = "data/trackers/fvg"
                elif dep_type == TrackerType.LIQ_TRACKER:
                    dep_dir = "data/trackers/liquidity"
            else:
                if dep_type == FileType.LVL_1:
                    dep_dir = "data/preprocessing/level_1"
                else:
                    dep_dir = "data/enhanced/grok_enhanced"
                    
            dep_path = self.base_path / dep_dir / dep_filename
            
            if not dep_path.exists():
                dependency_issues.append(f"Missing dependency: {dep_filename}")
                
        return dependency_issues
    
    def _auto_generate_tracker_files(self, file_data: Dict, components) -> List[str]:
        """Auto-generate tracker files for session data"""
        generated_files = []
        
        try:
            # Generate HTF Tracker
            htf_tracker = self._create_htf_tracker_data(file_data, components)
            htf_filename = self.naming_validator.generate_filename(
                components.session, TrackerType.HTF_TRACKER, components.date
            )
            htf_path = self.base_path / "data/trackers/htf" / htf_filename
            
            if safe_json_save(htf_tracker, str(htf_path)):
                generated_files.append(str(htf_path))
                
            # Generate FVG Tracker
            fvg_tracker = self._create_fvg_tracker_data(file_data, components)
            fvg_filename = self.naming_validator.generate_filename(
                components.session, TrackerType.FVG_TRACKER, components.date
            )
            fvg_path = self.base_path / "data/trackers/fvg" / fvg_filename
            
            if safe_json_save(fvg_tracker, str(fvg_path)):
                generated_files.append(str(fvg_path))
                
            # Generate Liquidity Tracker
            liq_tracker = self._create_liquidity_tracker_data(file_data, components)
            liq_filename = self.naming_validator.generate_filename(
                components.session, TrackerType.LIQ_TRACKER, components.date
            )
            liq_path = self.base_path / "data/trackers/liquidity" / liq_filename
            
            if safe_json_save(liq_tracker, str(liq_path)):
                generated_files.append(str(liq_path))
                
        except Exception as e:
            logger.error(f"❌ Tracker generation failed: {e}")
            
        return generated_files
    
    def _create_htf_tracker_data(self, file_data: Dict, components) -> Dict:
        """Create HTF tracker data"""
        structures = file_data.get("structures_identified", {})
        session_levels = structures.get("session_levels", [])
        
        return {
            "tracker_metadata": {
                "tracker_id": f"htf_{components.session.value.lower()}_{components.date}",
                "session_reference": f"{components.session.value}_{components.date}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "HTF_Context"
            },
            "active_structures": [
                {
                    "level": level.get("level", 0.0),
                    "structure_type": level.get("type", "unknown"),
                    "strength": 0.8,
                    "age_minutes": 0,
                    "interaction_count": len(level.get("touches", []))
                }
                for level in session_levels
            ],
            "htf_distance_influence": 0.5,
            "structure_registry": {
                "total_structures": len(session_levels),
                "last_update": datetime.now().isoformat()
            }
        }
    
    def _create_fvg_tracker_data(self, file_data: Dict, components) -> Dict:
        """Create FVG tracker data"""
        session_character = file_data.get("session_metadata", {}).get("session_character", "neutral")
        
        # Calculate energy rate based on session character
        energy_rate = 1.0
        if "expansion" in session_character.lower():
            energy_rate = 1.3
        elif "consolidation" in session_character.lower():
            energy_rate = 0.8
            
        return {
            "tracker_metadata": {
                "tracker_id": f"fvg_{components.session.value.lower()}_{components.date}",
                "session_reference": f"{components.session.value}_{components.date}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "FVG_State"
            },
            "t_memory": 22.5,
            "energy_rate": energy_rate,
            "carryover_fvgs": file_data.get("fvg_analysis", {}).get("fair_value_gaps", []),
            "session_character": session_character
        }
    
    def _create_liquidity_tracker_data(self, file_data: Dict, components) -> Dict:
        """Create liquidity tracker data"""
        liquidity_analysis = file_data.get("liquidity_analysis", {})
        
        return {
            "tracker_metadata": {
                "tracker_id": f"liq_{components.session.value.lower()}_{components.date}",
                "session_reference": f"{components.session.value}_{components.date}",
                "creation_time": datetime.now().isoformat(),
                "tracker_type": "Liquidity_State"
            },
            "untaken_liquidity_registry": liquidity_analysis.get("untaken_liquidity", []),
            "liquidity_gradient": 0.3,
            "session_bias": "neutral"
        }
    
    def _update_dependency_tracking(self, file_path: str, components):
        """Update dependency tracking system"""
        # This would update internal dependency tracking
        # For now, just log the action
        logger.info(f"📊 Updated dependency tracking for {Path(file_path).name}")
    
    def _update_file_index(self, file_path: str, components) -> bool:
        """Update file index"""
        try:
            index_file = self.base_path / "data" / "file_index.json"
            
            # Load existing index
            index = {}
            if index_file.exists():
                index = safe_json_load(str(index_file)) or {}
                
            # Add new file entry
            file_entry = {
                "file_path": str(file_path),
                "session_type": components.session.value,
                "file_type": str(components.file_type.value),
                "date": components.date,
                "created_timestamp": datetime.now().isoformat(),
                "size_bytes": os.path.getsize(file_path) if os.path.exists(file_path) else 0
            }
            
            if "files" not in index:
                index["files"] = []
            index["files"].append(file_entry)
            
            # Update index metadata
            index["last_updated"] = datetime.now().isoformat()
            index["total_files"] = len(index["files"])
            
            # Save updated index
            return safe_json_save(index, str(index_file))
            
        except Exception as e:
            logger.error(f"❌ Failed to update file index: {e}")
            return False
    
    def _generate_metadata_file(self, file_path: str, file_data: Dict, components) -> Optional[str]:
        """Generate metadata file"""
        try:
            metadata = {
                "source_file": str(file_path),
                "governance_metadata": {
                    "session_type": components.session.value,
                    "file_type": str(components.file_type.value),
                    "date": components.date,
                    "naming_compliant": True,
                    "template_validated": True
                },
                "creation_metadata": {
                    "created_timestamp": datetime.now().isoformat(),
                    "file_size_bytes": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                    "data_quality_score": self._calculate_data_quality_score(file_data)
                },
                "processing_metadata": {
                    "processing_level": file_data.get("processing_metadata", {}).get("processing_level", "unknown"),
                    "enhancement_type": file_data.get("processing_metadata", {}).get("enhancement_type", "none")
                }
            }
            
            # Generate metadata filename
            base_name = Path(file_path).stem
            metadata_filename = f"{base_name}_metadata.json"
            metadata_path = Path(file_path).parent / metadata_filename
            
            if safe_json_save(metadata, str(metadata_path)):
                return str(metadata_path)
                
        except Exception as e:
            logger.error(f"❌ Failed to generate metadata file: {e}")
            
        return None
    
    def _check_followup_processing(self, file_path: str, components) -> List[str]:
        """Check for follow-up processing needs"""
        followup_needs = []
        
        # Check if Level 1 files need Level 3 enhancement
        if isinstance(components.file_type, FileType) and components.file_type == FileType.LVL_1:
            followup_needs.append("Level 3 enhancement recommended")
            
        # Check if enhanced files need prediction generation
        if isinstance(components.file_type, FileType) and components.file_type == FileType.GROK_ENHANCED:
            followup_needs.append("Prediction generation available")
            followup_needs.append("Event timing analysis available")
            
        return followup_needs
    
    def _should_auto_rename(self, file_path: str) -> bool:
        """Determine if file should be auto-renamed"""
        # For now, disable auto-renaming to prevent accidental changes
        return False
    
    def _load_dependency_graph(self) -> Dict:
        """Load dependency graph"""
        graph_file = self.base_path / "data" / "dependency_graph.json"
        return safe_json_load(str(graph_file)) or {"nodes": {}, "edges": []}
    
    def _update_dependency_graph(self, graph: Dict, file_path: str, components, file_data: Dict):
        """Update dependency graph with new file"""
        file_id = str(Path(file_path).name)
        
        # Add node
        graph["nodes"][file_id] = {
            "file_path": str(file_path),
            "session_type": components.session.value,
            "file_type": str(components.file_type.value),
            "date": components.date,
            "created": datetime.now().isoformat()
        }
        
        # Add edges for dependencies
        required_deps = self.dependency_map.get(components.file_type, [])
        for dep_type in required_deps:
            dep_filename = self.naming_validator.generate_filename(
                components.session, dep_type, components.date
            )
            
            graph["edges"].append({
                "from": dep_filename,
                "to": file_id,
                "relationship": "dependency"
            })
    
    def _check_missing_dependencies(self, file_path: str, components) -> List[str]:
        """Check for missing dependencies"""
        missing = []
        required_deps = self.dependency_map.get(components.file_type, [])
        
        for dep_type in required_deps:
            dep_filename = self.naming_validator.generate_filename(
                components.session, dep_type, components.date
            )
            missing.append(dep_filename)  # Simplified - would check actual existence
            
        return missing
    
    def _check_circular_dependencies(self, graph: Dict, file_path: str) -> List[str]:
        """Check for circular dependencies"""
        # Simplified circular dependency check
        # In practice, would implement proper graph traversal
        return []
    
    def _generate_dependency_report(self, file_path: str, components, graph: Dict) -> Optional[str]:
        """Generate dependency report"""
        try:
            report = {
                "file": str(file_path),
                "dependencies": self.dependency_map.get(components.file_type, []),
                "dependents": [],  # Files that depend on this one
                "report_generated": datetime.now().isoformat()
            }
            
            report_filename = f"{Path(file_path).stem}_dependencies.json"
            report_path = Path(file_path).parent / report_filename
            
            if safe_json_save(report, str(report_path)):
                return str(report_path)
                
        except Exception as e:
            logger.error(f"❌ Failed to generate dependency report: {e}")
            
        return None
    
    def _calculate_data_quality_score(self, file_data: Dict) -> float:
        """Calculate data quality score"""
        # Simplified quality scoring
        required_fields = ["session_metadata", "price_data"]
        present_fields = sum(1 for field in required_fields if field in file_data)
        return present_fields / len(required_fields)
    
    def _create_error_result(self, hook_type: str, errors: List[str]) -> LifecycleHookResult:
        """Create error result"""
        return LifecycleHookResult(
            success=False,
            hook_type=hook_type,
            actions_taken=["Error occurred"],
            files_created=[],
            files_modified=[],
            errors=errors,
            warnings=[]
        )

# Hook functions that can be triggered by the system

def pre_creation_validator(file_path: str, file_data: Dict) -> LifecycleHookResult:
    """Hook for pre-creation validation"""
    manager = FileLifecycleManager()
    return manager.pre_creation_validator_hook(file_path, file_data)

def post_creation_processor(file_path: str, file_data: Dict) -> LifecycleHookResult:
    """Hook for post-creation processing"""
    manager = FileLifecycleManager()
    return manager.post_creation_processor_hook(file_path, file_data)

def naming_convention_enforcer(file_path: str) -> LifecycleHookResult:
    """Hook for naming convention enforcement"""
    manager = FileLifecycleManager()
    return manager.naming_convention_enforcer_hook(file_path)

def dependency_tracker(file_path: str, file_data: Dict) -> LifecycleHookResult:
    """Hook for dependency tracking"""
    manager = FileLifecycleManager()
    return manager.dependency_tracker_hook(file_path, file_data)

if __name__ == "__main__":
    # Test lifecycle hooks
    print("⚙️ JSON Governance System - File Lifecycle Hooks")
    print("=" * 60)
    
    manager = FileLifecycleManager()
    
    # Test with sample data
    sample_file_path = "data/preprocessing/level_1/ASIA_Lvl-1_2025_07_25.json"
    sample_data = {
        "session_metadata": {
            "session_type": "Asia",
            "date": "2025-07-25"
        },
        "price_data": {
            "open": 23400.0,
            "close": 23420.0
        }
    }
    
    # Test pre-creation validation
    pre_result = manager.pre_creation_validator_hook(sample_file_path, sample_data)
    print(f"Pre-creation validation: {'✅ Success' if pre_result.success else '❌ Failed'}")
    print(f"Actions taken: {len(pre_result.actions_taken)}")
    
    if pre_result.errors:
        for error in pre_result.errors[:2]:
            print(f"  ❌ {error}")
            
    if pre_result.warnings:
        for warning in pre_result.warnings[:2]:
            print(f"  ⚠️ {warning}")
    
    # Test naming convention enforcement
    naming_result = manager.naming_convention_enforcer_hook(sample_file_path)
    print(f"\nNaming enforcement: {'✅ Success' if naming_result.success else '❌ Failed'}")
    print(f"Actions taken: {len(naming_result.actions_taken)}")