#!/usr/bin/env python3
"""
JSON Governance System - Temporal Integrity Hooks
Ensures temporal sequence validation and cross-session mathematical continuity
"""

import json
import re
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass

from src.utils import get_logger, safe_json_load
from governance.naming_conventions import NamingConventionValidator, SessionType

logger = get_logger(__name__)

@dataclass
class TemporalValidationResult:
    """Result of temporal validation"""
    is_valid: bool
    validation_type: str
    errors: List[str]
    warnings: List[str]
    sequence_gaps: List[str]
    continuity_issues: List[str]

class TemporalIntegrityValidator:
    """
    Validates temporal integrity across session sequences
    Ensures proper ASIA→LONDON→NY order and mathematical continuity
    """
    
    # Standard session sequence order
    SESSION_SEQUENCE = [
        SessionType.MIDNIGHT,
        SessionType.ASIA, 
        SessionType.LONDON,
        SessionType.PREMARKET,
        SessionType.NYAM,
        SessionType.LUNCH,
        SessionType.NYPM
    ]
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.naming_validator = NamingConventionValidator()
        
    def validate_session_sequence(self, session_files: List[str], target_date: str) -> TemporalValidationResult:
        """
        Validate session sequence for a given date
        
        Args:
            session_files: List of session file paths
            target_date: Target date in YYYY_MM_DD format
            
        Returns:
            TemporalValidationResult with sequence validation
        """
        logger.info(f"🕐 Validating session sequence for {target_date}")
        
        errors = []
        warnings = []
        sequence_gaps = []
        
        # Parse session files for the target date
        date_sessions = self._parse_sessions_for_date(session_files, target_date)
        
        # Check sequence completeness
        missing_sessions = self._check_sequence_completeness(date_sessions)
        if missing_sessions:
            sequence_gaps.extend(missing_sessions)
            
        # Check sequence order
        order_issues = self._check_sequence_order(date_sessions)
        if order_issues:
            errors.extend(order_issues)
            
        # Check time gaps between sessions
        time_gap_issues = self._check_time_gaps(date_sessions)
        if time_gap_issues:
            warnings.extend(time_gap_issues)
            
        is_valid = len(errors) == 0
        
        return TemporalValidationResult(
            is_valid=is_valid,
            validation_type="session_sequence",
            errors=errors,
            warnings=warnings,
            sequence_gaps=sequence_gaps,
            continuity_issues=[]
        )
    
    def validate_date_continuity(self, session_files: List[str]) -> TemporalValidationResult:
        """
        Validate date continuity across multiple dates
        
        Args:
            session_files: List of session file paths
            
        Returns:
            TemporalValidationResult with date continuity validation
        """
        logger.info("📅 Validating date continuity across sessions")
        
        errors = []
        warnings = []
        continuity_issues = []
        
        # Group files by date
        date_groups = self._group_files_by_date(session_files)
        dates = sorted(date_groups.keys())
        
        # Check for date gaps
        date_gaps = self._check_date_gaps(dates)
        if date_gaps:
            continuity_issues.extend(date_gaps)
            
        # Check weekend handling
        weekend_issues = self._check_weekend_continuity(dates)
        if weekend_issues:
            warnings.extend(weekend_issues)
            
        is_valid = len(errors) == 0
        
        return TemporalValidationResult(
            is_valid=is_valid,
            validation_type="date_continuity",
            errors=errors,
            warnings=warnings,
            sequence_gaps=[],
            continuity_issues=continuity_issues
        )
    
    def validate_tracker_state_continuity(self, tracker_files: List[str]) -> TemporalValidationResult:
        """
        Validate tracker state continuity across sessions
        
        Args:
            tracker_files: List of tracker file paths
            
        Returns:
            TemporalValidationResult with tracker continuity validation
        """
        logger.info("🔗 Validating tracker state continuity")
        
        errors = []
        warnings = []
        continuity_issues = []
        
        # Group tracker files by type and date
        tracker_groups = self._group_tracker_files(tracker_files)
        
        # Validate each tracker type
        for tracker_type, files_by_date in tracker_groups.items():
            type_issues = self._validate_tracker_type_continuity(tracker_type, files_by_date)
            continuity_issues.extend(type_issues)
            
        # Check mathematical continuity
        math_issues = self._check_mathematical_continuity(tracker_groups)
        if math_issues:
            continuity_issues.extend(math_issues)
            
        is_valid = len(errors) == 0 and len(continuity_issues) == 0
        
        return TemporalValidationResult(
            is_valid=is_valid,
            validation_type="tracker_continuity",
            errors=errors,
            warnings=warnings,
            sequence_gaps=[],
            continuity_issues=continuity_issues
        )
    
    def validate_cross_session_integrity(self, session_files: List[str]) -> TemporalValidationResult:
        """
        Validate cross-session mathematical integrity
        
        Args:
            session_files: List of session file paths
            
        Returns:
            TemporalValidationResult with cross-session validation
        """
        logger.info("🔄 Validating cross-session mathematical integrity")
        
        errors = []
        warnings = []
        continuity_issues = []
        
        # Group sessions by date
        date_groups = self._group_files_by_date(session_files)
        
        # Validate mathematical handoffs between sessions
        for date, sessions in date_groups.items():
            handoff_issues = self._validate_session_handoffs(sessions, date)
            continuity_issues.extend(handoff_issues)
            
        # Check price continuity across sessions
        price_issues = self._check_price_continuity(date_groups)
        if price_issues:
            continuity_issues.extend(price_issues)
            
        # Check momentum transfer
        momentum_issues = self._check_momentum_transfer(date_groups)
        if momentum_issues:
            warnings.extend(momentum_issues)
            
        is_valid = len(errors) == 0
        
        return TemporalValidationResult(
            is_valid=is_valid,
            validation_type="cross_session_integrity",
            errors=errors,
            warnings=warnings,
            sequence_gaps=[],
            continuity_issues=continuity_issues
        )
    
    def _parse_sessions_for_date(self, session_files: List[str], target_date: str) -> Dict[SessionType, Dict]:
        """Parse sessions for a specific date"""
        date_sessions = {}
        
        for file_path in session_files:
            filename = Path(file_path).name
            is_valid, _, components = self.naming_validator.validate_filename(filename)
            
            if is_valid and components and components.date == target_date:
                try:
                    session_data = safe_json_load(file_path)
                    if session_data:
                        date_sessions[components.session] = {
                            "file_path": file_path,
                            "components": components,
                            "data": session_data
                        }
                except Exception as e:
                    logger.warning(f"Could not load session file {file_path}: {e}")
                    
        return date_sessions
    
    def _check_sequence_completeness(self, date_sessions: Dict[SessionType, Dict]) -> List[str]:
        """Check if all required sessions are present"""
        missing_sessions = []
        
        for required_session in self.SESSION_SEQUENCE:
            if required_session not in date_sessions:
                missing_sessions.append(f"Missing {required_session.value} session")
                
        return missing_sessions
    
    def _check_sequence_order(self, date_sessions: Dict[SessionType, Dict]) -> List[str]:
        """Check if sessions are in correct temporal order"""
        order_issues = []
        
        present_sessions = list(date_sessions.keys())
        
        # Check if present sessions follow sequence order
        last_position = -1
        for session in present_sessions:
            try:
                position = self.SESSION_SEQUENCE.index(session)
                if position <= last_position:
                    order_issues.append(f"Session {session.value} out of sequence order")
                last_position = position
            except ValueError:
                order_issues.append(f"Unknown session type: {session.value}")
                
        return order_issues
    
    def _check_time_gaps(self, date_sessions: Dict[SessionType, Dict]) -> List[str]:
        """Check for unusual time gaps between sessions"""
        time_gap_issues = []
        
        # Expected time gaps between sessions (in hours)
        expected_gaps = {
            (SessionType.ASIA, SessionType.LONDON): 4,
            (SessionType.LONDON, SessionType.PREMARKET): 3,
            (SessionType.PREMARKET, SessionType.NYAM): 1,
            (SessionType.NYAM, SessionType.LUNCH): 3,
            (SessionType.LUNCH, SessionType.NYPM): 1
        }
        
        for (prev_session, next_session), expected_hours in expected_gaps.items():
            if prev_session in date_sessions and next_session in date_sessions:
                prev_end = self._extract_session_end_time(date_sessions[prev_session]["data"])
                next_start = self._extract_session_start_time(date_sessions[next_session]["data"])
                
                if prev_end and next_start:
                    gap_hours = self._calculate_time_gap(prev_end, next_start)
                    if abs(gap_hours - expected_hours) > 2:  # Allow 2-hour tolerance
                        time_gap_issues.append(
                            f"Unusual time gap between {prev_session.value} and {next_session.value}: {gap_hours:.1f}h (expected ~{expected_hours}h)"
                        )
                        
        return time_gap_issues
    
    def _group_files_by_date(self, session_files: List[str]) -> Dict[str, List[str]]:
        """Group files by date"""
        date_groups = {}
        
        for file_path in session_files:
            filename = Path(file_path).name
            is_valid, _, components = self.naming_validator.validate_filename(filename)
            
            if is_valid and components:
                date = components.date
                if date not in date_groups:
                    date_groups[date] = []
                date_groups[date].append(file_path)
                
        return date_groups
    
    def _check_date_gaps(self, dates: List[str]) -> List[str]:
        """Check for gaps in date sequence"""
        date_gaps = []
        
        if len(dates) < 2:
            return date_gaps
            
        for i in range(1, len(dates)):
            prev_date = datetime.strptime(dates[i-1], "%Y_%m_%d")
            curr_date = datetime.strptime(dates[i], "%Y_%m_%d")
            
            # Calculate business days gap (excluding weekends)
            gap_days = (curr_date - prev_date).days
            
            # Check for unusual gaps (more than 3 business days)
            if gap_days > 3:
                date_gaps.append(f"Large gap between {dates[i-1]} and {dates[i]}: {gap_days} days")
                
        return date_gaps
    
    def _check_weekend_continuity(self, dates: List[str]) -> List[str]:
        """Check weekend continuity handling"""
        weekend_issues = []
        
        for date_str in dates:
            date = datetime.strptime(date_str, "%Y_%m_%d")
            if date.weekday() in [5, 6]:  # Saturday, Sunday
                weekend_issues.append(f"Trading session on weekend: {date_str}")
                
        return weekend_issues
    
    def _group_tracker_files(self, tracker_files: List[str]) -> Dict[str, Dict[str, List[str]]]:
        """Group tracker files by type and date"""
        tracker_groups = {
            "HTF": {},
            "FVG": {},
            "LIQ": {}
        }
        
        for file_path in tracker_files:
            filename = Path(file_path).name
            is_valid, _, components = self.naming_validator.validate_filename(filename)
            
            if is_valid and components:
                # Determine tracker type
                tracker_type = None
                if "HTF_Tracker" in str(components.file_type):
                    tracker_type = "HTF"
                elif "FVG_Tracker" in str(components.file_type):
                    tracker_type = "FVG"
                elif "LIQ_Tracker" in str(components.file_type):
                    tracker_type = "LIQ"
                    
                if tracker_type:
                    date = components.date
                    if date not in tracker_groups[tracker_type]:
                        tracker_groups[tracker_type][date] = []
                    tracker_groups[tracker_type][date].append(file_path)
                    
        return tracker_groups
    
    def _validate_tracker_type_continuity(self, tracker_type: str, files_by_date: Dict[str, List[str]]) -> List[str]:
        """Validate continuity for a specific tracker type"""
        continuity_issues = []
        
        dates = sorted(files_by_date.keys())
        
        for i in range(1, len(dates)):
            prev_date = dates[i-1]
            curr_date = dates[i]
            
            # Load tracker files
            prev_files = files_by_date[prev_date]
            curr_files = files_by_date[curr_date]
            
            # Check carryover continuity
            carryover_issues = self._check_tracker_carryover(tracker_type, prev_files, curr_files, prev_date, curr_date)
            continuity_issues.extend(carryover_issues)
            
        return continuity_issues
    
    def _check_tracker_carryover(self, tracker_type: str, prev_files: List[str], curr_files: List[str], prev_date: str, curr_date: str) -> List[str]:
        """Check tracker carryover between dates"""
        issues = []
        
        try:
            # Load first files (assuming one per date for simplicity)
            if prev_files and curr_files:
                prev_data = safe_json_load(prev_files[0])
                curr_data = safe_json_load(curr_files[0])
                
                if prev_data and curr_data:
                    if tracker_type == "FVG":
                        # Check T_memory continuity
                        prev_t_memory = prev_data.get("t_memory", 0)
                        curr_t_memory = curr_data.get("t_memory", 0)
                        
                        if abs(prev_t_memory - curr_t_memory) > 10:  # Allow 10-point variance
                            issues.append(f"FVG T_memory discontinuity: {prev_date}({prev_t_memory}) → {curr_date}({curr_t_memory})")
                            
                    elif tracker_type == "HTF":
                        # Check structure carryover
                        prev_structures = len(prev_data.get("active_structures", []))
                        curr_structures = len(curr_data.get("active_structures", []))
                        
                        if prev_structures > 0 and curr_structures == 0:
                            issues.append(f"HTF structure loss: {prev_date}({prev_structures}) → {curr_date}(0)")
                            
        except Exception as e:
            issues.append(f"Error checking {tracker_type} carryover {prev_date}→{curr_date}: {e}")
            
        return issues
    
    def _check_mathematical_continuity(self, tracker_groups: Dict[str, Dict[str, List[str]]]) -> List[str]:
        """Check mathematical continuity across trackers"""
        math_issues = []
        
        # Check for consistent energy rates across FVG trackers
        fvg_files = tracker_groups.get("FVG", {})
        energy_rates = []
        
        for date, files in fvg_files.items():
            if files:
                try:
                    data = safe_json_load(files[0])
                    if data:
                        energy_rate = data.get("energy_rate", 1.0)
                        energy_rates.append((date, energy_rate))
                except Exception:
                    pass
                    
        # Check for dramatic energy rate changes
        for i in range(1, len(energy_rates)):
            prev_date, prev_rate = energy_rates[i-1]
            curr_date, curr_rate = energy_rates[i]
            
            if abs(curr_rate - prev_rate) > 1.0:  # Large energy rate change
                math_issues.append(f"Large energy rate change: {prev_date}({prev_rate:.2f}) → {curr_date}({curr_rate:.2f})")
                
        return math_issues
    
    def _validate_session_handoffs(self, sessions: List[str], date: str) -> List[str]:
        """Validate mathematical handoffs between sessions"""
        handoff_issues = []
        
        # Parse sessions for the date
        session_data = {}
        for session_file in sessions:
            filename = Path(session_file).name
            is_valid, _, components = self.naming_validator.validate_filename(filename)
            
            if is_valid and components:
                try:
                    data = safe_json_load(session_file)
                    if data:
                        session_data[components.session] = data
                except Exception:
                    pass
                    
        # Check Asia→London handoff
        if SessionType.ASIA in session_data and SessionType.LONDON in session_data:
            asia_close = session_data[SessionType.ASIA].get("price_data", {}).get("close", 0)
            london_open = session_data[SessionType.LONDON].get("price_data", {}).get("open", 0)
            
            if abs(asia_close - london_open) > 50:  # Large gap
                handoff_issues.append(f"Large Asia→London gap on {date}: {asia_close} → {london_open}")
                
        # Check London→NY handoff
        if SessionType.LONDON in session_data and SessionType.NYAM in session_data:
            london_close = session_data[SessionType.LONDON].get("price_data", {}).get("close", 0)
            ny_open = session_data[SessionType.NYAM].get("price_data", {}).get("open", 0)
            
            if abs(london_close - ny_open) > 50:  # Large gap
                handoff_issues.append(f"Large London→NY gap on {date}: {london_close} → {ny_open}")
                
        return handoff_issues
    
    def _check_price_continuity(self, date_groups: Dict[str, List[str]]) -> List[str]:
        """Check price continuity across sessions"""
        price_issues = []
        
        for date, sessions in date_groups.items():
            # Load all sessions for the date
            session_prices = {}
            
            for session_file in sessions:
                filename = Path(session_file).name
                is_valid, _, components = self.naming_validator.validate_filename(filename)
                
                if is_valid and components:
                    try:
                        data = safe_json_load(session_file)
                        if data:
                            price_data = data.get("price_data", {})
                            session_prices[components.session] = {
                                "open": price_data.get("open", 0),
                                "close": price_data.get("close", 0),
                                "high": price_data.get("high", 0),
                                "low": price_data.get("low", 0)
                            }
                    except Exception:
                        pass
                        
            # Check for unrealistic price moves
            if len(session_prices) > 1:
                all_prices = []
                for session, prices in session_prices.items():
                    all_prices.extend([prices["open"], prices["close"], prices["high"], prices["low"]])
                    
                if all_prices:
                    min_price = min(all_prices)
                    max_price = max(all_prices)
                    
                    if (max_price - min_price) > 500:  # Very large daily range
                        price_issues.append(f"Unusually large price range on {date}: {min_price} - {max_price}")
                        
        return price_issues
    
    def _check_momentum_transfer(self, date_groups: Dict[str, List[str]]) -> List[str]:
        """Check momentum transfer between sessions"""
        momentum_issues = []
        
        # This is a simplified check - in practice would involve more complex momentum analysis
        for date, sessions in date_groups.items():
            if len(sessions) >= 2:
                momentum_issues.append(f"Momentum transfer analysis needed for {date}")
                
        return momentum_issues
    
    def _extract_session_end_time(self, session_data: Dict) -> Optional[str]:
        """Extract session end time"""
        return session_data.get("session_metadata", {}).get("end_time", "")
    
    def _extract_session_start_time(self, session_data: Dict) -> Optional[str]:
        """Extract session start time"""
        return session_data.get("session_metadata", {}).get("start_time", "")
    
    def _calculate_time_gap(self, end_time: str, start_time: str) -> float:
        """Calculate time gap in hours"""
        try:
            # Parse times (assuming format like "23:59:00 ET")
            end_time_part = end_time.split()[0]
            start_time_part = start_time.split()[0]
            
            end_dt = datetime.strptime(end_time_part, "%H:%M:%S")
            start_dt = datetime.strptime(start_time_part, "%H:%M:%S")
            
            # Handle day rollover
            if start_dt < end_dt:
                start_dt = start_dt.replace(day=start_dt.day + 1)
                
            gap = (start_dt - end_dt).total_seconds() / 3600
            return gap
        except Exception:
            return 0.0

# Hook functions that can be triggered by the system

def session_sequence_validator_hook(session_files: List[str], target_date: str) -> TemporalValidationResult:
    """
    Hook to validate session sequence
    Triggered when processing a full day's sessions
    """
    validator = TemporalIntegrityValidator()
    return validator.validate_session_sequence(session_files, target_date)

def date_continuity_checker_hook(session_files: List[str]) -> TemporalValidationResult:
    """
    Hook to check date continuity
    Triggered when processing multiple dates
    """
    validator = TemporalIntegrityValidator()
    return validator.validate_date_continuity(session_files)

def tracker_state_validator_hook(tracker_files: List[str]) -> TemporalValidationResult:
    """
    Hook to validate tracker state continuity
    Triggered when tracker files are updated
    """
    validator = TemporalIntegrityValidator()
    return validator.validate_tracker_state_continuity(tracker_files)

def cross_session_integrity_hook(session_files: List[str]) -> TemporalValidationResult:
    """
    Hook to validate cross-session mathematical integrity
    Triggered after session processing
    """
    validator = TemporalIntegrityValidator()
    return validator.validate_cross_session_integrity(session_files)

if __name__ == "__main__":
    # Test temporal hooks
    print("🕐 JSON Governance System - Temporal Integrity Hooks")
    print("=" * 60)
    
    validator = TemporalIntegrityValidator()
    
    # Test with sample files (if they exist)
    test_files = list(Path("data").rglob("*.json"))
    
    if test_files:
        print(f"Testing with {len(test_files)} files...")
        
        # Test session sequence validation
        result = validator.validate_session_sequence([str(f) for f in test_files[:5]], "2025_07_25")
        print(f"Session sequence validation: {'✅ Valid' if result.is_valid else '❌ Invalid'}")
        
        if result.errors:
            for error in result.errors[:3]:  # Show first 3 errors
                print(f"  ❌ {error}")
                
        if result.warnings:
            for warning in result.warnings[:3]:  # Show first 3 warnings
                print(f"  ⚠️ {warning}")
    else:
        print("No test files found")