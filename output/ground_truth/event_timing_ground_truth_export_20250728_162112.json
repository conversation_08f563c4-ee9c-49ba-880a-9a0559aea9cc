{"report_metadata": {"generated": "2025-07-28T16:21:12.823832", "report_type": "ground_truth_validation", "version": "1.0.0"}, "executive_summary": {"storage_summary": {"total_sessions": 3, "sessions_with_ground_truth": 3, "sessions_with_predictions": 3, "sessions_validated": 1, "validation_coverage": 33.33333333333333}, "accuracy_metrics": {"consolidation_accuracy": null, "expansion_accuracy": null}, "epistemic_closure_detected": []}, "detailed_sessions": [{"session_id": "timing_session_20250728_162112", "timestamp": "2025-07-28T16:21:12.812590", "file_source": "expansion_session_grokEnhanced_2025_07_28.json", "has_ground_truth": true, "has_predictions": true, "has_validation": true, "ground_truth_summary": {"consolidation_occurred": false, "expansion_occurred": true, "session_range": 0, "net_direction": "unknown", "volatility_character": "unknown"}, "validation_summary": {"cascade_timing_accuracy": "unable_to_validate", "expansion_timing_accuracy": "unable_to_validate", "dominant_phase_accuracy": false, "overall_timing_quality": "no_predictions_available", "potential_timing_epistemic_closure": false, "test_timestamp": "2025-07-28T16:21:12.821157", "test_file": "test_session_grokEnhanced_2025_07_28.json", "paradigm": "event_timing_focused"}}, {"session_id": "timing_session_20250728_162112", "timestamp": "2025-07-28T16:21:12.816142", "file_source": "NYAM_grokEnhanced_2025_07_25.json", "has_ground_truth": true, "has_predictions": true, "has_validation": false, "ground_truth_summary": {"consolidation_occurred": false, "expansion_occurred": true, "session_range": 0, "net_direction": "unknown", "volatility_character": "unknown"}}, {"session_id": "timing_session_20250728_162112", "timestamp": "2025-07-28T16:21:12.819620", "file_source": "test_session_grokEnhanced_2025_07_28.json", "has_ground_truth": true, "has_predictions": true, "has_validation": false, "ground_truth_summary": {"consolidation_occurred": false, "expansion_occurred": false, "session_range": 0, "net_direction": "unknown", "volatility_character": "unknown"}}]}