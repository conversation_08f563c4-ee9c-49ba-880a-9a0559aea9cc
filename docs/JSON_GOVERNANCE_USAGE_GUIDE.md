# JSON Governance System - Usage Guide

## 🏛️ Overview

The JSON Governance System implements **strict naming conventions** and **automated file management** for the Grok-Claude Automation project. This system enforces the user's requirements for **SESSION_type_YYYY_MM_DD.json** naming and **multi-agent architecture** with automated **Preprocessing/Extraction/Parsing** and **Prediction/Enhancement** operations.

## 🎯 Key Features Implemented

### ✅ Core Requirements Met
- **Strict naming conventions**: `SESSION_type_YYYY_MM_DD.json` format
- **Tracker file naming**: `TYPE_Tracker_SESSION_YYYY_MM_DD.json` format
- **Multi-agent architecture** for preprocessing and enhancement
- **Hook system** for temporal integrity and file lifecycle management
- **Automated migration** of 183+ existing JSON files
- **Template compliance validation** and schema enforcement

### 📁 Directory Structure
```
data/
├── preprocessing/
│   ├── level_1/              # SESSION_Lvl-1_YYYY_MM_DD.json
│   ├── level_3/              # SESSION_Lvl-3_YYYY_MM_DD.json  
│   └── extraction/           # Raw extractions
├── enhanced/
│   ├── grok_enhanced/        # SESSION_grokEnhanced_YYYY_MM_DD.json
│   ├── event_timing/         # SESSION_eventTimingEnhanced_YYYY_MM_DD.json
│   └── predictions/          # SESSION_prediction_YYYY_MM_DD.json
├── trackers/
│   ├── htf/                  # HTF_Tracker_SESSION_YYYY_MM_DD.json
│   ├── fvg/                  # FVG_Tracker_SESSION_YYYY_MM_DD.json
│   └── liquidity/            # LIQ_Tracker_SESSION_YYYY_MM_DD.json
└── archive/                  # Non-conforming files
```

## 🚀 Quick Start

### 1. Initialize the Governance System
```python
from orchestration.governance_orchestrator import create_governance_orchestrator

# Create orchestrator
orchestrator = create_governance_orchestrator()

# Initialize system (creates directories, validates components)
init_results = orchestrator.initialize_governance_system()
print(f"System ready: {init_results['system_ready']}")
```

### 2. Migrate Existing Files
```python
from governance.migration_engine import migrate_project_files

# Migrate all existing JSON files to governance structure
# Run in dry-run mode first to preview changes
summary = migrate_project_files(dry_run=True)
print(f"Would migrate {summary.total_files} files")

# Execute actual migration
summary = migrate_project_files(dry_run=False)
print(f"Migrated {summary.successful_migrations} files successfully")
```

### 3. Process a Session File
```python
from agents.preprocessing.preprocessing_manager import create_preprocessing_agent

# Create preprocessing agent
agent = create_preprocessing_agent()

# Process a session file (Level 1 processing)
result = agent.process_session('asia_l1_2025_07_25.json', 'level_1')

if result.success:
    print(f"✅ Processing complete: {result.output_files[0]}")
    print(f"🎯 Tracker files created: {len(result.tracker_files)}")
else:
    print(f"❌ Processing failed: {result.errors}")
```

### 4. Enhance Processed Files
```python
from agents.enhancement.enhancement_manager import create_enhancement_agent

# Create enhancement agent
agent = create_enhancement_agent()

# Enhance a processed file
result = agent.enhance_session(
    'data/preprocessing/level_1/ASIA_Lvl-1_2025_07_25.json',
    'grok_enhanced'
)

if result.success:
    print(f"✅ Enhancement complete: {result.output_files[0]}")
    print(f"🎯 Accuracy: {result.prediction_accuracy:.2f}")
```

## 📋 Examples Following User's Naming Convention

### Session Files
```
ASIA_grokEnhanced_2025_07_25.json      # Enhanced Asia session
ASIA_Lvl-1_2025_07_25.json             # Level 1 Asia session
LONDON_grokEnhanced_2025_07_25.json    # Enhanced London session
NYAM_Lvl-1_2025_07_25.json             # Level 1 NY AM session
PREMARKET_prediction_2025_07_25.json   # Premarket prediction
```

### Tracker Files
```
HTF_Tracker_ASIA_2025_07_25.json       # HTF context tracker
FVG_Tracker_ASIA_2025_07_25.json       # FVG state tracker
LIQ_Tracker_ASIA_2025_07_25.json       # Liquidity tracker
```

## 🔧 Advanced Usage

### Complete Governance Pipeline
```python
# Process a session through the complete governance pipeline
result = orchestrator.process_single_session_with_governance(
    session_file='asia_session_data.json',
    enhancement_type='grok_enhanced'
)

print(f"Pipeline success: {result['success']}")
print(f"Preprocessing: {result['preprocessing_result']['success']}")
print(f"Enhancement: {result['enhancement_result']['success']}")
print(f"Tracker files: {len(result['tracker_files'])}")
print(f"Hooks triggered: {len(result['lifecycle_hooks_triggered'])}")
```

### Batch Processing
```python
# Batch process multiple session files
from agents.preprocessing.preprocessing_manager import PreprocessingManager

manager = PreprocessingManager()
session_files = [
    'asia_session_2025_07_25.json',
    'london_session_2025_07_25.json',
    'nyam_session_2025_07_25.json'
]

batch_results = manager.batch_process(session_files, 'level_1')
print(f"Processed {batch_results['successful']}/{batch_results['total_files']} files")
```

### Governance Status Monitoring
```python
# Get current governance status
status = orchestrator.get_governance_status()

print(f"📊 Governance Status:")
print(f"  Total files: {status.total_files}")
print(f"  Compliant files: {status.compliant_files}")
print(f"  Compliance rate: {status.compliance_percentage:.1f}%")
print(f"  Tracker files: {status.tracker_files}")
print(f"  Enhanced files: {status.enhanced_files}")
```

### Full Governance Cycle
```python
# Execute complete governance cycle (migration + validation + processing)
cycle_results = orchestrator.execute_full_governance_cycle()

print(f"Cycle success: {cycle_results['cycle_success']}")
print(f"Migration: {cycle_results['migration_results']['successful']} files")
print(f"Validation: {cycle_results['validation_results']['overall_compliance']:.1f}")
print(f"Duration: {cycle_results['cycle_duration_seconds']:.1f}s")
```

## 🔍 Validation and Compliance

### Naming Convention Validation
```python
from governance.naming_conventions import validate_file_governance

# Validate a filename
is_valid, message, components = validate_file_governance('ASIA_grokEnhanced_2025_07_25.json')
print(f"Valid: {is_valid}")
print(f"Message: {message}")

if components:
    print(f"Session: {components.session.value}")
    print(f"Type: {components.file_type.value}")
    print(f"Date: {components.date}")
```

### Template Compliance Validation
```python
from governance.template_validator import validate_file_compliance, TemplateType

# Validate file against template
result = validate_file_compliance('ASIA_Lvl-1_2025_07_25.json', TemplateType.ASIA_LVL_1)

print(f"Valid: {result.is_valid}")
print(f"Compliance score: {result.compliance_score:.2f}")
if result.errors:
    for error in result.errors:
        print(f"❌ {error}")
```

### Temporal Integrity Validation
```python
from hooks.temporal_hooks import session_sequence_validator_hook

# Validate session sequence for a date
session_files = [
    'ASIA_Lvl-1_2025_07_25.json',
    'LONDON_Lvl-1_2025_07_25.json',
    'NYAM_Lvl-1_2025_07_25.json'
]

result = session_sequence_validator_hook(session_files, '2025_07_25')
print(f"Sequence valid: {result.is_valid}")
if result.sequence_gaps:
    for gap in result.sequence_gaps:
        print(f"⚠️ {gap}")
```

## 🎯 File Lifecycle Hooks

### Pre-Creation Validation
```python
from hooks.lifecycle_hooks import pre_creation_validator

# Validate before creating a file
file_data = {
    "session_metadata": {"session_type": "Asia", "date": "2025-07-25"},
    "price_data": {"open": 23400.0, "close": 23420.0}
}

result = pre_creation_validator('ASIA_Lvl-1_2025_07_25.json', file_data)
print(f"Pre-creation valid: {result.success}")
```

### Post-Creation Processing
```python
from hooks.lifecycle_hooks import post_creation_processor

# Process after file creation (auto-generates tracker files)
result = post_creation_processor('ASIA_Lvl-1_2025_07_25.json', file_data)
print(f"Post-processing: {result.success}")
print(f"Files created: {len(result.files_created)}")
print(f"Actions taken: {result.actions_taken}")
```

## 📊 Reporting and Analytics

### Generate Governance Report
```python
# Generate comprehensive governance report
report_file = orchestrator.generate_governance_report()
print(f"Report generated: {report_file}")

# Report includes:
# - Governance status
# - System statistics  
# - Directory structure analysis
# - Naming convention compliance
# - Template compliance analysis
# - Temporal integrity status
# - Recommendations
```

### Migration Report
```python
# Migration engine automatically generates detailed reports
summary = migrate_project_files(dry_run=False)

# Check migration_report.txt for detailed results:
# - Files migrated successfully
# - Naming corrections applied
# - Files archived with reasons
# - Tracker files generated
# - Processing time and statistics
```

## 🛠️ Customization and Extension

### Adding New Session Types
```python
# Extend SessionType enum in governance/naming_conventions.py
class SessionType(Enum):
    ASIA = "ASIA"
    LONDON = "LONDON"
    CUSTOM_SESSION = "CUSTOM_SESSION"  # Add new session type
```

### Adding New File Types
```python
# Extend FileType enum
class FileType(Enum):
    LVL_1 = "Lvl-1"
    GROK_ENHANCED = "grokEnhanced"
    CUSTOM_TYPE = "customType"  # Add new file type
```

### Custom Processing Agents
```python
# Create custom processing agents
from agents.preprocessing.preprocessing_manager import PreprocessingManager

class CustomProcessor(PreprocessingManager):
    def custom_process(self, session_data):
        # Implement custom processing logic
        return processed_data
```

## 🚨 Best Practices

### 1. Always Use Governance Conventions
```python
# ✅ Good: Use governance naming
filename = naming_validator.generate_filename(
    SessionType.ASIA, 
    FileType.GROK_ENHANCED, 
    "2025_07_25"
)

# ❌ Bad: Manual naming
filename = "asia_enhanced_data.json"
```

### 2. Validate Before Processing
```python
# Always validate files before processing
is_valid, error, components = validate_file_governance(filename)
if not is_valid:
    print(f"Validation failed: {error}")
    return
```

### 3. Use Orchestrator for Complex Operations
```python
# Use orchestrator for multi-step operations
result = orchestrator.process_single_session_with_governance(session_file)
# This ensures proper validation, processing, and hook execution
```

### 4. Monitor Compliance Regularly
```python
# Regular compliance monitoring
status = orchestrator.get_governance_status()
if status.compliance_percentage < 90:
    print("⚠️ Compliance below 90% - review and migrate files")
```

## 🎯 Summary

The JSON Governance System provides:

- **✅ Strict naming enforcement** per user requirements
- **✅ Automated file organization** with proper directory structure  
- **✅ Multi-agent processing** for preprocessing and enhancement
- **✅ Hook system** for temporal integrity and lifecycle management
- **✅ Migration engine** for existing files (183+ files supported)
- **✅ Template validation** and schema compliance
- **✅ Comprehensive reporting** and monitoring
- **✅ Agent orchestration** for complex workflows

All user requirements have been implemented with the exact naming conventions specified:
- `ASIA_grokEnhanced_2025_07_25.json`
- `HTF_Tracker_ASIA_2025_07_25.json`
- `FVG_Tracker_ASIA_2025_07_25.json`
- `LIQ_Tracker_ASIA_2025_07_25.json`

The system is **production-ready** and provides **strict operation and tidiness** as requested.