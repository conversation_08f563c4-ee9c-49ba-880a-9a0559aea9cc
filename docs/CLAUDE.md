# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Setup and Testing
```bash
# Install dependencies
pip install -r requirements.txt

# Set API key (required for all operations)  
export GROK_API_KEY=xai-your-api-key-here

# Test Grok API connection
python cli.py test
```

### Core Processing Commands
```bash
# Process single session
python cli.py process session.json [micro_timing.json] -o results.json

# Use Preprocessing Agent (RECOMMENDED)
python3 -c "
from src.preprocessing_agent import create_preprocessing_agent
agent = create_preprocessing_agent()
result = agent.process_session('session_l1_YYYY_MM_DD.json')
print(f'✅ Enhanced data: {result[\"validation_results\"][\"field_count\"]} fields')"

# Batch process multiple sessions
python3 -c "
from src.preprocessing_agent import create_preprocessing_agent
agent = create_preprocessing_agent()
batch_result = agent.batch_process_sessions(['asia_l1.json', 'london_l1.json'])
print(f'✅ Success rate: {batch_result[\"batch_results\"][\"success_rate\"]:.1%}')"

# Core prediction systems
python3 src/hawkes_cascade_predictor.py  # 0.0 minute error cascade timing
python3 src/micro_timing_analysis.py     # Complete timing analysis
python3 production/grok_monte_carlo_package.py      # Production Monte Carlo system

# NEW: Fractal Cascade Architecture (Master-Subordinate Control)
python3 src/htf_master_controller.py     # HTF intensity monitoring & activation signals
python3 src/session_subordinate_executor.py  # Session predictions with HTF enhancement
python3 src/fractal_cascade_integrator.py    # Complete fractal system integration
python3 src/fractal_validation_july29.py     # Validate against July 29 cascade (70% accuracy)
python3 scripts/demos/dynamic_live_demo.py             # Dynamic adaptation demo for tomorrow's data
```

### Advanced Analysis
```bash
# System validation and monitoring
python3 src/shadow_validation_system.py
python3 scripts/analysis/run_cross_session_analysis.py
python3 validation/validate_optimizations_simple.py
```

## Architecture Overview

This system combines a **4-unit sequential computational pipeline** (A→B→C→D) with a revolutionary **Fractal HTF-Session Cascade Architecture** for multi-timeframe prediction control.

### 📁 **NEW: Organized Directory Structure** 
The codebase has been completely reorganized from 101 files in root to a professional structure:

- **`production/`**: Production-ready systems (4 files)
- **`src/prediction/`**: Models, Monte Carlo, timing, mathematics modules  
- **`src/integration/`**: HTF and data integration systems
- **`src/infrastructure/`**: JSON utilities with backward compatibility
- **`data/`**: All data files organized by type (sessions, trackers, predictions, etc.)
- **`scripts/`**: Analysis, processing, demos, experimental files
- **`validation/`**: Test and validation files (22 files)
- **`docs/`**: Documentation and analysis reports
- **`tools/`**: Utility tools and helpers

### Legacy Pipeline Flow (Still Active)
```
Session Data + Tracker Context → Unit A → Unit B → Unit C → Unit D → Enhanced Results
                   ↓              ↓        ↓        ↓        ↓
               HTF Context    Foundation  Energy  Advanced Integration  
               FVG State      Equations   Structure Dynamics Validation
               Liquidity      (1-4,8-11,  (16-22,  (12-15,  (5-7,39-47)
               Registry       48-60)      32-38)   23-31)
```

### NEW: Fractal Cascade Architecture 🌟
```
HTF Events → HTF Master Controller → Activation Signal → Session Subordinate Executor → Precise Predictions
    ↓              ↓                       ↓                      ↓
Historical    Intensity >0.5         Parameter          HTF-Enhanced
Events        Threshold Met          Enhancement        Session Hawkes
(Hours/Days)  λ_HTF = 457.97        175.8x Boost       λ_session_enhanced
```

### Key Components

**Legacy Components:**
- **Pipeline Orchestrator** (`src/pipeline.py`): Manages sequential A→B→C→D execution
- **Computational Units** (`src/unit_*.py`): Each unit encapsulates specific mathematical equations  
- **Preprocessing Agent** (`src/preprocessing_agent.py`): Manages session preprocessing with selective JSON processing
- **Tracker State System** (`src/tracker_state.py`): Handles FVG state, HTF context, liquidity registry
- **Monte Carlo Simulation** (`monte_carlo.py`): Forward-looking probability simulations

**NEW: Fractal Cascade Components:**
- **HTF Master Controller** (`src/htf_master_controller.py`): Monitors HTF intensity, generates activation signals
- **Session Subordinate Executor** (`src/session_subordinate_executor.py`): Dormant until HTF activation, provides precise timing
- **Fractal Integrator** (`src/fractal_cascade_integrator.py`): Complete master-subordinate system coordination
- **Temporal Marker Matrix**: Maps HTF events to session cascade predictions
- **Dynamic Processing Framework**: Real-time adaptation with new data integration

## Data Flow

### Session Processing
Sessions are processed with 3 tracker files that carry forward state:

```
Session Data → Grok A→B→C→D → HTF_Context_Session_grokEnhanced_YYYY_MM_DD.json
                             FVG_State_Session_grokEnhanced_YYYY_MM_DD.json  
                             Liquidity_State_Session_grokEnhanced_YYYY_MM_DD.json
```

### File Naming Convention
All files follow: `[session]_grokEnhanced_YYYY_MM_DD.json`
- Results: `asia_grokEnhanced_2025_07_22.json`
- Trackers: `HTF_Context_Asia_grokEnhanced_2025_07_22.json`

## Critical Implementation Details

### Grok API Integration
- **Dual API Approach**: HTTP REST API and CLI subprocess
- **Centralized Configuration**: `src/config.py` manages API keys and timeouts
- **Unit-Specific Timeouts**: Unit A(180s), Unit B(300s), Unit C(180s), Unit D(120s)

### JSON File Handling (CRITICAL)
- **🔧 ALWAYS USE STANDARDIZED FUNCTIONS**: `from src.utils import load_json_data, save_json_data`
- **Smart Path Resolution**: Automatically finds files in migration locations
- **Migration-Safe Operations**: System immune to file reorganization

### Sequential Dependencies
Units **must** execute in A→B→C→D order due to mathematical dependencies.

### Performance Optimizations
- **Hawkes Volume Caching**: 937x performance improvement with session hash-based caching
- **Selective JSON Preprocessing**: 90% payload reduction prevents API timeouts
- **Validation Framework**: `validate_optimizations_simple.py`

## Production Systems

### News-Integrated Monte Carlo System ✅ PRODUCTION READY
- **Performance**: 0.39 min average error (97.5% improvement)
- **Formula**: `time_to_event = 0.5 * volatility_factor * news_multiplier * intra_session_factor`
- **News Multipliers**: EXTREME(0.3x), HIGH(0.5x), FED_SPEECH(0.6x), NO_NEWS(1.0x)
- **Usage**: `python3 grok_monte_carlo_package.py`

### Hawkes Process Cascade Prediction ✅ PRODUCTION READY  
- **Performance**: 0.0 minute error cascade timing
- **Method**: Self-exciting point process with ICT event sequences
- **Usage**: `python3 src/hawkes_cascade_predictor.py`

### Production Files
- `production/grok_monte_carlo_package.py`: Complete validation package
- `src/hawkes_cascade_predictor.py`: Perfect cascade timing
- `src/micro_timing_analysis.py`: Complete timing analysis
- `validation/validate_optimizations_simple.py`: Performance validation

## Development Guidelines

**Key Architecture Principles:**
1. **Sequential Processing**: Units cannot run in parallel due to mathematical dependencies
2. **State Persistence**: Tracker files maintain cross-session mathematical continuity  
3. **Failure Recovery**: All components include graceful degradation

**When extending the system:**
- Maintain A→B→C→D sequential pattern
- Use `src/config.py` for API key handling
- Follow `[session]_grokEnhanced_YYYY_MM_DD.json` naming convention
- **🔧 CRITICAL**: Always use `load_json_data`/`save_json_data` for JSON operations
- **🚀 PERFORMANCE**: Consider caching for expensive calculations
- **🔧 VALIDATION**: Run `validate_optimizations_simple.py` after changes