# Grok Claude Automation - Dependencies Guide

## Overview
This document outlines the dependency structure, import patterns, and module relationships within the Grok Claude Automation project.

## Core Dependencies

### Required Python Packages
```bash
pip install numpy pandas requests python-dateutil dataclasses-json
```

### Internal Module Structure

#### Core Pipeline (`src/`)
- **`src.pipeline`** - Main pipeline orchestrator (GrokPipeline)
- **`src.unit_a`** - Foundation calculations unit
- **`src.unit_b`** - Energy structure unit  
- **`src.unit_c`** - Advanced dynamics unit
- **`src.unit_d`** - Integration validation unit
- **`src.grok_client`** - Grok API client base classes
- **`src.config`** - Centralized configuration management
- **`src.utils`** - Logging and utility functions

#### Experimental Features (`src.experimental/`)
- **`src.experimental.cross_session_predictor`** - Cross-session analysis
- **`src.experimental.grok_interface`** - Enhanced Grok 4 interface
- **`src.experimental.event_chain_discovery`** - Event pattern discovery

## Import Guidelines

### ✅ Correct Import Patterns
```python
# Standard imports
from src.pipeline import GrokPipeline
from src.utils import get_logger
from src.config import get_api_key

# Experimental imports
from src.experimental.cross_session_predictor import CrossSessionPredictionSystem
```

### ❌ Deprecated Patterns (Fixed)
```python
# REMOVED - No longer used
import sys
sys.path.append('src')
sys.path.append('.')
```

## Module Dependencies

### Prediction Systems Hierarchy
```
Multi-Agent Prediction Systems:
├── adaptive_ensemble_predictor.py
│   ├── corrected_grok_predictor.py
│   ├── fvg_enhanced_event_engine.py  
│   └── performance_tracker.py
├── timing_agent_competition.py
│   ├── market_state_hmm.py
│   └── parallel_event_streams.py
└── cross_session_event_predictor.py
    └── src.experimental.cross_session_predictor
```

### Core Pipeline Dependencies
```
Pipeline Processing:
├── src.pipeline.GrokPipeline
│   ├── src.unit_a
│   ├── src.unit_b
│   ├── src.unit_c
│   └── src.unit_d
├── src.tracker_state.TrackerStateManager
└── src.config (API keys, timeouts)
```

## External API Dependencies

### Grok API Requirements
- **API Key**: Set via `GROK_API_KEY` environment variable
- **Endpoints**: Configured in `src.grok_api_client`
- **Timeouts**: Configured in `src.config.TimeoutConfig`

### Optional Dependencies
- **HMM Libraries**: For market state detection (falls back to basic implementation)
- **Advanced Analytics**: NumPy, SciPy for Monte Carlo simulations

## Data File Dependencies

### Session Data Structure
```
data/
├── sessions/           # Session analysis results
│   ├── *grokEnhanced*.json
│   └── *eventTimingEnhanced*.json
├── trackers/          # Cross-session state tracking
│   ├── HTF_Context_*.json
│   ├── FVG_State_*.json
│   └── Liquidity_State_*.json
└── predictions/       # Prediction results
    └── *prediction*.json
```

## Installation & Setup

### Quick Start
```bash
# Clone repository
git clone <repository>
cd grok-claude-automation

# Install dependencies
pip install -r requirements.txt

# Set API key
export GROK_API_KEY=xai-your-api-key-here

# Test installation
python3 -c "from src.config import get_api_key; print('✅ Setup complete')"
```

### Development Setup
```bash
# Enable development mode with logging
export LOG_LEVEL=DEBUG

# Run tests
python3 -m pytest tests/ (when available)

# Check imports
python3 -c "import adaptive_ensemble_predictor; print('✅ Imports working')"
```

## Common Issues & Solutions

### ImportError Solutions
1. **Module not found**: Ensure working directory is project root
2. **Circular imports**: Check for mutual dependencies between modules
3. **Missing dependencies**: Run `pip install -r requirements.txt`

### API Issues
1. **API key not found**: Set `GROK_API_KEY` environment variable
2. **Timeout errors**: Check `src.config.TimeoutConfig` settings
3. **Rate limiting**: Implement backoff in prediction loops

### File Path Issues
1. **Data files not found**: Check `data/` directory structure
2. **Session files missing**: Ensure proper file naming convention
3. **Permission errors**: Check file/directory permissions

## Development Guidelines

### Adding New Predictors
1. Follow existing patterns in `adaptive_ensemble_predictor.py`
2. Use `src.utils.get_logger()` for logging
3. Import from `src` modules using absolute imports
4. Handle ImportError gracefully with fallbacks

### Integration Testing
```python
# Test core pipeline
from src.pipeline import GrokPipeline
pipeline = GrokPipeline()

# Test prediction systems  
from adaptive_ensemble_predictor import AdaptivePredictor
predictor = AdaptivePredictor()

# Test utilities
from src.utils import get_logger
logger = get_logger(__name__)
```

## Migration Notes

### Recent Changes (2025-07-28)
- ✅ Removed all `sys.path.append` patterns
- ✅ Standardized import system
- ✅ Organized data files into `data/` structure
- ✅ Added centralized logging utilities
- ✅ Created proper `.gitignore` for project

### Breaking Changes
- **Import paths**: Some imports may need updating if using old patterns
- **File locations**: Data files moved to `data/` subdirectories
- **Logging**: Replace print statements with `get_logger()` for consistency

For questions or issues, refer to the main `CLAUDE.md` documentation or check the `src.config` module for configuration details.