# Asia Weekend Prediction System - Quick Reference Guide

## 🚀 Quick Start

### Basic Usage
```python
from asia_weekend_predictor import AsiaWeekendPredictor

# Initialize predictor
predictor = AsiaWeekendPredictor()

# Make prediction with NWOG
prediction = predictor.predict_asia_weekend_session(
    friday_close=23250.0,        # Previous week's close
    sunday_open=23320.0,         # New Week Opening Gap (NWOG)
    session_volatility=1.0       # Expected volatility (0.5-2.0)
)

# Get results
print(f"Cascade Time: {prediction.consensus_cascade_time:.2f} minutes")
print(f"Confidence: {prediction.consensus_confidence:.1%}")
print(f"Deployment: {prediction.deployment_recommendation}")
```

### With Weekend News
```python
weekend_news = {
    'high_impact_events': [
        {'event': 'Fed Chair Weekend Statement', 'impact': 'high'}
    ],
    'medium_impact_events': [
        {'event': 'Oil Supply Disruption', 'impact': 'medium'}
    ],
    'geopolitical_events': [],
    'sentiment_analysis': {'bullish': 0.7, 'bearish': 0.2, 'neutral': 0.1}
}

prediction = predictor.predict_asia_weekend_session(
    friday_close=23250.0,
    sunday_open=23320.0,
    weekend_news=weekend_news,
    session_volatility=1.2
)
```

## 📊 Gap Classification Guide

| Gap Size | Classification | Expected Timing | Confidence | Deployment |
|----------|---------------|-----------------|------------|------------|
| >50 points | Major | 0.05-0.10 min | 80-85% | Enhanced monitoring |
| 20-50 points | Moderate | 0.10-0.15 min | 85-90% | Standard deployment |
| 5-20 points | Minor | 0.30-2.0 min | 60-80% | Standard deployment |
| <5 points | Minimal | 2.0-9.0 min | 65-70% | Monitoring mode |

## 🔧 Command Reference

### Individual Components
```bash
# Weekend gap analysis only
python3 src/weekend_gap_analyzer.py

# Monte Carlo predictions only  
python3 weekend_adapted_monte_carlo.py

# Hawkes process predictions only
python3 hawkes_weekend_adaptation.py

# Complete multi-method system
python3 asia_weekend_predictor.py

# System demonstration
python3 demo_asia_weekend_prediction.py
```

### Integration with Existing Systems
```bash
# Use with preprocessing agent
python3 -c "
from src.preprocessing_agent import create_preprocessing_agent
from asia_weekend_predictor import AsiaWeekendPredictor

# Process regular session
agent = create_preprocessing_agent()
session_result = agent.process_session('asia_session_l1.json')

# Get weekend prediction
predictor = AsiaWeekendPredictor()
weekend_prediction = predictor.predict_asia_weekend_session(
    friday_close=23250.0,
    sunday_open=23320.0
)
"
```

## 📈 Performance Expectations

### Method Performance (Theoretical)
- **Weekend Monte Carlo**: 0.08-0.43 min (builds on 0.39min base)
- **NWOG Hawkes Process**: 0.05-29.70 min (builds on 0.0min base)  
- **Gap Heuristic**: 0.10-1.97 min (supplementary method)
- **Ensemble Validation**: Cross-validation scoring

### Confidence Levels
- **>85%**: High confidence deployment
- **70-85%**: Moderate confidence deployment
- **<70%**: Monitoring mode recommended

## ⚠️ Important Notes

### Data Requirements
- **Friday Close**: Previous week's closing price
- **Sunday NWOG**: Actual New Week Opening Gap price (critical)
- **Weekend News**: Optional but significantly improves accuracy
- **Session Volatility**: Expected volatility factor (default: 1.0)

### Validation Status
- ✅ **Mathematical Framework**: Complete
- ✅ **Theoretical Performance**: Based on proven systems
- ❌ **Real-World Validation**: Requires historical backtesting

### Best Practices
1. **Always use real NWOG**: Theoretical gaps reduce accuracy significantly
2. **Include weekend news**: Major impact on prediction quality
3. **Monitor major gaps**: >50pt gaps require enhanced monitoring
4. **Validate predictions**: Track actual vs predicted for calibration
5. **Use confidence levels**: Deploy based on system recommendations

## 🔍 Troubleshooting

### Common Issues
```python
# Import errors
# Solution: Ensure all files are in correct locations
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Low confidence predictions
# Solution: Check gap magnitude and weekend news quality
if prediction.consensus_confidence < 0.7:
    print("Consider enhanced monitoring mode")

# Extreme predictions (>30 min)
# Solution: Review input data quality
if prediction.consensus_cascade_time > 30:
    print("Check Friday close and NWOG data accuracy")
```

### Performance Optimization
```python
# Cache predictor instance for multiple predictions
predictor = AsiaWeekendPredictor()  # Initialize once

# Batch multiple weekend scenarios
scenarios = [
    {'friday_close': 23250, 'sunday_open': 23320},
    {'friday_close': 23275, 'sunday_open': 23285},
    # ... more scenarios
]

results = []
for scenario in scenarios:
    prediction = predictor.predict_asia_weekend_session(**scenario)
    results.append(prediction)
```

## 📞 System Integration

### With Monte Carlo System
```python
# Get both weekend and regular predictions
from monte_carlo_adapter import run_monte_carlo_from_session
from asia_weekend_predictor import AsiaWeekendPredictor

# Regular session prediction
regular_prediction = run_monte_carlo_from_session('session.json')

# Weekend-enhanced prediction  
weekend_predictor = AsiaWeekendPredictor()
weekend_prediction = weekend_predictor.predict_asia_weekend_session(
    friday_close=23250.0, sunday_open=23320.0
)

# Compare results
print(f"Regular: {regular_prediction.timing:.2f} min")
print(f"Weekend: {weekend_prediction.consensus_cascade_time:.2f} min")
```

### With Hawkes Process
```python
# Compare with base Hawkes system
from src.hawkes_cascade_predictor import HawkesCascadePredictor
from asia_weekend_predictor import AsiaWeekendPredictor

# Base Hawkes prediction
base_hawkes = HawkesCascadePredictor()
base_result = base_hawkes.predict_cascade_timing(session_data)

# Weekend-enhanced Hawkes
weekend_predictor = AsiaWeekendPredictor()
weekend_result = weekend_predictor.predict_asia_weekend_session(
    friday_close=23250.0, sunday_open=23320.0
)

print(f"Base Hawkes: {base_result.predicted_cascade_time:.2f} min")
print(f"Weekend Enhanced: {weekend_result.consensus_cascade_time:.2f} min")
```

---

## 🏆 System Status

**Implementation**: ✅ Complete  
**Testing**: ✅ Theoretical validation complete  
**Production**: ⚠️ Awaiting historical data validation  
**Documentation**: ✅ Complete

For detailed information, see:
- `CHANGELOG_ASIA_WEEKEND_SYSTEM.md` - Complete implementation details
- `CLAUDE.md` - Full system documentation  
- `demo_asia_weekend_prediction.py` - Working examples