# Asia Lvl-1 Processing Guide for <PERSON>

## 📋 Complete Workflow

### Step 1: Prepare the Prompt
Use the ready-to-send prompt from `CLAUDE_ASIA_LVL1_PROMPT.txt`:

```bash
# Copy the complete prompt
cat CLAUDE_ASIA_LVL1_PROMPT.txt
```

### Step 2: Insert Actual Session Data
Replace `[INSERT ACTUAL ASIA SESSION PRICE ACTION DATA HERE]` with real market data:

**Example Data Format:**
```
19:00:00 - Asia session opens at 23520.00 (NWOG level)
19:00:05 - Price drops to 23518.25 (cascade initiation)
19:01:32 - Further drop to 23495.50 (liquidity sweep)
19:03:45 - Gap fill reached at 23449.50 (near Friday close)
19:05:12 - Session low at 23445.25 (slight overshoot)
19:08:23 - Partial retracement to 23467.75
[Continue with all significant moves and timestamps]
```

### Step 3: Send to <PERSON>
Send the complete prompt with data to <PERSON>. Expect **ONLY JSON output** - no explanations.

### Step 4: Validate Output
```bash
# Save <PERSON>'s output as asia_session_output.json
# Then validate:
python3 validate_asia_lvl1_json.py asia_session_output.json
```

### Step 5: Process with System
```bash
# Once validated, process through pipeline:
python3 -c "
from src.preprocessing_agent import create_preprocessing_agent
agent = create_preprocessing_agent()
result = agent.process_session('asia_session_output.json')
print(f'✅ Enhanced data: {result[\"validation_results\"][\"field_count\"]} fields')
"
```

## 🎯 Key Validation Points

### Critical Timing Analysis
- **Cascade Prediction**: Was 4.5-second prediction accurate?
- **Gap Fill Behavior**: Did 72.2% probability manifest?
- **Method Performance**: Which prediction method was most accurate?

### Required Data Quality
- **All timestamps in HH:MM:SS format**
- **All prices as numbers (not strings)**
- **Boolean values as true/false (lowercase)**
- **No trailing commas in JSON**
- **Complete validation_context section**

### Success Criteria
1. **JSON validates without errors**
2. **Cascade timing documented precisely**
3. **Gap fill behavior tracked completely**
4. **NWOG prediction accuracy measured**
5. **All method performance assessments included**

## 📊 Expected Validation Results

### If Prediction Was Accurate
```json
"cascade_validation": {
  "predicted_cascade_time": "19:00:05",
  "actual_cascade_time": "19:00:04",
  "prediction_accuracy_seconds": 1.0,
  "cascade_occurred": true,
  "prediction_accuracy_assessment": "EXCELLENT"
}
```

### If Gap Fill Occurred
```json
"gap_fill_analysis": {
  "predicted_fill_probability": 72.2,
  "actual_fill_behavior": "rapid_fill_with_slight_overshoot",
  "fill_accuracy_validation": "SUCCESS"
}
```

## 🔧 Troubleshooting

### Common Issues
1. **JSON Syntax Errors**: Check for trailing commas, missing quotes
2. **Type Errors**: Ensure numbers are numbers, booleans are true/false
3. **Missing Fields**: Verify all required sections are present
4. **Timestamp Format**: Must be HH:MM:SS (24-hour)

### Validation Commands
```bash
# Check JSON syntax only
python3 -c "import json; print('Valid JSON' if json.load(open('file.json')) else 'Invalid')"

# Full structure validation
python3 validate_asia_lvl1_json.py file.json

# Process validated file
python3 -c "from src.utils import load_json_data; print(load_json_data('file.json')['session_metadata'])"
```

## 🏆 Real-World Validation Context

This Asia session represents the **FIRST REAL-WORLD TEST** of the NWOG prediction system:

- **Historic Significance**: Testing 4.5-second cascade prediction
- **Gap Analysis**: Validating 72.2% fill probability  
- **Method Comparison**: Measuring which approach was most accurate
- **System Calibration**: Learning from real market behavior

The resulting Lvl-1 JSON will provide crucial validation data for the entire NWOG-integrated weekend prediction system.

## 📝 Template Files Reference

- **`CLAUDE_ASIA_LVL1_PROMPT.txt`**: Ready-to-send prompt
- **`ASIA_LVL1_JSON_TEMPLATE.json`**: Perfect format example
- **`validate_asia_lvl1_json.py`**: Validation script
- **`ASIA_LVL1_PROMPT_TEMPLATE.md`**: Detailed instructions

**Status**: 🎯 **READY FOR REAL-WORLD VALIDATION** - System prepared for first NWOG prediction test