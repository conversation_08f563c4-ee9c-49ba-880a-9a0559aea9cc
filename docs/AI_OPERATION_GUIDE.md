# AI Operation Guide - Production Systems

This guide instructs AI agents on operating the production-ready trading prediction systems.

## 🎯 SYSTEM OVERVIEW

**PRIMARY FUNCTION**: Event timing prediction with news integration and cascade analysis
**PRODUCTION STATUS**: ✅ Ready for deployment
**CORE SYSTEMS**: <PERSON> (0.39min error), <PERSON>s Process (0.0min error)

## 📋 REQUIRED FILES CHECKLIST

### Production System Files
```
✅ grok_monte_carlo_package.py            # Main production system
✅ src/hawkes_cascade_predictor.py        # Perfect cascade timing  
✅ src/micro_timing_analysis.py           # Complete timing analysis
✅ src/preprocessing_agent.py             # Session preprocessing
✅ validate_optimizations_simple.py       # Performance validation
```

### Session Data Requirements
```
REQUIRED for predictions:
├── [session]_grokEnhanced_YYYY_MM_DD.json     # Main session data
├── HTF_Context_[session]_grokEnhanced_YYYY_MM_DD.json
├── FVG_State_[session]_grokEnhanced_YYYY_MM_DD.json  
└── Liquidity_State_[session]_grokEnhanced_YYYY_MM_DD.json
```

## 🚀 PRODUCTION OPERATIONS

### 1. PRIMARY SYSTEM - Monte Carlo with News Integration
```bash
# Run complete production validation
python3 grok_monte_carlo_package.py

# Expected output:
# Phase 1: Universal Base Robustness ✅ 100% success
# Phase 2: Distance Factor Sensitivity ✅ Minimal variance  
# Phase 3: Session-Specific Testing ✅ Universal base optimal
```

**News Multipliers:**
- EXTREME (RED++): 0.3x multiplier → 0.154 min error
- HIGH (RED): 0.5x multiplier → 0.202-0.253 min error
- FED_SPEECH: 0.6x multiplier → 0.307 min error
- NO_NEWS: 1.0x multiplier → 0.503 min error

### 2. HAWKES PROCESS - Perfect Cascade Timing
```bash
# Run perfect cascade prediction
python3 src/hawkes_cascade_predictor.py
# Expected: 0.0 minute cascade prediction error

# Run integrated micro timing analysis
python3 src/micro_timing_analysis.py
# Expected: Complete timing analysis with Hawkes integration
```

### 3. SESSION PREPROCESSING 
```bash
# Process single session with preprocessing agent
python3 -c "
from src.preprocessing_agent import create_preprocessing_agent
agent = create_preprocessing_agent()
result = agent.process_session('session_l1_YYYY_MM_DD.json')
print(f'✅ Enhanced data: {result[\"validation_results\"][\"field_count\"]} fields')
print(f'✅ Tracker files: {len(result[\"tracker_files\"])}')
"

# Batch process multiple sessions
python3 -c "
from src.preprocessing_agent import create_preprocessing_agent
agent = create_preprocessing_agent()
batch_result = agent.batch_process_sessions(['asia_l1.json', 'london_l1.json'])
print(f'✅ Success rate: {batch_result[\"batch_results\"][\"success_rate\"]:.1%}')
"
```

### 4. SYSTEM VALIDATION
```bash
# Validate performance optimizations
python3 validate_optimizations_simple.py

# Run shadow validation system  
python3 src/shadow_validation_system.py

# Cross-session analysis
python3 run_cross_session_analysis.py
```

## 🔧 COMMON OPERATION PATTERNS

### Pattern 1: Daily Session Processing
```bash
# 1. Process session data
python3 -c "
from src.preprocessing_agent import create_preprocessing_agent
agent = create_preprocessing_agent()
result = agent.process_session('session_l1_YYYY_MM_DD.json')
"

# 2. Run Monte Carlo prediction
python3 grok_monte_carlo_package.py

# 3. Run Hawkes cascade prediction
python3 src/hawkes_cascade_predictor.py

# 4. Validate results
python3 validate_optimizations_simple.py
```

### Pattern 2: Real-Time Monitoring
```bash
# Start continuous validation
python3 src/shadow_validation_system.py

# Monitor micro timing
python3 src/micro_timing_analysis.py
```

### Pattern 3: Cross-Session Analysis
```bash
# Run Asia → London predictions
python3 run_cross_session_analysis.py

# Process batch sessions
python3 -c "
from src.preprocessing_agent import create_preprocessing_agent
agent = create_preprocessing_agent()
batch_result = agent.batch_process_sessions(['asia_l1.json', 'london_l1.json', 'ny_am_l1.json'])
"
```

## ❌ COMMON ERROR CONDITIONS

### Missing Session Files
```
❌ Problem: FileNotFoundError for grokEnhanced files
✅ Solution: Ensure all 4 session files exist:
   - [session]_grokEnhanced_YYYY_MM_DD.json
   - HTF_Context_[Session]_grokEnhanced_YYYY_MM_DD.json  
   - FVG_State_[Session]_grokEnhanced_YYYY_MM_DD.json
   - Liquidity_State_[Session]_grokEnhanced_YYYY_MM_DD.json
```

### API Timeouts
```
❌ Problem: Grok API timeout errors
✅ Solution: Use preprocessing agent with selective JSON extraction:
   - Reduces payload from 10KB+ to <1KB
   - Prevents all timeout issues
   - Guaranteed <300 second processing time
```

### Import Failures
```
❌ Problem: ModuleNotFoundError
✅ Solution: Verify core files exist:
   python3 -c "import src.hawkes_cascade_predictor, src.micro_timing_analysis"
```

## 📊 OUTPUT INTERPRETATION

### Monte Carlo Results
```json
{
  "universal_base_performance": {
    "success_rate": 1.0,
    "average_error_minutes": 0.39,
    "news_impact_validated": true
  }
}
```

### Hawkes Process Results  
```json
{
  "hawkes_cascade_prediction": {
    "predicted_cascade_time": 8.0,
    "actual_cascade_time": 8.0,
    "prediction_error_minutes": 0.0,
    "accuracy_grade": "excellent"
  }
}
```

### Preprocessing Results
```json
{
  "preprocessing_results": {
    "processing_time_ms": 233551,
    "enhanced_data_generated": true,
    "tracker_files_created": 3,
    "api_timeouts": 0
  }
}
```

## 🚨 CRITICAL SUCCESS REQUIREMENTS

### Before Running Predictions:
1. ✅ Verify all 4 session files exist with correct naming
2. ✅ Check GROK_API_KEY is set: `export GROK_API_KEY=xai-your-key`
3. ✅ Ensure core system files are accessible
4. ✅ Validate session data contains required fields

### For Accurate Results:
1. ✅ Use recent session data (same day preferred)
2. ✅ Include all tracker context files  
3. ✅ Run preprocessing agent first to prevent timeouts
4. ✅ Monitor for news events and apply appropriate multipliers
5. ✅ Validate results against actual events when available

## 🎯 SUCCESS VALIDATION

**System operating correctly when:**
- Monte Carlo predictions show 100% success rate in validation phases
- Hawkes process achieves 0.0 minute cascade timing errors
- Preprocessing agent completes sessions in <300 seconds
- No API timeout errors occur
- Enhanced data validation passes (energy_rate ≠ 1.0)

**Performance targets:**
- **Monte Carlo**: 0.39 min average error with news integration
- **Hawkes Process**: 0.0 min error for cascade timing
- **Preprocessing**: <300 seconds per session
- **Success Rate**: >90% timing predictions within target windows

## 📋 DAILY OPERATION CHECKLIST

### Production Workflow:
- [ ] Check all required system files exist
- [ ] Set GROK_API_KEY environment variable
- [ ] Process session data using preprocessing agent
- [ ] Run Monte Carlo validation package
- [ ] Execute Hawkes cascade prediction
- [ ] Validate performance optimizations
- [ ] Monitor for API timeouts or errors
- [ ] Log results and actual event timing
- [ ] Update news impact multipliers based on calendar
- [ ] Run cross-session analysis if needed

This guide ensures AI agents can operate the production systems correctly with minimal complexity while maintaining full functionality.